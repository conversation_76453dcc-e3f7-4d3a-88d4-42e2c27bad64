<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M11,8,6,14.5,1,8,6,1.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M15,8l-5,6.5L5,8l5-6.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M10.4,1.2H9.6L8,3.28,6.4,1.2H5.6L.6,7.7v.61l5,6.5H6.4L8,12.72l1.6,2.085H10.4l5-6.5V7.7ZM6,13.68,1.631,8,6,2.32,7.369,4.1,4.6,7.7v.61l2.765,3.6ZM8,4.92,10.369,8,8,11.08,5.631,8Zm2,8.76L8.631,11.9l2.765-3.6V7.7L8.631,4.1,10,2.32,14.369,8Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
