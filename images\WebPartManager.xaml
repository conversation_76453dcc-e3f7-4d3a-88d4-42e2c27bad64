<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,3H7V2H8Zm5,4h1V6H13ZM9,3h1V2H9Zm4.5-1H13V3h1V2.5ZM13,5h1V4H13Zm0,4h1V8H13ZM11,3h1V2H11ZM8,14H9V13H8ZM2,10H3V9H2Zm4,4H7V13H6Zm-4-.5.5.5H3V13H2ZM2,12H3V11H2ZM2,8H3V7H2Zm2,6H5V13H4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M4.5,4.5h-2v-2h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4.5,5h-2L2,4.5v-2L2.5,2h2l.5.5v2ZM3,4H4V3H3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5.038,8.5a2.994,2.994,0,0,1,.253-1.795L7.282,8.7,8.7,7.282l-2-1.991a2.983,2.983,0,0,1,3.988,3.983L14,12.586v.707L13.293,14h-.707L9.274,10.688A2.98,2.98,0,0,1,5.038,8.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
