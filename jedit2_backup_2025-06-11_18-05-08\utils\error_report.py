"""Error report dialog for JEdit2.

This module provides a dialog for viewing and managing error reports.
"""

import wx
import wx.grid
import csv
from datetime import datetime
from typing import Dict, Any, List, Optional
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>evel, ErrorCategory


class ErrorReportDialog(wx.Dialog):
    """Dialog for viewing and managing error reports."""
    
    def __init__(self, parent: wx.Window) -> None:
        """Initialize the error report dialog.
        
        Args:
            parent: Parent window
        """
        super().__init__(
            parent,
            title="Error Report",
            size=(800, 600),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.error_handler = ErrorHandler.get_instance()
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create statistics panel
        stats_panel = self._create_stats_panel()
        main_sizer.Add(stats_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Create error history grid
        self.grid = self._create_history_grid()
        main_sizer.Add(self.grid, 1, wx.EXPAND | wx.ALL, 5)
        
        # Create button sizer
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        export_button = wx.Button(self, label="Export Report")
        export_button.Bind(wx.EVT_BUTTON, self._on_export)
        button_sizer.Add(export_button, 0, wx.ALL, 5)
        
        clear_button = wx.Button(self, label="Clear History")
        clear_button.Bind(wx.EVT_BUTTON, self._on_clear)
        button_sizer.Add(clear_button, 0, wx.ALL, 5)
        
        close_button = wx.Button(self, label="Close")
        close_button.Bind(wx.EVT_BUTTON, self._on_close)
        button_sizer.Add(close_button, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.ALIGN_RIGHT | wx.ALL, 5)
        
        # Set main sizer
        self.SetSizer(main_sizer)
    
    def _create_stats_panel(self) -> wx.Panel:
        """Create the statistics panel.
        
        Returns:
            Statistics panel
        """
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Get error statistics
        stats = self.error_handler.get_error_stats()
        
        # Create stat boxes
        for level in ["total", "info", "warning", "error", "critical"]:
            box = wx.StaticBox(panel, label=level.title())
            box_sizer = wx.StaticBoxSizer(box, wx.VERTICAL)
            
            value = wx.StaticText(
                panel,
                label=str(stats.get(level, 0)),
                style=wx.ALIGN_CENTER
            )
            value.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            
            box_sizer.Add(value, 0, wx.ALIGN_CENTER | wx.ALL, 5)
            sizer.Add(box_sizer, 1, wx.EXPAND | wx.ALL, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_history_grid(self) -> wx.grid.Grid:
        """Create the error history grid.
        
        Returns:
            Error history grid
        """
        grid = wx.grid.Grid(self)
        
        # Set up grid
        grid.CreateGrid(0, 5)
        grid.SetColLabelValue(0, "Timestamp")
        grid.SetColLabelValue(1, "Level")
        grid.SetColLabelValue(2, "Category")
        grid.SetColLabelValue(3, "Message")
        grid.SetColLabelValue(4, "Context")
        
        # Set column sizes
        grid.SetColSize(0, 150)  # Timestamp
        grid.SetColSize(1, 80)   # Level
        grid.SetColSize(2, 100)  # Category
        grid.SetColSize(3, 300)  # Message
        grid.SetColSize(4, 150)  # Context
        
        # Enable sorting (if available in this wxPython version)
        try:
            grid.EnableSorting(True)
        except AttributeError:
            pass  # EnableSorting not available in this wxPython version
        
        # Populate grid
        self._populate_grid(grid)
        
        return grid
    
    def _populate_grid(self, grid: wx.grid.Grid) -> None:
        """Populate the grid with error history.
        
        Args:
            grid: Grid to populate
        """
        # Clear existing rows
        if grid.GetNumberRows() > 0:
            grid.DeleteRows(0, grid.GetNumberRows())
        
        # Get error history
        history = self.error_handler.get_error_history()
        
        # Add rows
        for record in history:
            row = grid.GetNumberRows()
            grid.AppendRows(1)
            
            # Set cell values
            grid.SetCellValue(row, 0, record["timestamp"])
            grid.SetCellValue(row, 1, record["level"])
            grid.SetCellValue(row, 2, record["category"])
            grid.SetCellValue(row, 3, record["message"])
            
            # Format context
            context = record.get("context", {})
            context_str = ", ".join(f"{k}: {v}" for k, v in context.items())
            grid.SetCellValue(row, 4, context_str)
            
            # Set cell colors based on level
            if record["level"] == ErrorLevel.ERROR.value:
                color = wx.Colour(255, 200, 200)  # Light red
            elif record["level"] == ErrorLevel.WARNING.value:
                color = wx.Colour(255, 255, 200)  # Light yellow
            elif record["level"] == ErrorLevel.CRITICAL.value:
                color = wx.Colour(255, 150, 150)  # Darker red
            else:
                color = wx.Colour(200, 255, 200)  # Light green
            
            for col in range(5):
                grid.SetCellBackgroundColour(row, col, color)
    
    def _on_export(self, event: wx.CommandEvent) -> None:
        """Handle export button click.
        
        Args:
            event: Command event
        """
        # Create file dialog
        with wx.FileDialog(
            self,
            "Export Error Report",
            wildcard="CSV files (*.csv)|*.csv",
            style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT
        ) as file_dialog:
            if file_dialog.ShowModal() == wx.ID_CANCEL:
                return
            
            # Get selected path
            path = file_dialog.GetPath()
            
            # Export report
            self._export_report(path)
    
    def _export_report(self, path: str) -> None:
        """Export error report to CSV file.
        
        Args:
            path: Path to save report
        """
        try:
            with open(path, "w", newline="") as file:
                writer = csv.writer(file)
                
                # Write header
                writer.writerow([
                    "Timestamp",
                    "Level",
                    "Category",
                    "Message",
                    "Context"
                ])
                
                # Write data
                for record in self.error_handler.get_error_history():
                    context = record.get("context", {})
                    context_str = ", ".join(f"{k}: {v}" for k, v in context.items())
                    
                    writer.writerow([
                        record["timestamp"],
                        record["level"],
                        record["category"],
                        record["message"],
                        context_str
                    ])
            
            wx.MessageBox(
                "Error report exported successfully.",
                "Export Complete",
                wx.OK | wx.ICON_INFORMATION
            )
        except Exception as e:
            wx.MessageBox(
                f"Failed to export error report: {str(e)}",
                "Export Error",
                wx.OK | wx.ICON_ERROR
            )
    
    def _on_clear(self, event: wx.CommandEvent) -> None:
        """Handle clear button click.
        
        Args:
            event: Command event
        """
        # Confirm clear
        result = wx.MessageBox(
            "Are you sure you want to clear the error history?",
            "Confirm Clear",
            wx.YES_NO | wx.ICON_QUESTION
        )
        
        if result == wx.YES:
            # Clear history
            self.error_handler.clear_error_history()
            
            # Update grid
            self._populate_grid(self.grid)
            
            # Update statistics
            stats_panel = self._create_stats_panel()
            self.GetSizer().Replace(0, stats_panel)
            stats_panel.Layout()
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button click.
        
        Args:
            event: Command event
        """
        self.EndModal(wx.ID_CLOSE) 