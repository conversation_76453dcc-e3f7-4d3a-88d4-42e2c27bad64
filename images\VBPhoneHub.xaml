<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5.5V13H9V8.086L7.914,7H6.5V.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9,14V13h5V1H7V7H6V.5L6.5,0h8l.5.5v13l-.5.5M11,3H10V2h1Zm1,9H9V11h3Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M7.5,15.5H.5v-7h7Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7.5,8H.5L0,8.5v7l.5.5h7l.5-.5v-7ZM7,15H1V10H7Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3,12H2V11H3Zm0,1H2v1H3Zm3-2H4v1H6Zm0,2H4v1H6Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
