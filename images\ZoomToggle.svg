<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.light-blue{fill: #005dba; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>ZoomToggle</title>
  <g id="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <g class="cls-1">
      <path class="light-defaultgrey-10" d="M6.5,1.5a5,5,0,0,0-5,5,4.07,4.07,0,0,0,.03.5H6v3H4v.83a4.919,4.919,0,0,0,2.5.67,5,5,0,0,0,0-10Z" />
      <path class="light-defaultgrey" d="M10.72,10.02A5.5,5.5,0,1,0,1,6.5a4.07,4.07,0,0,0,.03.5h1A4.07,4.07,0,0,1,2,6.5a4.5,4.5,0,1,1,2,3.74v1.15A5.4,5.4,0,0,0,6.5,12a5.468,5.468,0,0,0,3.52-1.28l5.13,5.13.7-.7Z" />
    </g>
    <path class="light-blue" d="M3,13H5v1H3v2H2V14H0V13H2V11H3ZM0,8V9H5V8Z" />
  </g>
</svg>
