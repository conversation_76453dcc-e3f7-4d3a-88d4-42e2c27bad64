#!/usr/bin/env python3
"""
Failure Pattern Analyzer for JEdit2 AI System

Systematically analyzes AI response failures to identify patterns,
categorize issues, and provide insights for improvement.
"""

import logging
import json
import re
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Set
from enum import Enum
from datetime import datetime


class FailureCategory(Enum):
    """Categories of AI response failures."""

    STRUCTURAL_INCONSISTENCY = "structural_inconsistency"
    SEMANTIC_AMBIGUITY = "semantic_ambiguity"
    PARAMETER_VALIDATION = "parameter_validation"
    COMMAND_SEQUENCE = "command_sequence"
    REFERENCE_ERROR = "reference_error"
    FORMAT_ERROR = "format_error"
    LOGIC_ERROR = "logic_error"
    UNKNOWN = "unknown"


class FailureSeverity(Enum):
    """Severity levels for failures."""

    CRITICAL = "critical"  # Completely wrong response
    HIGH = "high"  # Major functionality broken
    MEDIUM = "medium"  # Partial functionality issues
    LOW = "low"  # Minor issues
    COSMETIC = "cosmetic"  # Style/format issues only


@dataclass
class FailurePattern:
    """Represents a detected failure pattern."""

    pattern_id: str
    category: FailureCategory
    severity: FailureSeverity
    description: str
    trigger_conditions: List[str]
    example_queries: List[str]
    frequency: int
    affected_commands: Set[str]
    root_cause: str
    suggested_fix: str


@dataclass
class FailureAnalysis:
    """Complete analysis of a single failure."""

    query: str
    expected_response: Optional[List[Dict[str, Any]]]
    actual_response: Optional[List[Dict[str, Any]]]
    failure_categories: List[FailureCategory]
    severity: FailureSeverity
    specific_issues: List[str]
    pattern_matches: List[str]
    analysis_timestamp: str


@dataclass
class AnalysisReport:
    """Comprehensive failure analysis report."""

    total_failures: int
    analysis_period: str
    category_breakdown: Dict[str, int]
    severity_breakdown: Dict[str, int]
    top_patterns: List[FailurePattern]
    command_failure_rates: Dict[str, float]
    improvement_recommendations: List[str]
    generated_timestamp: str


class FailurePatternAnalyzer:
    """
    Advanced failure pattern detection and analysis system.

    Analyzes AI response failures to identify systemic issues,
    patterns, and improvement opportunities.
    """

    def __init__(self):
        """Initialize the failure pattern analyzer."""
        self.logger = logging.getLogger(__name__)

        # Pattern detection rules
        self.pattern_detectors = self._initialize_pattern_detectors()

        # Analysis storage
        self.failure_analyses: List[FailureAnalysis] = []
        self.detected_patterns: Dict[str, FailurePattern] = {}

        # Statistics tracking
        self.command_stats = defaultdict(lambda: {"total": 0, "failures": 0})
        self.pattern_frequency = Counter()

    def _initialize_pattern_detectors(self) -> Dict[str, Dict[str, Any]]:
        """Initialize pattern detection rules and conditions."""
        return {
            "column_reference_shift": {
                "category": FailureCategory.REFERENCE_ERROR,
                "severity": FailureSeverity.HIGH,
                "conditions": [
                    "INSERT.*COLUMN.*then.*COPY.*COLUMN",
                    "DELETE.*COLUMN.*then.*reference",
                    "structural.*operation.*followed.*by.*reference",
                ],
                "description": "Column references become invalid after structural operations",
                "root_cause": "AI doesn't account for column index shifts",
                "suggested_fix": "Implement context-aware column reference tracking",
            },
            "ambiguous_new_column": {
                "category": FailureCategory.SEMANTIC_AMBIGUITY,
                "severity": FailureSeverity.MEDIUM,
                "conditions": [
                    "new column [A-Z]",
                    "the new column",
                    "column [A-Z] after.*insert",
                ],
                "description": "'New column X' references are ambiguous after insertions",
                "root_cause": "Temporal ambiguity in column references",
                "suggested_fix": "Explicit column position tracking",
            },
            "missing_parameters": {
                "category": FailureCategory.PARAMETER_VALIDATION,
                "severity": FailureSeverity.CRITICAL,
                "conditions": [
                    "params.*{}",
                    "missing.*required.*parameter",
                    "None.*in.*params",
                ],
                "description": "Required command parameters are missing or None",
                "root_cause": "Incomplete parameter extraction from query",
                "suggested_fix": "Enhanced parameter validation and inference",
            },
            "invalid_command_sequence": {
                "category": FailureCategory.COMMAND_SEQUENCE,
                "severity": FailureSeverity.HIGH,
                "conditions": [
                    "COPY.*before.*OPEN",
                    "reference.*before.*create",
                    "operation.*on.*closed.*file",
                ],
                "description": "Commands executed in logically invalid order",
                "root_cause": "Lack of prerequisite validation",
                "suggested_fix": "Command dependency validation",
            },
            "format_inconsistency": {
                "category": FailureCategory.FORMAT_ERROR,
                "severity": FailureSeverity.LOW,
                "conditions": [
                    "malformed.*json",
                    "unexpected.*format",
                    "parsing.*error",
                ],
                "description": "Response format doesn't match expected structure",
                "root_cause": "Inconsistent response formatting",
                "suggested_fix": "Strict response format validation",
            },
            "contradiction_logic": {
                "category": FailureCategory.LOGIC_ERROR,
                "severity": FailureSeverity.HIGH,
                "conditions": [
                    "copy.*column.*A.*to.*column.*A",
                    "delete.*then.*reference.*same",
                    "contradictory.*operations",
                ],
                "description": "Logically contradictory operations in sequence",
                "root_cause": "Lack of logical consistency checking",
                "suggested_fix": "Pre-execution logic validation",
            },
        }

    def analyze_failure(
        self,
        query: str,
        expected_response: Optional[List[Dict[str, Any]]] = None,
        actual_response: Optional[List[Dict[str, Any]]] = None,
        error_details: Optional[str] = None,
    ) -> FailureAnalysis:
        """
        Analyze a single failure case.

        Args:
            query: The user query that failed
            expected_response: Expected command sequence (if known)
            actual_response: Actual AI response (if any)
            error_details: Additional error information

        Returns:
            Detailed failure analysis
        """
        # Detect failure categories
        categories = self._categorize_failure(query, actual_response, error_details)

        # Determine severity
        severity = self._assess_severity(categories, actual_response)

        # Identify specific issues
        issues = self._identify_specific_issues(query, actual_response, error_details)

        # Match against known patterns
        pattern_matches = self._match_patterns(query, actual_response, issues)

        analysis = FailureAnalysis(
            query=query,
            expected_response=expected_response,
            actual_response=actual_response,
            failure_categories=categories,
            severity=severity,
            specific_issues=issues,
            pattern_matches=pattern_matches,
            analysis_timestamp=datetime.now().isoformat(),
        )

        # Store analysis
        self.failure_analyses.append(analysis)

        # Update statistics
        self._update_statistics(analysis)

        return analysis

    def _categorize_failure(
        self,
        query: str,
        actual_response: Optional[List[Dict[str, Any]]],
        error_details: Optional[str],
    ) -> List[FailureCategory]:
        """Categorize the type of failure."""
        categories = []

        # Check for format issues
        if actual_response is None and error_details:
            if "json" in error_details.lower() or "format" in error_details.lower():
                categories.append(FailureCategory.FORMAT_ERROR)

        # Check for parameter issues
        if actual_response:
            for cmd in actual_response:
                if not cmd.get("params") or any(
                    v is None for v in cmd["params"].values()
                ):
                    categories.append(FailureCategory.PARAMETER_VALIDATION)
                    break

        # Check for sequence issues
        if self._has_sequence_issues(query, actual_response):
            categories.append(FailureCategory.COMMAND_SEQUENCE)

        # Check for reference issues
        if self._has_reference_issues(query, actual_response):
            categories.append(FailureCategory.REFERENCE_ERROR)

        # Check for semantic ambiguity
        if self._has_semantic_ambiguity(query):
            categories.append(FailureCategory.SEMANTIC_AMBIGUITY)

        # Default to unknown if no specific category detected
        if not categories:
            categories.append(FailureCategory.UNKNOWN)

        return categories

    def _assess_severity(
        self,
        categories: List[FailureCategory],
        actual_response: Optional[List[Dict[str, Any]]],
    ) -> FailureSeverity:
        """Assess the severity of the failure."""
        if actual_response is None:
            return FailureSeverity.CRITICAL

        # Critical severity conditions
        if (
            FailureCategory.PARAMETER_VALIDATION in categories
            or FailureCategory.COMMAND_SEQUENCE in categories
        ):
            return FailureSeverity.CRITICAL

        # High severity conditions
        if (
            FailureCategory.REFERENCE_ERROR in categories
            or FailureCategory.LOGIC_ERROR in categories
        ):
            return FailureSeverity.HIGH

        # Medium severity conditions
        if FailureCategory.SEMANTIC_AMBIGUITY in categories:
            return FailureSeverity.MEDIUM

        # Low severity (format issues, etc.)
        return FailureSeverity.LOW

    def _identify_specific_issues(
        self,
        query: str,
        actual_response: Optional[List[Dict[str, Any]]],
        error_details: Optional[str],
    ) -> List[str]:
        """Identify specific issues in the failure."""
        issues = []

        if actual_response is None:
            issues.append("No response generated")
            if error_details:
                issues.append(f"Error: {error_details}")
        else:
            # Check for missing commands
            if not actual_response:
                issues.append("Empty command list")

            # Check for parameter issues
            for i, cmd in enumerate(actual_response):
                if not cmd.get("command"):
                    issues.append(f"Command {i + 1}: Missing command name")

                if not cmd.get("params"):
                    issues.append(f"Command {i + 1}: Missing parameters")
                else:
                    for param, value in cmd["params"].items():
                        if value is None:
                            issues.append(
                                f"Command {i + 1}: Parameter '{param}' is None"
                            )

        # Check for query complexity issues
        if len(query.split()) > 20:
            issues.append("Query may be too complex")

        # Check for ambiguous language
        ambiguous_phrases = ["new column", "the column", "it", "that"]
        for phrase in ambiguous_phrases:
            if phrase in query.lower():
                issues.append(f"Ambiguous reference: '{phrase}'")

        return issues

    def _match_patterns(
        self,
        query: str,
        actual_response: Optional[List[Dict[str, Any]]],
        issues: List[str],
    ) -> List[str]:
        """Match the failure against known patterns."""
        matches = []

        combined_text = f"{query} {str(actual_response)} {' '.join(issues)}"

        for pattern_id, pattern_info in self.pattern_detectors.items():
            for condition in pattern_info["conditions"]:
                if re.search(condition, combined_text, re.IGNORECASE):
                    matches.append(pattern_id)
                    # Update pattern frequency
                    self.pattern_frequency[pattern_id] += 1
                    break

        return matches

    def _has_sequence_issues(
        self, query: str, actual_response: Optional[List[Dict[str, Any]]]
    ) -> bool:
        """Check for command sequence issues."""
        if not actual_response or len(actual_response) < 2:
            return False

        # Check for operations before file opening
        has_open = any(cmd.get("command") == "OPEN_FILE" for cmd in actual_response)
        if has_open:
            open_index = next(
                i
                for i, cmd in enumerate(actual_response)
                if cmd.get("command") == "OPEN_FILE"
            )
            if open_index > 0:
                return True  # Operations before OPEN_FILE

        return False

    def _has_reference_issues(
        self, query: str, actual_response: Optional[List[Dict[str, Any]]]
    ) -> bool:
        """Check for column/row reference issues."""
        if not actual_response:
            return False

        # Look for structural operations followed by references
        has_structural = False
        has_reference = False

        for cmd in actual_response:
            command_name = cmd.get("command", "")
            if "INSERT" in command_name or "DELETE" in command_name:
                has_structural = True
            elif has_structural and ("COPY" in command_name or "SORT" in command_name):
                has_reference = True
                break

        return has_structural and has_reference

    def _has_semantic_ambiguity(self, query: str) -> bool:
        """Check for semantic ambiguity indicators."""
        ambiguous_patterns = [
            r"new column [A-Z]",
            r"the column",
            r"that column",
            r"it",
            r"column [A-Z] after.*insert",
        ]

        for pattern in ambiguous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True

        return False

    def _update_statistics(self, analysis: FailureAnalysis) -> None:
        """Update internal statistics based on analysis."""
        # Extract commands from actual response
        if analysis.actual_response:
            for cmd in analysis.actual_response:
                command_name = cmd.get("command", "UNKNOWN")
                self.command_stats[command_name]["total"] += 1
                if analysis.severity in [
                    FailureSeverity.CRITICAL,
                    FailureSeverity.HIGH,
                ]:
                    self.command_stats[command_name]["failures"] += 1

    def detect_failure_patterns(self) -> List[FailurePattern]:
        """
        Detect patterns from accumulated failure analyses.

        Returns:
            List of detected failure patterns
        """
        patterns = []

        # Group analyses by pattern matches
        pattern_groups = defaultdict(list)
        for analysis in self.failure_analyses:
            for pattern_id in analysis.pattern_matches:
                pattern_groups[pattern_id].append(analysis)

        # Create pattern objects
        for pattern_id, analyses in pattern_groups.items():
            if pattern_id in self.pattern_detectors:
                detector = self.pattern_detectors[pattern_id]

                # Collect affected commands
                affected_commands = set()
                example_queries = []

                for analysis in analyses[:5]:  # Max 5 examples
                    example_queries.append(analysis.query)
                    if analysis.actual_response:
                        for cmd in analysis.actual_response:
                            affected_commands.add(cmd.get("command", "UNKNOWN"))

                pattern = FailurePattern(
                    pattern_id=pattern_id,
                    category=detector["category"],
                    severity=detector["severity"],
                    description=detector["description"],
                    trigger_conditions=detector["conditions"],
                    example_queries=example_queries,
                    frequency=len(analyses),
                    affected_commands=affected_commands,
                    root_cause=detector["root_cause"],
                    suggested_fix=detector["suggested_fix"],
                )
                patterns.append(pattern)

        # Sort by frequency (most common first)
        patterns.sort(key=lambda p: p.frequency, reverse=True)

        # Store detected patterns
        self.detected_patterns = {p.pattern_id: p for p in patterns}

        return patterns

    def generate_analysis_report(self) -> AnalysisReport:
        """
        Generate a comprehensive analysis report.

        Returns:
            Complete analysis report with insights and recommendations
        """
        total_failures = len(self.failure_analyses)

        if total_failures == 0:
            return AnalysisReport(
                total_failures=0,
                analysis_period="No data",
                category_breakdown={},
                severity_breakdown={},
                top_patterns=[],
                command_failure_rates={},
                improvement_recommendations=[],
                generated_timestamp=datetime.now().isoformat(),
            )

        # Category breakdown
        category_counts = Counter()
        for analysis in self.failure_analyses:
            for category in analysis.failure_categories:
                category_counts[category.value] += 1

        # Severity breakdown
        severity_counts = Counter()
        for analysis in self.failure_analyses:
            severity_counts[analysis.severity.value] += 1

        # Command failure rates
        failure_rates = {}
        for cmd, stats in self.command_stats.items():
            if stats["total"] > 0:
                failure_rates[cmd] = stats["failures"] / stats["total"]

        # Detect patterns
        top_patterns = self.detect_failure_patterns()

        # Generate recommendations
        recommendations = self._generate_recommendations(top_patterns, failure_rates)

        return AnalysisReport(
            total_failures=total_failures,
            analysis_period=f"{self.failure_analyses[0].analysis_timestamp} to {self.failure_analyses[-1].analysis_timestamp}",
            category_breakdown=dict(category_counts),
            severity_breakdown=dict(severity_counts),
            top_patterns=top_patterns[:10],  # Top 10 patterns
            command_failure_rates=failure_rates,
            improvement_recommendations=recommendations,
            generated_timestamp=datetime.now().isoformat(),
        )

    def _generate_recommendations(
        self, patterns: List[FailurePattern], failure_rates: Dict[str, float]
    ) -> List[str]:
        """Generate improvement recommendations based on analysis."""
        recommendations = []

        # Pattern-based recommendations
        for pattern in patterns[:5]:  # Top 5 patterns
            if pattern.frequency >= 3:  # Only recommend for frequent patterns
                recommendations.append(
                    f"HIGH PRIORITY: {pattern.suggested_fix} "
                    f"(fixes {pattern.frequency} failures in {pattern.category.value})"
                )

        # Command-specific recommendations
        high_failure_commands = {
            cmd: rate
            for cmd, rate in failure_rates.items()
            if rate > 0.3  # 30% or higher failure rate
        }

        for cmd, rate in high_failure_commands.items():
            recommendations.append(
                f"REVIEW NEEDED: Command '{cmd}' has {rate:.1%} failure rate"
            )

        # General recommendations
        critical_count = sum(
            1
            for analysis in self.failure_analyses
            if analysis.severity == FailureSeverity.CRITICAL
        )

        if critical_count > len(self.failure_analyses) * 0.2:  # >20% critical
            recommendations.append(
                "URGENT: High critical failure rate indicates fundamental issues"
            )

        return recommendations

    def export_analysis(self, filepath: str) -> None:
        """
        Export failure analysis data to JSON file.

        Args:
            filepath: Path to save the analysis data
        """
        report = self.generate_analysis_report()

        export_data = {
            "report": asdict(report),
            "individual_analyses": [
                asdict(analysis) for analysis in self.failure_analyses
            ],
            "detected_patterns": {
                pattern_id: asdict(pattern)
                for pattern_id, pattern in self.detected_patterns.items()
            },
            "command_statistics": dict(self.command_stats),
        }

        # Handle sets in export (convert to lists)
        for pattern_data in export_data["detected_patterns"].values():
            pattern_data["affected_commands"] = list(pattern_data["affected_commands"])

        with open(filepath, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

        self.logger.info(f"Analysis exported to {filepath}")

    def load_analysis(self, filepath: str) -> None:
        """
        Load failure analysis data from JSON file.

        Args:
            filepath: Path to load the analysis data from
        """
        with open(filepath, "r") as f:
            data = json.load(f)

        # Reconstruct analyses
        self.failure_analyses = []
        for analysis_data in data.get("individual_analyses", []):
            # Convert string enums back to enum objects
            analysis_data["failure_categories"] = [
                FailureCategory(cat) for cat in analysis_data["failure_categories"]
            ]
            analysis_data["severity"] = FailureSeverity(analysis_data["severity"])

            analysis = FailureAnalysis(**analysis_data)
            self.failure_analyses.append(analysis)

        # Reconstruct patterns
        self.detected_patterns = {}
        for pattern_id, pattern_data in data.get("detected_patterns", {}).items():
            pattern_data["category"] = FailureCategory(pattern_data["category"])
            pattern_data["severity"] = FailureSeverity(pattern_data["severity"])
            pattern_data["affected_commands"] = set(pattern_data["affected_commands"])

            pattern = FailurePattern(**pattern_data)
            self.detected_patterns[pattern_id] = pattern

        # Reconstruct command stats
        self.command_stats = defaultdict(lambda: {"total": 0, "failures": 0})
        for cmd, stats in data.get("command_statistics", {}).items():
            self.command_stats[cmd] = stats

        self.logger.info(f"Analysis loaded from {filepath}")


def main():
    """Main function for testing the FailurePatternAnalyzer."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    analyzer = FailurePatternAnalyzer()

    print("🔍 Failure Pattern Analyzer - Planning Mode")
    print("=" * 50)

    # Simulate some test failures
    test_failures = [
        {
            "query": "open price.json then add new column after A then copy column C into new column B",
            "actual_response": [
                {"command": "OPEN_FILE", "params": {"file_path": "price.json"}},
                {"command": "INSERT_COLUMN_RIGHT", "params": {"column_index": "A"}},
                {
                    "command": "COPY_COLUMN_TO_NEW_COLUMN_AFTER",
                    "params": {"source_column": "C", "target_column": "B"},
                },
            ],
        },
        {
            "query": "copy column A to new column B",
            "actual_response": None,
            "error_details": "Failed to decode JSON from AI response",
        },
        {
            "query": "delete column B then sort by column B",
            "actual_response": [
                {"command": "DELETE_COLUMN", "params": {"column_index": "B"}},
                {"command": "SORT_COLUMN_ASCENDING", "params": {"column": "B"}},
            ],
        },
    ]

    # Analyze test failures
    for failure in test_failures:
        analysis = analyzer.analyze_failure(
            query=failure["query"],
            actual_response=failure.get("actual_response"),
            error_details=failure.get("error_details"),
        )
        print(f'✅ Analyzed: "{analysis.query[:50]}..."')
        print(f"   Categories: {[cat.value for cat in analysis.failure_categories]}")
        print(f"   Severity: {analysis.severity.value}")
        print(f"   Pattern matches: {analysis.pattern_matches}")
        print()

    # Generate report
    report = analyzer.generate_analysis_report()

    print("Analysis Report Summary:")
    print(f"✅ Total Failures Analyzed: {report.total_failures}")
    print(f"✅ Top Failure Categories: {list(report.category_breakdown.keys())}")
    print(f"✅ Severity Distribution: {report.severity_breakdown}")
    print(f"✅ Detected Patterns: {len(report.top_patterns)}")
    print(f"✅ Recommendations Generated: {len(report.improvement_recommendations)}")

    if report.improvement_recommendations:
        print("\nTop Recommendations:")
        for i, rec in enumerate(report.improvement_recommendations[:3], 1):
            print(f"{i}. {rec}")

    print("\n✅ FailurePatternAnalyzer ready for deployment")


if __name__ == "__main__":
    main()
