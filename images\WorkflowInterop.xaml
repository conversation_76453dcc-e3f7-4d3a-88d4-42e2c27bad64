<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.854,2.146v.708l-2,2-.708-.708L11.293,3H7.5a5.484,5.484,0,0,0-.618.037L6.944,3,5.706,2.257A6.482,6.482,0,0,1,7.5,2h3.793L10.146.854l.708-.708ZM7.5,14A5.494,5.494,0,0,1,2.975,5.382l-1.6.959A6.437,6.437,0,0,0,1,8.5a6.5,6.5,0,0,0,11.1,4.6l-.707-.707A5.464,5.464,0,0,1,7.5,14Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M5,3,0,6V0Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M12,6.6l-4.25,4.25H7.043L4.793,8.6,5.5,7.892l1.9,1.9,3.9-3.9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
