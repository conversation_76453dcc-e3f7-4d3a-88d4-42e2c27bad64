"""Index management dialog for JEdit2.

This module provides a dialog for managing data indexes.
"""

import wx
import wx.grid
from datetime import datetime
from typing import Dict, Optional, List, Any
from .data_index import DataIndex, IndexType, IndexStats


class IndexDialog(wx.Dialog):
    """Dialog for managing data indexes."""
    
    def __init__(self, parent: wx.Window) -> None:
        """Initialize the dialog.
        
        Args:
            parent: Parent window
        """
        super().__init__(
            parent,
            title="Index Manager",
            size=(800, 600)
        )
        
        self.indexes: Dict[str, DataIndex] = {}
        
        self._init_ui()
        self._create_layout()
        self._bind_events()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main panel
        self.panel = wx.Panel(self)
        
        # Create index list
        self.index_list = wx.ListCtrl(
            self.panel,
            style=wx.LC_REPORT | wx.BORDER_SUNKEN
        )
        self.index_list.InsertColumn(0, "Name", width=150)
        self.index_list.InsertColumn(1, "Type", width=100)
        self.index_list.InsertColumn(2, "Size", width=100)
        self.index_list.InsertColumn(3, "Memory", width=100)
        self.index_list.InsertColumn(4, "Last Update", width=150)
        
        # Create stats grid
        self.stats_grid = wx.grid.Grid(self.panel)
        self.stats_grid.CreateGrid(0, 2)
        self.stats_grid.SetColLabelValue(0, "Metric")
        self.stats_grid.SetColLabelValue(1, "Value")
        self.stats_grid.SetColSize(0, 200)
        self.stats_grid.SetColSize(1, 300)
        
        # Create buttons
        self.add_btn = wx.Button(self.panel, label="Add Index")
        self.edit_btn = wx.Button(self.panel, label="Edit Index")
        self.remove_btn = wx.Button(self.panel, label="Remove Index")
        self.optimize_btn = wx.Button(self.panel, label="Optimize")
        self.close_btn = wx.Button(self.panel, label="Close")
    

    
    def _create_layout(self) -> None:
        """Create the layout."""
        # Create sizers
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        list_sizer = wx.BoxSizer(wx.HORIZONTAL)
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Add widgets to sizers
        list_sizer.Add(self.index_list, 1, wx.EXPAND | wx.ALL, 5)
        list_sizer.Add(self.stats_grid, 1, wx.EXPAND | wx.ALL, 5)
        
        button_sizer.Add(self.add_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.edit_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.remove_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.optimize_btn, 0, wx.ALL, 5)
        button_sizer.AddStretchSpacer()
        button_sizer.Add(self.close_btn, 0, wx.ALL, 5)
        
        main_sizer.Add(list_sizer, 1, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(button_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        self.panel.SetSizer(main_sizer)
    
    def _bind_events(self) -> None:
        """Bind events."""
        # Button events
        self.add_btn.Bind(wx.EVT_BUTTON, self._on_add)
        self.edit_btn.Bind(wx.EVT_BUTTON, self._on_edit)
        self.remove_btn.Bind(wx.EVT_BUTTON, self._on_remove)
        self.optimize_btn.Bind(wx.EVT_BUTTON, self._on_optimize)
        self.close_btn.Bind(wx.EVT_BUTTON, self._on_close)
        
        # List events
        self.index_list.Bind(wx.EVT_LIST_ITEM_SELECTED, self._on_select)
        self.index_list.Bind(wx.EVT_LIST_ITEM_DESELECTED, self._on_deselect)
    
    def _on_new(self, event: wx.CommandEvent) -> None:
        """Handle new index event."""
        dialog = wx.TextEntryDialog(
            self,
            "Enter index name:",
            "New Index"
        )
        if dialog.ShowModal() == wx.ID_OK:
            name = dialog.GetValue()
            if name and name not in self.indexes:
                self.indexes[name] = DataIndex(name)
                self._refresh_list()
        dialog.Destroy()
    
    def _on_open(self, event: wx.CommandEvent) -> None:
        """Handle open index event."""
        # TODO: Implement index loading
        pass
    
    def _on_save(self, event: wx.CommandEvent) -> None:
        """Handle save index event."""
        # TODO: Implement index saving
        pass
    
    def _on_exit(self, event: wx.CommandEvent) -> None:
        """Handle exit event."""
        self.EndModal(wx.ID_CANCEL)
    
    def _on_add(self, event: wx.CommandEvent) -> None:
        """Handle add index event."""
        self._on_new(event)
    
    def _on_edit(self, event: wx.CommandEvent) -> None:
        """Handle edit index event."""
        index = self._get_selected_index()
        if index:
            dialog = wx.Dialog(self, title="Edit Index")
            # TODO: Add edit controls
            dialog.ShowModal()
            dialog.Destroy()
    
    def _on_remove(self, event: wx.CommandEvent) -> None:
        """Handle remove index event."""
        index = self._get_selected_index()
        if index:
            if wx.MessageBox(
                f"Are you sure you want to remove index '{index.name}'?",
                "Confirm",
                wx.YES_NO | wx.ICON_QUESTION
            ) == wx.YES:
                del self.indexes[index.name]
                self._refresh_list()
    
    def _on_refresh(self, event: wx.CommandEvent) -> None:
        """Handle refresh event."""
        self._refresh_list()
    
    def _on_optimize(self, event: wx.CommandEvent) -> None:
        """Handle optimize event."""
        index = self._get_selected_index()
        if index:
            index._optimize()
            self._refresh_list()
    
    def _on_optimize_all(self, event: wx.CommandEvent) -> None:
        """Handle optimize all event."""
        for index in self.indexes.values():
            index._optimize()
        self._refresh_list()
    
    def _on_statistics(self, event: wx.CommandEvent) -> None:
        """Handle statistics event."""
        index = self._get_selected_index()
        if index:
            stats = index.get_stats()
            self._show_statistics(stats)
    
    def _on_help(self, event: wx.CommandEvent) -> None:
        """Handle help event."""
        wx.MessageBox(
            "Index Manager Help\n\n"
            "1. Add Index: Create a new index\n"
            "2. Edit Index: Modify index settings\n"
            "3. Remove Index: Delete an index\n"
            "4. Optimize: Clean up index memory\n"
            "5. Statistics: View index performance",
            "Help",
            wx.OK | wx.ICON_INFORMATION
        )
    
    def _on_about(self, event: wx.CommandEvent) -> None:
        """Handle about event."""
        wx.MessageBox(
            "Index Manager\n\n"
            "A tool for managing data indexes in JEdit2.",
            "About",
            wx.OK | wx.ICON_INFORMATION
        )
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close event."""
        self.EndModal(wx.ID_OK)
    
    def _on_select(self, event: wx.ListEvent) -> None:
        """Handle index selection event."""
        index = self._get_selected_index()
        if index:
            self._show_statistics(index.get_stats())
    
    def _on_deselect(self, event: wx.ListEvent) -> None:
        """Handle index deselection event."""
        self.stats_grid.ClearGrid()
        if self.stats_grid.GetNumberRows() > 0:
            self.stats_grid.DeleteRows(0, self.stats_grid.GetNumberRows())
    
    def _get_selected_index(self) -> Optional[DataIndex]:
        """Get the selected index.
        
        Returns:
            Selected index or None
        """
        selection = self.index_list.GetFirstSelected()
        if selection >= 0:
            name = self.index_list.GetItem(selection, 0).GetText()
            return self.indexes.get(name)
        return None
    
    def _refresh_list(self) -> None:
        """Refresh the index list."""
        self.index_list.DeleteAllItems()
        for index in self.indexes.values():
            stats = index.get_stats()
            self.index_list.Append((
                stats.name,
                stats.type.value,
                str(stats.size),
                f"{stats.memory_usage / 1024 / 1024:.1f} MB",
                datetime.fromtimestamp(stats.last_update_time).strftime("%Y-%m-%d %H:%M:%S")
            ))
    
    def _show_statistics(self, stats: IndexStats) -> None:
        """Show index statistics.
        
        Args:
            stats: Index statistics
        """
        self.stats_grid.ClearGrid()
        if self.stats_grid.GetNumberRows() > 0:
            self.stats_grid.DeleteRows(0, self.stats_grid.GetNumberRows())
        
        # Add statistics rows
        self.stats_grid.AppendRows(8)
        self.stats_grid.SetCellValue(0, 0, "Name")
        self.stats_grid.SetCellValue(0, 1, stats.name)
        self.stats_grid.SetCellValue(1, 0, "Type")
        self.stats_grid.SetCellValue(1, 1, stats.type.value)
        self.stats_grid.SetCellValue(2, 0, "Size")
        self.stats_grid.SetCellValue(2, 1, str(stats.size))
        self.stats_grid.SetCellValue(3, 0, "Memory Usage")
        self.stats_grid.SetCellValue(3, 1, f"{stats.memory_usage / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(4, 0, "Creation Time")
        self.stats_grid.SetCellValue(4, 1, datetime.fromtimestamp(stats.creation_time).strftime("%Y-%m-%d %H:%M:%S"))
        self.stats_grid.SetCellValue(5, 0, "Last Update")
        self.stats_grid.SetCellValue(5, 1, datetime.fromtimestamp(stats.last_update_time).strftime("%Y-%m-%d %H:%M:%S"))
        self.stats_grid.SetCellValue(6, 0, "Hit Rate")
        total = stats.hit_count + stats.miss_count
        hit_rate = (stats.hit_count / total * 100) if total > 0 else 0
        self.stats_grid.SetCellValue(6, 1, f"{hit_rate:.1f}%")
        self.stats_grid.SetCellValue(7, 0, "Average Search Time")
        self.stats_grid.SetCellValue(7, 1, f"{stats.avg_search_time * 1000:.1f} ms") 