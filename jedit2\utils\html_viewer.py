"""
HTML Viewer Panel for JEdit2
Provides built-in HTML viewing capability for help documentation.
"""

import wx
import wx.html
import os
from typing import Optional


class HtmlViewerPanel(wx.Panel):
    """A panel that can display HTML content with navigation controls."""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.history = []
        self.history_index = -1
        self._create_ui()
        
    def _create_ui(self):
        """Create the HTML viewer interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create toolbar
        toolbar_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Navigation buttons
        self.back_btn = wx.Button(self, wx.ID_BACKWARD, "← Back", size=(80, 30))
        self.forward_btn = wx.Button(self, wx.ID_FORWARD, "Forward →", size=(80, 30))
        self.home_btn = wx.But<PERSON>(self, wx.ID_HOME, "🏠 Home", size=(80, 30))
        
        # Initially disable navigation buttons
        self.back_btn.Enable(False)
        self.forward_btn.Enable(False)
        
        toolbar_sizer.Add(self.back_btn, 0, wx.ALL, 2)
        toolbar_sizer.Add(self.forward_btn, 0, wx.ALL, 2)
        toolbar_sizer.Add(self.home_btn, 0, wx.ALL, 2)
        toolbar_sizer.AddStretchSpacer()
        
        # Close button
        self.close_btn = wx.Button(self, wx.ID_CLOSE, "✕ Close", size=(80, 30))
        toolbar_sizer.Add(self.close_btn, 0, wx.ALL, 2)
        
        main_sizer.Add(toolbar_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        # Create HTML window
        self.html_window = wx.html.HtmlWindow(self, style=wx.html.HW_SCROLLBAR_AUTO)
        
        # Set HTML window properties
        self.html_window.SetStandardFonts()
        
        # Bind events
        self.back_btn.Bind(wx.EVT_BUTTON, self._on_back)
        self.forward_btn.Bind(wx.EVT_BUTTON, self._on_forward)
        self.home_btn.Bind(wx.EVT_BUTTON, self._on_home)
        self.close_btn.Bind(wx.EVT_BUTTON, self._on_close)
        self.html_window.Bind(wx.html.EVT_HTML_LINK_CLICKED, self._on_link_clicked)
        
        main_sizer.Add(self.html_window, 1, wx.EXPAND | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
        
    def load_html_file(self, file_path: str) -> bool:
        """Load an HTML file into the viewer.

        Args:
            file_path: Path to the HTML file

        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                self._show_error(f"File not found: {file_path}")
                return False

            # Convert to file URL for proper loading
            file_url = f"file:///{file_path.replace(os.sep, '/')}"

            # Store the current page URL for anchor navigation
            self.html_window._current_page = file_url

            # Load the HTML file
            success = self.html_window.LoadPage(file_url)

            if success:
                # Add to history
                self._add_to_history(file_url)
                self._update_navigation_buttons()
                return True
            else:
                self._show_error(f"Failed to load HTML file: {file_path}")
                return False

        except Exception as e:
            self._show_error(f"Error loading HTML file: {str(e)}")
            return False
    
    def load_html_content(self, html_content: str, base_path: str = "") -> bool:
        """Load HTML content directly into the viewer.
        
        Args:
            html_content: HTML content as string
            base_path: Base path for relative links
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if base_path:
                self.html_window.SetPage(html_content, base_path)
            else:
                self.html_window.SetPage(html_content)
            
            # Add to history
            self._add_to_history("content")
            self._update_navigation_buttons()
            return True
            
        except Exception as e:
            self._show_error(f"Error loading HTML content: {str(e)}")
            return False
    
    def _add_to_history(self, location: str):
        """Add a location to the navigation history."""
        # Remove any forward history if we're not at the end
        if self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]
        
        # Add new location
        self.history.append(location)
        self.history_index = len(self.history) - 1
    
    def _update_navigation_buttons(self):
        """Update the state of navigation buttons."""
        self.back_btn.Enable(self.history_index > 0)
        self.forward_btn.Enable(self.history_index < len(self.history) - 1)
    
    def _on_back(self, event):
        """Handle back button click."""
        if self.history_index > 0:
            self.history_index -= 1
            location = self.history[self.history_index]
            if location != "content":
                self.html_window.LoadPage(location)
            self._update_navigation_buttons()
    
    def _on_forward(self, event):
        """Handle forward button click."""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            location = self.history[self.history_index]
            if location != "content":
                self.html_window.LoadPage(location)
            self._update_navigation_buttons()
    
    def _on_home(self, event):
        """Handle home button click."""
        # Load the main help file
        help_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs', 'help.html')
        self.load_html_file(help_file)
    
    def _on_close(self, event):
        """Handle close button click."""
        # Post close event to parent
        close_event = wx.CommandEvent(wx.wxEVT_COMMAND_BUTTON_CLICKED, wx.ID_CLOSE)
        wx.PostEvent(self.GetParent(), close_event)
    
    def _on_link_clicked(self, event):
        """Handle HTML link clicks."""
        link_info = event.GetLinkInfo()
        href = link_info.GetHref()

        # Handle internal anchors
        if href.startswith('#'):
            # For anchor navigation, manually scroll to the section
            anchor = href[1:]  # Remove the # symbol
            self._scroll_to_anchor(anchor)
            return

        # Handle external links
        if href.startswith('http://') or href.startswith('https://'):
            import webbrowser
            webbrowser.open(href)
            return

        # Handle relative file links
        if not os.path.isabs(href):
            base_dir = os.path.dirname(os.path.dirname(__file__))
            href = os.path.join(base_dir, 'docs', href)

        # Load the linked file
        if os.path.exists(href):
            self.load_html_file(href)
        else:
            self._show_error(f"Linked file not found: {href}")

    def _scroll_to_anchor(self, anchor):
        """Scroll to a specific anchor in the HTML document."""
        try:
            # Get the current HTML content
            current_page = getattr(self.html_window, '_current_page', None)
            if not current_page:
                return

            # Read the HTML file to find the anchor position
            if current_page.startswith('file:///'):
                file_path = current_page[8:]  # Remove 'file:///' prefix
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Find the anchor position in the content
                    import re
                    # Look for id="anchor" or name="anchor"
                    patterns = [
                        rf'<[^>]*id=["\']?{re.escape(anchor)}["\']?[^>]*>',
                        rf'<[^>]*name=["\']?{re.escape(anchor)}["\']?[^>]*>',
                        rf'<a[^>]*name=["\']?{re.escape(anchor)}["\']?[^>]*>'
                    ]

                    position_ratio = 0
                    for pattern in patterns:
                        match = re.search(pattern, content, re.IGNORECASE)
                        if match:
                            position = match.start()
                            total_length = len(content)
                            position_ratio = position / total_length if total_length > 0 else 0
                            break

                    if position_ratio > 0:
                        # Get the virtual size and scroll to approximate position
                        virtual_size = self.html_window.GetVirtualSize()
                        scroll_pixels_per_unit = self.html_window.GetScrollPixelsPerUnit()

                        if scroll_pixels_per_unit[1] > 0:
                            scroll_y = int(virtual_size.height * position_ratio)
                            scroll_units = scroll_y // scroll_pixels_per_unit[1]

                            # Scroll to the position
                            self.html_window.Scroll(-1, scroll_units)
                            return

            # If we couldn't find the anchor, show a message
            wx.MessageBox(f"Section '{anchor}' not found in the document.",
                         "Navigation", wx.OK | wx.ICON_INFORMATION)

        except Exception as e:
            print(f"Error scrolling to anchor: {e}")
            # Fallback: just show a message
            wx.MessageBox(f"Could not navigate to section '{anchor}'.",
                         "Navigation", wx.OK | wx.ICON_WARNING)
    
    def _show_error(self, message: str):
        """Show an error message in the HTML window."""
        error_html = f"""
        <html>
        <head>
            <title>Error</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .error {{ color: #d32f2f; background: #ffebee; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="error">
                <h3>Error</h3>
                <p>{message}</p>
            </div>
        </body>
        </html>
        """
        self.html_window.SetPage(error_html)


class HtmlViewerDialog(wx.Dialog):
    """A dialog containing the HTML viewer panel."""
    
    def __init__(self, parent, title="Help Viewer", size=(900, 700)):
        super().__init__(parent, title=title, size=size, 
                         style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER | wx.MAXIMIZE_BOX)
        
        # Create the HTML viewer panel
        self.html_panel = HtmlViewerPanel(self)
        
        # Create main sizer
        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.html_panel, 1, wx.EXPAND)
        
        self.SetSizer(sizer)
        self.CenterOnParent()
        
        # Bind close event from the panel
        self.Bind(wx.EVT_BUTTON, self._on_close, id=wx.ID_CLOSE)
    
    def load_html_file(self, file_path: str) -> bool:
        """Load an HTML file into the viewer."""
        return self.html_panel.load_html_file(file_path)
    
    def load_html_content(self, html_content: str, base_path: str = "") -> bool:
        """Load HTML content into the viewer."""
        return self.html_panel.load_html_content(html_content, base_path)
    
    def _on_close(self, event):
        """Handle close button click."""
        self.EndModal(wx.ID_CLOSE)


class HelpPanel(wx.Panel):
    """A help panel that can be embedded in the main window."""

    def __init__(self, parent):
        super().__init__(parent)
        self.html_viewer = HtmlViewerPanel(self)

        # Create sizer
        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.html_viewer, 1, wx.EXPAND)
        self.SetSizer(sizer)

        # Load default help file
        self.load_default_help()

    def load_default_help(self):
        """Load the default help file."""
        help_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs', 'help.html')
        self.html_viewer.load_html_file(help_file)

    def load_help_file(self, file_path: str) -> bool:
        """Load a specific help file."""
        return self.html_viewer.load_html_file(file_path)


def show_help_dialog(parent, help_file_path: Optional[str] = None, as_panel: bool = False) -> Optional[HelpPanel]:
    """Show the help dialog or return a help panel.

    Args:
        parent: Parent window
        help_file_path: Path to help file, or None for default
        as_panel: If True, return a panel instead of showing dialog

    Returns:
        HelpPanel if as_panel=True, None otherwise
    """
    if not help_file_path:
        # Default help file path
        help_file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs', 'help.html')

    if as_panel:
        # Return a panel that can be embedded
        panel = HelpPanel(parent)
        if help_file_path:
            panel.load_help_file(help_file_path)
        return panel
    else:
        # Show as modal dialog
        dialog = HtmlViewerDialog(parent, "JEdit2 Help")

        if dialog.load_html_file(help_file_path):
            dialog.ShowModal()
        else:
            # Fallback to simple message if HTML loading fails
            wx.MessageBox(
                "Help file could not be loaded.\n\n"
                "Please check that the help documentation is properly installed.",
                "Help Error",
                wx.OK | wx.ICON_WARNING,
                parent
            )

        dialog.Destroy()
        return None
