#!/usr/bin/env python3
"""
Simple AI Integration Test
Test that the AI components can be imported and initialized.
"""

import sys
sys.path.insert(0, '.')

def test_imports():
    """Test that all AI components can be imported."""
    print("🧪 Testing AI Component Imports")
    print("=" * 50)
    
    try:
        from jedit2.utils.ai_manager import AIManager
        print("✅ Basic AI Manager imported")
    except Exception as e:
        print(f"❌ Basic AI Manager import failed: {e}")
        return False
    
    try:
        from jedit2.utils.ai_manager_improved import ImprovedAIManager
        print("✅ Improved AI Manager imported")
    except Exception as e:
        print(f"❌ Improved AI Manager import failed: {e}")
        return False
    
    try:
        from jedit2.utils.ai_manager_production import ProductionAIManager
        print("✅ Production AI Manager imported")
    except Exception as e:
        print(f"❌ Production AI Manager import failed: {e}")
        return False
    
    try:
        from jedit2.utils.config_manager import ConfigManager
        config = ConfigManager()
        api_key = config.get_setting('api_key')
        print(f"✅ Config Manager imported, API key configured: {bool(api_key)}")
    except Exception as e:
        print(f"❌ Config Manager import failed: {e}")
        return False
    
    return True

def test_initialization():
    """Test that AI components can be initialized."""
    print("\n🔧 Testing AI Component Initialization")
    print("=" * 50)
    
    try:
        from jedit2.utils.ai_manager import AIManager
        from jedit2.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        api_key = config.get_setting('api_key')
        
        if not api_key:
            print("⚠️  No API key configured, skipping initialization tests")
            return True
        
        # Test basic AI manager
        ai = AIManager(api_key=api_key)
        print("✅ Basic AI Manager initialized")
        
        # Test improved AI manager
        from jedit2.utils.ai_manager_improved import ImprovedAIManager
        improved_ai = ImprovedAIManager(ai)
        print("✅ Improved AI Manager initialized")
        
        # Test production AI manager
        from jedit2.utils.ai_manager_production import ProductionAIManager
        prod_ai = ProductionAIManager(ai)
        print("✅ Production AI Manager initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_import():
    """Test that main window can be imported with AI integration."""
    print("\n🏠 Testing Main Window Import")
    print("=" * 50)
    
    try:
        # Test import without creating the window
        import main_window
        print("✅ Main window module imported")
        
        # Check if the AI integration code is present
        import inspect
        source = inspect.getsource(main_window.MainWindow.__init__)
        
        if 'ai_system_integrator' in source:
            print("✅ AI system integrator code present")
        elif 'production_ai_manager' in source:
            print("✅ Production AI manager integration present")
        else:
            print("⚠️  AI integration code not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Main window import failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 JEdit2 AI Integration Simple Test")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_imports()
    
    if imports_ok:
        # Test initialization
        init_ok = test_initialization()
        
        if init_ok:
            # Test main window
            main_ok = test_main_window_import()
            
            if main_ok:
                print("\n✅ ALL TESTS PASSED!")
                print("🎉 AI integration components are working!")
                print("\nThe AI query functionality should now work in the application.")
                print("Try running: python -m jedit2.main")
                print("Then use the AI input field to test queries like 'open test.csv'")
            else:
                print("\n❌ Main window tests failed")
        else:
            print("\n❌ Initialization tests failed")
    else:
        print("\n❌ Import tests failed")
