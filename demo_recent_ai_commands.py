#!/usr/bin/env python3
"""
Demo script showing the Recently Used AI Commands feature.
This creates a simple GUI to demonstrate the functionality.
"""

import wx
import json
import sys
import os

class RecentAICommandsDemo(wx.Frame):
    def __init__(self):
        super().__init__(None, title="Recent AI Commands Demo", size=(600, 400))
        
        # Initialize recent commands system
        self.recent_ai_commands = []
        self.max_recent_ai_commands = 10
        self.config_file = "demo_config.json"
        self._load_recent_ai_commands()
        
        self._create_ui()
        self._bind_events()
        
        # Center the window
        self.CenterOnScreen()
    
    def _create_ui(self):
        """Create the demo UI."""
        panel = wx.Panel(self)
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title = wx.StaticText(panel, label="Recently Used AI Commands Demo")
        title_font = title.GetFont()
        title_font.SetPointSize(16)
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title.SetFont(title_font)
        
        # Instructions
        instructions = wx.StaticText(panel, label=
            "This demo shows the recently used AI commands feature.\n"
            "Type commands in the input field and press Enter to add them.\n"
            "Then select from the dropdown to reuse commands.")
        
        # AI Input field
        input_label = wx.StaticText(panel, label="AI Command Input:")
        self.ai_input = wx.TextCtrl(panel, style=wx.TE_PROCESS_ENTER, size=(400, -1))
        self.ai_input.SetHint("Enter an AI command (e.g., 'delete row 5', 'sort column A')")
        
        # Recent commands dropdown
        dropdown_label = wx.StaticText(panel, label="Recent Commands:")
        self.ai_recent_combo = wx.ComboBox(
            panel, style=wx.CB_DROPDOWN | wx.CB_READONLY, size=(400, -1)
        )
        self._update_recent_ai_commands_dropdown()
        
        # Buttons
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        add_btn = wx.Button(panel, label="Add Command")
        clear_btn = wx.Button(panel, label="Clear All")
        
        button_sizer.Add(add_btn, 0, wx.ALL, 5)
        button_sizer.Add(clear_btn, 0, wx.ALL, 5)
        
        # Recent commands list
        list_label = wx.StaticText(panel, label="Current Recent Commands:")
        self.commands_list = wx.ListCtrl(panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL)
        self.commands_list.AppendColumn("Command", width=500)
        self._update_commands_list()
        
        # Layout
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 10)
        main_sizer.Add(instructions, 0, wx.ALL | wx.EXPAND, 10)
        main_sizer.Add(input_label, 0, wx.LEFT | wx.RIGHT, 10)
        main_sizer.Add(self.ai_input, 0, wx.ALL | wx.EXPAND, 10)
        main_sizer.Add(dropdown_label, 0, wx.LEFT | wx.RIGHT, 10)
        main_sizer.Add(self.ai_recent_combo, 0, wx.ALL | wx.EXPAND, 10)
        main_sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 10)
        main_sizer.Add(list_label, 0, wx.LEFT | wx.RIGHT, 10)
        main_sizer.Add(self.commands_list, 1, wx.ALL | wx.EXPAND, 10)
        
        panel.SetSizer(main_sizer)
        
        # Store button references for event binding
        self.add_btn = add_btn
        self.clear_btn = clear_btn
    
    def _bind_events(self):
        """Bind UI events."""
        self.ai_input.Bind(wx.EVT_TEXT_ENTER, self._on_add_command)
        self.add_btn.Bind(wx.EVT_BUTTON, self._on_add_command)
        self.clear_btn.Bind(wx.EVT_BUTTON, self._on_clear_all)
        self.ai_recent_combo.Bind(wx.EVT_COMBOBOX, self._on_recent_selected)
    
    def _load_recent_ai_commands(self):
        """Load recent AI commands from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                    self.recent_ai_commands = data.get("recent_ai_commands", [])
        except Exception as e:
            print(f"Error loading recent commands: {e}")
            self.recent_ai_commands = []
    
    def _save_recent_ai_commands(self):
        """Save recent AI commands to file."""
        try:
            data = {"recent_ai_commands": self.recent_ai_commands}
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving recent commands: {e}")
    
    def _add_to_recent_ai_commands(self, command):
        """Add an AI command to the recent commands list."""
        if command in self.recent_ai_commands:
            self.recent_ai_commands.remove(command)
        self.recent_ai_commands.insert(0, command)

        # Limit the list size
        if len(self.recent_ai_commands) > self.max_recent_ai_commands:
            self.recent_ai_commands = self.recent_ai_commands[:self.max_recent_ai_commands]

        self._save_recent_ai_commands()
        self._update_recent_ai_commands_dropdown()
        self._update_commands_list()
    
    def _update_recent_ai_commands_dropdown(self):
        """Update the recent AI commands dropdown."""
        # Clear existing items
        self.ai_recent_combo.Clear()
        
        if not self.recent_ai_commands:
            self.ai_recent_combo.Append("(No recent commands)")
            self.ai_recent_combo.SetSelection(0)
            self.ai_recent_combo.Enable(False)
        else:
            self.ai_recent_combo.Enable(True)
            for command in self.recent_ai_commands:
                # Truncate long commands for display
                display_command = command[:50] + "..." if len(command) > 50 else command
                self.ai_recent_combo.Append(display_command)
            
            # Set placeholder text
            self.ai_recent_combo.SetValue("Select recent command...")
    
    def _update_commands_list(self):
        """Update the commands list display."""
        self.commands_list.DeleteAllItems()
        
        for i, command in enumerate(self.recent_ai_commands):
            self.commands_list.InsertItem(i, command)
    
    def _on_add_command(self, event):
        """Handle adding a new command."""
        command = self.ai_input.GetValue().strip()
        if command:
            self._add_to_recent_ai_commands(command)
            self.ai_input.Clear()
            wx.MessageBox(f"Added command: '{command}'", "Command Added", wx.OK | wx.ICON_INFORMATION)
        else:
            wx.MessageBox("Please enter a command first.", "No Command", wx.OK | wx.ICON_WARNING)
    
    def _on_clear_all(self, event):
        """Handle clearing all recent commands."""
        if wx.MessageBox("Clear all recent commands?", "Confirm", wx.YES_NO | wx.ICON_QUESTION) == wx.YES:
            self.recent_ai_commands = []
            self._save_recent_ai_commands()
            self._update_recent_ai_commands_dropdown()
            self._update_commands_list()
            wx.MessageBox("All recent commands cleared.", "Cleared", wx.OK | wx.ICON_INFORMATION)
    
    def _on_recent_selected(self, event):
        """Handle selection from recent commands dropdown."""
        selection = self.ai_recent_combo.GetSelection()
        if selection >= 0 and selection < len(self.recent_ai_commands):
            command = self.recent_ai_commands[selection]
            self.ai_input.SetValue(command)
            self.ai_input.SetFocus()
            # Reset dropdown to placeholder
            self.ai_recent_combo.SetValue("Select recent command...")
            wx.MessageBox(f"Selected command: '{command}'\nNow you can modify it or press Enter to add it again.", 
                         "Command Selected", wx.OK | wx.ICON_INFORMATION)


class DemoApp(wx.App):
    def OnInit(self):
        frame = RecentAICommandsDemo()
        frame.Show()
        return True


if __name__ == "__main__":
    app = DemoApp()
    app.MainLoop()
