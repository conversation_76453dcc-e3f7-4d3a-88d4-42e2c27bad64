<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M15,8a7.016,7.016,0,0,1-6,6.916l0,0V10.088L7.912,9H1C1,5.134,4.134,1,8,1A7,7,0,0,1,15,8Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,1A6.954,6.954,0,0,0,1.079,9h1.01A5.784,5.784,0,0,1,2,8a5.976,5.976,0,0,1,.35-2H4.7a10.514,10.514,0,0,0-.2,2c0,.335.018.669.051,1H5.558A8.757,8.757,0,0,1,5.5,8a9.487,9.487,0,0,1,.222-2h4.556A9.487,9.487,0,0,1,10.5,8a9.487,9.487,0,0,1-.222,2H8.912L9,10.088V11h1.005A9.5,9.5,0,0,1,9,13.089v1.829A6.995,6.995,0,0,0,8,1ZM4.946,5H2.812A6.049,6.049,0,0,1,3.9,3.667c.12-.113.226-.24.355-.344a5.96,5.96,0,0,1,1.8-.984c.054-.019.1-.049.157-.066A10.517,10.517,0,0,0,4.946,5ZM6,5a9.526,9.526,0,0,1,.87-1.875,9.451,9.451,0,0,1,.763-1.106C7.752,2.011,7.874,2,8,2s.248.011.372.019a9.451,9.451,0,0,1,.763,1.106A9.526,9.526,0,0,1,10.005,5ZM9.79,2.273c.055.017.1.047.157.066a5.96,5.96,0,0,1,1.8.984c.129.1.235.231.355.344A6.049,6.049,0,0,1,13.188,5H11.054A10.517,10.517,0,0,0,9.79,2.273Zm0,11.454A10.484,10.484,0,0,0,11.053,11h2.135A6.021,6.021,0,0,1,9.789,13.727ZM11.3,10a10.2,10.2,0,0,0,0-4H13.65a5.889,5.889,0,0,1,0,4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M7.485,15.5H.515V10.523h6.97Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4,14H2V12H4Zm2-2H5v2H6Zm2-1.5v5l-.5.5H.5L0,15.5v-5L.5,10h7ZM7,11H1.008l0,4H7Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
