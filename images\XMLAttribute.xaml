<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2.138,15.967,0,13.813v-.707L2.107,11l.707.707L1.061,13.461l1.787,1.8ZM5,11.738l1.788,1.8L5.034,15.293,5.741,16l2.106-2.106v-.706L5.71,11.033Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.492,2.345A5.5,5.5,0,0,0,9.685,1,5.406,5.406,0,0,0,5.657,2.605,5.531,5.531,0,0,0,4.081,6.646a5.213,5.213,0,0,0,1.164,3.44l.47-.467L7.79,11.71a6.462,6.462,0,0,0,1.71.237,7.438,7.438,0,0,0,2.9-.493V10.429a6.226,6.226,0,0,1-2.723.546A4.317,4.317,0,0,1,6.459,9.769,4.308,4.308,0,0,1,5.267,6.6,4.666,4.666,0,0,1,6.505,3.284,4.1,4.1,0,0,1,9.632,1.959a4.194,4.194,0,0,1,3.079,1.095A3.8,3.8,0,0,1,13.82,5.881a3.906,3.906,0,0,1-.41,1.908,1.2,1.2,0,0,1-1.034.735q-.682,0-.683-1.318,0-.068.312-3.583H10.9c-.053.387-.085.642-.1.766h-.02a1.157,1.157,0,0,0-.471-.616,1.4,1.4,0,0,0-.827-.256,2.344,2.344,0,0,0-1.966.992A4.035,4.035,0,0,0,6.777,7a2.779,2.779,0,0,0,.544,1.8,1.753,1.753,0,0,0,1.431.669A1.848,1.848,0,0,0,10.62,8.038h.034q.006,1.431,1.53,1.432a2.485,2.485,0,0,0,2-1.015A4.019,4.019,0,0,0,15,5.854,4.481,4.481,0,0,0,13.492,2.345ZM10.236,7.768a1.286,1.286,0,0,1-1.153.756.946.946,0,0,1-.8-.416,1.9,1.9,0,0,1-.3-1.135,3.341,3.341,0,0,1,.446-1.791A1.344,1.344,0,0,1,9.6,4.462q1.059,0,1.06,1.306A4.082,4.082,0,0,1,10.236,7.768Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
