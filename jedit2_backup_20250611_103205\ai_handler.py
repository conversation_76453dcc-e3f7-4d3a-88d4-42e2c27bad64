"""
Handles the processing of AI commands by interacting with the main window.
"""
from typing import Any, Optional, TYPE_CHECKING
import wx
import logging

if TYPE_CHECKING:
    from jedit2.main_window import MainWindow

class AICommandHandler:
    """
    This class is responsible for executing AI-driven commands.
    It acts as a bridge between the AI's instructions and the application's UI logic.
    """
    def __init__(self, main_window: "MainWindow"):
        """
        Initializes the AICommandHandler.
        Args:
            main_window: A reference to the main application window.
        """
        self.main_window = main_window

    def _get_current_grid(self):
        """Helper to get the currently active grid."""
        return self.main_window._get_current_grid()

    def _get_column_index(self, column_index: any) -> int:
        """Helper to get the column index."""
        return self.main_window._get_column_index(column_index)

    # High-level composite operations
    def copy_column_to_new_column_after(self, source_column: any, target_column: any, apply_bold: bool = False) -> None:
        """
        High-level operation: Insert a new column after the target column, copy source column data into it,
        and optionally apply bold formatting.
        
        This is an example of a composite operation that replaces multiple fragile low-level commands.
        """
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox("No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR)
            return

        # Get column indices
        source_col_idx = self._get_column_index(source_column)
        target_col_idx = self._get_column_index(target_column)
        
        # Validate source column
        if source_col_idx < 0 or source_col_idx >= grid.GetNumberCols():
            wx.MessageBox(f"Invalid source column: {source_column}", "Error", wx.OK | wx.ICON_ERROR)
            return
            
        # Validate target column
        if target_col_idx < 0 or target_col_idx >= grid.GetNumberCols():
            wx.MessageBox(f"Invalid target column: {target_column}", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            # Step 1: Insert column after target column
            new_col_idx = target_col_idx + 1
            grid._insert_column_at(new_col_idx)

            # Step 2: Adjust source column index if it was affected by the insertion
            adjusted_source_col_idx = source_col_idx
            if source_col_idx >= new_col_idx:
                adjusted_source_col_idx = source_col_idx + 1

            # Step 3: Copy all data from source column to new column
            for row in range(grid.GetNumberRows()):
                source_value = grid.GetCellValue(row, adjusted_source_col_idx)
                grid.SetCellValue(row, new_col_idx, source_value)
                
                # Also copy formatting if it exists
                source_font = grid.GetCellFont(row, adjusted_source_col_idx)
                source_color = grid.GetCellTextColour(row, adjusted_source_col_idx)
                source_bg_color = grid.GetCellBackgroundColour(row, adjusted_source_col_idx)
                source_alignment = grid.GetCellAlignment(row, adjusted_source_col_idx)
                
                grid.SetCellFont(row, new_col_idx, source_font)
                grid.SetCellTextColour(row, new_col_idx, source_color)
                grid.SetCellBackgroundColour(row, new_col_idx, source_bg_color)
                grid.SetCellAlignment(row, new_col_idx, source_alignment[0], source_alignment[1])
            
            # Step 3: Apply bold formatting to the entire new column if requested
            if apply_bold:
                for row in range(grid.GetNumberRows()):
                    current_font = grid.GetCellFont(row, new_col_idx)
                    bold_font = wx.Font(current_font.GetPointSize(), current_font.GetFamily(),
                                      current_font.GetStyle(), wx.FONTWEIGHT_BOLD,
                                      current_font.GetUnderlined(), current_font.GetFaceName())
                    grid.SetCellFont(row, new_col_idx, bold_font)
            
            # Force grid refresh
            grid.ForceRefresh()
            grid.Refresh()

            # Update status
            source_name = chr(ord('A') + source_col_idx)
            target_name = chr(ord('A') + target_col_idx)
            new_name = chr(ord('A') + new_col_idx)

            bold_text = " and made bold" if apply_bold else ""
            status_message = f"✅ Inserted column {new_name} after {target_name}, copied data from {source_name}{bold_text}"
            self.main_window.status_bar.SetStatusText(status_message)

            # Status message is already shown in status bar, no popup needed
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in copy_column_to_new_column_after: {e}")
            wx.MessageBox(f"Error performing column operation: {e}", "Error", wx.OK | wx.ICON_ERROR)

    def execute_command(self, command_name: str, **kwargs) -> None:
        """
        Execute a command by name with the given keyword arguments.
        This provides a clean interface for the AI to call any available command.
        """
        # Map of high-level command names to methods
        high_level_commands = {
            'COPY_COLUMN_TO_NEW_COLUMN_AFTER': self.copy_column_to_new_column_after,
        }
        
        # Check if it's a high-level command first
        if command_name in high_level_commands:
            try:
                high_level_commands[command_name](**kwargs)
                return
            except Exception as e:
                logging.getLogger(__name__).error(f"Error executing high-level command {command_name}: {e}")
                wx.MessageBox(f"Error executing {command_name}: {e}", "Error", wx.OK | wx.ICON_ERROR)
                return
        
        # If not a high-level command, delegate to the main window's AI methods
        method_name = f"_ai_{command_name.lower().replace('_', '_')}"
        if hasattr(self.main_window, method_name):
            try:
                method = getattr(self.main_window, method_name)
                method(**kwargs)
            except Exception as e:
                logging.getLogger(__name__).error(f"Error executing command {method_name}: {e}")
                wx.MessageBox(f"Error executing {command_name}: {e}", "Error", wx.OK | wx.ICON_ERROR)
        else:
            wx.MessageBox(f"Unknown command: {command_name}", "Error", wx.OK | wx.ICON_ERROR) 