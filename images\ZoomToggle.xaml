<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M6.5,1.5a5,5,0,0,0-5,5,4.07,4.07,0,0,0,.03.5H6v3H4v.83a4.919,4.919,0,0,0,2.5.67,5,5,0,0,0,0-10Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.72,10.02A5.5,5.5,0,1,0,1,6.5a4.07,4.07,0,0,0,.03.5h1A4.07,4.07,0,0,1,2,6.5a4.5,4.5,0,1,1,2,3.74v1.15A5.4,5.4,0,0,0,6.5,12a5.468,5.468,0,0,0,3.52-1.28l5.13,5.13.7-.7Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3,13H5v1H3v2H2V14H0V13H2V11H3ZM0,8V9H5V8Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
