"""Number Filter Dialog for advanced number filtering functionality."""

import wx
from typing import Optional, Dict, Any, List
from .filter_criteria import NumberFilterCriterion
from .filter_utils import parse_number, calculate_statistics
import logging

logger = logging.getLogger(__name__)


class NumberFilterDialog(wx.Dialog):
    """Dialog for creating advanced number filters."""
    
    def __init__(self, parent: wx.Window, column_name: str, column_data: List[str], 
                 current_criterion: Optional[NumberFilterCriterion] = None):
        """
        Initialize the number filter dialog.
        
        Args:
            parent: Parent window
            column_name: Name of the column being filtered
            column_data: All values in the column for statistics
            current_criterion: Existing number filter criterion (if any)
        """
        super().__init__(parent, title=f"Number Filters - {column_name}", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.column_name = column_name
        self.column_data = column_data
        self.current_criterion = current_criterion
        self.filter_result: Optional[NumberFilterCriterion] = None
        
        # Calculate statistics for the column
        self.stats = calculate_statistics(column_data, 'number')
        
        self._init_ui()
        self._bind_events()
        self._populate_current_values()
        
        # Set initial size and center
        self.SetSize((450, 400))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title_label = wx.StaticText(self, label=f"Show rows where '{self.column_name}':")
        title_font = title_label.GetFont()
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL, 10)
        
        # Statistics panel
        stats_panel = self._create_statistics_panel()
        main_sizer.Add(stats_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Filter condition panel
        condition_panel = self._create_condition_panel()
        main_sizer.Add(condition_panel, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
    
    def _create_statistics_panel(self) -> wx.Panel:
        """Create the statistics panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Statistics title
        stats_label = wx.StaticText(panel, label="Column Statistics:")
        stats_font = stats_label.GetFont()
        stats_font.SetWeight(wx.FONTWEIGHT_BOLD)
        stats_label.SetFont(stats_font)
        sizer.Add(stats_label, 0, wx.BOTTOM, 5)
        
        # Statistics content
        stats_text = []
        if 'number_count' in self.stats and self.stats['number_count'] > 0:
            stats_text.append(f"Count: {self.stats['number_count']}")
            if 'min_value' in self.stats:
                stats_text.append(f"Minimum: {self._format_number(self.stats['min_value'])}")
            if 'max_value' in self.stats:
                stats_text.append(f"Maximum: {self._format_number(self.stats['max_value'])}")
            if 'average' in self.stats:
                stats_text.append(f"Average: {self._format_number(self.stats['average'])}")
        else:
            stats_text.append("No numeric values found in column")
        
        stats_str = " • ".join(stats_text)
        stats_content = wx.StaticText(panel, label=stats_str)
        stats_content.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(stats_content, 0, wx.BOTTOM, 10)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_condition_panel(self) -> wx.Panel:
        """Create the condition selection panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operator choice
        operator_label = wx.StaticText(panel, label="Condition:")
        sizer.Add(operator_label, 0, wx.BOTTOM, 5)
        
        self.operator_choice = wx.Choice(panel, choices=[
            "Equals",
            "Does Not Equal",
            "Greater Than",
            "Greater Than or Equal",
            "Less Than", 
            "Less Than or Equal",
            "Between",
            "Not Between",
            "Top N Items",
            "Bottom N Items",
            "Above Average",
            "Below Average"
        ])
        self.operator_choice.SetSelection(0)  # Default to "Equals"
        sizer.Add(self.operator_choice, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Value inputs panel
        values_panel = self._create_values_panel(panel)
        sizer.Add(values_panel, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Help text
        self.help_text = wx.StaticText(panel, label="")
        self.help_text.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(self.help_text, 0, wx.EXPAND)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_values_panel(self, parent: wx.Window = None) -> wx.Panel:
        """Create the values input panel."""
        if parent is None:
            parent = self
        panel = wx.Panel(parent)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # First value
        self.value1_label = wx.StaticText(panel, label="Value:")
        sizer.Add(self.value1_label, 0, wx.BOTTOM, 2)
        
        self.value1_text = wx.TextCtrl(panel)
        sizer.Add(self.value1_text, 0, wx.EXPAND | wx.BOTTOM, 5)
        
        # Second value (for between operations)
        self.value2_label = wx.StaticText(panel, label="And:")
        sizer.Add(self.value2_label, 0, wx.BOTTOM, 2)
        
        self.value2_text = wx.TextCtrl(panel)
        sizer.Add(self.value2_text, 0, wx.EXPAND | wx.BOTTOM, 5)
        
        # N value (for top/bottom N)
        self.n_label = wx.StaticText(panel, label="Number of items:")
        sizer.Add(self.n_label, 0, wx.BOTTOM, 2)
        
        self.n_spin = wx.SpinCtrl(panel, min=1, max=10000, initial=10)
        sizer.Add(self.n_spin, 0, wx.EXPAND)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 5)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.operator_choice.Bind(wx.EVT_CHOICE, self._on_operator_change)
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _populate_current_values(self) -> None:
        """Populate the dialog with current criterion values."""
        if self.current_criterion:
            # Map operators to choice indices
            operator_map = {
                NumberFilterCriterion.EQUALS: 0,
                NumberFilterCriterion.NOT_EQUALS: 1,
                NumberFilterCriterion.GREATER_THAN: 2,
                NumberFilterCriterion.GREATER_EQUAL: 3,
                NumberFilterCriterion.LESS_THAN: 4,
                NumberFilterCriterion.LESS_EQUAL: 5,
                NumberFilterCriterion.BETWEEN: 6,
                NumberFilterCriterion.NOT_BETWEEN: 7,
                NumberFilterCriterion.TOP_N: 8,
                NumberFilterCriterion.BOTTOM_N: 9,
                NumberFilterCriterion.ABOVE_AVERAGE: 10,
                NumberFilterCriterion.BELOW_AVERAGE: 11
            }
            
            index = operator_map.get(self.current_criterion.operator, 0)
            self.operator_choice.SetSelection(index)
            
            self.value1_text.SetValue(str(self.current_criterion.value1))
            if self.current_criterion.value2 is not None:
                self.value2_text.SetValue(str(self.current_criterion.value2))
            self.n_spin.SetValue(self.current_criterion.n)
        
        self._update_ui_state()
    
    def _on_operator_change(self, event: wx.CommandEvent) -> None:
        """Handle operator selection changes."""
        self._update_ui_state()
    
    def _update_ui_state(self) -> None:
        """Update UI state based on selected operator."""
        selection = self.operator_choice.GetSelection()
        
        # Show/hide controls based on operator
        single_value_ops = [0, 1, 2, 3, 4, 5]  # equals, not equals, greater, less
        range_ops = [6, 7]  # between, not between
        n_ops = [8, 9]  # top N, bottom N
        no_value_ops = [10, 11]  # above/below average
        
        # Value 1
        if selection in single_value_ops + range_ops:
            self.value1_label.Show()
            self.value1_text.Show()
            self.value1_label.SetLabel("Value:")
        else:
            self.value1_label.Hide()
            self.value1_text.Hide()
        
        # Value 2
        if selection in range_ops:
            self.value2_label.Show()
            self.value2_text.Show()
        else:
            self.value2_label.Hide()
            self.value2_text.Hide()
        
        # N value
        if selection in n_ops:
            self.n_label.Show()
            self.n_spin.Show()
        else:
            self.n_label.Hide()
            self.n_spin.Hide()
        
        # Update help text
        help_texts = {
            0: "Show rows where the number equals the specified value",
            1: "Show rows where the number does not equal the specified value",
            2: "Show rows where the number is greater than the specified value",
            3: "Show rows where the number is greater than or equal to the specified value",
            4: "Show rows where the number is less than the specified value",
            5: "Show rows where the number is less than or equal to the specified value",
            6: "Show rows where the number is between the two specified values (inclusive)",
            7: "Show rows where the number is not between the two specified values",
            8: "Show the top N largest values in the column",
            9: "Show the bottom N smallest values in the column",
            10: f"Show values above the average ({self._format_number(self.stats.get('average', 0))})",
            11: f"Show values below the average ({self._format_number(self.stats.get('average', 0))})"
        }
        
        help_text = help_texts.get(selection, "")
        self.help_text.SetLabel(help_text)
        self.help_text.Wrap(400)
        
        # Force layout update
        self.Layout()
    
    def _format_number(self, value: float) -> str:
        """Format a number for display."""
        if value.is_integer():
            return str(int(value))
        else:
            return f"{value:.6g}"
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        selection = self.operator_choice.GetSelection()
        
        # Map choice indices to operators
        operators = [
            NumberFilterCriterion.EQUALS,
            NumberFilterCriterion.NOT_EQUALS,
            NumberFilterCriterion.GREATER_THAN,
            NumberFilterCriterion.GREATER_EQUAL,
            NumberFilterCriterion.LESS_THAN,
            NumberFilterCriterion.LESS_EQUAL,
            NumberFilterCriterion.BETWEEN,
            NumberFilterCriterion.NOT_BETWEEN,
            NumberFilterCriterion.TOP_N,
            NumberFilterCriterion.BOTTOM_N,
            NumberFilterCriterion.ABOVE_AVERAGE,
            NumberFilterCriterion.BELOW_AVERAGE
        ]
        
        operator = operators[selection]
        
        # Get values based on operator type
        value1 = 0.0
        value2 = None
        n = 10
        
        # Validate and get values
        if operator in [NumberFilterCriterion.EQUALS, NumberFilterCriterion.NOT_EQUALS,
                       NumberFilterCriterion.GREATER_THAN, NumberFilterCriterion.GREATER_EQUAL,
                       NumberFilterCriterion.LESS_THAN, NumberFilterCriterion.LESS_EQUAL]:
            # Single value operators
            try:
                value_str = self.value1_text.GetValue().strip()
                if not value_str:
                    wx.MessageBox("Please enter a number value.", "Missing Input", 
                                wx.OK | wx.ICON_WARNING)
                    self.value1_text.SetFocus()
                    return
                value1 = float(value_str)
                # Check for reasonable range
                if abs(value1) > 1e15:
                    wx.MessageBox("Number is too large. Please enter a smaller value.", "Invalid Input", 
                                wx.OK | wx.ICON_WARNING)
                    self.value1_text.SetFocus()
                    return
            except ValueError:
                wx.MessageBox(f"'{self.value1_text.GetValue()}' is not a valid number.\nPlease enter a numeric value like: 123, 45.67, or -89.01", "Invalid Input", 
                            wx.OK | wx.ICON_WARNING)
                self.value1_text.SetFocus()
                return
                
        elif operator in [NumberFilterCriterion.BETWEEN, NumberFilterCriterion.NOT_BETWEEN]:
            # Range operators
            try:
                value1_str = self.value1_text.GetValue().strip()
                value2_str = self.value2_text.GetValue().strip()
                
                if not value1_str or not value2_str:
                    wx.MessageBox("Please enter both range values.", "Missing Input", 
                                wx.OK | wx.ICON_WARNING)
                    if not value1_str:
                        self.value1_text.SetFocus()
                    else:
                        self.value2_text.SetFocus()
                    return
                
                value1 = float(value1_str)
                value2 = float(value2_str)
                
                # Check for reasonable range
                if abs(value1) > 1e15 or abs(value2) > 1e15:
                    wx.MessageBox("Numbers are too large. Please enter smaller values.", "Invalid Input", 
                                wx.OK | wx.ICON_WARNING)
                    return
                
                # Warn if range is inverted
                if value1 > value2:
                    result = wx.MessageBox(f"The range values appear to be inverted ({value1} to {value2}).\nContinue anyway?", 
                                         "Range Check", wx.YES_NO | wx.ICON_QUESTION)
                    if result != wx.YES:
                        return
                        
            except ValueError as e:
                wx.MessageBox(f"Invalid number format in range values.\nPlease enter numeric values like: 123, 45.67, or -89.01", "Invalid Input", 
                            wx.OK | wx.ICON_WARNING)
                return
                
        elif operator in [NumberFilterCriterion.TOP_N, NumberFilterCriterion.BOTTOM_N]:
            # N operators
            n = self.n_spin.GetValue()
            if n <= 0:
                wx.MessageBox("Number of items must be greater than 0.", "Invalid Input", 
                            wx.OK | wx.ICON_WARNING)
                return
        
        # Create the filter criterion
        self.filter_result = NumberFilterCriterion(
            operator=operator,
            value1=value1,
            value2=value2,
            n=n
        )
        
        # Set average for above/below average operations
        if operator in [NumberFilterCriterion.ABOVE_AVERAGE, NumberFilterCriterion.BELOW_AVERAGE]:
            if 'average' in self.stats:
                self.filter_result.set_average(self.stats['average'])
            else:
                wx.MessageBox("Cannot calculate average for this column.", "Error", 
                            wx.OK | wx.ICON_ERROR)
                return
        
        self.EndModal(wx.ID_OK)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filter_result = None
        self.EndModal(wx.ID_CANCEL)
    
    def get_filter_result(self) -> Optional[NumberFilterCriterion]:
        """Get the filter result after dialog is closed."""
        return self.filter_result 