"""Cache management dialog for JEdit2.

This module provides a dialog for managing caches and viewing cache statistics.
"""

import wx
import wx.grid
from typing import Dict, Any, List, Optional, Tuple
from .cache import CacheManager, CacheType


class CacheDialog(wx.Dialog):
    """Dialog for managing caches."""
    
    def __init__(self, parent: wx.Window, cache_manager: CacheManager) -> None:
        """Initialize the cache dialog.
        
        Args:
            parent: Parent window
            cache_manager: Cache manager
        """
        super().__init__(
            parent,
            title="Cache Manager",
            size=(800, 600),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.cache_manager = cache_manager
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create cache grid
        self.grid = self._create_cache_grid()
        main_sizer.Add(self.grid, 1, wx.EXPAND | wx.ALL, 5)
        
        # Create button sizer
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        add_button = wx.Button(self, label="Add Cache")
        add_button.Bind(wx.EVT_BUTTON, self._on_add)
        button_sizer.Add(add_button, 0, wx.ALL, 5)
        
        remove_button = wx.Button(self, label="Remove Cache")
        remove_button.Bind(wx.EVT_BUTTON, self._on_remove)
        button_sizer.Add(remove_button, 0, wx.ALL, 5)
        
        clear_button = wx.Button(self, label="Clear All")
        clear_button.Bind(wx.EVT_BUTTON, self._on_clear)
        button_sizer.Add(clear_button, 0, wx.ALL, 5)
        
        button_sizer.AddStretchSpacer()
        
        refresh_button = wx.Button(self, label="Refresh")
        refresh_button.Bind(wx.EVT_BUTTON, self._on_refresh)
        button_sizer.Add(refresh_button, 0, wx.ALL, 5)
        
        close_button = wx.Button(self, label="Close")
        close_button.Bind(wx.EVT_BUTTON, self._on_close)
        button_sizer.Add(close_button, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        # Set main sizer
        self.SetSizer(main_sizer)
        
        # Populate grid
        self._populate_grid()
    
    def _create_cache_grid(self) -> wx.grid.Grid:
        """Create the cache grid.
        
        Returns:
            Cache grid
        """
        grid = wx.grid.Grid(self)
        
        # Set up grid
        grid.CreateGrid(0, 7)
        grid.SetColLabelValue(0, "Name")
        grid.SetColLabelValue(1, "Type")
        grid.SetColLabelValue(2, "Size")
        grid.SetColLabelValue(3, "Memory")
        grid.SetColLabelValue(4, "Hits")
        grid.SetColLabelValue(5, "Misses")
        grid.SetColLabelValue(6, "Evictions")
        
        # Set column sizes
        grid.SetColSize(0, 150)  # Name
        grid.SetColSize(1, 100)  # Type
        grid.SetColSize(2, 100)  # Size
        grid.SetColSize(3, 100)  # Memory
        grid.SetColSize(4, 100)  # Hits
        grid.SetColSize(5, 100)  # Misses
        grid.SetColSize(6, 100)  # Evictions
        
        # Enable sorting (if available in this wxPython version)
        try:
            grid.EnableSorting(True)
        except AttributeError:
            pass  # EnableSorting not available in this wxPython version
        
        return grid
    
    def _populate_grid(self) -> None:
        """Populate the grid with cache statistics."""
        # Clear existing rows
        if self.grid.GetNumberRows() > 0:
            self.grid.DeleteRows(0, self.grid.GetNumberRows())
        
        # Get cache statistics
        stats = self.cache_manager.get_stats()
        
        # Add caches
        for name, cache_stats in stats.items():
            row = self.grid.GetNumberRows()
            self.grid.AppendRows(1)
            
            # Get cache
            cache = self.cache_manager.get_cache(name)
            if not cache:
                continue
            
            # Set cell values
            self.grid.SetCellValue(row, 0, name)
            self.grid.SetCellValue(row, 1, cache.cache_type.value)
            self.grid.SetCellValue(row, 2, str(len(cache.entries)))
            self.grid.SetCellValue(row, 3, f"{cache_stats['memory_usage'] / 1024 / 1024:.2f} MB")
            self.grid.SetCellValue(row, 4, str(cache_stats["hits"]))
            self.grid.SetCellValue(row, 5, str(cache_stats["misses"]))
            self.grid.SetCellValue(row, 6, str(cache_stats["evictions"]))
    
    def _on_add(self, event: wx.CommandEvent) -> None:
        """Handle add button click.
        
        Args:
            event: Command event
        """
        dialog = AddCacheDialog(self)
        if dialog.ShowModal() == wx.ID_OK:
            name, max_size, max_memory, cache_type, ttl = dialog.get_values()
            try:
                self.cache_manager.create_cache(
                    name=name,
                    max_size=max_size,
                    max_memory=max_memory,
                    cache_type=cache_type,
                    ttl=ttl
                )
                self._populate_grid()
            except ValueError as e:
                wx.MessageBox(
                    str(e),
                    "Error",
                    wx.OK | wx.ICON_ERROR
                )
        dialog.Destroy()
    
    def _on_remove(self, event: wx.CommandEvent) -> None:
        """Handle remove button click.
        
        Args:
            event: Command event
        """
        # Get selected row
        row = self.grid.GetGridCursorRow()
        if row < 0:
            wx.MessageBox(
                "Please select a cache to remove.",
                "No Selection",
                wx.OK | wx.ICON_INFORMATION
            )
            return
        
        # Get cache name
        name = self.grid.GetCellValue(row, 0)
        
        # Confirm removal
        if wx.MessageBox(
            f"Are you sure you want to remove cache {name}?",
            "Confirm",
            wx.YES_NO | wx.ICON_QUESTION
        ) == wx.YES:
            self.cache_manager.remove_cache(name)
            self._populate_grid()
    
    def _on_clear(self, event: wx.CommandEvent) -> None:
        """Handle clear button click.
        
        Args:
            event: Command event
        """
        # Confirm clearing
        if wx.MessageBox(
            "Are you sure you want to clear all caches?",
            "Confirm",
            wx.YES_NO | wx.ICON_QUESTION
        ) == wx.YES:
            self.cache_manager.clear_all()
            self._populate_grid()
    
    def _on_refresh(self, event: wx.CommandEvent) -> None:
        """Handle refresh button click.
        
        Args:
            event: Command event
        """
        self._populate_grid()
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button click.
        
        Args:
            event: Command event
        """
        self.EndModal(wx.ID_OK)


class AddCacheDialog(wx.Dialog):
    """Dialog for adding a new cache."""
    
    def __init__(self, parent: wx.Window) -> None:
        """Initialize the add cache dialog.
        
        Args:
            parent: Parent window
        """
        super().__init__(
            parent,
            title="Add Cache",
            size=(400, 300),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create form
        form_sizer = wx.FlexGridSizer(5, 2, 5, 5)
        form_sizer.AddGrowableCol(1)
        
        # Name
        name_label = wx.StaticText(self, label="Name:")
        self.name_text = wx.TextCtrl(self)
        form_sizer.Add(name_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.name_text, 0, wx.EXPAND)
        
        # Type
        type_label = wx.StaticText(self, label="Type:")
        self.type_choice = wx.Choice(
            self,
            choices=[t.value for t in CacheType]
        )
        self.type_choice.SetSelection(0)
        form_sizer.Add(type_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.type_choice, 0, wx.EXPAND)
        
        # Max Size
        size_label = wx.StaticText(self, label="Max Size:")
        self.size_text = wx.TextCtrl(self)
        self.size_text.SetValue("1000")
        form_sizer.Add(size_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.size_text, 0, wx.EXPAND)
        
        # Max Memory
        memory_label = wx.StaticText(self, label="Max Memory (MB):")
        self.memory_text = wx.TextCtrl(self)
        self.memory_text.SetValue("100")
        form_sizer.Add(memory_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.memory_text, 0, wx.EXPAND)
        
        # TTL
        ttl_label = wx.StaticText(self, label="TTL (seconds):")
        self.ttl_text = wx.TextCtrl(self)
        form_sizer.Add(ttl_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.ttl_text, 0, wx.EXPAND)
        
        main_sizer.Add(form_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        # Create button sizer
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        ok_button = wx.Button(self, wx.ID_OK, "OK")
        ok_button.Bind(wx.EVT_BUTTON, self._on_ok)
        button_sizer.Add(ok_button, 0, wx.ALL, 5)
        
        cancel_button = wx.Button(self, wx.ID_CANCEL, "Cancel")
        button_sizer.Add(cancel_button, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.ALIGN_RIGHT | wx.ALL, 5)
        
        # Set main sizer
        self.SetSizer(main_sizer)
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click.
        
        Args:
            event: Command event
        """
        # Validate input
        name = self.name_text.GetValue().strip()
        if not name:
            wx.MessageBox(
                "Please enter a cache name.",
                "Validation Error",
                wx.OK | wx.ICON_ERROR
            )
            return
        
        try:
            max_size = int(self.size_text.GetValue())
            if max_size <= 0:
                raise ValueError()
        except ValueError:
            wx.MessageBox(
                "Please enter a valid max size.",
                "Validation Error",
                wx.OK | wx.ICON_ERROR
            )
            return
        
        try:
            max_memory = int(self.memory_text.GetValue()) * 1024 * 1024
            if max_memory <= 0:
                raise ValueError()
        except ValueError:
            wx.MessageBox(
                "Please enter a valid max memory.",
                "Validation Error",
                wx.OK | wx.ICON_ERROR
            )
            return
        
        ttl = None
        ttl_text = self.ttl_text.GetValue().strip()
        if ttl_text:
            try:
                ttl = float(ttl_text)
                if ttl <= 0:
                    raise ValueError()
            except ValueError:
                wx.MessageBox(
                    "Please enter a valid TTL.",
                    "Validation Error",
                    wx.OK | wx.ICON_ERROR
                )
                return
        
        self.EndModal(wx.ID_OK)
    
    def get_values(self) -> Tuple[str, int, int, CacheType, Optional[float]]:
        """Get dialog values.
        
        Returns:
            Tuple of (name, max_size, max_memory, cache_type, ttl)
        """
        name = self.name_text.GetValue().strip()
        max_size = int(self.size_text.GetValue())
        max_memory = int(self.memory_text.GetValue()) * 1024 * 1024
        cache_type = list(CacheType)[self.type_choice.GetSelection()]
        
        ttl = None
        ttl_text = self.ttl_text.GetValue().strip()
        if ttl_text:
            ttl = float(ttl_text)
        
        return name, max_size, max_memory, cache_type, ttl 