"""AI Manager for JEdit2.

This module provides the AIManager class, which is responsible for all
interactions with the Google Gemini API.
"""

import google.generativeai as genai
from typing import Optional, Dict, Any, List
import json
import re

# Define the capabilities that the AI can use
CAPABILITIES = [
    {
        "name": "DELETE_ROW",
        "description": "Deletes a specified row from the spreadsheet.",
        "parameters": [
            {
                "name": "row_index",
                "type": "int or 'current'",
                "description": "The 1-based index of the row to delete, or 'current' for the currently selected row.",
            }
        ],
    },
    {
        "name": "APPLY_BOLD_FORMATTING",
        "description": "Applies bold formatting to the selected cells.",
        "parameters": [],
    },
    {
        "name": "SORT_COLUMN_ASCENDING",
        "description": "Sorts a specified column in ascending order.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int or string",
                "description": "The 1-based index or letter of the column to sort (e.g., 1 or 'A').",
            }
        ],
    },
    {
        "name": "ADD_COLUMN",
        "description": "Adds a new column to the spreadsheet.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int or string",
                "description": "The 1-based index or letter of the column *after which* the new column will be inserted. If not provided, adds to the end.",
            },
            {
                "name": "column_name",
                "type": "string",
                "description": "Optional name for the new column header.",
            },
        ],
    },
    {
        "name": "ADD_ROW",
        "description": "Adds a new row to the spreadsheet.",
        "parameters": [
            {
                "name": "row_index",
                "type": "int or 'current'",
                "description": "The 1-based index of the row *before which* the new row will be inserted. If not provided, adds to the end.",
            }
        ],
    },
    {
        "name": "INSERT_ROW_ABOVE",
        "description": "Inserts a new row above the specified row.",
        "parameters": [
            {
                "name": "row_index",
                "type": "int or 'current'",
                "description": "The 1-based index of the row above which to insert the new row, or 'current' for the currently selected row.",
            }
        ],
    },
    {
        "name": "INSERT_ROW_BELOW",
        "description": "Inserts a new row below the specified row.",
        "parameters": [
            {
                "name": "row_index",
                "type": "int or 'current'",
                "description": "The 1-based index of the row below which to insert the new row, or 'current' for the currently selected row.",
            }
        ],
    },
    {
        "name": "INSERT_COLUMN_LEFT",
        "description": "Inserts a new column to the left of the specified column.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to the left of which to insert the new column.",
            }
        ],
    },
    {
        "name": "INSERT_COLUMN_RIGHT",
        "description": "Inserts a new column to the right of the specified column.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to the right of which to insert the new column.",
            }
        ],
    },
    {
        "name": "APPLY_ITALIC_FORMATTING",
        "description": "Applies italic formatting to the selected cells.",
        "parameters": [],
    },
    {
        "name": "APPLY_UNDERLINE_FORMATTING",
        "description": "Applies underline formatting to the selected cells.",
        "parameters": [],
    },
    {
        "name": "ALIGN_LEFT",
        "description": "Aligns the content of the selected cells to the left.",
        "parameters": [],
    },
    {
        "name": "ALIGN_CENTER",
        "description": "Aligns the content of the selected cells to the center.",
        "parameters": [],
    },
    {
        "name": "ALIGN_RIGHT",
        "description": "Aligns the content of the selected cells to the right.",
        "parameters": [],
    },
    {
        "name": "FORMAT_AS_CURRENCY",
        "description": "Formats the selected cells as currency.",
        "parameters": [],
    },
    {
        "name": "FORMAT_AS_PERCENT",
        "description": "Formats the selected cells as percentages.",
        "parameters": [],
    },
    {
        "name": "DELETE_COLUMN",
        "description": "Deletes a specified column from the spreadsheet.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to delete.",
            }
        ],
    },
    {
        "name": "SORT_COLUMN_DESCENDING",
        "description": "Sorts a specified column in descending order.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int or string",
                "description": "The 1-based index or letter of the column to sort (e.g., 1 or 'A').",
            }
        ],
    },
    {
        "name": "UNDO_LAST_ACTION",
        "description": "Undoes the last performed action.",
        "parameters": [],
    },
    {
        "name": "REDO_LAST_ACTION",
        "description": "Redoes the last undone action.",
        "parameters": [],
    },
    {
        "name": "COPY_SELECTION",
        "description": "Copies the currently selected cells, rows, or columns.",
        "parameters": [],
    },
    {
        "name": "PASTE_SELECTION",
        "description": "Pastes the clipboard content into the current selection.",
        "parameters": [],
    },
    {
        "name": "CLEAR_SELECTION",
        "description": "Clears the contents of the selected cells, but not the cells themselves.",
        "parameters": [],
    },
    {
        "name": "NEW_FILE",
        "description": "Creates a new, empty text file in a new tab.",
        "parameters": [],
    },
    {
        "name": "NEW_SPREADSHEET",
        "description": "Creates a new, empty spreadsheet in a new tab.",
        "parameters": [],
    },
    {
        "name": "SAVE_FILE",
        "description": "Saves the file in the currently active tab. If the file is new, it may prompt the user for a name and location.",
        "parameters": [],
    },
    {
        "name": "CLOSE_TAB",
        "description": "Closes a tab. If no name or index is provided, closes the current tab.",
        "parameters": [
            {
                "name": "tab_name",
                "type": "string or int",
                "description": "The name or 1-based index of the tab to close. Use 'current' to close the active tab.",
            }
        ],
    },
    {
        "name": "SWITCH_TAB",
        "description": "Switches to a different open tab.",
        "parameters": [
            {
                "name": "tab_name",
                "type": "string or int",
                "description": "The name or 1-based index of the tab to activate.",
            }
        ],
    },
    {
        "name": "OPEN_FILE",
        "description": "Opens a file from the workspace.",
        "parameters": [
            {
                "name": "file_path",
                "type": "string",
                "description": "The name or relative path of the file to open.",
            }
        ],
    },
    {
        "name": "FILTER_COLUMN",
        "description": "Applies a filter to a specified column using various filter types.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "filter_type",
                "type": "string",
                "description": "Type of filter: 'text', 'number', 'date', 'values', or 'condition'",
            },
            {
                "name": "operator",
                "type": "string",
                "description": "Filter operator (equals, contains, greater_than, less_than, between, etc.)",
            },
            {
                "name": "value",
                "type": "string or number",
                "description": "Filter value or first value for range filters",
            },
            {
                "name": "value2",
                "type": "string or number",
                "description": "Second value for range filters (between, not_between)",
            },
            {
                "name": "case_sensitive",
                "type": "boolean",
                "description": "Whether text filters should be case sensitive (default: false)",
            },
        ],
    },
    {
        "name": "COPY_COLUMN_TO_NEW_COLUMN_AFTER",
        "description": "COMPLETE ATOMIC OPERATION: Inserts a new column after the specified target column, copies ALL data and formatting from the source column to the new column, and optionally applies bold formatting. DO NOT use INSERT_COLUMN commands when using this - this handles everything automatically in one operation. Use this instead of multiple INSERT/COPY/FORMAT commands. IMPORTANT: When users refer to 'new column X' in multi-step operations, they typically mean the original column X position before any insertions.",
        "parameters": [
            {
                "name": "source_column",
                "type": "string",
                "description": "The letter of the column to copy from (e.g., 'A', 'B', 'C') - ALWAYS uses original column positions before any insertion. If user says 'copy from new column C', they mean the original column C position.",
            },
            {
                "name": "target_column",
                "type": "string",
                "description": "The letter of the column after which to insert the new column (e.g., 'A' will insert after column A, creating new column B)",
            },
            {
                "name": "apply_bold",
                "type": "boolean",
                "description": "Whether to apply bold formatting to the entire new column (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_NUMBER_RANGE",
        "description": "Filters a column to show values within a specific numeric range.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "min_value",
                "type": "number",
                "description": "Minimum value in the range (inclusive)",
            },
            {
                "name": "max_value",
                "type": "number",
                "description": "Maximum value in the range (inclusive)",
            },
        ],
    },
    {
        "name": "FILTER_NUMBER_GREATER_THAN",
        "description": "Filters a column to show values greater than a specified number.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {"name": "value", "type": "number", "description": "The threshold value"},
            {
                "name": "include_equal",
                "type": "boolean",
                "description": "Whether to include values equal to the threshold (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_NUMBER_LESS_THAN",
        "description": "Filters a column to show values less than a specified number.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {"name": "value", "type": "number", "description": "The threshold value"},
            {
                "name": "include_equal",
                "type": "boolean",
                "description": "Whether to include values equal to the threshold (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_TEXT_CONTAINS",
        "description": "Filters a column to show rows where text contains a specific substring.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {"name": "text", "type": "string", "description": "The text to search for"},
            {
                "name": "case_sensitive",
                "type": "boolean",
                "description": "Whether the search should be case sensitive (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_TEXT_EQUALS",
        "description": "Filters a column to show rows where text exactly matches a specific value.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "text",
                "type": "string",
                "description": "The exact text to match",
            },
            {
                "name": "case_sensitive",
                "type": "boolean",
                "description": "Whether the match should be case sensitive (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_TEXT_STARTS_WITH",
        "description": "Filters a column to show rows where text starts with a specific prefix.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "prefix",
                "type": "string",
                "description": "The prefix to search for",
            },
            {
                "name": "case_sensitive",
                "type": "boolean",
                "description": "Whether the search should be case sensitive (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_TEXT_ENDS_WITH",
        "description": "Filters a column to show rows where text ends with a specific suffix.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "suffix",
                "type": "string",
                "description": "The suffix to search for",
            },
            {
                "name": "case_sensitive",
                "type": "boolean",
                "description": "Whether the search should be case sensitive (default: false)",
            },
        ],
    },
    {
        "name": "FILTER_BLANKS",
        "description": "Filters a column to show only blank/empty cells.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            }
        ],
    },
    {
        "name": "FILTER_NON_BLANKS",
        "description": "Filters a column to show only non-blank/non-empty cells.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            }
        ],
    },
    {
        "name": "FILTER_TOP_N",
        "description": "Filters a column to show the top N largest values.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {"name": "n", "type": "int", "description": "Number of top values to show"},
        ],
    },
    {
        "name": "FILTER_BOTTOM_N",
        "description": "Filters a column to show the bottom N smallest values.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            },
            {
                "name": "n",
                "type": "int",
                "description": "Number of bottom values to show",
            },
        ],
    },
    {
        "name": "FILTER_ABOVE_AVERAGE",
        "description": "Filters a column to show values above the average.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            }
        ],
    },
    {
        "name": "FILTER_BELOW_AVERAGE",
        "description": "Filters a column to show values below the average.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to filter.",
            }
        ],
    },
    {
        "name": "CLEAR_FILTER",
        "description": "Clears the filter from a specified column.",
        "parameters": [
            {
                "name": "column_index",
                "type": "int, string, or 'current'",
                "description": "The 1-based index, letter, or 'current' for the column to clear filter from.",
            }
        ],
    },
    {
        "name": "CLEAR_ALL_FILTERS",
        "description": "Clears all active filters from the spreadsheet.",
        "parameters": [],
    },
    {
        "name": "SHOW_FILTER_HISTORY",
        "description": "Opens the filter history dialog to manage active filters.",
        "parameters": [],
    },
    {
        "name": "FORMAT_AS_COMMA",
        "description": "Formats the selected cells with comma (thousands) separator.",
        "parameters": [],
    },
    {
        "name": "FREEZE_PANES",
        "description": "Freezes panes at the current cursor position, like Excel's freeze panes feature.",
        "parameters": [],
    },
    {
        "name": "UNFREEZE_PANES",
        "description": "Unfreezes all frozen panes in the current spreadsheet.",
        "parameters": [],
    },
    {
        "name": "EXCEL_LIST_WORKSHEETS",
        "description": "Lists all worksheets available in the current Excel file.",
        "parameters": [],
    },
    {
        "name": "EXCEL_SWITCH_WORKSHEET",
        "description": "Switches to a different worksheet in the current Excel file (future enhancement).",
        "parameters": [
            {
                "name": "worksheet_name",
                "type": "string",
                "description": "The name of the worksheet to switch to.",
            }
        ],
    },
    {
        "name": "EXCEL_SAVE_AS_CSV",
        "description": "Saves the current Excel worksheet as a CSV file.",
        "parameters": [
            {
                "name": "filename",
                "type": "string",
                "description": "Optional filename for the CSV file. If not provided, uses current name with .csv extension.",
            }
        ],
    },
    {
        "name": "EXCEL_VALIDATE_FILE",
        "description": "Validates the current Excel file and shows information about its structure.",
        "parameters": [],
    },
]


def get_system_prompt() -> str:
    """
    Generates the system prompt for the AI, instructing it to act as a helpful
    assistant that can execute a series of commands.

    Returns:
        str: The fully formatted system prompt.
    """
    # Convert the capabilities list to a more readable format for the AI
    capabilities_str = ""
    for cap in CAPABILITIES:
        params_str = ", ".join([f"{p['name']}: {p['type']}" for p in cap["parameters"]])
        capabilities_str += (
            f"- **{cap['name']}**: {cap['description']} "
            f"(Parameters: {params_str if params_str else 'None'})\n"
        )

    return f"""You are an expert AI assistant integrated into a data editing application called JEdit2.
Your goal is to help users manipulate data in spreadsheets and text files by translating their natural language queries into a sequence of commands.

**INSTRUCTIONS:**
1.  Analyze the user's request to understand their intent.
2.  Break down the request into a series of steps.
3.  For each step, choose the most appropriate command from the list of available capabilities below.
4.  You MUST respond with a valid JSON array of command objects. Each object in the array should represent one command in the sequence.
5.  If a user's request requires multiple actions (e.g., "open a file and then add a column"), you MUST return a list of all required commands in the correct order.
6.  If the user's request is unclear or cannot be fulfilled with the available commands, return an empty JSON array `[]`.

**OUTPUT FORMAT:**
You must respond with a JSON array, where each object has a "command" name and a "params" object.
Example for "open price.json and add a new column after A":
```json
[
  {{
    "command": "OPEN_FILE",
    "params": {{
      "file_path": "price.json"
    }}
  }},
  {{
    "command": "INSERT_COLUMN_RIGHT",
    "params": {{
      "column_index": "A"
    }}
  }}
]
```

**AVAILABLE COMMANDS:**
{capabilities_str}
"""


class AIManager:
    """Manages all interactions with the Google Gemini API."""

    _model: Optional[genai.GenerativeModel] = None

    def __init__(self, api_key: Optional[str] = None):
        """
        Initializes the AIManager.

        Args:
            api_key: The Google AI Studio API key. Can be set later.
        """
        self._api_key = api_key
        if api_key:
            self._configure()

    def set_api_key(self, api_key: str) -> None:
        """Sets or updates the API key and re-configures the model."""
        self._api_key = api_key
        self._configure()

    def _configure(self) -> None:
        """Configures the Gemini API with the provided key."""
        if not self._api_key:
            self._model = None
            return

        try:
            genai.configure(api_key=self._api_key)
            # Prioritize the recommended free-tier model
            primary_model = "gemini-1.5-flash-latest"

            try:
                self._model = genai.GenerativeModel(primary_model)
                # A quick check to see if the model is viable
                _ = self._model.generate_content(
                    "test",
                    generation_config=genai.types.GenerationConfig(candidate_count=1),
                )
            except Exception as e:
                print(f"Failed to initialize primary model '{primary_model}': {e}")
                print("Attempting to find a fallback model.")
                self._model = self._find_fallback_model()

        except Exception as e:
            print(f"Error configuring Gemini API: {e}")
            self._model = None

    def _find_fallback_model(self) -> Optional[genai.GenerativeModel]:
        """Finds a suitable fallback model that supports content generation."""
        try:
            for m in genai.list_models():
                if "generateContent" in m.supported_generation_methods:
                    print(f"Found a fallback model: {m.name}")
                    return genai.GenerativeModel(m.name)
        except Exception as e:
            print(f"Could not find a suitable fallback model: {e}")

        return None

    @staticmethod
    def test_connection(api_key: str) -> bool:
        """
        Tests the connection to the Google Gemini API with a given key.
        """
        try:
            genai.configure(api_key=api_key)
            # Use the same model as in _configure() for consistency
            model = genai.GenerativeModel("gemini-1.5-flash-latest")
            model.generate_content(
                "test",
                generation_config=genai.types.GenerationConfig(max_output_tokens=1),
            )
            return True
        except Exception as e:
            print(f"AI connection test failed: {e}")
            return False

    def get_ai_response(self, query: str) -> tuple[Optional[list[dict[str, Any]]], str]:
        """
        Gets a structured command response from the AI for a given query.

        Args:
            query (str): The natural language query from the user.

        Returns:
            A tuple containing:
            - A list of command dictionaries, or None if an error occurs.
            - The raw text response from the AI.
        """
        if not self._model:
            raw_response = "AI model not configured."
            print(raw_response)
            return None, raw_response

        system_prompt = get_system_prompt()
        try:
            full_prompt = f'{system_prompt}\\n\\nUser: "{query}"'
            response = self._model.generate_content(full_prompt)
            raw_response = response.text

            json_str = self._extract_json_from_response(raw_response)

            if not json_str:
                print("No valid JSON found in AI response.")
                return None, raw_response

            # The AI should return a list of commands
            parsed_json = json.loads(json_str)

            if isinstance(parsed_json, list):
                # Validate that it's a list of dicts
                if all(isinstance(item, dict) for item in parsed_json):
                    return parsed_json, raw_response
                else:
                    print(
                        "AI response is a list, but not all items are command objects."
                    )
                    return None, raw_response
            elif isinstance(parsed_json, dict):
                # Handle cases where the AI might have forgotten to wrap a single command in a list
                print("AI returned a single command object, wrapping it in a list.")
                return [parsed_json], raw_response
            else:
                print(
                    f"AI response is not a valid list of commands: {type(parsed_json)}"
                )
                return None, raw_response

        except Exception as e:
            raw_response = f"Error getting AI response: {e}"
            print(raw_response)
            return None, raw_response

    def _extract_json_from_response(self, text: str) -> Optional[str]:
        """
        Extracts a JSON string from the AI's raw text response.
        Enhanced with multiple fallback strategies for robust parsing.
        """
        # Strategy 1: Standard markdown and bracket extraction
        json_str = self._extract_standard_json(text)
        if json_str:
            return json_str

        # Strategy 2: Fuzzy extraction with bracket matching
        json_str = self._extract_fuzzy_json(text)
        if json_str:
            return json_str

        # Strategy 3: Pattern-based regex extraction
        json_str = self._extract_pattern_json(text)
        if json_str:
            return json_str

        # Strategy 4: Cleaned text extraction
        json_str = self._extract_cleaned_json(text)
        if json_str:
            return json_str

        print("All JSON extraction strategies failed.")
        return None

    def _extract_standard_json(self, text: str) -> Optional[str]:
        """Standard JSON extraction with markdown handling."""
        try:
            # Strip markdown fences
            cleaned = text.strip()
            if cleaned.startswith("```json"):
                cleaned = cleaned[7:]
            if cleaned.startswith("```"):
                cleaned = cleaned[3:]
            if cleaned.endswith("```"):
                cleaned = cleaned[:-3]

            # Find JSON boundaries
            start_bracket = cleaned.find("[")
            start_brace = cleaned.find("{")

            if start_bracket != -1 and (
                start_bracket < start_brace or start_brace == -1
            ):
                start = start_bracket
                end_char = "]"
            elif start_brace != -1:
                start = start_brace
                end_char = "}"
            else:
                return None

            end = cleaned.rfind(end_char)

            if start != -1 and end != -1 and end > start:
                json_str = cleaned[start : end + 1]
                json.loads(json_str)  # Validate
                return json_str

        except (json.JSONDecodeError, IndexError, ValueError):
            pass

        return None

    def _extract_fuzzy_json(self, text: str) -> Optional[str]:
        """Fuzzy JSON extraction with bracket matching."""
        import re

        try:
            # Remove common prefixes/suffixes that might interfere
            cleaned = re.sub(r"^[^{\[]*", "", text)  # Remove non-JSON prefix
            cleaned = re.sub(r"[^}\]]*$", "", cleaned)  # Remove non-JSON suffix

            # Try to find balanced brackets/braces
            for start_char, end_char in [("[", "]"), ("{", "}")]:
                start_idx = cleaned.find(start_char)
                if start_idx == -1:
                    continue

                # Find matching closing bracket/brace
                count = 0
                end_idx = -1
                for i, char in enumerate(cleaned[start_idx:], start_idx):
                    if char == start_char:
                        count += 1
                    elif char == end_char:
                        count -= 1
                        if count == 0:
                            end_idx = i
                            break

                if end_idx != -1:
                    json_str = cleaned[start_idx : end_idx + 1]
                    json.loads(json_str)  # Validate
                    return json_str

        except (json.JSONDecodeError, IndexError, ValueError):
            pass

        return None

    def _extract_pattern_json(self, text: str) -> Optional[str]:
        """Pattern-based JSON extraction using regex."""
        import re

        try:
            # Look for array patterns
            array_pattern = r"\[\s*\{.*?\}\s*(?:\s*,\s*\{.*?\}\s*)*\]"
            matches = re.findall(array_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    json.loads(match)
                    return match
                except json.JSONDecodeError:
                    continue

            # Look for single object patterns
            object_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(object_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    json.loads(match)
                    return match
                except json.JSONDecodeError:
                    continue

        except Exception:
            pass

        return None

    def _extract_cleaned_json(self, text: str) -> Optional[str]:
        """Clean text and attempt extraction."""
        import re

        try:
            # Remove common Gemini response artifacts
            cleaned = text

            # Remove explanatory text patterns
            patterns_to_remove = [
                r"Here\'s the JSON.*?:",
                r"I\'ll help you.*?:",
                r"Based on your request.*?:",
                r"The commands are:",
                r"Here are the commands:",
            ]

            for pattern in patterns_to_remove:
                cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

            # Try standard extraction on cleaned text
            return self._extract_standard_json(cleaned)

        except Exception:
            pass

        return None
