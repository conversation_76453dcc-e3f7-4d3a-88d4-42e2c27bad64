#!/usr/bin/env python3
"""
Test script for the recently used AI commands feature.
This script tests the functionality without requiring the full GUI.
"""

import sys
import os
import json
import tempfile
from unittest.mock import Mock, MagicMock

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_recent_ai_commands():
    """Test the recent AI commands functionality."""
    print("🧪 Testing Recent AI Commands Feature")
    print("=" * 50)
    
    # Mock the config manager
    config_manager = Mock()
    config_data = {}
    
    def mock_get_setting(key, default=""):
        return config_data.get(key, default)
    
    def mock_set_setting(key, value):
        config_data[key] = value
    
    config_manager.get_setting = mock_get_setting
    config_manager.set_setting = mock_set_setting
    
    # Create a mock main window with just the recent AI commands functionality
    class MockMainWindow:
        def __init__(self):
            self.config_manager = config_manager
            self.recent_ai_commands = []
            self.max_recent_ai_commands = 10
            self.ai_recent_combo = Mock()
            
            # Initialize the recent AI commands
            self._load_recent_ai_commands()
        
        def _load_recent_ai_commands(self):
            """Load recent AI commands list from configuration."""
            try:
                recent_commands_str = self.config_manager.get_setting("recent_ai_commands", "")
                if recent_commands_str:
                    self.recent_ai_commands = json.loads(recent_commands_str)
            except (json.JSONDecodeError, KeyError, AttributeError) as e:
                print(f"Error loading recent AI commands: {e}")
                self.recent_ai_commands = []

        def _save_recent_ai_commands(self):
            """Save recent AI commands list to configuration."""
            try:
                self.config_manager.set_setting(
                    "recent_ai_commands", json.dumps(self.recent_ai_commands)
                )
            except (json.JSONDecodeError, KeyError, AttributeError) as e:
                print(f"Error saving recent AI commands: {e}")

        def _add_to_recent_ai_commands(self, command):
            """Add an AI command to the recent commands list."""
            if command in self.recent_ai_commands:
                self.recent_ai_commands.remove(command)
            self.recent_ai_commands.insert(0, command)

            # Limit the list size
            if len(self.recent_ai_commands) > self.max_recent_ai_commands:
                self.recent_ai_commands = self.recent_ai_commands[: self.max_recent_ai_commands]

            self._save_recent_ai_commands()
            self._update_recent_ai_commands_dropdown()

        def _update_recent_ai_commands_dropdown(self):
            """Update the recent AI commands dropdown."""
            # Mock implementation
            self.ai_recent_combo.Clear()
            
            if not self.recent_ai_commands:
                self.ai_recent_combo.Append("(No recent commands)")
                self.ai_recent_combo.SetSelection(0)
                self.ai_recent_combo.Enable(False)
            else:
                self.ai_recent_combo.Enable(True)
                for i, command in enumerate(self.recent_ai_commands):
                    # Truncate long commands for display
                    display_command = command[:50] + "..." if len(command) > 50 else command
                    self.ai_recent_combo.Append(display_command)
                
                # Set placeholder text
                self.ai_recent_combo.SetValue("Select recent command...")
    
    # Create mock window and test
    window = MockMainWindow()
    
    # Test 1: Initial state
    print("✅ Test 1: Initial state")
    assert len(window.recent_ai_commands) == 0, "Should start with no recent commands"
    print("   - Recent commands list is empty: ✓")
    
    # Test 2: Add first command
    print("\n✅ Test 2: Add first command")
    window._add_to_recent_ai_commands("delete row 5")
    assert len(window.recent_ai_commands) == 1, "Should have 1 command"
    assert window.recent_ai_commands[0] == "delete row 5", "Command should be stored correctly"
    print("   - Added 'delete row 5': ✓")
    
    # Test 3: Add more commands
    print("\n✅ Test 3: Add multiple commands")
    commands = [
        "sort column A ascending",
        "make column B bold",
        "add new row",
        "copy column C to column D"
    ]
    
    for cmd in commands:
        window._add_to_recent_ai_commands(cmd)
    
    assert len(window.recent_ai_commands) == 5, f"Should have 5 commands, got {len(window.recent_ai_commands)}"
    assert window.recent_ai_commands[0] == "copy column C to column D", "Most recent should be first"
    print(f"   - Added {len(commands)} more commands: ✓")
    print(f"   - Most recent command is first: ✓")
    
    # Test 4: Duplicate command handling
    print("\n✅ Test 4: Duplicate command handling")
    window._add_to_recent_ai_commands("delete row 5")  # This was the first command
    assert len(window.recent_ai_commands) == 5, "Should still have 5 commands (no duplicates)"
    assert window.recent_ai_commands[0] == "delete row 5", "Duplicate should move to front"
    print("   - Duplicate command moved to front: ✓")
    
    # Test 5: Max limit handling
    print("\n✅ Test 5: Max limit handling")
    # Add more commands to exceed the limit
    for i in range(15):
        window._add_to_recent_ai_commands(f"test command {i}")
    
    assert len(window.recent_ai_commands) <= window.max_recent_ai_commands, \
        f"Should not exceed max limit of {window.max_recent_ai_commands}"
    print(f"   - List limited to {window.max_recent_ai_commands} commands: ✓")
    
    # Test 6: Persistence
    print("\n✅ Test 6: Persistence")
    # Create a new window instance to test loading
    window2 = MockMainWindow()
    assert len(window2.recent_ai_commands) == len(window.recent_ai_commands), \
        "New instance should load saved commands"
    assert window2.recent_ai_commands[0] == window.recent_ai_commands[0], \
        "Commands should be loaded in correct order"
    print("   - Commands persist across sessions: ✓")
    
    # Test 7: Long command truncation
    print("\n✅ Test 7: Long command handling")
    long_command = "this is a very long AI command that should be truncated when displayed in the dropdown but stored in full"
    window._add_to_recent_ai_commands(long_command)
    assert window.recent_ai_commands[0] == long_command, "Full command should be stored"
    print("   - Long commands stored in full: ✓")
    
    print(f"\n🎉 All tests passed!")
    print(f"📊 Final state: {len(window.recent_ai_commands)} recent commands")
    print("Recent commands:")
    for i, cmd in enumerate(window.recent_ai_commands[:5]):
        display_cmd = cmd[:50] + "..." if len(cmd) > 50 else cmd
        print(f"   {i+1}. {display_cmd}")


if __name__ == "__main__":
    test_recent_ai_commands()
