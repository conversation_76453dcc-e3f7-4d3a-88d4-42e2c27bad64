<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.025,1.354,11.412,1,9.48,1.518l-.353.613,2.848,10.625.613.353,1.931-.517.354-.613Zm.786,10.66-2.588-9.66.965-.258,2.589,9.659ZM3.5,1.1l.5.5V6H3V2.1H2V6H1V1.6l.5-.5Zm4,0,.5.5v11l-.5.5H7V2.1H6V6H5V1.6l.5-.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,15.5H.5v-8h5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.389,12.108l-1.931.518L9.611,2l1.931-.518ZM4,6V5.109H3.5V1.6h-2V5.109H1V6Zm1.5-.891H5V6h.914L7,7.086v5.023h.5V1.6h-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.5,16H.5L0,15.5v-8L.5,7h5l.5.5v8ZM1,15H5V8H1Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4,14H2V13H4Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
