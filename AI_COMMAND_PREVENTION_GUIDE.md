# AI Command Issue Prevention Guide

This guide explains how to prevent and catch AI command mapping issues like the `SORT_COLUMN_ASCENDING` problem we just fixed.

## The Problem

AI capabilities defined in `ai_manager.py` need to map to actual implementation methods in `main_window.py`. When there's a mismatch, users get confusing error messages like "Unknown AI command: 'SORT_COLUMN_ASCENDING'".

### Root Cause
The dynamic method lookup in `_process_ai_command()` converts capability names like `SORT_COLUMN_ASCENDING` to method names like `_ai_sort_column_ascending`, but the actual method is `_ai_sort_column`.

## Prevention Strategy

### 1. Automated Analysis Tools

We've created several tools to catch these issues:

- **`analyze_ai_commands.py`** - Analyzes current state and finds mismatches
- **`validate_ai_commands.py`** - Validates consistency and can be used in CI/CD
- **`pre_commit_ai_validation.py`** - Pre-commit hook to catch issues before they're committed

### 2. Development Workflow

#### Before Adding New AI Capabilities:

1. **Run Analysis**: `python analyze_ai_commands.py`
2. **Add Capability**: Update `CAPABILITIES` in `ai_manager.py`
3. **Choose Implementation Strategy**:
   - **Option A**: Create method following naming convention (`_ai_capability_name`)
   - **Option B**: Add special case handling in `_process_ai_command()`
4. **Validate**: `python validate_ai_commands.py`
5. **Test**: Try the command in the application

#### For Complex Commands:
Use special case handling when:
- Command name doesn't map cleanly to method name
- Multiple capabilities share the same method (like sorting)
- Command needs parameter transformation

```python
# Add to _process_ai_command() special cases:
if capability == "YOUR_COMMAND":
    self._ai_your_method(params.get("param1"), params.get("param2"))
    return
```

### 3. Naming Conventions

#### Capability Names (in ai_manager.py):
- Use UPPER_CASE_WITH_UNDERSCORES
- Be descriptive and specific
- Group related commands with common prefixes

#### Method Names (in main_window.py):
- Use `_ai_` prefix
- Use lowercase_with_underscores
- Should be callable: `_ai_{capability.lower()}`

### 4. Testing Strategy

#### Manual Testing:
1. Open the application
2. Try the AI command in the AI panel
3. Verify it works as expected

#### Automated Testing:
```python
# Add to test suite
def test_ai_command_mapping():
    issues, working, orphaned = analyze_command_consistency()
    assert len(issues) == 0, f"AI command mapping issues found: {issues}"
```

## Common Patterns

### Pattern 1: Direct Mapping
```python
# Capability: DELETE_ROW
# Method: _ai_delete_row
# Works automatically with dynamic lookup
```

### Pattern 2: Special Case (Parameter Transformation)
```python
# Capability: SORT_COLUMN_ASCENDING
# Method: _ai_sort_column(column_index, ascending=True)
if capability == "SORT_COLUMN_ASCENDING":
    self._ai_sort_column(params.get("column_index"))
    return
```

### Pattern 3: High-Level Commands
```python
# Capability: COPY_COLUMN_TO_NEW_COLUMN_AFTER
# Handler: ai_handler.copy_column_to_new_column_after()
if capability == "COPY_COLUMN_TO_NEW_COLUMN_AFTER":
    self.ai_handler.execute_command(capability, **params)
    return
```

## Maintenance

### Regular Checks:
- Run `python analyze_ai_commands.py` monthly
- Include validation in CI/CD pipeline
- Review orphaned methods periodically

### When Adding Features:
1. Update capabilities first
2. Implement methods second
3. Add special cases if needed
4. Validate before committing

### Documentation:
- Generate mapping docs: `python validate_ai_commands.py --docs`
- Keep this guide updated
- Document complex command patterns

## Quick Reference

```bash
# Check current state
python analyze_ai_commands.py

# Validate for CI/CD
python validate_ai_commands.py

# Generate documentation
python validate_ai_commands.py --docs

# Set up pre-commit hook
cp pre_commit_ai_validation.py .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

## Example Fix

The `SORT_COLUMN_ASCENDING` issue was fixed by adding special case handling:

```python
# Special case for sorting commands
if capability == "SORT_COLUMN_ASCENDING":
    self._ai_sort_column(params.get("column_index"))
    return
elif capability == "SORT_COLUMN_DESCENDING":
    self._ai_sort_column(params.get("column_index"), ascending=False)
    return
```

This approach allows multiple capabilities to share the same implementation method with different parameters.
