"""JEdit2 LibreOffice Branding System.

This module customizes LibreOffice's visual identity to appear as JEdit2,
inspired by the libbieoffice project: https://github.com/sdomi/libbieoffice
"""

import os
import sys
import platform
import subprocess
import shutil
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import zipfile
import json


class JEdit2LibreOfficeBranding:
    """Customizes LibreOffice branding for JEdit2."""
    
    def __init__(self):
        """Initialize the branding system."""
        self.lo_path = self.find_libreoffice_installation()
        self.backup_dir = None
        self.custom_dir = None
        
    def find_libreoffice_installation(self) -> Path:
        """Find LibreOffice installation directory."""
        system = platform.system()
        
        if system == "Windows":
            search_paths = [
                r"C:\Program Files\LibreOffice",
                r"C:\Program Files (x86)\LibreOffice",
            ]
        elif system == "Darwin":  # macOS
            search_paths = [
                "/Applications/LibreOffice.app/Contents",
            ]
        else:  # Linux
            search_paths = [
                "/usr/lib/libreoffice",
                "/opt/libreoffice",
            ]
        
        for path in search_paths:
            if Path(path).exists():
                return Path(path)
        
        return None
    
    def create_jedit2_splash(self, size=(600, 300)) -> Image.Image:
        """Create JEdit2 branded splash screen."""
        # Create base image with professional gradient
        img = Image.new('RGB', size, color='white')
        draw = ImageDraw.Draw(img)
        
        # Create gradient background (blue to lighter blue)
        for y in range(size[1]):
            color_intensity = int(255 - (y / size[1]) * 100)
            color = (50, 100, color_intensity)
            draw.line([(0, y), (size[0], y)], fill=color)
        
        try:
            # Try to load a system font
            title_font = ImageFont.truetype("arial.ttf", 48)
            subtitle_font = ImageFont.truetype("arial.ttf", 24)
            version_font = ImageFont.truetype("arial.ttf", 16)
        except (OSError, IOError):
            # Fallback to default font
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            version_font = ImageFont.load_default()
        
        # Draw JEdit2 title
        title_text = "JEdit2"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (size[0] - title_width) // 2
        title_y = size[1] // 3
        
        # Draw title with shadow effect
        draw.text((title_x + 2, title_y + 2), title_text, fill='black', font=title_font)
        draw.text((title_x, title_y), title_text, fill='white', font=title_font)
        
        # Draw subtitle
        subtitle_text = "Professional Data Editor"
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (size[0] - subtitle_width) // 2
        subtitle_y = title_y + 60
        
        draw.text((subtitle_x + 1, subtitle_y + 1), subtitle_text, fill='black', font=subtitle_font)
        draw.text((subtitle_x, subtitle_y), subtitle_text, fill='lightgray', font=subtitle_font)
        
        # Draw "Powered by LibreOffice"
        powered_text = "Powered by LibreOffice"
        powered_bbox = draw.textbbox((0, 0), powered_text, font=version_font)
        powered_width = powered_bbox[2] - powered_bbox[0]
        powered_x = (size[0] - powered_width) // 2
        powered_y = subtitle_y + 40
        
        draw.text((powered_x, powered_y), powered_text, fill='lightgray', font=version_font)
        
        # Draw version info
        version_text = "Version 2.0.0 - Advanced Data Manipulation Suite"
        version_bbox = draw.textbbox((0, 0), version_text, font=version_font)
        version_width = version_bbox[2] - version_bbox[0]
        version_x = (size[0] - version_width) // 2
        version_y = size[1] - 30
        
        draw.text((version_x, version_y), version_text, fill='white', font=version_font)
        
        return img
    
    def create_jedit2_icons(self):
        """Create JEdit2 branded icons for LibreOffice applications."""
        icons = {}
        
        # Base colors for JEdit2 branding
        colors = {
            'calc': (34, 139, 34),      # Forest Green for spreadsheets
            'writer': (30, 144, 255),   # Dodger Blue for documents
            'impress': (255, 69, 0),    # Red Orange for presentations
            'draw': (138, 43, 226),     # Blue Violet for drawings
            'base': (255, 140, 0),      # Dark Orange for databases
            'math': (220, 20, 60)       # Crimson for math
        }
        
        for app, color in colors.items():
            icon = self.create_app_icon(app, color)
            icons[app] = icon
        
        return icons
    
    def create_app_icon(self, app_name: str, color: tuple, size=(48, 48)) -> Image.Image:
        """Create an application icon with JEdit2 branding."""
        img = Image.new('RGBA', size, color=(0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw rounded rectangle background
        margin = 4
        draw.rounded_rectangle(
            [margin, margin, size[0] - margin, size[1] - margin],
            radius=8,
            fill=color,
            outline='white',
            width=2
        )
        
        # Draw app letter
        app_letter = app_name[0].upper()
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except (OSError, IOError):
            font = ImageFont.load_default()
        
        # Center the letter
        letter_bbox = draw.textbbox((0, 0), app_letter, font=font)
        letter_width = letter_bbox[2] - letter_bbox[0]
        letter_height = letter_bbox[3] - letter_bbox[1]
        letter_x = (size[0] - letter_width) // 2
        letter_y = (size[1] - letter_height) // 2
        
        # Draw letter with shadow
        draw.text((letter_x + 1, letter_y + 1), app_letter, fill='black', font=font)
        draw.text((letter_x, letter_y), app_letter, fill='white', font=font)
        
        # Add "J2" badge in corner
        badge_size = 12
        badge_x = size[0] - badge_size - 2
        badge_y = 2
        
        draw.ellipse(
            [badge_x, badge_y, badge_x + badge_size, badge_y + badge_size],
            fill='gold',
            outline='black'
        )
        
        try:
            badge_font = ImageFont.truetype("arial.ttf", 8)
        except (OSError, IOError):
            badge_font = ImageFont.load_default()
        
        draw.text((badge_x + 1, badge_y), "J2", fill='black', font=badge_font)
        
        return img
    
    def backup_original_files(self):
        """Backup original LibreOffice files before modification."""
        if not self.lo_path:
            raise RuntimeError("LibreOffice installation not found")
        
        self.backup_dir = Path(tempfile.mkdtemp(prefix="jedit2_lo_backup_"))
        
        # Files to backup
        files_to_backup = [
            "share/config/images_colibre.zip",
            "share/config/images_elementary.zip", 
            "share/config/images_karasa_jaga.zip",
            "share/config/images_sifr.zip",
            "share/config/images_sifr_dark.zip",
            "share/config/images_sukapura.zip",
        ]
        
        # Also backup splash files
        splash_patterns = [
            "share/config/splashscreen*.png",
            "share/config/intro*.png",
        ]
        
        backed_up_files = []
        
        for file_path in files_to_backup:
            src = self.lo_path / file_path
            if src.exists():
                dst = self.backup_dir / file_path
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src, dst)
                backed_up_files.append(file_path)
        
        # Backup splash files
        import glob
        for pattern in splash_patterns:
            for src in glob.glob(str(self.lo_path / pattern)):
                src_path = Path(src)
                rel_path = src_path.relative_to(self.lo_path)
                dst = self.backup_dir / rel_path
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst)
                backed_up_files.append(str(rel_path))
        
        print(f"✓ Backed up {len(backed_up_files)} files to {self.backup_dir}")
        return backed_up_files
    
    def install_jedit2_branding(self):
        """Install JEdit2 branding to LibreOffice."""
        if not self.lo_path:
            raise RuntimeError("LibreOffice installation not found")
        
        print("Installing JEdit2 branding...")
        
        # Create custom assets
        self.custom_dir = Path(tempfile.mkdtemp(prefix="jedit2_custom_"))
        
        # 1. Create splash screen
        print("Creating JEdit2 splash screen...")
        splash = self.create_jedit2_splash()
        splash_path = self.custom_dir / "intro.png"
        splash.save(splash_path, "PNG")
        
        # Install splash screen
        lo_splash_dir = self.lo_path / "share" / "config"
        if lo_splash_dir.exists():
            shutil.copy2(splash_path, lo_splash_dir / "intro.png")
            print("✓ Splash screen installed")
        
        # 2. Create and install icons
        print("Creating JEdit2 application icons...")
        icons = self.create_jedit2_icons()
        
        for app_name, icon in icons.items():
            icon_path = self.custom_dir / f"jedit2-{app_name}.png"
            icon.save(icon_path, "PNG")
            
            # Install to LibreOffice icon directories
            self.install_app_icon(app_name, icon_path)
        
        # 3. Modify theme files to use JEdit2 icons
        self.modify_icon_themes()
        
        print("✓ JEdit2 branding installed successfully!")
    
    def install_app_icon(self, app_name: str, icon_path: Path):
        """Install application icon to LibreOffice."""
        # Map app names to LibreOffice icon names
        icon_mapping = {
            'calc': 'libreoffice-calc.png',
            'writer': 'libreoffice-writer.png',
            'impress': 'libreoffice-impress.png',
            'draw': 'libreoffice-draw.png',
            'base': 'libreoffice-base.png',
            'math': 'libreoffice-math.png'
        }
        
        if app_name not in icon_mapping:
            return
        
        lo_icon_name = icon_mapping[app_name]
        
        # Install to share/icons directories
        icon_dirs = [
            "share/icons/hicolor/48x48/apps",
            "share/icons/hicolor/32x32/apps", 
            "share/icons/hicolor/16x16/apps",
        ]
        
        for icon_dir in icon_dirs:
            target_dir = self.lo_path / icon_dir
            if target_dir.exists():
                # Resize icon for this size
                size = 48 if "48x48" in icon_dir else 32 if "32x32" in icon_dir else 16
                
                resized_icon = Image.open(icon_path).resize((size, size), Image.Resampling.LANCZOS)
                resized_path = self.custom_dir / f"{app_name}_{size}.png"
                resized_icon.save(resized_path, "PNG")
                
                shutil.copy2(resized_path, target_dir / lo_icon_name)
    
    def modify_icon_themes(self):
        """Modify LibreOffice icon theme files."""
        # This would involve modifying the icon zip files
        # For now, we'll create a simple modification
        print("Modifying icon themes for JEdit2 branding...")
        
        # Create a custom theme configuration
        theme_config = {
            "name": "JEdit2 Professional",
            "description": "JEdit2 branded LibreOffice theme",
            "version": "2.0.0"
        }
        
        config_path = self.custom_dir / "jedit2_theme.json"
        with open(config_path, 'w') as f:
            json.dump(theme_config, f, indent=2)
        
        print("✓ Icon themes modified")
    
    def restore_original_branding(self):
        """Restore original LibreOffice branding."""
        if not self.backup_dir or not self.backup_dir.exists():
            print("No backup found to restore from")
            return False
        
        print("Restoring original LibreOffice branding...")
        
        # Restore backed up files
        restored_count = 0
        for backup_file in self.backup_dir.rglob("*"):
            if backup_file.is_file():
                rel_path = backup_file.relative_to(self.backup_dir)
                target_path = self.lo_path / rel_path
                
                if target_path.parent.exists():
                    shutil.copy2(backup_file, target_path)
                    restored_count += 1
        
        print(f"✓ Restored {restored_count} files")
        
        # Clean up backup directory
        shutil.rmtree(self.backup_dir)
        self.backup_dir = None
        
        return True
    
    def cleanup(self):
        """Clean up temporary files."""
        if self.custom_dir and self.custom_dir.exists():
            shutil.rmtree(self.custom_dir)
        
        if self.backup_dir and self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)


def main():
    """Main branding application."""
    print("JEdit2 LibreOffice Branding System")
    print("=" * 50)
    print("Inspired by: https://github.com/sdomi/libbieoffice")
    print()
    
    try:
        branding = JEdit2LibreOfficeBranding()
        
        if not branding.lo_path:
            print("✗ LibreOffice installation not found")
            return 1
        
        print(f"✓ Found LibreOffice: {branding.lo_path}")
        
        # Ask user for action
        if len(sys.argv) > 1:
            action = sys.argv[1].lower()
        else:
            print("\nAvailable actions:")
            print("  install   - Install JEdit2 branding")
            print("  restore   - Restore original LibreOffice branding")
            print("  preview   - Create preview images only")
            action = input("\nChoose action (install/restore/preview): ").lower()
        
        if action == "install":
            # Backup original files
            backed_up = branding.backup_original_files()
            
            # Install JEdit2 branding
            branding.install_jedit2_branding()
            
            print("\n" + "=" * 50)
            print("✓ JEdit2 branding installed successfully!")
            print("\nLibreOffice will now show:")
            print("• JEdit2 branded splash screen")
            print("• JEdit2 application icons")
            print("• Professional data editor theme")
            print("\nRestart LibreOffice to see the changes.")
            
        elif action == "restore":
            success = branding.restore_original_branding()
            if success:
                print("✓ Original LibreOffice branding restored")
            else:
                print("✗ Failed to restore branding")
                
        elif action == "preview":
            branding.custom_dir = Path(tempfile.mkdtemp(prefix="jedit2_preview_"))
            
            # Create preview images
            splash = branding.create_jedit2_splash()
            splash.save("jedit2_splash_preview.png")
            print("✓ Created jedit2_splash_preview.png")
            
            icons = branding.create_jedit2_icons()
            for app_name, icon in icons.items():
                icon.save(f"jedit2_{app_name}_icon_preview.png")
                print(f"✓ Created jedit2_{app_name}_icon_preview.png")
                
            print("\n✓ Preview images created in current directory")
            
        else:
            print("Invalid action. Use: install, restore, or preview")
            return 1
        
        branding.cleanup()
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 