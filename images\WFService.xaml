<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M11.5,8.5a2,2,0,1,1-2-2A2,2,0,0,1,11.5,8.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.5,2.5h4v2h-4Zm-3,11h3v-2h-3Zm7-2v2h3v-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,11H14V8.5L13.5,8H11.945A2.5,2.5,0,0,0,10,6.05V5h1.5l.5-.5v-2L11.5,2h-4L7,2.5v2l.5.5H9V6.05A2.5,2.5,0,0,0,7.055,8H5.5L5,8.5V11H4.5l-.5.5v2l.5.5h3l.5-.5v-2L7.5,11H6V9H7.05a2.5,2.5,0,0,0,4.9,0H13v2H11.5l-.5.5v2l.5.5h3l.5-.5v-2ZM8,4V3h3V4ZM7,12v1H5V12Zm2.5-2A1.5,1.5,0,1,1,11,8.5,1.5,1.5,0,0,1,9.5,10ZM14,13H12V12h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4,5V6H0V5ZM2,8H4V7H2ZM0,10H4V9H0Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
