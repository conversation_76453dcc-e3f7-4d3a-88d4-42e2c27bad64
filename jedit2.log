2025-06-08 17:07:30,413 - jedit2 - ERROR - ERROR - application: Application initialization error: C++ assertion "CheckExpectedParentIs(w, m_containingWindow)" failed at ..\..\src\common\sizer.cpp(887) in wxSizer::SetContainingWindow(): Windows managed by the sizer associated with the given window must have this window as parent, otherwise they will not be repositioned correctly.

Please use the window wxPanel@000001756774B590 ("panel", HWND=0000000000211138) with which this sizer is associated, as the parent when creating the window wxButton@00000175676C63E0 ("Lazy Loading", HWND=00000000003616C2) managed by it.
2025-06-08 17:11:10,808 - jedit2 - INFO - INFO - memory: Application exit - Memory stats: MemoryStats(total_memory=16907755520, used_memory=14360924160, free_memory=2546831360, swap_used=2973249536, swap_free=31386488832, fragmentation=0.0, gc_count=162, gc_time=0.9719994068145752, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 19:33:57,788 - jedit2 - ERROR - CRITICAL - application: module 'wx' has no attribute 'ART_ALIGN_LEFT'
2025-06-08 19:35:04,655 - jedit2 - ERROR - CRITICAL - application: module 'wx' has no attribute 'SYS_COLOUR_BUTTONFACE'
2025-06-08 19:38:33,986 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14024589312, free_memory=2883166208, swap_used=3198935040, swap_free=31160803328, fragmentation=0.0, gc_count=101, gc_time=0.42653465270996094, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 19:40:08,340 - jedit2 - ERROR - CRITICAL - application: '<=' not supported between instances of 'MainWindow' and 'int'
2025-06-08 19:41:51,443 - jedit2 - ERROR - CRITICAL - application: '<=' not supported between instances of 'MainWindow' and 'int'
2025-06-08 19:42:59,506 - jedit2 - ERROR - CRITICAL - application: '<=' not supported between instances of 'MainWindow' and 'int'
2025-06-08 19:45:29,698 - jedit2 - ERROR - CRITICAL - application: '<=' not supported between instances of 'MainWindow' and 'int'
2025-06-08 19:47:07,796 - jedit2 - ERROR - CRITICAL - application: '<=' not supported between instances of 'MainWindow' and 'int'
2025-06-08 19:50:48,377 - jedit2 - ERROR - CRITICAL - application: module 'wx' has no attribute 'ART_NORMAL_ICON'
2025-06-08 19:56:43,244 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14119419904, free_memory=2788335616, swap_used=3418419200, swap_free=30941319168, fragmentation=0.0, gc_count=211, gc_time=0.866051435470581, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 19:59:40,208 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14438174720, free_memory=2469580800, swap_used=3258363904, swap_free=31101374464, fragmentation=0.0, gc_count=34, gc_time=0.25664591789245605, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:06:48,954 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15014785024, free_memory=1892970496, swap_used=3230326784, swap_free=31129411584, fragmentation=0.0, gc_count=292, gc_time=2.4903266429901123, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:32:42,376 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14717763584, free_memory=2189991936, swap_used=3327234048, swap_free=31032504320, fragmentation=0.0, gc_count=154, gc_time=1.1691548824310303, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:41:53,404 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14711533568, free_memory=2196221952, swap_used=3403137024, swap_free=30956601344, fragmentation=0.0, gc_count=44, gc_time=0.36916255950927734, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:44:19,452 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14802001920, free_memory=2105753600, swap_used=3425026048, swap_free=30934712320, fragmentation=0.0, gc_count=38, gc_time=0.29164576530456543, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:46:02,567 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14813007872, free_memory=2094747648, swap_used=3390074880, swap_free=30969663488, fragmentation=0.0, gc_count=20, gc_time=0.22778081893920898, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:48:06,117 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15078346752, free_memory=1829408768, swap_used=3409666048, swap_free=30950072320, fragmentation=0.0, gc_count=104, gc_time=1.10015869140625, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:52:02,912 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14995578880, free_memory=1912176640, swap_used=3396612096, swap_free=30963126272, fragmentation=0.0, gc_count=59, gc_time=0.4698452949523926, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:54:58,144 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15142694912, free_memory=1765060608, swap_used=3346567168, swap_free=31013171200, fragmentation=0.0, gc_count=40, gc_time=0.2732415199279785, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 20:59:08,717 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15317164032, free_memory=1590591488, swap_used=3420864512, swap_free=30938873856, fragmentation=0.0, gc_count=52, gc_time=0.4245634078979492, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:04:11,740 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15740973056, free_memory=1166782464, swap_used=3002793984, swap_free=31356944384, fragmentation=0.0, gc_count=79, gc_time=0.6402485370635986, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:06:36,859 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15336751104, free_memory=1571004416, swap_used=3361271808, swap_free=30998466560, fragmentation=0.0, gc_count=55, gc_time=0.367311954498291, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:06:39,801 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15343501312, free_memory=1564254208, swap_used=3361271808, swap_free=30998466560, fragmentation=0.0, gc_count=16, gc_time=0.10927224159240723, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:07:41,035 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15361257472, free_memory=1546498048, swap_used=3349245952, swap_free=31010492416, fragmentation=0.0, gc_count=38, gc_time=0.2206573486328125, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:09:59,520 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15455461376, free_memory=1452294144, swap_used=3348729856, swap_free=31011008512, fragmentation=0.0, gc_count=29, gc_time=0.21645164489746094, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:20:09,717 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15528517632, free_memory=1379237888, swap_used=3398627328, swap_free=30961111040, fragmentation=0.0, gc_count=70, gc_time=0.37853360176086426, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:20:11,281 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15510429696, free_memory=1397325824, swap_used=3398627328, swap_free=30961111040, fragmentation=0.0, gc_count=208, gc_time=1.447026014328003, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:23:59,831 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15126884352, free_memory=1780871168, swap_used=3489144832, swap_free=30870593536, fragmentation=0.0, gc_count=50, gc_time=0.24010825157165527, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:25:53,191 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15406632960, free_memory=1501122560, swap_used=3154198528, swap_free=31205539840, fragmentation=0.0, gc_count=55, gc_time=0.36061549186706543, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:28:49,489 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15507410944, free_memory=1400344576, swap_used=3389079552, swap_free=30970658816, fragmentation=0.0, gc_count=96, gc_time=0.7117578983306885, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:32:00,495 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15427661824, free_memory=1480093696, swap_used=3381657600, swap_free=30978080768, fragmentation=0.0, gc_count=38, gc_time=0.193497896194458, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:32:04,442 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15442546688, free_memory=1465208832, swap_used=3381657600, swap_free=30978080768, fragmentation=0.0, gc_count=154, gc_time=1.3348956108093262, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-08 21:32:40,050 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15386693632, free_memory=1521061888, swap_used=3381657600, swap_free=30978080768, fragmentation=0.0, gc_count=27, gc_time=0.09341573715209961, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 05:50:40,651 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15009718272, free_memory=1898037248, swap_used=3639767040, swap_free=30719971328, fragmentation=0.0, gc_count=2533, gc_time=18.559898853302002, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 06:18:36,910 - jedit2 - ERROR - CRITICAL - application: Image.SetAlpha(): arguments did not match any overloaded call:
  overload 1: not enough arguments
  overload 2: argument 1 has unexpected type 'int'
2025-06-09 08:05:28,837 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14592155648, free_memory=2315599872, swap_used=2716667904, swap_free=31643070464, fragmentation=0.0, gc_count=602, gc_time=2.298760414123535, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 08:05:29,881 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14580563968, free_memory=2327191552, swap_used=2716663808, swap_free=31643074560, fragmentation=0.0, gc_count=868, gc_time=5.278848648071289, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 08:07:10,217 - jedit2 - ERROR - CRITICAL - application: 'GraphicsContext' object has no attribute 'DrawLine'
2025-06-09 08:16:14,099 - jedit2 - ERROR - CRITICAL - application: Pen(): arguments did not match any overloaded call:
  overload 1: too many arguments
  overload 2: argument 1 has unexpected type 'Colour'
  overload 3: argument 2 has unexpected type 'float'
  overload 4: argument 1 has unexpected type 'Colour'
2025-06-09 12:43:21,534 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14484094976, free_memory=2423660544, swap_used=3381227520, swap_free=30978510848, fragmentation=0.0, gc_count=2158, gc_time=12.796974182128906, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 13:35:31,852 - jedit2 - ERROR - CRITICAL - application: 'RibbonButtonBar' object has no attribute 'AddControl'
2025-06-09 13:36:58,311 - jedit2 - ERROR - CRITICAL - application: C++ assertion "CheckSizerFlags(!((flags) & (wxALIGN_RIGHT | wxALIGN_CENTRE_HORIZONTAL | wxALIGN_BOTTOM | wxALIGN_CENTRE_VERTICAL)))" failed at ..\..\src\common\sizer.cpp(2305) in wxBoxSizer::DoInsert(): wxALIGN_RIGHT | wxALIGN_CENTRE_HORIZONTAL | wxALIGN_BOTTOM | wxALIGN_CENTRE_VERTICAL will be ignored in this sizer: wxEXPAND overrides alignment flags in box sizers

DO NOT PANIC !!

If you're an end user running a program not developed by you, please ignore this message, it is harmless, and please try reporting the problem to the program developers.

You may also set WXSUPPRESS_SIZER_FLAGS_CHECK environment variable to suppress all such checks when running this program.

If you're the developer, simply remove this flag from your code to avoid getting this message. You can also call wxSizerFlags::DisableConsistencyChecks() to globally disable all such checks, but this is strongly not recommended.
2025-06-09 13:49:13,674 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15749767168, free_memory=1157988352, swap_used=3618541568, swap_free=30741196800, fragmentation=0.0, gc_count=620, gc_time=15.282339334487915, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 13:53:10,728 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15365869568, free_memory=1541885952, swap_used=3604553728, swap_free=30755184640, fragmentation=0.0, gc_count=186, gc_time=3.9985098838806152, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:02:54,216 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14430588928, free_memory=2477166592, swap_used=3574177792, swap_free=30785560576, fragmentation=0.0, gc_count=518, gc_time=9.917059421539307, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:03:58,142 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14899429376, free_memory=2008326144, swap_used=3572854784, swap_free=30786883584, fragmentation=0.0, gc_count=34, gc_time=0.7020890712738037, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:13:54,067 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14993833984, free_memory=1913921536, swap_used=3642458112, swap_free=30717280256, fragmentation=0.0, gc_count=202, gc_time=6.63867712020874, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:17:22,799 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15173787648, free_memory=1733967872, swap_used=3623948288, swap_free=30735790080, fragmentation=0.0, gc_count=188, gc_time=6.088763952255249, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:21:59,684 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15129239552, free_memory=1778515968, swap_used=3780866048, swap_free=30578872320, fragmentation=0.0, gc_count=177, gc_time=3.605414628982544, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:36:55,230 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15408234496, free_memory=1499521024, swap_used=3710726144, swap_free=30649012224, fragmentation=0.0, gc_count=384, gc_time=12.243089437484741, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:37:16,303 - jedit2 - ERROR - CRITICAL - application: C++ assertion ""!m_frameStatusBar"" failed at ..\..\src\common\framecmn.cpp(386) in wxFrameBase::CreateStatusBar(): recreating status bar in wxFrame
2025-06-09 14:42:29,218 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14841851904, free_memory=2065903616, swap_used=3962347520, swap_free=30397390848, fragmentation=0.0, gc_count=95, gc_time=1.9834129810333252, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:52:07,512 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15363641344, free_memory=1544114176, swap_used=3854618624, swap_free=30505119744, fragmentation=0.0, gc_count=173, gc_time=5.147253036499023, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:53:15,501 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15075856384, free_memory=1831899136, swap_used=3825438720, swap_free=30534299648, fragmentation=0.0, gc_count=54, gc_time=1.0903394222259521, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 14:57:22,397 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14653284352, free_memory=2254471168, swap_used=3819036672, swap_free=30540701696, fragmentation=0.0, gc_count=35, gc_time=0.6884801387786865, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:02:05,139 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14894395392, free_memory=2013360128, swap_used=3825356800, swap_free=30534381568, fragmentation=0.0, gc_count=37, gc_time=0.7084012031555176, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:06:36,501 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14894854144, free_memory=2012901376, swap_used=3952021504, swap_free=30407716864, fragmentation=0.0, gc_count=45, gc_time=0.8626799583435059, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:10:54,185 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14834704384, free_memory=2073051136, swap_used=3881865216, swap_free=30477873152, fragmentation=0.0, gc_count=37, gc_time=0.6969146728515625, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:14:54,066 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15766290432, free_memory=1141465088, swap_used=3756707840, swap_free=30603030528, fragmentation=0.0, gc_count=40, gc_time=0.7649385929107666, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:20:45,868 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14444302336, free_memory=2463453184, swap_used=4027211776, swap_free=30332526592, fragmentation=0.0, gc_count=43, gc_time=0.8265800476074219, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 15:33:47,628 - jedit2 - ERROR - CRITICAL - application: 'RibbonButtonBar' object has no attribute 'AddControl'
2025-06-09 16:31:11,717 - jedit2 - ERROR - CRITICAL - application: 'MainWindow' object has no attribute '_on_find'
2025-06-09 16:33:02,109 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15114326016, free_memory=1793429504, swap_used=4111622144, swap_free=30248116224, fragmentation=0.0, gc_count=58, gc_time=1.4952056407928467, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 16:37:23,673 - jedit2 - ERROR - CRITICAL - application: 'GraphicsContext' object has no attribute 'DrawLine'
2025-06-09 16:40:18,627 - jedit2 - ERROR - CRITICAL - application: C++ assertion "!bitmap.GetSelectedInto()" failed at ..\..\src\msw\bitmap.cpp(1638) in wxMask::Create(): bitmap can't be selected in another DC
2025-06-09 16:43:06,858 - jedit2 - ERROR - CRITICAL - application: Pen(): arguments did not match any overloaded call:
  overload 1: too many arguments
  overload 2: argument 1 has unexpected type 'str'
  overload 3: argument 2 has unexpected type 'float'
  overload 4: argument 1 has unexpected type 'str'
2025-06-09 16:44:08,534 - jedit2 - ERROR - CRITICAL - application: 'MainWindow' object has no attribute 'ID_FORMAT_BOLD'
2025-06-09 16:47:05,374 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15480770560, free_memory=1426984960, swap_used=4037910528, swap_free=30321827840, fragmentation=0.0, gc_count=105, gc_time=3.157057523727417, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 16:48:52,098 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14887866368, free_memory=2019889152, swap_used=4197249024, swap_free=30162489344, fragmentation=0.0, gc_count=8, gc_time=0.1891162395477295, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 16:53:51,439 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14297591808, free_memory=2610163712, swap_used=4313296896, swap_free=30046441472, fragmentation=0.0, gc_count=16, gc_time=0.3571593761444092, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 16:54:24,972 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14584627200, free_memory=2323128320, swap_used=4312510464, swap_free=30047227904, fragmentation=0.0, gc_count=4, gc_time=0.1031031608581543, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:31:36,774 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14234361856, free_memory=2673393664, swap_used=4705988608, swap_free=29653749760, fragmentation=0.0, gc_count=13, gc_time=0.3197178840637207, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:34:02,159 - jedit2 - ERROR - CRITICAL - application: Image.SetAlpha(): arguments did not match any overloaded call:
  overload 1: not enough arguments
  overload 2: not enough arguments
2025-06-09 18:36:52,238 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14540709888, free_memory=2367045632, swap_used=4468006912, swap_free=29891731456, fragmentation=0.0, gc_count=11, gc_time=0.23900699615478516, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:39:15,089 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14386565120, free_memory=2521190400, swap_used=4465582080, swap_free=29894156288, fragmentation=0.0, gc_count=30, gc_time=0.8119046688079834, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:42:40,392 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14643400704, free_memory=2264354816, swap_used=4321751040, swap_free=30037987328, fragmentation=0.0, gc_count=69, gc_time=1.313321590423584, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:50:00,664 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14516289536, free_memory=2391465984, swap_used=4585234432, swap_free=29774503936, fragmentation=0.0, gc_count=49, gc_time=0.9239974021911621, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:56:04,047 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14714011648, free_memory=2193743872, swap_used=4429676544, swap_free=29930061824, fragmentation=0.0, gc_count=134, gc_time=3.190070390701294, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:57:23,066 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14672781312, free_memory=2234974208, swap_used=4428230656, swap_free=29931507712, fragmentation=0.0, gc_count=22, gc_time=0.4433565139770508, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 18:59:36,235 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14792962048, free_memory=2114793472, swap_used=4511207424, swap_free=29848530944, fragmentation=0.0, gc_count=80, gc_time=1.8874938488006592, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:02:57,705 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15202320384, free_memory=1705435136, swap_used=4454223872, swap_free=29905514496, fragmentation=0.0, gc_count=72, gc_time=1.3671307563781738, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:14:34,293 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15902687232, free_memory=1005068288, swap_used=3925413888, swap_free=30434324480, fragmentation=0.0, gc_count=27, gc_time=0.884129524230957, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:14:38,380 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15848857600, free_memory=1058897920, swap_used=3925413888, swap_free=30434324480, fragmentation=0.0, gc_count=516, gc_time=18.747112274169922, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:17:32,681 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15653744640, free_memory=1254010880, swap_used=4203081728, swap_free=30156656640, fragmentation=0.0, gc_count=48, gc_time=1.8103272914886475, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:23:32,410 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15727636480, free_memory=1180119040, swap_used=4277690368, swap_free=30082048000, fragmentation=0.0, gc_count=24, gc_time=0.6049399375915527, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:23:33,184 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15727292416, free_memory=1180463104, swap_used=4277690368, swap_free=30082048000, fragmentation=0.0, gc_count=236, gc_time=8.65978217124939, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 19:25:18,683 - jedit2 - ERROR - CRITICAL - application: C++ assertion ""!HasAlpha()"" failed at ..\..\src\common\image.cpp(2218) in wxImage::InitAlpha(): image already has an alpha channel
2025-06-09 20:56:26,669 - jedit2 - ERROR - CRITICAL - application: module 'wx.lib.agw.ribbon' has no attribute 'RibbonArtProvider'
2025-06-09 20:58:45,953 - jedit2 - ERROR - CRITICAL - application: module 'wx.ribbon' has no attribute 'RIBBON_ART_PANEL_BACKGROUND_COLOUR'
2025-06-09 21:09:16,330 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14675853312, free_memory=**********, swap_used=**********, swap_free=29641039872, fragmentation=0.0, gc_count=53, gc_time=1.3892576694488525, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 21:12:24,338 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14837342208, free_memory=2070413312, swap_used=4729745408, swap_free=29629992960, fragmentation=0.0, gc_count=121, gc_time=2.98241925239563, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 21:19:46,094 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15079481344, free_memory=1828274176, swap_used=5216219136, swap_free=29143519232, fragmentation=0.0, gc_count=151, gc_time=3.2870092391967773, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 21:19:47,163 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15075672064, free_memory=1832083456, swap_used=5216219136, swap_free=29143519232, fragmentation=0.0, gc_count=307, gc_time=10.274251461029053, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-09 21:23:53,797 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14656737280, free_memory=2251018240, swap_used=5202640896, swap_free=29157097472, fragmentation=0.0, gc_count=138, gc_time=3.4782214164733887, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 06:39:31,782 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14764380160, free_memory=2143375360, swap_used=5205475328, swap_free=29154263040, fragmentation=0.0, gc_count=430, gc_time=16.63912081718445, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 06:56:53,381 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14780805120, free_memory=2126950400, swap_used=4809465856, swap_free=29550272512, fragmentation=0.0, gc_count=607, gc_time=22.479846954345703, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:24:10,495 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14944849920, free_memory=1962905600, swap_used=5313884160, swap_free=29045854208, fragmentation=0.0, gc_count=41, gc_time=1.0596981048583984, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:31:43,063 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14025814016, free_memory=2881941504, swap_used=5112193024, swap_free=29247545344, fragmentation=0.0, gc_count=105, gc_time=3.509857654571533, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:33:56,442 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14980587520, free_memory=1927168000, swap_used=4816982016, swap_free=29542756352, fragmentation=0.0, gc_count=51, gc_time=1.7010672092437744, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:39:26,886 - jedit2.ui.advanced_filter_dialog - ERROR - Error showing number filter dialog: C++ assertion "CheckExpectedParentIs(w, m_containingWindow)" failed at ..\..\src\common\sizer.cpp(887) in wxSizer::SetContainingWindow(): Windows managed by the sizer associated with the given window must have this window as parent, otherwise they will not be repositioned correctly.

Please use the window wxPanel@000002887CCC2BB0 ("panel", HWND=0000000000261AA0) with which this sizer is associated, as the parent when creating the window wxPanel@000002887CCC2E60 ("panel", HWND=00000000000B1D66) managed by it.
2025-06-10 07:48:08,541 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15763038208, free_memory=1144717312, swap_used=5199200256, swap_free=29160538112, fragmentation=0.0, gc_count=118, gc_time=7.002928018569946, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:50:36,331 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14454288384, free_memory=2453467136, swap_used=5483266048, swap_free=28876472320, fragmentation=0.0, gc_count=67, gc_time=1.6048743724822998, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:54:57,987 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15135096832, free_memory=1772658688, swap_used=5356646400, swap_free=29003091968, fragmentation=0.0, gc_count=70, gc_time=1.6810550689697266, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 07:56:39,552 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14965858304, free_memory=1941897216, swap_used=5393227776, swap_free=28966510592, fragmentation=0.0, gc_count=51, gc_time=1.113929033279419, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:04:48,839 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15922384896, free_memory=985370624, swap_used=4965359616, swap_free=29394378752, fragmentation=0.0, gc_count=109, gc_time=2.92655086517334, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:05:38,546 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14564847616, free_memory=2342907904, swap_used=5422784512, swap_free=28936953856, fragmentation=0.0, gc_count=35, gc_time=0.9833462238311768, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:07:42,400 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14989144064, free_memory=1918611456, swap_used=5350936576, swap_free=29008801792, fragmentation=0.0, gc_count=69, gc_time=1.9999473094940186, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:09:18,809 - jedit2.main_window - INFO - AI Response: {'capability': 'FILTER_NUMBER_GREATER_THAN', 'parameters': {'column_index': 2, 'value': 48000, 'include_equal': True}}
2025-06-10 08:10:20,774 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14797701120, free_memory=2110054400, swap_used=5421096960, swap_free=28938641408, fragmentation=0.0, gc_count=89, gc_time=2.343764066696167, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:12:34,490 - jedit2.main_window - INFO - AI Response: {'capability': 'FILTER_NUMBER_GREATER_THAN', 'parameters': {'column_index': 2, 'value': 48000, 'include_equal': False}}
2025-06-10 08:15:39,454 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14719479808, free_memory=2188275712, swap_used=5347278848, swap_free=29012459520, fragmentation=0.0, gc_count=218, gc_time=5.754889965057373, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:18:30,355 - jedit2 - ERROR - CRITICAL - application: 'MainWindow' object has no attribute 'ID_QUICK_NUMBER_FILTER'
2025-06-10 08:35:15,660 - jedit2 - ERROR - CRITICAL - application: TextCtrl(): arguments did not match any overloaded call:
  overload 1: too many arguments
  overload 2: 'tool_tip' is not a valid keyword argument
2025-06-10 08:49:14,401 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=16312709120, free_memory=595046400, swap_used=4992528384, swap_free=29367209984, fragmentation=0.0, gc_count=408, gc_time=13.947864055633545, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 08:51:23,615 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15089315840, free_memory=1818439680, swap_used=5428064256, swap_free=28931674112, fragmentation=0.0, gc_count=367, gc_time=9.985973119735718, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:03:57,287 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=16046796800, free_memory=860958720, swap_used=5071204352, swap_free=29288534016, fragmentation=0.0, gc_count=211, gc_time=6.474496603012085, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:07:47,870 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15099678720, free_memory=1808076800, swap_used=5581615104, swap_free=28778123264, fragmentation=0.0, gc_count=56, gc_time=1.433257818222046, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:14:00,493 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15501389824, free_memory=1406365696, swap_used=5436841984, swap_free=28922896384, fragmentation=0.0, gc_count=100, gc_time=2.4106388092041016, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:23:51,980 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=13886889984, free_memory=3020865536, swap_used=5629227008, swap_free=28730511360, fragmentation=0.0, gc_count=359, gc_time=7.34061074256897, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:24:33,582 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14283857920, free_memory=2623897600, swap_used=5628833792, swap_free=28730904576, fragmentation=0.0, gc_count=33, gc_time=1.135880708694458, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:26:20,986 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15282147328, free_memory=1625608192, swap_used=5713825792, swap_free=28645912576, fragmentation=0.0, gc_count=18, gc_time=0.4962756633758545, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:31:38,520 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15541092352, free_memory=1366663168, swap_used=5524791296, swap_free=28834947072, fragmentation=0.0, gc_count=110, gc_time=2.837244987487793, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 09:50:11,501 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15007232000, free_memory=1900523520, swap_used=5791326208, swap_free=29648429056, fragmentation=0.0, gc_count=781, gc_time=16.754279375076294, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 12:16:46,712 - jedit2.formats.xlsx_handler - INFO - Saving Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:16:46,734 - jedit2.formats.xlsx_handler - INFO - Successfully saved Excel file with 9 rows
2025-06-10 12:17:00,391 - jedit2.formats.xlsx_handler - INFO - Parsing Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:17:00,397 - jedit2.formats.xlsx_handler - INFO - Successfully parsed Excel file with 9 rows
2025-06-10 12:17:56,611 - jedit2.formats.xlsx_handler - INFO - Saving Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:17:56,655 - jedit2.formats.xlsx_handler - INFO - Successfully saved Excel file with 9 rows
2025-06-10 12:19:05,624 - jedit2.formats.xlsx_handler - INFO - Saving Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:19:05,629 - jedit2.formats.xlsx_handler - INFO - Successfully saved Excel file with 9 rows
2025-06-10 12:20:04,842 - jedit2.formats.xlsx_handler - INFO - Saving Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:20:04,847 - jedit2.formats.xlsx_handler - INFO - Successfully saved Excel file with 9 rows
2025-06-10 12:20:15,431 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15843065856, free_memory=1064689664, swap_used=5577728000, swap_free=30941065216, fragmentation=0.0, gc_count=235, gc_time=6.009627103805542, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 12:26:06,556 - jedit2.formats.xlsx_handler - INFO - Parsing Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 12:26:06,562 - jedit2.formats.xlsx_handler - INFO - Successfully parsed Excel file with 9 rows
2025-06-10 12:26:49,285 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15046598656, free_memory=1861156864, swap_used=5720571904, swap_free=30798221312, fragmentation=0.0, gc_count=66, gc_time=1.674424171447754, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 12:30:35,529 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15798640640, free_memory=1109114880, swap_used=5649870848, swap_free=30868922368, fragmentation=0.0, gc_count=32, gc_time=1.1831326484680176, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 14:37:20,330 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14985965568, free_memory=1921789952, swap_used=5560860672, swap_free=32049676288, fragmentation=0.0, gc_count=650, gc_time=15.026948928833008, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 14:41:56,587 - jedit2 - ERROR - CRITICAL - application: 'MainWindow' object has no attribute '_on_tab_changed'
2025-06-10 14:46:54,709 - jedit2 - ERROR - CRITICAL - application: 'MainWindow' object has no attribute '_on_tab_changed'
2025-06-10 14:50:21,527 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14602031104, free_memory=2305724416, swap_used=6160867328, swap_free=31449669632, fragmentation=0.0, gc_count=116, gc_time=3.1798837184906006, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 14:58:51,946 - jedit2.formats.xlsx_handler - INFO - Parsing Excel file: C:\Users\<USER>\Documents\Python\jedit2\test_excel.xlsx
2025-06-10 14:58:51,964 - jedit2.formats.xlsx_handler - INFO - Successfully parsed Excel file with 9 rows
2025-06-10 15:00:58,513 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15595843584, free_memory=1311911936, swap_used=6079496192, swap_free=31531040768, fragmentation=0.0, gc_count=173, gc_time=4.742804527282715, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:08:01,044 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14451851264, free_memory=2455904256, swap_used=6354669568, swap_free=31255867392, fragmentation=0.0, gc_count=93, gc_time=2.9122982025146484, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:26:00,811 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15700512768, free_memory=1207242752, swap_used=6027202560, swap_free=31583334400, fragmentation=0.0, gc_count=778, gc_time=32.618783950805664, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:26:21,294 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15196364800, free_memory=1711390720, swap_used=6150041600, swap_free=31460495360, fragmentation=0.0, gc_count=12, gc_time=0.31580209732055664, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:34:35,301 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15597797376, free_memory=1309958144, swap_used=6090387456, swap_free=31520149504, fragmentation=0.0, gc_count=268, gc_time=12.331834316253662, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:38:10,984 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=16087183360, free_memory=820572160, swap_used=5873332224, swap_free=32817090560, fragmentation=0.0, gc_count=185, gc_time=7.140455484390259, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:46:24,319 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15836286976, free_memory=1071468544, swap_used=6104621056, swap_free=33671413760, fragmentation=0.0, gc_count=47, gc_time=1.2669670581817627, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:46:24,594 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15831670784, free_memory=1076084736, swap_used=6104621056, swap_free=33671413760, fragmentation=0.0, gc_count=294, gc_time=11.521567106246948, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:46:25,436 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15830855680, free_memory=1076899840, swap_used=6104621056, swap_free=33671413760, fragmentation=0.0, gc_count=511, gc_time=20.654608249664307, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 15:48:35,792 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14594629632, free_memory=2313125888, swap_used=6256189440, swap_free=33519845376, fragmentation=0.0, gc_count=28, gc_time=0.7528872489929199, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:01:59,466 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15745495040, free_memory=1162260480, swap_used=6188462080, swap_free=33587572736, fragmentation=0.0, gc_count=28, gc_time=0.9927723407745361, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:02:15,918 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15700643840, free_memory=1207111680, swap_used=6187646975, swap_free=33588387841, fragmentation=0.0, gc_count=96, gc_time=4.586641073226929, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:44:31,115 - jedit2.main_window - INFO - AI Response: ([{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}], '```json\n[\n  {\n    "command": "OPEN_FILE",\n    "params": {\n      "file_path": "price.json"\n    }\n  }\n]\n```\n')
2025-06-10 16:44:37,029 - jedit2.main_window - INFO - AI Response: ([{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}], '```json\n[\n  {\n    "command": "OPEN_FILE",\n    "params": {\n      "file_path": "price.json"\n    }\n  }\n]\n```\n')
2025-06-10 16:44:47,139 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15140929536, free_memory=1766825984, swap_used=5094264832, swap_free=33091305472, fragmentation=0.0, gc_count=504, gc_time=16.7663414478302, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:47:56,968 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15658541056, free_memory=1249214464, swap_used=4953415679, swap_free=33232154625, fragmentation=0.0, gc_count=23, gc_time=0.5527658462524414, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:51:51,845 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14941421568, free_memory=1966333952, swap_used=5141512192, swap_free=33044058112, fragmentation=0.0, gc_count=112, gc_time=4.349665880203247, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:52:23,722 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}]
2025-06-10 16:53:59,973 - jedit2.main_window - INFO - AI Response: [{'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'INSERT_ROW_BELOW', 'params': {'row_index': 1}}]
2025-06-10 16:55:54,236 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14922383360, free_memory=1985372160, swap_used=5127254016, swap_free=33058316288, fragmentation=0.0, gc_count=219, gc_time=6.129181861877441, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 16:56:54,010 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'INSERT_ROW_BELOW', 'params': {'row_index': 1}}, {'command': 'APPLY_BOLD_FORMATTING', 'params': {}}]
2025-06-10 16:57:18,553 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15011631104, free_memory=1896124416, swap_used=4916281344, swap_free=33269288960, fragmentation=0.0, gc_count=74, gc_time=1.3923978805541992, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 18:35:37,262 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'APPLY_BOLD_FORMATTING', 'params': {}}, {'command': 'INSERT_ROW_BELOW', 'params': {'row_index': 1}}]
2025-06-10 18:40:40,318 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14727245824, free_memory=2180509696, swap_used=4786237440, swap_free=33399332864, fragmentation=0.0, gc_count=338, gc_time=11.295661926269531, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 18:44:28,853 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}]
2025-06-10 18:44:55,815 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14190505984, free_memory=2717249536, swap_used=4741787648, swap_free=33443782656, fragmentation=0.0, gc_count=35, gc_time=0.7008488178253174, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:00:01,102 - jedit2.main_window - INFO - AI Response: [{'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'C'}}, {'command': 'COPY_SELECTION', 'params': {}}, {'command': 'SELECT_COLUMN', 'params': {'column_index': 'D'}}, {'command': 'PASTE_SELECTION', 'params': {}}, {'command': 'APPLY_BOLD_FORMATTING', 'params': {}}]
2025-06-10 19:02:56,174 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15365857280, free_memory=1541898240, swap_used=5160005632, swap_free=33025564672, fragmentation=0.0, gc_count=6, gc_time=0.21895408630371094, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:15:44,642 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15555543040, free_memory=1352212480, swap_used=5030834176, swap_free=33154736128, fragmentation=0.0, gc_count=60, gc_time=1.4874563217163086, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:18:13,353 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'C', 'target_column': 'A', 'apply_bold': False}}]
2025-06-10 19:20:28,133 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'C', 'target_column': 'B', 'apply_bold': True}}]
2025-06-10 19:25:39,712 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14335651840, free_memory=2572103680, swap_used=4798828544, swap_free=33386741760, fragmentation=0.0, gc_count=112, gc_time=3.727219820022583, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:25:40,376 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14334128128, free_memory=2573627392, swap_used=4798828544, swap_free=33386741760, fragmentation=0.0, gc_count=341, gc_time=12.235377550125122, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:25:43,149 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14243037184, free_memory=2664718336, swap_used=4798828544, swap_free=33386741760, fragmentation=0.0, gc_count=395, gc_time=14.53157377243042, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 19:41:03,609 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=15097819136, free_memory=1809936384, swap_used=5146419199, swap_free=33039151105, fragmentation=0.0, gc_count=18, gc_time=0.5804047584533691, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 20:57:31,968 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'C', 'target_column': 'A', 'apply_bold': False}}]
2025-06-10 20:58:06,569 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=12466659328, free_memory=4441096192, swap_used=1806962688, swap_free=36319424512, fragmentation=0.0, gc_count=0, gc_time=0.0, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 21:01:04,604 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'C', 'target_column': 'B', 'apply_bold': True}}]
2025-06-10 21:02:16,925 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=12655476736, free_memory=4252278784, swap_used=1787789312, swap_free=36338597888, fragmentation=0.0, gc_count=0, gc_time=0.0, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-10 21:11:59,867 - jedit2.main_window - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'A', 'target_column': 'B', 'apply_bold': False}}]
2025-06-10 21:12:21,882 - jedit2.main_window - INFO - AI Response: [{'command': 'APPLY_BOLD_FORMATTING', 'params': {}}, {'command': 'INSERT_ROW_BELOW', 'params': {'row_index': 10}}, {'command': 'INSERT_ROW_BELOW', 'params': {'row_index': 11}}]
2025-06-10 21:12:29,418 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=12725411840, free_memory=4182343680, swap_used=1649922048, swap_free=36476465152, fragmentation=0.0, gc_count=0, gc_time=0.0, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 07:11:16,210 - jedit2.utils.ai_manager_production - WARNING - Attempt 1 failed: No valid JSON extracted
2025-06-11 07:11:16,211 - jedit2.utils.ai_manager_production - INFO - Retry attempt 1 after 4.0s delay
2025-06-11 07:11:20,213 - jedit2.utils.ai_manager_production - WARNING - Attempt 2 failed: No valid JSON extracted
2025-06-11 07:11:20,213 - jedit2.utils.ai_manager_production - INFO - Retry attempt 2 after 8.0s delay
2025-06-11 07:11:28,215 - jedit2.utils.ai_manager_production - WARNING - Attempt 3 failed: No valid JSON extracted
2025-06-11 07:11:31,018 - jedit2.utils.ai_manager_production - ERROR - AI processing failed for query: Open price Jason add a new column after column A and take the information from the new column C and copy it into the new column B
2025-06-11 07:11:44,305 - jedit2.utils.ai_manager_production - WARNING - Attempt 1 failed: No valid JSON extracted
2025-06-11 07:11:44,306 - jedit2.utils.ai_manager_production - INFO - Retry attempt 1 after 4.0s delay
2025-06-11 07:11:48,306 - jedit2.utils.ai_manager_production - WARNING - Attempt 2 failed: No valid JSON extracted
2025-06-11 07:11:48,306 - jedit2.utils.ai_manager_production - INFO - Retry attempt 2 after 8.0s delay
2025-06-11 07:11:56,308 - jedit2.utils.ai_manager_production - WARNING - Attempt 3 failed: No valid JSON extracted
2025-06-11 07:12:11,369 - jedit2.utils.ai_manager_production - ERROR - AI processing failed for query: Open price.json add a new column after column A and take the information from the new column C and copy it into the new column B
2025-06-11 07:12:29,423 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14097960960, free_memory=2809794560, swap_used=2214981632, swap_free=35911405568, fragmentation=0.0, gc_count=94, gc_time=4.083592653274536, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 07:17:36,101 - jedit2.utils.ai_manager_production - INFO - Context-aware preprocessing: 'open price.json add a new column after column a then copy the data from the new column c into the new column b' -> 'open price.json add a new column after column a then copy the data from the new column c into the new column b (Note: Use original column positions before any insertion for source column C)'
2025-06-11 07:17:36,103 - jedit2.utils.ai_manager_production - WARNING - Attempt 1 failed: No valid JSON extracted
2025-06-11 07:17:36,103 - jedit2.utils.ai_manager_production - INFO - Retry attempt 1 after 4.0s delay
2025-06-11 07:17:40,106 - jedit2.utils.ai_manager_production - WARNING - Attempt 2 failed: No valid JSON extracted
2025-06-11 07:17:40,107 - jedit2.utils.ai_manager_production - INFO - Retry attempt 2 after 8.0s delay
2025-06-11 07:17:48,108 - jedit2.utils.ai_manager_production - WARNING - Attempt 3 failed: No valid JSON extracted
2025-06-11 07:19:18,905 - jedit2.utils.ai_manager_production - ERROR - AI processing failed for query: open price.json add a new column after column a then copy the data from the new column c into the new column b
2025-06-11 07:29:33,022 - jedit2.utils.ai_manager_production - WARNING - Attempt 1 failed: No valid JSON extracted
2025-06-11 07:29:33,022 - jedit2.utils.ai_manager_production - INFO - Retry attempt 1 after 4.0s delay
2025-06-11 07:29:37,023 - jedit2.utils.ai_manager_production - WARNING - Attempt 2 failed: No valid JSON extracted
2025-06-11 07:29:37,025 - jedit2.utils.ai_manager_production - INFO - Retry attempt 2 after 8.0s delay
2025-06-11 07:29:45,025 - jedit2.utils.ai_manager_production - WARNING - Attempt 3 failed: No valid JSON extracted
2025-06-11 07:29:47,383 - jedit2.utils.ai_manager_production - ERROR - AI processing failed for query: open price.json
2025-06-11 09:07:03,572 - jedit2.utils.ai_manager_production - WARNING - Attempt 1 failed: No valid JSON extracted
2025-06-11 09:07:03,573 - jedit2.utils.ai_manager_production - INFO - Retry attempt 1 after 4.0s delay
2025-06-11 09:07:07,575 - jedit2.utils.ai_manager_production - WARNING - Attempt 2 failed: No valid JSON extracted
2025-06-11 09:07:07,575 - jedit2.utils.ai_manager_production - INFO - Retry attempt 2 after 8.0s delay
2025-06-11 09:07:15,577 - jedit2.utils.ai_manager_production - WARNING - Attempt 3 failed: No valid JSON extracted
2025-06-11 09:07:17,572 - jedit2.utils.ai_manager_production - ERROR - AI processing failed for query: open price.json
2025-06-11 09:07:25,651 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14158958592, free_memory=2748796928, swap_used=2515156992, swap_free=35485728768, fragmentation=0.0, gc_count=30, gc_time=0.598651647567749, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:18:30,136 - jedit2.utils.ai_manager_production - INFO - Successful response after 0 retries
2025-06-11 09:19:09,704 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=13868904448, free_memory=3038851072, swap_used=2455384064, swap_free=35545501696, fragmentation=0.0, gc_count=45, gc_time=0.9279608726501465, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:31:14,287 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14657318912, free_memory=2250436608, swap_used=2302656512, swap_free=35698229248, fragmentation=0.0, gc_count=59, gc_time=1.2634356021881104, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:34:30,884 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14780145664, free_memory=2127609856, swap_used=2587058176, swap_free=35413827584, fragmentation=0.0, gc_count=16, gc_time=0.41709470748901367, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:36:22,868 - jedit2.utils.ai_manager_production - INFO - Successful response after 0 retries
2025-06-11 09:36:22,934 - jedit2.utils.ai_manager_production - INFO - AI Response: [{'command': 'OPEN_FILE', 'params': {'file_path': 'price.json'}}]
2025-06-11 09:37:03,756 - jedit2.utils.ai_manager_production - INFO - Successful response after 0 retries
2025-06-11 09:37:03,756 - jedit2.utils.ai_manager_production - WARNING - Unknown AI capability: INSERT_COLUMN_RIGHT
2025-06-11 09:37:03,757 - jedit2.utils.ai_manager_production - WARNING - Unknown AI capability: COPY_COLUMN_TO_NEW_COLUMN_AFTER
2025-06-11 09:37:03,757 - jedit2.utils.ai_manager_production - INFO - AI Response: [{'command': 'INSERT_COLUMN_RIGHT', 'params': {'column_index': 'A'}}, {'command': 'COPY_COLUMN_TO_NEW_COLUMN_AFTER', 'params': {'source_column': 'C', 'target_column': 'A', 'apply_bold': True}}]
2025-06-11 09:37:21,650 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14862450688, free_memory=2045304832, swap_used=2438762496, swap_free=35562123264, fragmentation=0.0, gc_count=71, gc_time=1.8233628273010254, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:40:35,352 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14861803520, free_memory=2045952000, swap_used=2438238208, swap_free=35562647552, fragmentation=0.0, gc_count=20, gc_time=0.3870985507965088, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:43:53,950 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14949629952, free_memory=1958125568, swap_used=2676740096, swap_free=35324145664, fragmentation=0.0, gc_count=54, gc_time=1.2310903072357178, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:44:17,432 - jedit2 - INFO - INFO - memory: Application exit. Memory stats: MemoryStats(total_memory=16907755520, used_memory=14959423488, free_memory=1948332032, swap_used=2676740096, swap_free=35324145664, fragmentation=0.0, gc_count=11, gc_time=0.28035545349121094, pool_stats={<MemoryPoolType.SMALL: 'small'>: MemoryPool(type=<MemoryPoolType.SMALL: 'small'>, blocks=[], total_size=1048576, used_size=0, fragmentation=0.0), <MemoryPoolType.MEDIUM: 'medium'>: MemoryPool(type=<MemoryPoolType.MEDIUM: 'medium'>, blocks=[], total_size=10485760, used_size=0, fragmentation=0.0), <MemoryPoolType.LARGE: 'large'>: MemoryPool(type=<MemoryPoolType.LARGE: 'large'>, blocks=[], total_size=104857600, used_size=0, fragmentation=0.0), <MemoryPoolType.HUGE: 'huge'>: MemoryPool(type=<MemoryPoolType.HUGE: 'huge'>, blocks=[], total_size=**********, used_size=0, fragmentation=0.0)})
2025-06-11 09:47:43,934 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Enhanced AI submit called
2025-06-11 09:47:43,935 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Processing query: open price.json
2025-06-11 09:47:44,797 - jedit2.utils.ai_manager_production - INFO - Successful response after 0 retries
2025-06-11 09:47:44,798 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Enhanced AI response: commands=True, raw_length=106
2025-06-11 09:47:44,863 - jedit2.utils.ai_system_integrator.AISystemIntegrator - ERROR - Enhanced AI submit error: 'ProductionMonitor' object has no attribute 'record_query'
2025-06-11 09:47:44,864 - jedit2.utils.ai_system_integrator.AISystemIntegrator - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python\jedit2\jedit2\utils\ai_system_integrator.py", line 360, in enhanced_ai_submit
    self.production_monitor.record_query(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ProductionMonitor' object has no attribute 'record_query'

2025-06-11 09:48:12,181 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Enhanced AI submit called
2025-06-11 09:48:12,181 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Processing query: add a new column after column a then copy the data from the new column c into the new colum b and make that column bold
2025-06-11 09:48:12,182 - jedit2.utils.ai_manager_production - INFO - Context-aware preprocessing: 'add a new column after column a then copy the data from the new column c into the new colum b and make that column bold' -> 'add a new column after column a then copy the data from the new column c into the new colum b and make that column bold (Note: Use original column positions before any insertion for source column C)'
2025-06-11 09:48:13,229 - jedit2.utils.ai_manager_production - INFO - Successful response after 0 retries
2025-06-11 09:48:13,229 - jedit2.utils.test_case_generator - INFO - Generated 3 semantic ambiguity test cases
2025-06-11 09:48:13,230 - jedit2.utils.ai_system_integrator.AISystemIntegrator - INFO - Enhanced AI response: commands=True, raw_length=177
2025-06-11 09:48:16,985 - jedit2.utils.ai_system_integrator.AISystemIntegrator - ERROR - Enhanced AI submit error: 'ProductionMonitor' object has no attribute 'record_query'
2025-06-11 09:48:16,986 - jedit2.utils.ai_system_integrator.AISystemIntegrator - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python\jedit2\jedit2\utils\ai_system_integrator.py", line 360, in enhanced_ai_submit
    self.main_window.ai_debug_display.SetValue(debug_text)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ProductionMonitor' object has no attribute 'record_query'

