# AI Command Mapping Documentation

This document shows how AI capabilities map to implementation methods.

## Working Commands

| Capability | Method | Mapping Type |
|------------|--------|--------------|
| `ADD_COLUMN` | `_ai_add_column` | special_case |
| `ADD_ROW` | `_ai_add_row` | special_case |
| `ALIGN_CENTER` | `_ai_align_center` | special_case |
| `ALIGN_LEFT` | `_ai_align_left` | special_case |
| `ALIGN_RIGHT` | `_ai_align_right` | special_case |
| `APPLY_BOLD_FORMATTING` | `_ai_make_column_bold` | special_case |
| `APPLY_ITALIC_FORMATTING` | `_ai_apply_italic_formatting` | special_case |
| `APPLY_UNDERLINE_FORMATTING` | `_ai_apply_underline_formatting` | special_case |
| `CLEAR_ALL_FILTERS` | `_ai_clear_all_filters` | special_case |
| `CLEAR_FILTER` | `_ai_clear_filter` | special_case |
| `CLEAR_SELECTION` | `_ai_clear_selection` | special_case |
| `CLOSE_TAB` | `_ai_close_tab` | special_case |
| `COPY_SELECTION` | `_ai_copy` | special_case |
| `DELETE_COLUMN` | `_ai_delete_column` | special_case |
| `DELETE_ROW` | `_ai_delete_row` | special_case |
| `FILTER_ABOVE_AVERAGE` | `_ai_filter_above_average` | special_case |
| `FILTER_BELOW_AVERAGE` | `_ai_filter_below_average` | special_case |
| `FILTER_BLANKS` | `_ai_filter_blanks` | special_case |
| `FILTER_BOTTOM_N` | `_ai_filter_bottom_n` | special_case |
| `FILTER_COLUMN` | `_ai_filter_column` | special_case |
| `FILTER_NON_BLANKS` | `_ai_filter_non_blanks` | special_case |
| `FILTER_NUMBER_GREATER_THAN` | `_ai_filter_number_greater_than` | special_case |
| `FILTER_NUMBER_LESS_THAN` | `_ai_filter_number_less_than` | special_case |
| `FILTER_NUMBER_RANGE` | `_ai_filter_number_range` | special_case |
| `FILTER_TEXT_CONTAINS` | `_ai_filter_text_contains` | special_case |
| `FILTER_TEXT_ENDS_WITH` | `_ai_filter_text_ends_with` | special_case |
| `FILTER_TEXT_EQUALS` | `_ai_filter_text_equals` | special_case |
| `FILTER_TEXT_STARTS_WITH` | `_ai_filter_text_starts_with` | special_case |
| `FILTER_TOP_N` | `_ai_filter_top_n` | special_case |
| `FORMAT_AS_COMMA` | `_ai_format_as_comma` | special_case |
| `FORMAT_AS_CURRENCY` | `_ai_format_as_currency` | special_case |
| `FORMAT_AS_PERCENT` | `_ai_format_as_percent` | special_case |
| `FREEZE_PANES` | `_ai_freeze_panes` | special_case |
| `INSERT_COLUMN_LEFT` | `_ai_insert_column_left` | special_case |
| `INSERT_COLUMN_RIGHT` | `_ai_insert_column_right` | special_case |
| `INSERT_ROW_ABOVE` | `_ai_insert_row_above` | special_case |
| `INSERT_ROW_BELOW` | `_ai_insert_row_below` | special_case |
| `NEW_FILE` | `_ai_new_file` | special_case |
| `NEW_SPREADSHEET` | `_ai_new_spreadsheet` | special_case |
| `OPEN_FILE` | `_ai_open_file` | special_case |
| `PASTE_SELECTION` | `_ai_paste` | special_case |
| `REDO_LAST_ACTION` | `_ai_redo` | special_case |
| `SAVE_FILE` | `_ai_save_file` | special_case |
| `SHOW_FILTER_HISTORY` | `_ai_show_filter_history` | special_case |
| `SORT_COLUMN_ASCENDING` | `_ai_sort_column` | special_case |
| `SORT_COLUMN_DESCENDING` | `_ai_sort_column` | special_case |
| `SWITCH_TAB` | `_ai_switch_tab` | special_case |
| `UNDO_LAST_ACTION` | `_ai_undo` | special_case |
| `UNFREEZE_PANES` | `_ai_unfreeze_panes` | special_case |

## Issues Found

- **COPY_COLUMN_TO_NEW_COLUMN_AFTER**: missing_method
- **EXCEL_LIST_WORKSHEETS**: missing_method
- **EXCEL_SWITCH_WORKSHEET**: missing_method
- **EXCEL_SAVE_AS_CSV**: missing_method
- **EXCEL_VALIDATE_FILE**: missing_method

## Orphaned Methods

These methods exist but have no corresponding capability:

- `_ai_about`
- `_ai_advanced_filter`
- `_ai_ai_settings`
- `_ai_apply_bold_formatting`
- `_ai_cache_manager`
- `_ai_copy_cell`
- `_ai_copy_column`
- `_ai_copy_multiple_columns`
- `_ai_copy_multiple_rows`
- `_ai_copy_range`
- `_ai_copy_row`
- `_ai_cut`
- `_ai_dec_decimal`
- `_ai_error_report`
- `_ai_find`
- `_ai_format_validation`
- `_ai_help`
- `_ai_inc_decimal`
- `_ai_index_manager`
- `_ai_lazy_loading`
- `_ai_memory_manager`
- `_ai_paste_cell`
- `_ai_paste_column`
- `_ai_paste_range`
- `_ai_paste_row`
- `_ai_save_as_file`
- `_ai_select_all`
- `_ai_select_column`
- `_ai_select_row`
- `_ai_show_calculator`
- `_ai_switch_view`
- `_ai_tab_close_all`
- `_ai_tab_close_others`
- `_ai_tab_rename`
- `_ai_transpose`
- `_ai_zoom_in`
- `_ai_zoom_out`
