#!/usr/bin/env python3
"""
Improved AI Manager with Enhanced JSON Parsing
Addresses the 3.4% failure rate from JSON parsing edge cases.
"""

import json
import re
from typing import Optional, List, Dict, Any, Tuple


class ImprovedJSONExtractor:
    """Enhanced JSON extraction with multiple fallback strategies."""

    @staticmethod
    def extract_json_robust(text: str) -> Optional[str]:
        """
        Enhanced JSON extraction with multiple parsing strategies.
        Addresses the 3.4% failure rate from JSON formatting edge cases.
        """
        if not text or not text.strip():
            return None

        # Strategy 1: Standard markdown code block parsing
        json_str = ImprovedJSONExtractor._extract_standard_json(text)
        if json_str:
            return json_str

        # Strategy 2: Fuzzy JSON boundary detection
        json_str = ImprovedJSONExtractor._extract_fuzzy_json(text)
        if json_str:
            return json_str

        # Strategy 3: Pattern-based extraction
        json_str = ImprovedJSONExtractor._extract_pattern_json(text)
        if json_str:
            return json_str

        # Strategy 4: Content cleaning and retry
        json_str = ImprovedJSONExtractor._extract_cleaned_json(text)
        if json_str:
            return json_str

        return None

    @staticmethod
    def _extract_standard_json(text: str) -> Optional[str]:
        """Standard extraction method (current implementation)."""
        try:
            # Strip markdown fences
            cleaned = text.strip()
            if cleaned.startswith("```json"):
                cleaned = cleaned[7:]
            if cleaned.startswith("```"):
                cleaned = cleaned[3:]
            if cleaned.endswith("```"):
                cleaned = cleaned[:-3]

            # Find JSON boundaries
            start_bracket = cleaned.find("[")
            start_brace = cleaned.find("{")

            if start_bracket != -1 and (
                start_bracket < start_brace or start_brace == -1
            ):
                start = start_bracket
                end_char = "]"
            elif start_brace != -1:
                start = start_brace
                end_char = "}"
            else:
                return None

            end = cleaned.rfind(end_char)

            if start != -1 and end != -1 and end > start:
                json_str = cleaned[start : end + 1]
                json.loads(json_str)  # Validate
                return json_str

        except (json.JSONDecodeError, IndexError, ValueError):
            pass

        return None

    @staticmethod
    def _extract_fuzzy_json(text: str) -> Optional[str]:
        """Fuzzy JSON extraction with bracket matching."""
        try:
            # Remove common prefixes/suffixes that might interfere
            cleaned = re.sub(r"^[^{\[]*", "", text)  # Remove non-JSON prefix
            cleaned = re.sub(r"[^}\]]*$", "", cleaned)  # Remove non-JSON suffix

            # Try to find balanced brackets/braces
            for start_char, end_char in [("[", "]"), ("{", "}")]:
                start_idx = cleaned.find(start_char)
                if start_idx == -1:
                    continue

                # Find matching closing bracket/brace
                count = 0
                end_idx = -1
                for i, char in enumerate(cleaned[start_idx:], start_idx):
                    if char == start_char:
                        count += 1
                    elif char == end_char:
                        count -= 1
                        if count == 0:
                            end_idx = i
                            break

                if end_idx != -1:
                    json_str = cleaned[start_idx : end_idx + 1]
                    json.loads(json_str)  # Validate
                    return json_str

        except (json.JSONDecodeError, IndexError, ValueError):
            pass

        return None

    @staticmethod
    def _extract_pattern_json(text: str) -> Optional[str]:
        """Pattern-based JSON extraction using regex."""
        try:
            # Look for array patterns
            array_pattern = r"\[\s*\{.*?\}\s*(?:\s*,\s*\{.*?\}\s*)*\]"
            matches = re.findall(array_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    json.loads(match)
                    return match
                except json.JSONDecodeError:
                    continue

            # Look for single object patterns
            object_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(object_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    json.loads(match)
                    return match
                except json.JSONDecodeError:
                    continue

        except Exception:
            pass

        return None

    @staticmethod
    def _extract_cleaned_json(text: str) -> Optional[str]:
        """Clean text and attempt extraction."""
        try:
            # Remove common Gemini response artifacts
            cleaned = text

            # Remove explanatory text patterns
            patterns_to_remove = [
                r"Here\'s the JSON.*?:",
                r"I\'ll help you.*?:",
                r"Based on your request.*?:",
                r"The commands are:",
                r"Here are the commands:",
            ]

            for pattern in patterns_to_remove:
                cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

            # Try standard extraction on cleaned text
            return ImprovedJSONExtractor._extract_standard_json(cleaned)

        except Exception:
            pass

        return None


class ImprovedAIManager:
    """AI Manager with enhanced JSON parsing capabilities."""

    def __init__(self, ai_manager_instance):
        """Initialize with existing AI manager instance."""
        self.ai_manager = ai_manager_instance

    def get_ai_response_robust(
        self, query: str
    ) -> Tuple[Optional[List[Dict[str, Any]]], str]:
        """
        Enhanced version of get_ai_response with robust JSON parsing.

        Args:
            query: The natural language query from the user.

        Returns:
            A tuple containing:
            - A list of command dictionaries, or None if an error occurs.
            - The raw text response from the AI.
        """
        if not self.ai_manager._model:
            raw_response = "AI model not configured."
            return None, raw_response

        # Get the raw response using existing infrastructure
        try:
            from jedit2.utils.ai_manager import get_system_prompt

            system_prompt = get_system_prompt()
            full_prompt = f'{system_prompt}\n\nUser: "{query}"'
            response = self.ai_manager._model.generate_content(full_prompt)
            raw_response = response.text
        except Exception as e:
            raw_response = f"Error generating AI response: {str(e)}"
            return None, raw_response

        # Use enhanced JSON extraction
        json_str = ImprovedJSONExtractor.extract_json_robust(raw_response)

        if not json_str:
            # For debugging: log what we received
            if len(raw_response) < 200:
                print(f"DEBUG - No JSON found in response: {raw_response}")
            else:
                print(f"DEBUG - No JSON found in response (first 200 chars): {raw_response[:200]}...")
            return None, raw_response

        try:
            # Parse the JSON
            parsed_json = json.loads(json_str)

            # Ensure it's a list
            if isinstance(parsed_json, dict):
                parsed_json = [parsed_json]
            elif not isinstance(parsed_json, list):
                return None, raw_response

            return parsed_json, raw_response

        except json.JSONDecodeError as e:
            print(f"Final JSON parsing failed: {str(e)}")
            return None, raw_response


def create_robust_ai_manager_wrapper(original_ai_manager):
    """
    Create a wrapper that provides robust JSON parsing for existing AI manager.

    Usage:
        ai_manager = AIManager(api_key=api_key)
        robust_ai_manager = create_robust_ai_manager_wrapper(ai_manager)
        response, raw = robust_ai_manager.get_ai_response_robust(query)
    """
    return ImprovedAIManager(original_ai_manager)


# Monkey patch method for easy integration
def patch_ai_manager_with_robust_parsing():
    """
    Monkey patch the existing AIManager to use robust JSON parsing.
    Call this once at application startup.
    """
    from jedit2.utils.ai_manager import AIManager

    def get_ai_response_robust(
        self, query: str
    ) -> Tuple[Optional[List[Dict[str, Any]]], str]:
        """Enhanced get_ai_response with robust JSON parsing."""
        wrapper = ImprovedAIManager(self)
        return wrapper.get_ai_response_robust(query)

    # Add the robust method to the existing class
    AIManager.get_ai_response_robust = get_ai_response_robust

    print("✅ AI Manager patched with robust JSON parsing")


if __name__ == "__main__":
    # Test the improved JSON extraction
    test_cases = [
        # Standard case
        '```json\n[{"command": "TEST", "params": {}}]\n```',
        # Missing markdown
        '[{"command": "TEST", "params": {}}]',
        # Extra text
        'Here are the commands: [{"command": "TEST", "params": {}}] Hope!',
        # Malformed boundaries
        'json\n[{"command": "TEST", "params": {}}',
        # Complex case with explanations
        '''I'll help you with that task:

        ```json
        [
          {
            "command": "OPEN_FILE",
            "params": {"file_path": "data.csv"}
          },
          {
            "command": "INSERT_COLUMN_RIGHT",
            "params": {"column_index": "A"}
          }
        ]
        ```

        These commands will handle your request.''',
    ]

    print("Testing Improved JSON Extractor:")
    for i, test_case in enumerate(test_cases, 1):
        result = ImprovedJSONExtractor.extract_json_robust(test_case)
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"Test {i}: {status}")
        if result:
            print(f"  Extracted: {result[:50]}...")
        print() 