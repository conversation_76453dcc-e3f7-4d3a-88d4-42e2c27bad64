<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M1.5,7.157V1.6h2V7.027C3.335,7.016,3.174,7,3,7A7.034,7.034,0,0,0,1.5,7.157Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.389,12.108l-1.931.518L9.611,2l1.931-.518Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M1,7.3V1.6l.5-.5h2l.5.5V7.07A7.534,7.534,0,0,0,3,7V2.1H2V7.07A5.759,5.759,0,0,0,1,7.3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.025,1.354,11.412,1,9.48,1.518l-.353.613,2.848,10.625.613.353,1.932-.517.353-.613Zm.787,10.66-2.589-9.66.965-.258,2.589,9.659Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,9.5v5c0,.552-1.119,1-2.5,1s-2.5-.448-2.5-1v-5c0-.552,1.119-1,2.5-1S5.5,8.948,5.5,9.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.5,1.6v11H7V9.5A2.253,2.253,0,0,0,5.5,7.489h0V1.6Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,1.6v11l-.5.5H7V2.1H6V7.769a3.47,3.47,0,0,0-.438-.249L5.524,7.5A4.362,4.362,0,0,0,5,7.3H5V1.6l.5-.5h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3,8C1.555,8,0,8.47,0,9.5v5C0,15.53,1.555,16,3,16s3-.47,3-1.5v-5C6,8.47,4.445,8,3,8ZM3,9a3.052,3.052,0,0,1,1.987.5A3.052,3.052,0,0,1,3,10a3.35,3.35,0,0,1-2-.47V9.522A2.862,2.862,0,0,1,3,9Zm0,6a3.024,3.024,0,0,1-2-.5V10.651A5.41,5.41,0,0,0,3,11a5.419,5.419,0,0,0,2-.349l0,3.823A2.836,2.836,0,0,1,3,15Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
