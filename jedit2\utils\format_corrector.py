"""Format correction for different file types in JEdit2.

This module provides automatic correction capabilities for various file formats.
"""

import json
import yaml
import csv
import io
import re
import ast
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class CorrectionResult:
    """Result of format correction."""
    success: bool
    corrected_content: str
    changes_made: List[str]
    errors: List[str]
    warnings: List[str]


class FormatCorrector:
    """Corrector for different file formats."""
    
    def correct_json(self, content: str) -> CorrectionResult:
        """Correct JSON format issues.
        
        Args:
            content: JSON content to correct
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content
        
        try:
            # First, try to parse as-is to see if it's already valid
            try:
                json.loads(content)
                return CorrectionResult(True, content, [], [], ["JSON is already valid"])
            except json.JSONDecodeError:
                pass  # Continue with corrections
            
            # Fix 1: Remove trailing commas
            if re.search(r',\s*[}\]]', content):
                corrected_content = re.sub(r',(\s*[}\]])', r'\1', corrected_content)
                changes_made.append("Removed trailing commas")
            
            # Fix 2: Convert single quotes to double quotes (basic)
            # This is a simplified approach - a full implementation would need proper parsing
            if "'" in corrected_content and '"' not in corrected_content:
                corrected_content = corrected_content.replace("'", '"')
                changes_made.append("Converted single quotes to double quotes")
            
            # Fix 3: Add missing closing brackets/braces
            open_braces = corrected_content.count('{')
            close_braces = corrected_content.count('}')
            open_brackets = corrected_content.count('[')
            close_brackets = corrected_content.count(']')
            
            if open_braces > close_braces:
                corrected_content += '}' * (open_braces - close_braces)
                changes_made.append(f"Added {open_braces - close_braces} missing closing braces")
            
            if open_brackets > close_brackets:
                corrected_content += ']' * (open_brackets - close_brackets)
                changes_made.append(f"Added {open_brackets - close_brackets} missing closing brackets")
            
            # Fix 4: Basic indentation (if parseable)
            try:
                parsed = json.loads(corrected_content)
                formatted_content = json.dumps(parsed, indent=2, ensure_ascii=False)
                if formatted_content != corrected_content:
                    corrected_content = formatted_content
                    changes_made.append("Fixed indentation and formatting")
            except json.JSONDecodeError as e:
                errors.append(f"Could not fully correct JSON: {e.msg}")
                return CorrectionResult(False, content, changes_made, errors, warnings)
            
            return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            
        except Exception as e:
            errors.append(f"JSON correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_yaml(self, content: str) -> CorrectionResult:
        """Correct YAML format issues.
        
        Args:
            content: YAML content to correct
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content
        
        try:
            # First, try to parse as-is
            try:
                yaml.safe_load(content)
                return CorrectionResult(True, content, [], [], ["YAML is already valid"])
            except yaml.YAMLError:
                pass  # Continue with corrections
            
            lines = corrected_content.split('\n')
            corrected_lines = []
            
            # Fix 1: Convert tabs to spaces
            tab_fixes = 0
            for line in lines:
                original_line = line
                if '\t' in line:
                    tab_fixes += 1
                    line = line.replace('\t', '  ')  # Convert tabs to 2 spaces
                corrected_lines.append(line)

            if tab_fixes > 0:
                changes_made.append(f"Converted {tab_fixes} tabs to spaces")

            corrected_content = '\n'.join(corrected_lines)
            
            # Fix 2: Basic indentation consistency
            # This is a simplified approach - proper YAML indentation is complex
            indented_lines = []
            for line in corrected_lines:
                if line.strip() and not line.startswith(' ') and ':' in line and not line.strip().startswith('#'):
                    # This might be a top-level key that should not be indented
                    pass
                indented_lines.append(line)
            
            corrected_content = '\n'.join(indented_lines)
            
            # Try to parse the corrected content
            try:
                yaml.safe_load(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except yaml.YAMLError as e:
                errors.append(f"Could not fully correct YAML: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)
            
        except Exception as e:
            errors.append(f"YAML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_csv(self, content: str) -> CorrectionResult:
        """Correct CSV format issues.
        
        Args:
            content: CSV content to correct
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        
        try:
            # Parse CSV to detect issues
            reader = csv.reader(io.StringIO(content))
            rows = list(reader)
            
            if not rows:
                return CorrectionResult(True, content, [], [], ["CSV is empty"])
            
            # Fix 1: Standardize column counts
            max_cols = max(len(row) for row in rows) if rows else 0
            corrected_rows = []
            padding_added = 0
            
            for row in rows:
                if len(row) < max_cols:
                    # Pad with empty strings
                    padded_row = row + [''] * (max_cols - len(row))
                    corrected_rows.append(padded_row)
                    padding_added += max_cols - len(row)
                else:
                    corrected_rows.append(row)
            
            if padding_added > 0:
                changes_made.append(f"Added {padding_added} empty cells to standardize column counts")
            
            # Fix 2: Proper CSV formatting
            output = io.StringIO()
            writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)
            for row in corrected_rows:
                writer.writerow(row)
            
            corrected_content = output.getvalue().rstrip('\n')  # Remove trailing newline
            
            if corrected_content != content:
                if not changes_made:  # If no padding was added, formatting was changed
                    changes_made.append("Standardized CSV formatting and quoting")
            
            return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            
        except Exception as e:
            errors.append(f"CSV correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_python(self, content: str) -> CorrectionResult:
        """Correct Python syntax issues.
        
        Args:
            content: Python content to correct
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content
        
        try:
            # First, try to parse as-is
            try:
                ast.parse(content)
                return CorrectionResult(True, content, [], [], ["Python syntax is already valid"])
            except SyntaxError:
                pass  # Continue with corrections
            
            lines = corrected_content.split('\n')
            corrected_lines = []
            
            # Fix 1: Convert tabs to spaces
            tab_fixes = 0
            for line in lines:
                if '\t' in line:
                    tab_fixes += 1
                    # Convert tabs to 4 spaces (PEP 8 standard)
                    line = line.replace('\t', '    ')
                corrected_lines.append(line)
            
            if tab_fixes > 0:
                changes_made.append(f"Converted {tab_fixes} tabs to spaces")
                corrected_content = '\n'.join(corrected_lines)
            
            # Fix 2: Basic line length (very conservative approach)
            long_line_fixes = 0
            final_lines = []
            for line in corrected_lines:
                if len(line) > 88 and '#' not in line:  # Don't break comment lines
                    # Very basic line breaking for simple cases
                    if ' and ' in line:
                        parts = line.split(' and ')
                        if len(parts) == 2:
                            indent = len(line) - len(line.lstrip())
                            final_lines.append(parts[0] + ' and \\')
                            final_lines.append(' ' * (indent + 4) + parts[1])
                            long_line_fixes += 1
                            continue
                final_lines.append(line)
            
            if long_line_fixes > 0:
                changes_made.append(f"Wrapped {long_line_fixes} long lines")
                corrected_content = '\n'.join(final_lines)
            
            # Try to parse the corrected content
            try:
                ast.parse(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except SyntaxError as e:
                errors.append(f"Could not fully correct Python syntax: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)
            
        except Exception as e:
            errors.append(f"Python correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_xml(self, content: str) -> CorrectionResult:
        """Correct XML format issues.

        Args:
            content: XML content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content

        try:
            import xml.etree.ElementTree as ET

            # First, try to parse as-is
            try:
                ET.fromstring(content)
                return CorrectionResult(True, content, [], [], ["XML is already valid"])
            except ET.ParseError:
                pass  # Continue with corrections

            # Fix 1: Add XML declaration if missing
            if not corrected_content.strip().startswith('<?xml'):
                corrected_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + corrected_content
                changes_made.append("Added XML declaration")

            # Fix 2: Basic tag balancing (very simplified)
            # This is a basic approach - full XML correction is complex
            lines = corrected_content.split('\n')
            corrected_lines = []

            for line in lines:
                # Fix basic attribute quoting
                if '=' in line and '"' not in line and "'" not in line:
                    # Very basic attribute fixing
                    line = re.sub(r'=([^>\s]+)', r'="\1"', line)
                    if line != corrected_content.split('\n')[corrected_lines.__len__()]:
                        changes_made.append("Fixed attribute quoting")

                corrected_lines.append(line)

            corrected_content = '\n'.join(corrected_lines)

            # Try to parse the corrected content
            try:
                ET.fromstring(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except ET.ParseError as e:
                errors.append(f"Could not fully correct XML: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"XML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_markdown(self, content: str) -> CorrectionResult:
        """Correct Markdown format issues.

        Args:
            content: Markdown content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        lines = content.split('\n')
        corrected_lines = []

        # Fix 1: Header spacing
        header_fixes = 0
        for line in lines:
            if line.strip().startswith('#') and not re.match(r'^#+\s+', line.strip()):
                # Add space after # symbols
                header_level = len(line.strip()) - len(line.strip().lstrip('#'))
                if header_level <= 6:
                    indent = len(line) - len(line.lstrip())
                    header_text = line.strip()[header_level:].strip()
                    corrected_line = ' ' * indent + '#' * header_level + ' ' + header_text
                    corrected_lines.append(corrected_line)
                    header_fixes += 1
                    continue
            corrected_lines.append(line)

        if header_fixes > 0:
            changes_made.append(f"Fixed {header_fixes} header spacing issues")

        # Fix 2: Remove trailing whitespace
        final_lines = []
        trailing_fixes = 0
        for line in corrected_lines:
            if line.rstrip() != line and line.strip():  # Has trailing whitespace and isn't empty
                final_lines.append(line.rstrip())
                trailing_fixes += 1
            else:
                final_lines.append(line)

        if trailing_fixes > 0:
            changes_made.append(f"Removed trailing whitespace from {trailing_fixes} lines")

        # Fix 3: Balance code block markers
        corrected_content = '\n'.join(final_lines)
        code_block_count = corrected_content.count('```')
        if code_block_count % 2 != 0:
            corrected_content += '\n```'
            changes_made.append("Added missing closing code block marker")

        # Fix 4: Fix list item spacing
        list_lines = []
        list_fixes = 0
        for line in final_lines:
            if re.match(r'^[\s]*[-*+](?!\s)', line):
                # Add space after list marker
                marker_match = re.match(r'^(\s*)([-*+])(.*)', line)
                if marker_match:
                    indent, marker, rest = marker_match.groups()
                    corrected_line = indent + marker + ' ' + rest.lstrip()
                    list_lines.append(corrected_line)
                    list_fixes += 1
                    continue
            list_lines.append(line)

        if list_fixes > 0:
            changes_made.append(f"Fixed {list_fixes} list item spacing issues")
            corrected_content = '\n'.join(list_lines)

        return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

    def correct_format(self, content: str, file_type: str) -> CorrectionResult:
        """Correct content based on file type.
        
        Args:
            content: Content to correct
            file_type: Type of file (json, yaml, csv, python, etc.)
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        if file_type.lower() == 'json':
            return self.correct_json(content)
        elif file_type.lower() in ['yaml', 'yml']:
            return self.correct_yaml(content)
        elif file_type.lower() == 'csv':
            return self.correct_csv(content)
        elif file_type.lower() in ['python', 'py']:
            return self.correct_python(content)
        elif file_type.lower() == 'xml':
            return self.correct_xml(content)
        elif file_type.lower() in ['markdown', 'md']:
            return self.correct_markdown(content)
        else:
            return CorrectionResult(
                False,
                content,
                [],
                [f"No correction available for {file_type} files"],
                []
            )
