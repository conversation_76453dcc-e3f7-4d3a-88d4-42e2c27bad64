"""Format correction for different file types in JEdit2.

This module provides automatic correction capabilities for various file formats.
"""

import json
import yaml
import csv
import io
import re
import ast
import subprocess
import tempfile
import os
import shutil
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class CorrectionResult:
    """Result of format correction."""
    success: bool
    corrected_content: str
    changes_made: List[str]
    errors: List[str]
    warnings: List[str]


class FormatCorrector:
    """Corrector for different file formats using professional tools."""

    def _run_external_tool(self, command: List[str], input_content: str, cwd: str = None) -> Tuple[bool, str, str]:
        """Run an external tool with input content.

        Args:
            command: Command to run as list of strings
            input_content: Content to pass to stdin
            cwd: Working directory

        Returns:
            Tuple of (success, stdout, stderr)
        """
        try:
            result = subprocess.run(
                command,
                input=input_content,
                text=True,
                capture_output=True,
                cwd=cwd,
                timeout=30  # 30 second timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Tool execution timed out"
        except FileNotFoundError:
            return False, "", f"Tool not found: {command[0]}"
        except Exception as e:
            return False, "", f"Tool execution error: {str(e)}"

    def _check_tool_available(self, tool_name: str) -> bool:
        """Check if a tool is available in PATH."""
        return shutil.which(tool_name) is not None

    def correct_json(self, content: str) -> CorrectionResult:
        """Correct JSON format issues using professional tools.

        Args:
            content: JSON content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Handle empty or whitespace-only content
            if not content.strip():
                # Return empty JSON object for empty content
                formatted = json.dumps({}, indent=2, ensure_ascii=False)
                changes_made.append("Created empty JSON object from empty content")
                return CorrectionResult(True, formatted, changes_made, [], ["Created valid JSON from empty content"])

            # First, try to parse as-is to see if it's already valid
            try:
                json.loads(content)
                return CorrectionResult(True, content, [], [], ["JSON is already valid"])
            except json.JSONDecodeError:
                pass  # Continue with corrections

            # Method 1: Use python -m json.tool for formatting
            if self._check_tool_available('python'):
                success, stdout, stderr = self._run_external_tool(
                    ['python', '-m', 'json.tool'],
                    content
                )
                if success and stdout.strip():
                    changes_made.append("Formatted using python json.tool")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"json.tool warning: {stderr}")

            # Method 2: Use jq if available (better error handling and formatting)
            if self._check_tool_available('jq'):
                success, stdout, stderr = self._run_external_tool(['jq', '.'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using jq")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"jq warning: {stderr}")

            # Method 3: Use jsonschema validation if available
            try:
                import jsonschema
                # Try basic parsing with better error messages
                parsed = json.loads(content)
                formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                changes_made.append("Formatted using built-in JSON parser")
                return CorrectionResult(True, formatted, changes_made, errors, warnings)
            except ImportError:
                pass
            except json.JSONDecodeError as e:
                errors.append(f"JSON parsing error: {e.msg} at line {e.lineno}, column {e.colno}")

            # Fallback: Targeted correction attempts
            corrected_content = content

            # Fix 0: Missing commas between properties (do this FIRST before other fixes)
            # This is a very common issue that should be fixed early
            missing_comma_pattern = r'("(?:[^"\\]|\\.)*"|true|false|null|\d+(?:\.\d+)?)\s+("(?:[^"\\]|\\.)*"\s*:)'
            if re.search(missing_comma_pattern, corrected_content):
                corrected_content = re.sub(missing_comma_pattern, r'\1, \2', corrected_content)
                changes_made.append("Added missing commas between JSON properties")

                # Try parsing immediately after comma fix
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after comma correction")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass  # Continue with other fixes

            # Fix 1: Handle missing opening/closing braces (individual cases)
            stripped = corrected_content.strip()

            # Missing opening brace: "key": "value"}
            if stripped.endswith('}') and not stripped.startswith('{'):
                try:
                    wrapped_content = '{' + stripped
                    parsed = json.loads(wrapped_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Added missing opening brace")
                    changes_made.append("Formatted JSON object")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Missing closing brace: {"key": "value" or nested cases (CRITICAL FIX)
            if stripped.startswith('{') and not stripped.endswith('}'):
                # Advanced nested object fragment handling
                open_braces = stripped.count('{')
                close_braces = stripped.count('}')
                missing_braces = open_braces - close_braces

                if missing_braces > 0:
                    # Try simple brace addition first
                    wrapped_content = stripped + '}' * missing_braces
                    try:
                        parsed = json.loads(wrapped_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append(f"Added {missing_braces} missing closing brace(s)")
                        changes_made.append("Formatted JSON object")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            # Handle nested object fragments that don't start with brace
            # Pattern: "outer": {"inner": "value" (missing closing braces and wrapper)
            if ':' in stripped and '{' in stripped and not stripped.startswith('{'):
                # This looks like a nested object fragment
                # Try wrapping the entire thing in braces
                wrapped_content = '{' + stripped

                # Count braces to see how many closing braces we need
                open_braces = wrapped_content.count('{')
                close_braces = wrapped_content.count('}')
                missing_braces = open_braces - close_braces

                if missing_braces > 0:
                    final_content = wrapped_content + '}' * missing_braces
                    try:
                        parsed = json.loads(final_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Added missing opening brace and closing braces for nested object")
                        changes_made.append("Formatted JSON object")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            # Fix 2: Check for missing object braces (common when copying JSON fragments)
            # Look for content that starts with a property but no opening brace
            if (stripped.startswith('"') and
                ':' in corrected_content and
                not stripped.startswith('{') and
                not stripped.startswith('[')):

                # First try to fix trailing commas in the fragment, then wrap
                fragment_content = corrected_content

                # Remove trailing commas from the fragment
                if re.search(r',\s*$', fragment_content.strip()):
                    fragment_content = re.sub(r',\s*$', '', fragment_content.strip())
                    changes_made.append("Removed trailing comma from fragment")

                # Try wrapping in braces
                wrapped_content = '{' + fragment_content + '}'
                try:
                    parsed = json.loads(wrapped_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Added missing object braces")
                    changes_made.append("Formatted JSON object")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    # If wrapping doesn't work, continue with other fixes
                    pass

            # Fix 1: Remove trailing commas FIRST (most common and easiest to fix)
            if re.search(r',\s*[}\]]', corrected_content):
                corrected_content = re.sub(r',(\s*[}\]])', r'\1', corrected_content)
                changes_made.append("Removed trailing commas")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after trailing comma removal")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 2: COMPREHENSIVE MULTIPLE ERRORS FIX (CRITICAL)
            # This handles the complex case: {key1: "value1", "key2": value2,}
            # Multiple issues: unquoted key, unquoted value, trailing comma

            # Apply ALL common fixes in sequence for maximum coverage
            multi_fix_content = corrected_content
            multi_fixes = []

            # Step 1: Convert single quotes to double quotes
            if "'" in multi_fix_content:
                multi_fix_content = multi_fix_content.replace("'", '"')
                multi_fixes.append("Converted single quotes to double quotes")

            # Step 2: Fix Python boolean/null values
            python_replacements = [
                (r'\bTrue\b', 'true'),
                (r'\bFalse\b', 'false'),
                (r'\bNone\b', 'null')
            ]
            for pattern, replacement in python_replacements:
                if re.search(pattern, multi_fix_content):
                    multi_fix_content = re.sub(pattern, replacement, multi_fix_content)
                    if "Python values" not in str(multi_fixes):
                        multi_fixes.append("Converted Python values to JSON format")

            # Step 3: Fix unquoted keys (CRITICAL for multiple errors case)
            unquoted_key_pattern = r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:'
            if re.search(unquoted_key_pattern, multi_fix_content):
                multi_fix_content = re.sub(unquoted_key_pattern, r'\1"\2":', multi_fix_content)
                multi_fixes.append("Added quotes around unquoted property names")

            # Step 4: Fix unquoted string values
            unquoted_value_pattern = r'("(?:[^"\\]|\\.)*"\s*:\s*)([a-zA-Z_][a-zA-Z0-9_]*)\b(?=\s*[,}])'
            if re.search(unquoted_value_pattern, multi_fix_content):
                def quote_values(match):
                    key_part = match.group(1)
                    value = match.group(2)
                    if value.lower() in ['true', 'false', 'null']:
                        return match.group(0)
                    return key_part + '"' + value + '"'

                multi_fix_content = re.sub(unquoted_value_pattern, quote_values, multi_fix_content)
                multi_fixes.append("Added quotes around unquoted string values")

            # Step 5: Remove trailing commas
            if re.search(r',\s*[}\]]', multi_fix_content):
                multi_fix_content = re.sub(r',(\s*[}\]])', r'\1', multi_fix_content)
                multi_fixes.append("Removed trailing commas")

            # Step 6: Add missing commas
            missing_comma_pattern = r'("(?:[^"\\]|\\.)*"|true|false|null|\d+(?:\.\d+)?)\s+("(?:[^"\\]|\\.)*"\s*:)'
            if re.search(missing_comma_pattern, multi_fix_content):
                multi_fix_content = re.sub(missing_comma_pattern, r'\1, \2', multi_fix_content)
                multi_fixes.append("Added missing commas between properties")

            # Try parsing the multi-fixed content
            if multi_fixes:  # Only if we made changes
                try:
                    parsed = json.loads(multi_fix_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    multi_fixes.append("Formatted after comprehensive fixes")
                    changes_made.extend(multi_fixes)
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    # Update corrected_content for next attempts
                    corrected_content = multi_fix_content
                    changes_made.extend(multi_fixes[:-1])  # Don't add "Formatted" if it failed

            # Fix 3: Convert Python boolean/null values to JSON equivalents
            python_to_json_replacements = [
                (r'\bTrue\b', 'true'),
                (r'\bFalse\b', 'false'),
                (r'\bNone\b', 'null')
            ]

            python_fixes = 0
            for pattern, replacement in python_to_json_replacements:
                if re.search(pattern, corrected_content):
                    corrected_content = re.sub(pattern, replacement, corrected_content)
                    python_fixes += 1

            if python_fixes > 0:
                changes_made.append(f"Converted {python_fixes} Python values to JSON format")
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after Python value conversion")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 4: Add quotes around unquoted property names
            # Pattern: {name: "value" -> {"name": "value"
            unquoted_pattern = r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:'
            if re.search(unquoted_pattern, corrected_content):
                corrected_content = re.sub(unquoted_pattern, r'\1"\2":', corrected_content)
                changes_made.append("Added quotes around unquoted property names")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after adding quotes")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 3: Remove JSON comments (comprehensive approach)
            if '//' in corrected_content or '/*' in corrected_content:
                original_content = corrected_content

                # Remove single-line comments (but be careful about URLs)
                lines = corrected_content.split('\n')
                cleaned_lines = []
                for line in lines:
                    if '//' in line:
                        # Check if // is inside a string
                        in_string = False
                        escaped = False
                        comment_pos = -1

                        for i, char in enumerate(line):
                            if escaped:
                                escaped = False
                                continue
                            if char == '\\':
                                escaped = True
                                continue
                            if char == '"' and not escaped:
                                in_string = not in_string
                            elif char == '/' and i < len(line) - 1 and line[i+1] == '/' and not in_string:
                                comment_pos = i
                                break

                        if comment_pos >= 0:
                            # Remove comment, but handle trailing commas
                            line_before_comment = line[:comment_pos].rstrip()
                            if line_before_comment.endswith(','):
                                # Check if this comma is needed
                                cleaned_lines.append(line_before_comment)
                            else:
                                cleaned_lines.append(line_before_comment)
                        else:
                            cleaned_lines.append(line)
                    else:
                        cleaned_lines.append(line)

                corrected_content = '\n'.join(cleaned_lines)

                # Remove multi-line comments
                corrected_content = re.sub(r'/\*.*?\*/', '', corrected_content, flags=re.DOTALL)

                # Clean up any resulting formatting issues
                corrected_content = re.sub(r',\s*\n\s*}', '\n}', corrected_content)  # Remove trailing commas before }
                corrected_content = re.sub(r',\s*\n\s*]', '\n]', corrected_content)  # Remove trailing commas before ]

                if corrected_content != original_content:
                    changes_made.append("Removed JSON comments and cleaned formatting")

                    # Try parsing again
                    try:
                        parsed = json.loads(corrected_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Formatted after removing comments")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            # Fix 4: Escape unescaped quotes in string values (improved approach)
            # Pattern: "He said "hello"" -> "He said \"hello\""
            if '"' in corrected_content:
                # More sophisticated quote fixing
                lines = corrected_content.split('\n')
                quote_fixed_lines = []
                quote_fixes = 0

                for line in lines:
                    if line.count('"') > 2:  # More than just key-value quotes
                        # Look for pattern: "text"more text"text"
                        # This is a common issue with embedded quotes
                        fixed_line = line

                        # Pattern 1: "value "embedded" more"
                        pattern1 = r'"([^"]*)"([^"]*)"([^"]*)"(\s*[,}])'
                        if re.search(pattern1, line):
                            fixed_line = re.sub(pattern1, r'"\1\"\2\"\3"\4', line)
                            quote_fixes += 1

                        # Pattern 2: Simple case "text"text"
                        elif re.search(r'"[^"]*"[^",}\]]*"[^"]*"', line):
                            # Replace internal quotes with escaped quotes
                            # Find the string value part
                            match = re.search(r':\s*"([^"]*"[^"]*"[^"]*)"', line)
                            if match:
                                original_value = match.group(1)
                                escaped_value = original_value.replace('"', '\\"')
                                fixed_line = line.replace(f'"{original_value}"', f'"{escaped_value}"')
                                quote_fixes += 1

                        quote_fixed_lines.append(fixed_line)
                    else:
                        quote_fixed_lines.append(line)

                if quote_fixes > 0:
                    corrected_content = '\n'.join(quote_fixed_lines)
                    changes_made.append(f"Escaped {quote_fixes} unescaped quotes in string values")

                    # Try parsing again
                    try:
                        parsed = json.loads(corrected_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Formatted after escaping quotes")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass



            # Fix 6: Array without brackets (improved detection)
            # Pattern: "item1", "item2", "item3" (no surrounding structure)
            stripped_content = corrected_content.strip()
            if (',' in stripped_content and
                not stripped_content.startswith('[') and
                not stripped_content.startswith('{') and
                not ':' in stripped_content):  # No colons means it's not an object

                # This looks like a comma-separated list that should be an array
                try:
                    # Try wrapping in brackets first
                    wrapped_array = '[' + stripped_content + ']'
                    parsed = json.loads(wrapped_array)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Added missing array brackets")
                    changes_made.append("Formatted JSON array")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    # If that fails, try to fix common issues first
                    # Remove trailing commas if present
                    if stripped_content.endswith(','):
                        cleaned_content = stripped_content.rstrip(',').strip()
                        try:
                            wrapped_array = '[' + cleaned_content + ']'
                            parsed = json.loads(wrapped_array)
                            formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                            changes_made.append("Removed trailing comma and added missing array brackets")
                            changes_made.append("Formatted JSON array")
                            return CorrectionResult(True, formatted, changes_made, errors, warnings)
                        except json.JSONDecodeError:
                            pass

                    # Try fixing unquoted values in the array
                    # Pattern: item1, item2 -> "item1", "item2"
                    items = [item.strip() for item in stripped_content.split(',')]
                    fixed_items = []
                    for item in items:
                        if item and not (item.startswith('"') or item.isdigit() or item in ['true', 'false', 'null']):
                            # Unquoted string, add quotes
                            fixed_items.append(f'"{item}"')
                        else:
                            fixed_items.append(item)

                    if fixed_items != items:
                        fixed_content = ', '.join(fixed_items)
                        try:
                            wrapped_array = '[' + fixed_content + ']'
                            parsed = json.loads(wrapped_array)
                            formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                            changes_made.append("Added quotes to unquoted array items and array brackets")
                            changes_made.append("Formatted JSON array")
                            return CorrectionResult(True, formatted, changes_made, errors, warnings)
                        except json.JSONDecodeError:
                            pass



            # Fix 3: Line-by-line comma insertion for specific error patterns
            try:
                # Parse the JSON error to get line number
                json.loads(corrected_content)
            except json.JSONDecodeError as e:
                if "Expecting ',' delimiter" in str(e) and hasattr(e, 'lineno'):
                    lines = corrected_content.split('\n')
                    error_line_idx = e.lineno - 1  # Convert to 0-based

                    if 0 <= error_line_idx < len(lines):
                        # Look at the previous line to add comma
                        prev_line_idx = error_line_idx - 1
                        if prev_line_idx >= 0:
                            prev_line = lines[prev_line_idx].rstrip()
                            # Add comma if line ends with a value and doesn't already have one
                            if (prev_line.endswith('"') or prev_line.endswith('}') or
                                prev_line.endswith(']') or prev_line.endswith('0')) and not prev_line.endswith(','):
                                lines[prev_line_idx] = prev_line + ','
                                corrected_content = '\n'.join(lines)
                                changes_made.append(f"Added missing comma at line {prev_line_idx + 1}")

                                # Try parsing again
                                try:
                                    parsed = json.loads(corrected_content)
                                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                                    changes_made.append("Formatted after targeted comma fix")
                                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                                except json.JSONDecodeError:
                                    pass

            # Fix 7: Unquoted string values (improved pattern matching)
            # Look for patterns like: "key": value where value should be quoted
            # More comprehensive approach to handle various unquoted value patterns

            # Pattern 1: "key": unquoted_word
            unquoted_value_pattern1 = r'("(?:[^"\\]|\\.)*"\s*:\s*)([a-zA-Z_][a-zA-Z0-9_]*)\b(?=\s*[,}])'
            if re.search(unquoted_value_pattern1, corrected_content):
                def quote_unquoted_values(match):
                    key_part = match.group(1)
                    value = match.group(2)
                    # Don't quote JSON keywords
                    if value.lower() in ['true', 'false', 'null']:
                        return match.group(0)
                    return key_part + '"' + value + '"'

                corrected_content = re.sub(unquoted_value_pattern1, quote_unquoted_values, corrected_content)
                changes_made.append("Added quotes around unquoted string values")

                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after quoting values")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Pattern 2: Handle case where value has spaces: "key": value with spaces
            unquoted_value_pattern2 = r'("(?:[^"\\]|\\.)*"\s*:\s*)([a-zA-Z_][a-zA-Z0-9_\s]*?)(?=\s*[,}])'
            if re.search(unquoted_value_pattern2, corrected_content):
                def quote_spaced_values(match):
                    key_part = match.group(1)
                    value = match.group(2).strip()
                    # Don't quote JSON keywords or numbers
                    if (value.lower() in ['true', 'false', 'null'] or
                        value.isdigit() or
                        re.match(r'^\d+\.\d+$', value)):
                        return match.group(0)
                    return key_part + '"' + value + '"'

                corrected_content = re.sub(unquoted_value_pattern2, quote_spaced_values, corrected_content)
                changes_made.append("Added quotes around unquoted string values with spaces")

                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after quoting spaced values")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 8: Invalid numbers (leading zeros like 01.23 → 1.23)
            invalid_number_pattern = r':\s*0+(\d+\.?\d*)'
            if re.search(invalid_number_pattern, corrected_content):
                corrected_content = re.sub(invalid_number_pattern, r': \1', corrected_content)
                changes_made.append("Fixed invalid numbers (removed leading zeros)")

                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after number correction")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 9: Mixed array/object syntax (advanced parsing approach)
            # This is one of the 3 critical JSON cases that must be fixed

            # Advanced pattern matching for mixed syntax
            stripped = corrected_content.strip()

            # Case 1: {"key": "value", "item2"} - object with orphaned string
            if (stripped.startswith('{') and stripped.endswith('}') and
                ',' in stripped and ':' in stripped):

                # Parse the content manually to identify the issue
                inner = stripped[1:-1].strip()  # Remove braces

                # Split by commas but be careful about quoted strings
                parts = []
                current_part = ""
                in_quotes = False
                escape_next = False

                for char in inner:
                    if escape_next:
                        current_part += char
                        escape_next = False
                    elif char == '\\':
                        current_part += char
                        escape_next = True
                    elif char == '"' and not escape_next:
                        current_part += char
                        in_quotes = not in_quotes
                    elif char == ',' and not in_quotes:
                        parts.append(current_part.strip())
                        current_part = ""
                    else:
                        current_part += char

                if current_part.strip():
                    parts.append(current_part.strip())

                # Now analyze parts to fix mixed syntax
                fixed_parts = []
                mixed_fixes = 0

                for part in parts:
                    if ':' in part:
                        # This is a proper key-value pair
                        fixed_parts.append(part)
                    else:
                        # This is an orphaned value - convert to key-value
                        if part.startswith('"') and part.endswith('"'):
                            # Quoted string - make it a key with null value
                            fixed_parts.append(f'{part}: null')
                            mixed_fixes += 1
                        elif part.strip():
                            # Unquoted value - quote it and make it a key
                            fixed_parts.append(f'"{part}": null')
                            mixed_fixes += 1

                if mixed_fixes > 0:
                    fixed_content = '{' + ', '.join(fixed_parts) + '}'
                    try:
                        parsed = json.loads(fixed_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append(f"Fixed {mixed_fixes} mixed array/object syntax issues")
                        changes_made.append("Formatted after fixing mixed syntax")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            # Case 2: Convert to array if no colons at all
            # {"item1", "item2", "item3"} → ["item1", "item2", "item3"]
            if (stripped.startswith('{') and stripped.endswith('}') and ':' not in stripped):
                inner_content = stripped[1:-1]  # Remove { }
                try:
                    array_content = '[' + inner_content + ']'
                    parsed = json.loads(array_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Converted object syntax to proper array")
                    changes_made.append("Formatted JSON array")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 10: Handle multiline strings (escape newlines)
            if '\n' in corrected_content and '"' in corrected_content:
                # Look for strings that span multiple lines
                multiline_pattern = r'"([^"]*\n[^"]*)"'
                if re.search(multiline_pattern, corrected_content):
                    # Escape newlines in strings
                    def escape_newlines(match):
                        return '"' + match.group(1).replace('\n', '\\n') + '"'

                    corrected_content = re.sub(multiline_pattern, escape_newlines, corrected_content)
                    changes_made.append("Escaped newlines in multiline strings")

                    try:
                        parsed = json.loads(corrected_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Formatted after escaping newlines")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            # Final attempt: Try wrapping in braces if we haven't already
            if not any("missing object braces" in change for change in changes_made):
                if (corrected_content.strip().startswith('"') and
                    ':' in corrected_content and
                    not corrected_content.strip().startswith('{')):

                    wrapped_content = '{' + corrected_content + '}'
                    try:
                        parsed = json.loads(wrapped_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Added missing object braces (final attempt)")
                        changes_made.append("Formatted JSON object")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            errors.append("Could not correct JSON format - consider using a JSON validator")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"JSON correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_yaml(self, content: str) -> CorrectionResult:
        """Correct YAML format issues using professional tools.

        Args:
            content: YAML content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # First, convert tabs to spaces immediately (tabs break YAML parsing)
            corrected_content = content
            if '\t' in corrected_content:
                corrected_content = corrected_content.replace('\t', '  ')
                changes_made.append("Converted tabs to spaces")

            # Try to parse the tab-corrected content
            try:
                yaml.safe_load(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, [], ["YAML is valid after tab conversion"] if changes_made else ["YAML is already valid"])
            except yaml.YAMLError:
                pass  # Continue with other corrections

            # Method 1: Use ruamel.yaml for round-trip formatting (preserves comments/ordering)
            try:
                from ruamel.yaml import YAML
                yaml_processor = YAML()
                yaml_processor.preserve_quotes = True
                yaml_processor.width = 4096  # Prevent line wrapping

                # Parse and dump to fix formatting - use corrected_content with tabs fixed
                data = yaml_processor.load(corrected_content)
                from io import StringIO
                output = StringIO()
                yaml_processor.dump(data, output)
                final_content = output.getvalue()

                changes_made.append("Formatted using ruamel.yaml (preserves comments/ordering)")
                return CorrectionResult(True, final_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("ruamel.yaml not available - using basic PyYAML")
            except Exception as e:
                warnings.append(f"ruamel.yaml error: {str(e)}")

            # Method 2: Use PyYAML with round-trip
            try:
                data = yaml.safe_load(corrected_content)  # Use corrected_content with tabs fixed
                final_content = yaml.dump(data, default_flow_style=False, indent=2, sort_keys=False)
                changes_made.append("Formatted using PyYAML")
                return CorrectionResult(True, final_content, changes_made, errors, warnings)
            except yaml.YAMLError as e:
                errors.append(f"YAML parsing error: {str(e)}")

            # Method 3: Use external yamllint if available
            if self._check_tool_available('yamllint'):
                # First try to get yamllint suggestions - use corrected_content
                success, stdout, stderr = self._run_external_tool(['yamllint', '-'], corrected_content)
                if not success and stderr:
                    warnings.append(f"yamllint found issues: {stderr}")

            # Fallback: Manual corrections - start with corrected_content that has tabs fixed
            # (corrected_content already has tabs converted to spaces)

            # Fix 1: ADVANCED YAML INDENTATION AND STRUCTURE FIXING
            # This addresses the critical YAML issues that must be fixed

            lines = corrected_content.split('\n')

            # PHASE 1: Normalize tabs and analyze structure
            normalized_lines = []
            structure_info = []

            for i, line in enumerate(lines):
                # Convert tabs to spaces (critical for YAML)
                normalized_line = line.expandtabs(2)

                if line.strip() and not line.strip().startswith('#'):
                    indent = len(normalized_line) - len(normalized_line.lstrip())
                    content = normalized_line.strip()

                    # Analyze line type
                    line_type = 'unknown'
                    if ':' in content and not content.startswith('-'):
                        line_type = 'mapping'
                    elif content.startswith('-'):
                        line_type = 'list_item'
                    elif content.startswith('|') or content.startswith('>'):
                        line_type = 'multiline'

                    structure_info.append({
                        'line_num': i,
                        'indent': indent,
                        'content': content,
                        'type': line_type,
                        'original': line
                    })

                normalized_lines.append(normalized_line)

            # PHASE 2: Fix indentation issues systematically
            if structure_info:
                fixed_lines = list(normalized_lines)
                total_fixes = 0

                # Detect and fix wrong indentation levels
                for i, info in enumerate(structure_info):
                    if info['type'] == 'mapping':
                        # For mapping items, ensure proper indentation
                        expected_indent = 0

                        # Look at previous mapping items to determine expected level
                        for j in range(i-1, -1, -1):
                            prev_info = structure_info[j]
                            if prev_info['type'] == 'mapping':
                                # Same level mappings should have same indentation
                                if ':' in prev_info['content'] and ':' in info['content']:
                                    # Check if this should be at the same level
                                    if not info['content'].startswith(' ') or prev_info['indent'] == 0:
                                        expected_indent = prev_info['indent']
                                        break

                        # Fix indentation if wrong
                        if info['indent'] != expected_indent and info['indent'] % 2 != 0:
                            # Fix odd indentation to even
                            correct_indent = (info['indent'] // 2) * 2
                            if correct_indent != info['indent']:
                                new_line = ' ' * correct_indent + info['content']
                                fixed_lines[info['line_num']] = new_line
                                total_fixes += 1

                # PHASE 3: Fix inconsistent indentation patterns
                # Ensure all indentation is multiples of 2
                for info in structure_info:
                    if info['indent'] > 0 and info['indent'] % 2 != 0:
                        # Fix odd indentation
                        correct_indent = ((info['indent'] + 1) // 2) * 2
                        new_line = ' ' * correct_indent + info['content']
                        fixed_lines[info['line_num']] = new_line
                        total_fixes += 1

                if total_fixes > 0:
                    corrected_content = '\n'.join(fixed_lines)
                    changes_made.append(f"Fixed {total_fixes} YAML indentation and structure issues")

                    try:
                        yaml.safe_load(corrected_content)
                        return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                    except yaml.YAMLError:
                        pass

                # If we still have issues, try the normalized version
                elif '\t' in content:
                    corrected_content = '\n'.join(normalized_lines)
                    changes_made.append("Converted tabs to spaces")

                    try:
                        yaml.safe_load(corrected_content)
                        return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                    except yaml.YAMLError:
                        pass

            # Fix 2: Fix missing space after colon (key:value → key: value)
            lines = corrected_content.split('\n')
            corrected_lines = []
            spacing_fixes = 0

            for line in lines:
                if ':' in line and not line.strip().startswith('#'):
                    # Look for pattern key:value (no space after colon)
                    if re.search(r':\S', line):
                        # Add space after colon
                        fixed_line = re.sub(r':(\S)', r': \1', line)
                        corrected_lines.append(fixed_line)
                        spacing_fixes += 1
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if spacing_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Added space after colon in {spacing_fixes} lines")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 3: Fix missing colons (key value → key: value)
            lines = corrected_content.split('\n')
            corrected_lines = []
            colon_fixes = 0

            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and ':' not in line and not stripped.startswith('-'):
                    # Looks like a key without colon, try to fix
                    indent = len(line) - len(line.lstrip())
                    if ' ' in stripped:
                        # Split on first space and add colon
                        parts = stripped.split(' ', 1)
                        corrected_line = ' ' * indent + parts[0] + ': ' + parts[1]
                        corrected_lines.append(corrected_line)
                        colon_fixes += 1
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if colon_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Added {colon_fixes} missing colons")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 4: Fix unmatched quotes
            lines = corrected_content.split('\n')
            corrected_lines = []
            quote_fixes = 0

            for line in lines:
                if '"' in line and line.count('"') % 2 != 0:
                    # Odd number of quotes, try to fix
                    if line.strip().endswith('"'):
                        # Missing opening quote
                        fixed_line = line.replace(':', ': "', 1)
                        corrected_lines.append(fixed_line)
                        quote_fixes += 1
                    else:
                        # Missing closing quote
                        corrected_lines.append(line + '"')
                        quote_fixes += 1
                else:
                    corrected_lines.append(line)

            if quote_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {quote_fixes} unmatched quotes")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 5: Fix inconsistent indentation (more aggressive)
            lines = corrected_content.split('\n')
            if len(lines) > 1:
                # Analyze indentation patterns
                indents = []
                for line in lines:
                    if line.strip() and not line.strip().startswith('#'):
                        indent = len(line) - len(line.lstrip())
                        if indent > 0:
                            indents.append(indent)

                if indents:
                    # Use 2 spaces as standard YAML indentation
                    standard_indent = 2

                    corrected_lines = []
                    indent_fixes = 0

                    for line in lines:
                        if line.strip() and not line.strip().startswith('#'):
                            current_indent = len(line) - len(line.lstrip())
                            if current_indent > 0:
                                # Calculate logical level (0, 1, 2, etc.)
                                level = max(1, round(current_indent / standard_indent))
                                correct_indent = level * standard_indent

                                if current_indent != correct_indent:
                                    corrected_line = ' ' * correct_indent + line.lstrip()
                                    corrected_lines.append(corrected_line)
                                    indent_fixes += 1
                                else:
                                    corrected_lines.append(line)
                            else:
                                corrected_lines.append(line)
                        else:
                            corrected_lines.append(line)

                    if indent_fixes > 0:
                        corrected_content = '\n'.join(corrected_lines)
                        changes_made.append(f"Fixed {indent_fixes} inconsistent indentations to 2-space standard")

                        try:
                            yaml.safe_load(corrected_content)
                            return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                        except yaml.YAMLError:
                            pass

            # Fix 6: COMPREHENSIVE YAML STRUCTURE FIXES

            # Fix missing colons (key value → key: value)
            lines = corrected_content.split('\n')
            corrected_lines = []
            structure_fixes = 0

            for line in lines:
                stripped = line.strip()
                original_line = line

                # Fix missing colons
                if (stripped and not stripped.startswith('#') and
                    ':' not in line and not stripped.startswith('-') and
                    ' ' in stripped and not stripped.startswith('|') and not stripped.startswith('>')):
                    # This might be a key-value pair missing a colon
                    indent = len(line) - len(line.lstrip())
                    parts = stripped.split(' ', 1)
                    if len(parts) == 2 and not parts[0].isdigit():  # Don't fix numbers
                        corrected_line = ' ' * indent + parts[0] + ': ' + parts[1]
                        corrected_lines.append(corrected_line)
                        structure_fixes += 1
                        continue

                # Fix wrong indentation level (parent:\nchild: value → parent:\n  child: value)
                if ':' in stripped and not stripped.startswith('#'):
                    indent = len(line) - len(line.lstrip())

                    # Look at the previous line to determine if this should be indented
                    if len(corrected_lines) > 0:
                        prev_line = corrected_lines[-1]
                        prev_stripped = prev_line.strip()
                        prev_indent = len(prev_line) - len(prev_line.lstrip())

                        # If previous line ends with : and current line has same indent,
                        # current line should be indented more
                        if (prev_stripped.endswith(':') and not prev_stripped.endswith('::') and
                            indent == prev_indent and indent == 0):
                            # This should be indented under the parent
                            corrected_line = '  ' + line.lstrip()  # Add 2 spaces
                            corrected_lines.append(corrected_line)
                            structure_fixes += 1
                            continue

                corrected_lines.append(original_line)

            if structure_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {structure_fixes} YAML structure issues (colons and indentation)")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 7: ADVANCED YAML SYNTAX FIXES

            lines = corrected_content.split('\n')
            corrected_lines = []
            syntax_fixes = 0

            for i, line in enumerate(lines):
                original_line = line

                # Fix invalid list syntax (items:\n- item1\n item2 → items:\n- item1\n- item2)
                stripped = line.strip()
                if (stripped and not stripped.startswith('#') and
                    not stripped.startswith('-') and not ':' in stripped and
                    i > 0):
                    # Check if previous line was a list item
                    prev_line = lines[i-1].strip()
                    if prev_line.startswith('-'):
                        # This might be a list item missing the dash
                        indent = len(line) - len(line.lstrip())
                        prev_indent = len(lines[i-1]) - len(lines[i-1].lstrip())

                        # If indentation matches previous list item, add dash
                        if indent == prev_indent:
                            corrected_line = ' ' * indent + '- ' + stripped
                            corrected_lines.append(corrected_line)
                            syntax_fixes += 1
                            continue

                # Fix invalid mapping syntax (key: value: extra → key: "value: extra")
                if line.count(':') > 1 and not stripped.startswith('#'):
                    first_colon = line.find(':')
                    if first_colon > 0:
                        key_part = line[:first_colon + 1]
                        value_part = line[first_colon + 1:].strip()
                        if ':' in value_part and not value_part.startswith('"'):
                            # Quote the entire value part
                            corrected_line = key_part + ' "' + value_part + '"'
                            corrected_lines.append(corrected_line)
                            syntax_fixes += 1
                            continue

                # Fix multiline string issues (key: |\n  line1\n line2 → fix indentation)
                if (stripped.endswith('|') or stripped.endswith('>')) and i < len(lines) - 1:
                    # This is a multiline string indicator
                    corrected_lines.append(original_line)

                    # Check following lines for consistent indentation
                    base_indent = len(line) - len(line.lstrip()) + 2  # Expected indent for content
                    j = i + 1
                    while j < len(lines) and lines[j].strip():
                        next_line = lines[j]
                        if next_line.strip() and not next_line.strip().startswith('#'):
                            next_indent = len(next_line) - len(next_line.lstrip())
                            if next_indent != base_indent and next_indent > 0:
                                # Fix the indentation
                                fixed_line = ' ' * base_indent + next_line.lstrip()
                                lines[j] = fixed_line  # Update in place for next iterations
                                syntax_fixes += 1
                        j += 1
                    continue

                corrected_lines.append(original_line)

            if syntax_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {syntax_fixes} YAML syntax issues (lists, mappings, multiline)")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 3: Fix indentation issues
            lines = corrected_content.split('\n')
            corrected_lines = []
            indent_fixes = 0

            for i, line in enumerate(lines):
                if line.strip() and not line.strip().startswith('#'):
                    # Check if this line has inconsistent indentation
                    if i > 0 and lines[i-1].strip() and ':' in lines[i-1]:
                        # Previous line was a key, this should be indented
                        prev_indent = len(lines[i-1]) - len(lines[i-1].lstrip())
                        current_indent = len(line) - len(line.lstrip())

                        if current_indent <= prev_indent and not line.strip().endswith(':'):
                            # Need to indent this line
                            corrected_line = ' ' * (prev_indent + 2) + line.strip()
                            corrected_lines.append(corrected_line)
                            indent_fixes += 1
                        else:
                            corrected_lines.append(line)
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if indent_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {indent_fixes} indentation issues")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            errors.append("Could not correct YAML format - consider using yamllint")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"YAML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_csv(self, content: str) -> CorrectionResult:
        """Correct CSV format issues using professional tools.

        Args:
            content: CSV content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use csvkit for professional CSV handling
            if self._check_tool_available('csvclean'):
                success, stdout, stderr = self._run_external_tool(['csvclean'], content)
                if success and stdout.strip():
                    changes_made.append("Cleaned using csvkit csvclean")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"csvclean warning: {stderr}")

            # Method 2: Use pandas if available (excellent CSV handling)
            try:
                import pandas as pd
                from io import StringIO

                # Try to read with pandas (it's very forgiving)
                df = pd.read_csv(StringIO(content))

                # Write back with proper formatting
                output = StringIO()
                df.to_csv(output, index=False, quoting=csv.QUOTE_MINIMAL)
                corrected_content = output.getvalue().rstrip('\n')

                changes_made.append("Formatted using pandas DataFrame.to_csv")
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("pandas not available - using standard csv module")
            except Exception as e:
                warnings.append(f"pandas error: {str(e)}")

            # Method 3: Standard library with better error handling
            try:
                # Normalize line endings first
                if '\r\n' in content or '\r' in content:
                    # Convert Windows/Mac line endings to Unix
                    normalized_content = content.replace('\r\n', '\n').replace('\r', '\n')
                    if normalized_content != content:
                        changes_made.append("Normalized line endings to Unix format")
                        content = normalized_content

                # Check for tab-delimited content
                if '\t' in content and content.count('\t') > content.count(','):
                    # Likely tab-delimited, convert to comma-delimited
                    tab_content = content.replace('\t', ',')
                    try:
                        # Test if tab-to-comma conversion works
                        test_reader = csv.reader(io.StringIO(tab_content))
                        rows = list(test_reader)
                        if rows:  # Successfully parsed
                            changes_made.append("Converted tab delimiters to commas")
                            content = tab_content  # Use converted content
                    except csv.Error:
                        pass  # Continue with original content

                # Try different dialects
                sample = content[:1024]  # First 1KB for dialect detection
                sniffer = csv.Sniffer()

                try:
                    dialect = sniffer.sniff(sample)
                    reader = csv.reader(io.StringIO(content), dialect=dialect)
                except csv.Error:
                    # Fallback to excel dialect
                    reader = csv.reader(io.StringIO(content), dialect='excel')

                rows = list(reader)

                if not rows:
                    return CorrectionResult(True, content, [], [], ["CSV is empty"])

                # Check if headers are missing (heuristic: first row contains only numbers/dates)
                if rows and len(rows) > 1:
                    first_row = rows[0]
                    # Check if first row looks like data rather than headers
                    looks_like_data = all(
                        cell.strip().replace('.', '').replace('-', '').replace('/', '').isdigit() or
                        cell.strip() == '' or
                        re.match(r'^\d{4}-\d{2}-\d{2}$', cell.strip()) or  # Date pattern
                        re.match(r'^\d+\.\d+$', cell.strip())  # Decimal number
                        for cell in first_row if cell.strip()
                    )

                    if looks_like_data and len(first_row) > 1:
                        # Add generic headers
                        max_cols = max(len(row) for row in rows)
                        headers = [f"Column_{i+1}" for i in range(max_cols)]
                        rows.insert(0, headers)
                        changes_made.append(f"Added generic headers for {max_cols} columns")

                # Standardize column counts
                max_cols = max(len(row) for row in rows) if rows else 0
                corrected_rows = []
                padding_added = 0

                for row in rows:
                    if len(row) < max_cols:
                        padded_row = row + [''] * (max_cols - len(row))
                        corrected_rows.append(padded_row)
                        padding_added += max_cols - len(row)
                    else:
                        corrected_rows.append(row)

                if padding_added > 0:
                    changes_made.append(f"Added {padding_added} empty cells to standardize column counts")

                # Fix quote escaping issues and mixed quote styles
                quote_fixes = 0
                final_rows = []
                for row in corrected_rows:
                    fixed_row = []
                    for cell in row:
                        if isinstance(cell, str):
                            original_cell = cell

                            # Fix mixed quote styles (single quotes to double quotes)
                            if cell.startswith("'") and cell.endswith("'") and len(cell) > 1:
                                cell = '"' + cell[1:-1] + '"'
                                quote_fixes += 1

                            # Fix unescaped quotes
                            elif '"' in cell and not cell.startswith('"'):
                                # Cell contains unescaped quotes, fix them
                                if cell.count('"') % 2 != 0 or ('"' in cell and not (cell.startswith('"') and cell.endswith('"'))):
                                    # Escape internal quotes and wrap in quotes
                                    escaped_cell = '"' + cell.replace('"', '""') + '"'
                                    cell = escaped_cell
                                    quote_fixes += 1

                            fixed_row.append(cell)
                        else:
                            fixed_row.append(cell)
                    final_rows.append(fixed_row)

                if quote_fixes > 0:
                    changes_made.append(f"Fixed {quote_fixes} quote style and escaping issues")
                    corrected_rows = final_rows

                # Write with proper formatting
                output = io.StringIO()
                writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)
                for row in corrected_rows:
                    writer.writerow(row)

                corrected_content = output.getvalue().rstrip('\n')

                if corrected_content != content:
                    if not changes_made:
                        changes_made.append("Standardized CSV formatting and quoting")

                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except Exception as e:
                errors.append(f"CSV parsing error: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"CSV correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_python(self, content: str) -> CorrectionResult:
        """Correct Python syntax issues using professional tools.

        Args:
            content: Python content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # First, try to parse as-is
            try:
                ast.parse(content)
                return CorrectionResult(True, content, [], [], ["Python syntax is already valid"])
            except SyntaxError:
                pass  # Continue with corrections

            # Method 1: Use black for professional Python formatting
            if self._check_tool_available('black'):
                success, stdout, stderr = self._run_external_tool(['black', '--code', content], "")
                if success and stdout.strip():
                    changes_made.append("Formatted using black")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"black warning: {stderr}")

            # Method 2: Use autopep8 for PEP 8 compliance
            if self._check_tool_available('autopep8'):
                success, stdout, stderr = self._run_external_tool(['autopep8', '-'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using autopep8")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"autopep8 warning: {stderr}")

            # Method 3: Use yapf for formatting
            if self._check_tool_available('yapf'):
                success, stdout, stderr = self._run_external_tool(['yapf'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using yapf")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"yapf warning: {stderr}")

            # Fallback: Basic corrections
            corrected_content = content
            lines = corrected_content.split('\n')

            # Fix 1: Convert tabs to spaces
            tab_fixes = 0
            corrected_lines = []
            for line in lines:
                if '\t' in line:
                    tab_fixes += 1
                    line = line.replace('\t', '    ')  # PEP 8: 4 spaces
                corrected_lines.append(line)

            if tab_fixes > 0:
                changes_made.append(f"Converted {tab_fixes} tabs to spaces")
                corrected_content = '\n'.join(corrected_lines)

            # Try to parse the corrected content
            try:
                ast.parse(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except SyntaxError as e:
                errors.append(f"Could not fully correct Python syntax: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"Python correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_xml(self, content: str) -> CorrectionResult:
        """Correct XML format issues.

        Args:
            content: XML content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content

        try:
            import xml.etree.ElementTree as ET

            # First, try to parse as-is
            try:
                ET.fromstring(content)
                return CorrectionResult(True, content, [], [], ["XML is already valid"])
            except ET.ParseError:
                pass  # Continue with corrections

            # Fix 1: Add XML declaration if missing
            if not corrected_content.strip().startswith('<?xml'):
                corrected_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + corrected_content
                changes_made.append("Added XML declaration")

            # Fix 2: Basic tag balancing (very simplified)
            # This is a basic approach - full XML correction is complex
            lines = corrected_content.split('\n')
            corrected_lines = []

            for line in lines:
                # Fix basic attribute quoting
                if '=' in line and '"' not in line and "'" not in line:
                    # Very basic attribute fixing
                    line = re.sub(r'=([^>\s]+)', r'="\1"', line)
                    if line != corrected_content.split('\n')[corrected_lines.__len__()]:
                        changes_made.append("Fixed attribute quoting")

                corrected_lines.append(line)

            corrected_content = '\n'.join(corrected_lines)

            # Try to parse the corrected content
            try:
                ET.fromstring(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except ET.ParseError as e:
                errors.append(f"Could not fully correct XML: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"XML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_markdown(self, content: str) -> CorrectionResult:
        """Correct Markdown format issues using professional tools.

        Args:
            content: Markdown content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use mdformat for professional markdown formatting
            if self._check_tool_available('mdformat'):
                success, stdout, stderr = self._run_external_tool(['mdformat', '-'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using mdformat")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"mdformat warning: {stderr}")

            # Method 2: Use markdown-it-py / mistletoe for AST-based correction
            try:
                from markdown_it import MarkdownIt
                md = MarkdownIt()

                # Parse to AST and back (validates structure)
                tokens = md.parse(content)
                # For now, just validate that it parses
                changes_made.append("Validated using markdown-it-py")

                # Basic formatting fixes
                corrected_content = content

                # Fix link spacing [text] (url) -> [text](url)
                link_pattern = r'\[([^\]]+)\]\s+\(([^)]+)\)'
                if re.search(link_pattern, corrected_content):
                    corrected_content = re.sub(link_pattern, r'[\1](\2)', corrected_content)
                    changes_made.append("Fixed link formatting (removed spaces between text and URL)")

                # Standardize list markers to use dashes (-)
                lines = corrected_content.split('\n')
                marker_fixes = 0
                corrected_lines = []

                for line in lines:
                    # Replace * or + with - in list items
                    if re.match(r'^(\s*)[\*\+](\s+)', line):
                        fixed_line = re.sub(r'^(\s*)[\*\+](\s+)', r'\1-\2', line)
                        corrected_lines.append(fixed_line)
                        marker_fixes += 1
                    else:
                        corrected_lines.append(line)

                if marker_fixes > 0:
                    changes_made.append(f"Standardized {marker_fixes} list markers to use dashes")
                    corrected_content = '\n'.join(corrected_lines)

                # Fix list spacing (ensure space after dash)
                lines = corrected_content.split('\n')
                spacing_fixes = 0
                corrected_lines = []

                for line in lines:
                    # Fix -item -> - item
                    if re.match(r'^(\s*)-([^\s])', line):
                        fixed_line = re.sub(r'^(\s*)-([^\s])', r'\1- \2', line)
                        corrected_lines.append(fixed_line)
                        spacing_fixes += 1
                    else:
                        corrected_lines.append(line)

                if spacing_fixes > 0:
                    changes_made.append(f"Added space after dash in {spacing_fixes} list items")
                    corrected_content = '\n'.join(corrected_lines)

                # Fix trailing whitespace
                lines = corrected_content.split('\n')
                fixed_lines = [line.rstrip() for line in lines]
                trailing_fixes = sum(1 for orig, fixed in zip(lines, fixed_lines) if orig != fixed and orig.strip())
                if trailing_fixes > 0:
                    changes_made.append(f"Removed trailing whitespace from {trailing_fixes} lines")
                    corrected_content = '\n'.join(fixed_lines)

                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("markdown-it-py not available")
            except Exception as e:
                warnings.append(f"markdown-it-py error: {str(e)}")

            # Method 3: Use markdownlint via subprocess if available
            if self._check_tool_available('markdownlint'):
                success, stdout, stderr = self._run_external_tool(['markdownlint', '--stdin'], content)
                if stderr:  # markdownlint outputs issues to stderr
                    warnings.append(f"markdownlint found issues: {stderr}")

            # Fallback: Basic corrections
            corrected_content = content
            lines = corrected_content.split('\n')

            # Fix 1: Header spacing
            header_fixes = 0
            corrected_lines = []
            for line in lines:
                if line.strip().startswith('#') and not re.match(r'^#+\s+', line.strip()):
                    header_level = len(line.strip()) - len(line.strip().lstrip('#'))
                    if header_level <= 6:
                        indent = len(line) - len(line.lstrip())
                        header_text = line.strip()[header_level:].strip()
                        corrected_line = ' ' * indent + '#' * header_level + ' ' + header_text
                        corrected_lines.append(corrected_line)
                        header_fixes += 1
                        continue
                corrected_lines.append(line)

            if header_fixes > 0:
                changes_made.append(f"Fixed {header_fixes} header spacing issues")

            # Fix 2: Remove trailing whitespace
            final_lines = [line.rstrip() for line in corrected_lines]
            trailing_fixes = sum(1 for orig, fixed in zip(corrected_lines, final_lines)
                               if orig != fixed and orig.strip())

            if trailing_fixes > 0:
                changes_made.append(f"Removed trailing whitespace from {trailing_fixes} lines")

            # Fix 3: Fix link formatting [text] (url) -> [text](url)
            corrected_content = '\n'.join(final_lines)
            link_pattern = r'\[([^\]]+)\]\s+\(([^)]+)\)'
            if re.search(link_pattern, corrected_content):
                corrected_content = re.sub(link_pattern, r'[\1](\2)', corrected_content)
                changes_made.append("Fixed link formatting (removed spaces between text and URL)")

            # Fix 4: Standardize list markers (*, +, - → -)
            lines = corrected_content.split('\n')
            marker_fixes = 0
            corrected_lines = []

            for line in lines:
                # Standardize list markers to use dash (-)
                if re.match(r'^(\s*)[\*\+]\s+', line):
                    # Replace * or + with -
                    fixed_line = re.sub(r'^(\s*)[\*\+](\s+)', r'\1-\2', line)
                    corrected_lines.append(fixed_line)
                    marker_fixes += 1
                else:
                    corrected_lines.append(line)

            if marker_fixes > 0:
                changes_made.append(f"Standardized {marker_fixes} list markers to use dashes")
                corrected_content = '\n'.join(corrected_lines)

            # Fix 5: Fix list formatting (ensure space after dash)
            list_pattern = r'^(\s*)-([^\s])'
            lines = corrected_content.split('\n')
            list_fixes = 0
            corrected_lines = []

            for line in lines:
                if re.match(list_pattern, line):
                    corrected_line = re.sub(list_pattern, r'\1- \2', line)
                    corrected_lines.append(corrected_line)
                    list_fixes += 1
                else:
                    corrected_lines.append(line)

            if list_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {list_fixes} list formatting issues")

            # Fix 6: Fix broken links [text]( → [text](#)
            broken_link_pattern = r'\[([^\]]+)\]\(\s*$'
            if re.search(broken_link_pattern, corrected_content, re.MULTILINE):
                corrected_content = re.sub(broken_link_pattern, r'[\1](#)', corrected_content, flags=re.MULTILINE)
                changes_made.append("Fixed broken links (added placeholder URLs)")

            # Fix 7: Add table headers if missing
            lines = corrected_content.split('\n')
            corrected_lines = []
            table_fixes = 0

            for i, line in enumerate(lines):
                if line.strip().startswith('|') and line.strip().endswith('|'):
                    # This looks like a table row
                    # Check if previous line is also a table row (no header separator)
                    if (i == 0 or
                        (i > 0 and not lines[i-1].strip().startswith('|')) or
                        (i > 0 and lines[i-1].strip().startswith('|') and not re.match(r'^\|[\s\-\|:]+\|$', lines[i-1].strip()))):
                        # This might be a table without headers
                        # Count columns
                        cols = line.count('|') - 1
                        if cols > 0:
                            # Add generic headers
                            header = '| ' + ' | '.join([f'Column {j+1}' for j in range(cols)]) + ' |'
                            separator = '| ' + ' | '.join(['---' for _ in range(cols)]) + ' |'
                            corrected_lines.extend([header, separator])
                            table_fixes += 1

                corrected_lines.append(line)

            if table_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Added headers to {table_fixes} tables")

            # Fix 8: Balance code block markers
            code_block_count = corrected_content.count('```')
            if code_block_count % 2 != 0:
                corrected_content += '\n```'
                changes_made.append("Added missing closing code block marker")

            return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"Markdown correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_xlsx(self, content: str) -> CorrectionResult:
        """Correct XLSX/Excel format issues using professional tools.

        Args:
            content: XLSX content (base64 encoded or file path)

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use pandas for Excel file handling
            try:
                import pandas as pd
                from io import BytesIO
                import base64

                # Try to decode if it's base64 encoded content
                try:
                    if isinstance(content, str) and len(content) > 100:
                        # Assume it's base64 encoded
                        excel_data = base64.b64decode(content)
                        excel_file = BytesIO(excel_data)
                    else:
                        # Assume it's a file path
                        excel_file = content

                    # Read Excel file
                    df = pd.read_excel(excel_file)

                    # Basic cleanup
                    # Remove completely empty rows
                    df_cleaned = df.dropna(how='all')

                    # Remove completely empty columns
                    df_cleaned = df_cleaned.dropna(axis=1, how='all')

                    if len(df_cleaned) != len(df):
                        changes_made.append(f"Removed {len(df) - len(df_cleaned)} empty rows")

                    if len(df_cleaned.columns) != len(df.columns):
                        changes_made.append(f"Removed {len(df.columns) - len(df_cleaned.columns)} empty columns")

                    # Convert back to Excel format
                    output = BytesIO()
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        df_cleaned.to_excel(writer, index=False)

                    # Return as base64 if input was base64
                    if isinstance(content, str) and len(content) > 100:
                        corrected_content = base64.b64encode(output.getvalue()).decode('utf-8')
                    else:
                        corrected_content = output.getvalue()

                    changes_made.append("Processed using pandas DataFrame")
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

                except Exception as e:
                    warnings.append(f"pandas Excel processing error: {str(e)}")

            except ImportError:
                warnings.append("pandas not available for Excel processing")

            # Method 2: Use openpyxl directly
            try:
                import openpyxl
                from io import BytesIO
                import base64

                if isinstance(content, str) and len(content) > 100:
                    excel_data = base64.b64decode(content)
                    excel_file = BytesIO(excel_data)
                else:
                    excel_file = content

                workbook = openpyxl.load_workbook(excel_file)

                # Basic cleanup for each worksheet
                for sheet_name in workbook.sheetnames:
                    sheet = workbook[sheet_name]

                    # Remove empty rows from the end
                    max_row = sheet.max_row
                    while max_row > 1:
                        if all(cell.value is None for cell in sheet[max_row]):
                            sheet.delete_rows(max_row)
                            max_row -= 1
                        else:
                            break

                    # Remove empty columns from the end
                    max_col = sheet.max_column
                    while max_col > 1:
                        if all(sheet.cell(row, max_col).value is None for row in range(1, sheet.max_row + 1)):
                            sheet.delete_cols(max_col)
                            max_col -= 1
                        else:
                            break

                # Save the cleaned workbook
                output = BytesIO()
                workbook.save(output)

                if isinstance(content, str) and len(content) > 100:
                    corrected_content = base64.b64encode(output.getvalue()).decode('utf-8')
                else:
                    corrected_content = output.getvalue()

                changes_made.append("Cleaned using openpyxl")
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("openpyxl not available for Excel processing")
            except Exception as e:
                warnings.append(f"openpyxl error: {str(e)}")

            errors.append("No Excel processing libraries available (pandas, openpyxl)")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"XLSX correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_format(self, content: str, file_type: str) -> CorrectionResult:
        """Correct content based on file type.
        
        Args:
            content: Content to correct
            file_type: Type of file (json, yaml, csv, python, etc.)
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        if file_type.lower() == 'json':
            return self.correct_json(content)
        elif file_type.lower() in ['yaml', 'yml']:
            return self.correct_yaml(content)
        elif file_type.lower() == 'csv':
            return self.correct_csv(content)
        elif file_type.lower() in ['python', 'py']:
            return self.correct_python(content)
        elif file_type.lower() == 'xml':
            return self.correct_xml(content)
        elif file_type.lower() in ['markdown', 'md']:
            return self.correct_markdown(content)
        elif file_type.lower() in ['xlsx', 'excel']:
            return self.correct_xlsx(content)
        else:
            return CorrectionResult(
                False,
                content,
                [],
                [f"No correction available for {file_type} files"],
                []
            )
