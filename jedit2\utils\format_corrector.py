"""Format correction for different file types in JEdit2.

This module provides automatic correction capabilities for various file formats.
"""

import json
import yaml
import csv
import io
import re
import ast
import subprocess
import tempfile
import os
import shutil
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class CorrectionResult:
    """Result of format correction."""
    success: bool
    corrected_content: str
    changes_made: List[str]
    errors: List[str]
    warnings: List[str]


class FormatCorrector:
    """Corrector for different file formats using professional tools."""

    def _run_external_tool(self, command: List[str], input_content: str, cwd: str = None) -> Tuple[bool, str, str]:
        """Run an external tool with input content.

        Args:
            command: Command to run as list of strings
            input_content: Content to pass to stdin
            cwd: Working directory

        Returns:
            Tuple of (success, stdout, stderr)
        """
        try:
            result = subprocess.run(
                command,
                input=input_content,
                text=True,
                capture_output=True,
                cwd=cwd,
                timeout=30  # 30 second timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Tool execution timed out"
        except FileNotFoundError:
            return False, "", f"Tool not found: {command[0]}"
        except Exception as e:
            return False, "", f"Tool execution error: {str(e)}"

    def _check_tool_available(self, tool_name: str) -> bool:
        """Check if a tool is available in PATH."""
        return shutil.which(tool_name) is not None

    def correct_json(self, content: str) -> CorrectionResult:
        """Correct JSON format issues using professional tools.

        Args:
            content: JSON content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # First, try to parse as-is to see if it's already valid
            try:
                json.loads(content)
                return CorrectionResult(True, content, [], [], ["JSON is already valid"])
            except json.JSONDecodeError:
                pass  # Continue with corrections

            # Method 1: Use python -m json.tool for formatting
            if self._check_tool_available('python'):
                success, stdout, stderr = self._run_external_tool(
                    ['python', '-m', 'json.tool'],
                    content
                )
                if success and stdout.strip():
                    changes_made.append("Formatted using python json.tool")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"json.tool warning: {stderr}")

            # Method 2: Use jq if available (better error handling and formatting)
            if self._check_tool_available('jq'):
                success, stdout, stderr = self._run_external_tool(['jq', '.'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using jq")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"jq warning: {stderr}")

            # Method 3: Use jsonschema validation if available
            try:
                import jsonschema
                # Try basic parsing with better error messages
                parsed = json.loads(content)
                formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                changes_made.append("Formatted using built-in JSON parser")
                return CorrectionResult(True, formatted, changes_made, errors, warnings)
            except ImportError:
                pass
            except json.JSONDecodeError as e:
                errors.append(f"JSON parsing error: {e.msg} at line {e.lineno}, column {e.colno}")

            # Fallback: Targeted correction attempts
            corrected_content = content

            # Fix 0: Check for missing object braces (common when copying JSON fragments)
            # Look for content that starts with a property but no opening brace
            if (corrected_content.strip().startswith('"') and
                ':' in corrected_content and
                not corrected_content.strip().startswith('{')):

                # First try to fix trailing commas in the fragment, then wrap
                fragment_content = corrected_content

                # Remove trailing commas from the fragment
                if re.search(r',\s*$', fragment_content.strip()):
                    fragment_content = re.sub(r',\s*$', '', fragment_content.strip())
                    changes_made.append("Removed trailing comma from fragment")

                # Try wrapping in braces
                wrapped_content = '{' + fragment_content + '}'
                try:
                    parsed = json.loads(wrapped_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Added missing object braces")
                    changes_made.append("Formatted JSON object")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    # If wrapping doesn't work, continue with other fixes
                    pass

            # Fix 1: Remove trailing commas FIRST (most common and easiest to fix)
            if re.search(r',\s*[}\]]', corrected_content):
                corrected_content = re.sub(r',(\s*[}\]])', r'\1', corrected_content)
                changes_made.append("Removed trailing commas")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after trailing comma removal")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 2: Convert single quotes to double quotes (Python → JSON)
            if "'" in corrected_content and corrected_content.count("'") >= 2:
                # Simple conversion: 'key' → "key" and 'value' → "value"
                # This handles the most common case of Python-style single quotes
                single_quote_content = corrected_content.replace("'", '"')

                # Also fix Python boolean/null values in the same pass
                python_to_json_replacements = [
                    (r'\bTrue\b', 'true'),
                    (r'\bFalse\b', 'false'),
                    (r'\bNone\b', 'null')
                ]

                combined_fixes = ["Converted single quotes to double quotes"]
                for pattern, replacement in python_to_json_replacements:
                    if re.search(pattern, single_quote_content):
                        single_quote_content = re.sub(pattern, replacement, single_quote_content)
                        if len(combined_fixes) == 1:  # First Python value fix
                            combined_fixes.append("Converted Python values to JSON format")

                try:
                    parsed = json.loads(single_quote_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    combined_fixes.append("Formatted after conversion")
                    changes_made.extend(combined_fixes)
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    # Update corrected_content for next attempts
                    corrected_content = single_quote_content
                    changes_made.extend(combined_fixes[:-1])  # Don't add "Formatted" if it failed

            # Fix 3: Convert Python boolean/null values to JSON equivalents
            python_to_json_replacements = [
                (r'\bTrue\b', 'true'),
                (r'\bFalse\b', 'false'),
                (r'\bNone\b', 'null')
            ]

            python_fixes = 0
            for pattern, replacement in python_to_json_replacements:
                if re.search(pattern, corrected_content):
                    corrected_content = re.sub(pattern, replacement, corrected_content)
                    python_fixes += 1

            if python_fixes > 0:
                changes_made.append(f"Converted {python_fixes} Python values to JSON format")
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after Python value conversion")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 4: Add quotes around unquoted property names
            # Pattern: {name: "value" -> {"name": "value"
            unquoted_pattern = r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:'
            if re.search(unquoted_pattern, corrected_content):
                corrected_content = re.sub(unquoted_pattern, r'\1"\2":', corrected_content)
                changes_made.append("Added quotes around unquoted property names")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after adding quotes")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 3: Escape unescaped quotes in string values
            # Pattern: "He said "hello"" -> "He said \"hello\""
            # More sophisticated approach for nested quotes
            lines = corrected_content.split('\n')
            quote_fixes = 0
            corrected_lines = []

            for line in lines:
                # Look for strings with unescaped quotes
                if line.count('"') > 2:  # More than just opening and closing quotes
                    # Try to fix by escaping internal quotes
                    # This is a simplified approach - real JSON parsers are more complex
                    fixed_line = line
                    # Pattern: "text"more"text" -> "text\"more\"text"
                    pattern = r'"([^"]*)"([^"]*)"([^"]*)"'
                    if re.search(pattern, line):
                        fixed_line = re.sub(pattern, r'"\1\"\2\"\3"', line)
                        if fixed_line != line:
                            quote_fixes += 1
                    corrected_lines.append(fixed_line)
                else:
                    corrected_lines.append(line)

            if quote_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Escaped {quote_fixes} unescaped quotes in string values")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after escaping quotes")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass

            # Fix 4: Missing commas (most common issue)
            # Look for patterns like: "value" "nextkey" or "value" \n "nextkey"
            comma_pattern = r'("(?:[^"\\]|\\.)*")\s*\n?\s*("(?:[^"\\]|\\.)*"\s*:)'
            if re.search(comma_pattern, corrected_content):
                corrected_content = re.sub(comma_pattern, r'\1,\n\2', corrected_content)
                changes_made.append("Added missing commas between JSON properties")

                # Try parsing again
                try:
                    parsed = json.loads(corrected_content)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    changes_made.append("Formatted after comma correction")
                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                except json.JSONDecodeError:
                    pass



            # Fix 3: Line-by-line comma insertion for specific error patterns
            try:
                # Parse the JSON error to get line number
                json.loads(corrected_content)
            except json.JSONDecodeError as e:
                if "Expecting ',' delimiter" in str(e) and hasattr(e, 'lineno'):
                    lines = corrected_content.split('\n')
                    error_line_idx = e.lineno - 1  # Convert to 0-based

                    if 0 <= error_line_idx < len(lines):
                        # Look at the previous line to add comma
                        prev_line_idx = error_line_idx - 1
                        if prev_line_idx >= 0:
                            prev_line = lines[prev_line_idx].rstrip()
                            # Add comma if line ends with a value and doesn't already have one
                            if (prev_line.endswith('"') or prev_line.endswith('}') or
                                prev_line.endswith(']') or prev_line.endswith('0')) and not prev_line.endswith(','):
                                lines[prev_line_idx] = prev_line + ','
                                corrected_content = '\n'.join(lines)
                                changes_made.append(f"Added missing comma at line {prev_line_idx + 1}")

                                # Try parsing again
                                try:
                                    parsed = json.loads(corrected_content)
                                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                                    changes_made.append("Formatted after targeted comma fix")
                                    return CorrectionResult(True, formatted, changes_made, errors, warnings)
                                except json.JSONDecodeError:
                                    pass

            # Final attempt: Try wrapping in braces if we haven't already
            if not any("missing object braces" in change for change in changes_made):
                if (corrected_content.strip().startswith('"') and
                    ':' in corrected_content and
                    not corrected_content.strip().startswith('{')):

                    wrapped_content = '{' + corrected_content + '}'
                    try:
                        parsed = json.loads(wrapped_content)
                        formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                        changes_made.append("Added missing object braces (final attempt)")
                        changes_made.append("Formatted JSON object")
                        return CorrectionResult(True, formatted, changes_made, errors, warnings)
                    except json.JSONDecodeError:
                        pass

            errors.append("Could not correct JSON format - consider using a JSON validator")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"JSON correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_yaml(self, content: str) -> CorrectionResult:
        """Correct YAML format issues using professional tools.

        Args:
            content: YAML content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # First, convert tabs to spaces immediately (tabs break YAML parsing)
            corrected_content = content
            if '\t' in corrected_content:
                corrected_content = corrected_content.replace('\t', '  ')
                changes_made.append("Converted tabs to spaces")

            # Try to parse the tab-corrected content
            try:
                yaml.safe_load(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, [], ["YAML is valid after tab conversion"] if changes_made else ["YAML is already valid"])
            except yaml.YAMLError:
                pass  # Continue with other corrections

            # Method 1: Use ruamel.yaml for round-trip formatting (preserves comments/ordering)
            try:
                from ruamel.yaml import YAML
                yaml_processor = YAML()
                yaml_processor.preserve_quotes = True
                yaml_processor.width = 4096  # Prevent line wrapping

                # Parse and dump to fix formatting - use corrected_content with tabs fixed
                data = yaml_processor.load(corrected_content)
                from io import StringIO
                output = StringIO()
                yaml_processor.dump(data, output)
                final_content = output.getvalue()

                changes_made.append("Formatted using ruamel.yaml (preserves comments/ordering)")
                return CorrectionResult(True, final_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("ruamel.yaml not available - using basic PyYAML")
            except Exception as e:
                warnings.append(f"ruamel.yaml error: {str(e)}")

            # Method 2: Use PyYAML with round-trip
            try:
                data = yaml.safe_load(corrected_content)  # Use corrected_content with tabs fixed
                final_content = yaml.dump(data, default_flow_style=False, indent=2, sort_keys=False)
                changes_made.append("Formatted using PyYAML")
                return CorrectionResult(True, final_content, changes_made, errors, warnings)
            except yaml.YAMLError as e:
                errors.append(f"YAML parsing error: {str(e)}")

            # Method 3: Use external yamllint if available
            if self._check_tool_available('yamllint'):
                # First try to get yamllint suggestions - use corrected_content
                success, stdout, stderr = self._run_external_tool(['yamllint', '-'], corrected_content)
                if not success and stderr:
                    warnings.append(f"yamllint found issues: {stderr}")

            # Fallback: Manual corrections - start with corrected_content that has tabs fixed
            # (corrected_content already has tabs converted to spaces)

            # Fix 1: Convert tabs to spaces FIRST (tabs break YAML parsing)
            if '\t' in corrected_content:
                corrected_content = corrected_content.replace('\t', '  ')
                changes_made.append("Converted tabs to spaces")

                # Try parsing immediately after tab fix
                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 2: Fix missing space after colon (key:value → key: value)
            lines = corrected_content.split('\n')
            corrected_lines = []
            spacing_fixes = 0

            for line in lines:
                if ':' in line and not line.strip().startswith('#'):
                    # Look for pattern key:value (no space after colon)
                    if re.search(r':\S', line):
                        # Add space after colon
                        fixed_line = re.sub(r':(\S)', r': \1', line)
                        corrected_lines.append(fixed_line)
                        spacing_fixes += 1
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if spacing_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Added space after colon in {spacing_fixes} lines")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 3: Fix missing colons (key value → key: value)
            lines = corrected_content.split('\n')
            corrected_lines = []
            colon_fixes = 0

            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and ':' not in line and not stripped.startswith('-'):
                    # Looks like a key without colon, try to fix
                    indent = len(line) - len(line.lstrip())
                    if ' ' in stripped:
                        # Split on first space and add colon
                        parts = stripped.split(' ', 1)
                        corrected_line = ' ' * indent + parts[0] + ': ' + parts[1]
                        corrected_lines.append(corrected_line)
                        colon_fixes += 1
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if colon_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Added {colon_fixes} missing colons")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            # Fix 3: Fix indentation issues
            lines = corrected_content.split('\n')
            corrected_lines = []
            indent_fixes = 0

            for i, line in enumerate(lines):
                if line.strip() and not line.strip().startswith('#'):
                    # Check if this line has inconsistent indentation
                    if i > 0 and lines[i-1].strip() and ':' in lines[i-1]:
                        # Previous line was a key, this should be indented
                        prev_indent = len(lines[i-1]) - len(lines[i-1].lstrip())
                        current_indent = len(line) - len(line.lstrip())

                        if current_indent <= prev_indent and not line.strip().endswith(':'):
                            # Need to indent this line
                            corrected_line = ' ' * (prev_indent + 2) + line.strip()
                            corrected_lines.append(corrected_line)
                            indent_fixes += 1
                        else:
                            corrected_lines.append(line)
                    else:
                        corrected_lines.append(line)
                else:
                    corrected_lines.append(line)

            if indent_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {indent_fixes} indentation issues")

                try:
                    yaml.safe_load(corrected_content)
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
                except yaml.YAMLError:
                    pass

            errors.append("Could not correct YAML format - consider using yamllint")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"YAML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_csv(self, content: str) -> CorrectionResult:
        """Correct CSV format issues using professional tools.

        Args:
            content: CSV content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use csvkit for professional CSV handling
            if self._check_tool_available('csvclean'):
                success, stdout, stderr = self._run_external_tool(['csvclean'], content)
                if success and stdout.strip():
                    changes_made.append("Cleaned using csvkit csvclean")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"csvclean warning: {stderr}")

            # Method 2: Use pandas if available (excellent CSV handling)
            try:
                import pandas as pd
                from io import StringIO

                # Try to read with pandas (it's very forgiving)
                df = pd.read_csv(StringIO(content))

                # Write back with proper formatting
                output = StringIO()
                df.to_csv(output, index=False, quoting=csv.QUOTE_MINIMAL)
                corrected_content = output.getvalue().rstrip('\n')

                changes_made.append("Formatted using pandas DataFrame.to_csv")
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("pandas not available - using standard csv module")
            except Exception as e:
                warnings.append(f"pandas error: {str(e)}")

            # Method 3: Standard library with better error handling
            try:
                # Check for tab-delimited content first
                if '\t' in content and content.count('\t') > content.count(','):
                    # Likely tab-delimited, convert to comma-delimited
                    tab_content = content.replace('\t', ',')
                    try:
                        # Test if tab-to-comma conversion works
                        test_reader = csv.reader(io.StringIO(tab_content))
                        rows = list(test_reader)
                        if rows:  # Successfully parsed
                            changes_made.append("Converted tab delimiters to commas")
                            content = tab_content  # Use converted content
                    except csv.Error:
                        pass  # Continue with original content

                # Try different dialects
                sample = content[:1024]  # First 1KB for dialect detection
                sniffer = csv.Sniffer()

                try:
                    dialect = sniffer.sniff(sample)
                    reader = csv.reader(io.StringIO(content), dialect=dialect)
                except csv.Error:
                    # Fallback to excel dialect
                    reader = csv.reader(io.StringIO(content), dialect='excel')

                rows = list(reader)

                if not rows:
                    return CorrectionResult(True, content, [], [], ["CSV is empty"])

                # Check if headers are missing (heuristic: first row contains only numbers/dates)
                if rows and len(rows) > 1:
                    first_row = rows[0]
                    # Check if first row looks like data rather than headers
                    looks_like_data = all(
                        cell.strip().replace('.', '').replace('-', '').replace('/', '').isdigit() or
                        cell.strip() == '' or
                        re.match(r'^\d{4}-\d{2}-\d{2}$', cell.strip()) or  # Date pattern
                        re.match(r'^\d+\.\d+$', cell.strip())  # Decimal number
                        for cell in first_row if cell.strip()
                    )

                    if looks_like_data and len(first_row) > 1:
                        # Add generic headers
                        max_cols = max(len(row) for row in rows)
                        headers = [f"Column_{i+1}" for i in range(max_cols)]
                        rows.insert(0, headers)
                        changes_made.append(f"Added generic headers for {max_cols} columns")

                # Standardize column counts
                max_cols = max(len(row) for row in rows) if rows else 0
                corrected_rows = []
                padding_added = 0

                for row in rows:
                    if len(row) < max_cols:
                        padded_row = row + [''] * (max_cols - len(row))
                        corrected_rows.append(padded_row)
                        padding_added += max_cols - len(row)
                    else:
                        corrected_rows.append(row)

                if padding_added > 0:
                    changes_made.append(f"Added {padding_added} empty cells to standardize column counts")

                # Write with proper formatting
                output = io.StringIO()
                writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)
                for row in corrected_rows:
                    writer.writerow(row)

                corrected_content = output.getvalue().rstrip('\n')

                if corrected_content != content:
                    if not changes_made:
                        changes_made.append("Standardized CSV formatting and quoting")

                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except Exception as e:
                errors.append(f"CSV parsing error: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"CSV correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_python(self, content: str) -> CorrectionResult:
        """Correct Python syntax issues using professional tools.

        Args:
            content: Python content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # First, try to parse as-is
            try:
                ast.parse(content)
                return CorrectionResult(True, content, [], [], ["Python syntax is already valid"])
            except SyntaxError:
                pass  # Continue with corrections

            # Method 1: Use black for professional Python formatting
            if self._check_tool_available('black'):
                success, stdout, stderr = self._run_external_tool(['black', '--code', content], "")
                if success and stdout.strip():
                    changes_made.append("Formatted using black")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"black warning: {stderr}")

            # Method 2: Use autopep8 for PEP 8 compliance
            if self._check_tool_available('autopep8'):
                success, stdout, stderr = self._run_external_tool(['autopep8', '-'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using autopep8")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"autopep8 warning: {stderr}")

            # Method 3: Use yapf for formatting
            if self._check_tool_available('yapf'):
                success, stdout, stderr = self._run_external_tool(['yapf'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using yapf")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"yapf warning: {stderr}")

            # Fallback: Basic corrections
            corrected_content = content
            lines = corrected_content.split('\n')

            # Fix 1: Convert tabs to spaces
            tab_fixes = 0
            corrected_lines = []
            for line in lines:
                if '\t' in line:
                    tab_fixes += 1
                    line = line.replace('\t', '    ')  # PEP 8: 4 spaces
                corrected_lines.append(line)

            if tab_fixes > 0:
                changes_made.append(f"Converted {tab_fixes} tabs to spaces")
                corrected_content = '\n'.join(corrected_lines)

            # Try to parse the corrected content
            try:
                ast.parse(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except SyntaxError as e:
                errors.append(f"Could not fully correct Python syntax: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"Python correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)
    
    def correct_xml(self, content: str) -> CorrectionResult:
        """Correct XML format issues.

        Args:
            content: XML content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []
        corrected_content = content

        try:
            import xml.etree.ElementTree as ET

            # First, try to parse as-is
            try:
                ET.fromstring(content)
                return CorrectionResult(True, content, [], [], ["XML is already valid"])
            except ET.ParseError:
                pass  # Continue with corrections

            # Fix 1: Add XML declaration if missing
            if not corrected_content.strip().startswith('<?xml'):
                corrected_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + corrected_content
                changes_made.append("Added XML declaration")

            # Fix 2: Basic tag balancing (very simplified)
            # This is a basic approach - full XML correction is complex
            lines = corrected_content.split('\n')
            corrected_lines = []

            for line in lines:
                # Fix basic attribute quoting
                if '=' in line and '"' not in line and "'" not in line:
                    # Very basic attribute fixing
                    line = re.sub(r'=([^>\s]+)', r'="\1"', line)
                    if line != corrected_content.split('\n')[corrected_lines.__len__()]:
                        changes_made.append("Fixed attribute quoting")

                corrected_lines.append(line)

            corrected_content = '\n'.join(corrected_lines)

            # Try to parse the corrected content
            try:
                ET.fromstring(corrected_content)
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)
            except ET.ParseError as e:
                errors.append(f"Could not fully correct XML: {str(e)}")
                return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"XML correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_markdown(self, content: str) -> CorrectionResult:
        """Correct Markdown format issues using professional tools.

        Args:
            content: Markdown content to correct

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use mdformat for professional markdown formatting
            if self._check_tool_available('mdformat'):
                success, stdout, stderr = self._run_external_tool(['mdformat', '-'], content)
                if success and stdout.strip():
                    changes_made.append("Formatted using mdformat")
                    return CorrectionResult(True, stdout.strip(), changes_made, errors, warnings)
                elif stderr:
                    warnings.append(f"mdformat warning: {stderr}")

            # Method 2: Use markdown-it-py / mistletoe for AST-based correction
            try:
                from markdown_it import MarkdownIt
                md = MarkdownIt()

                # Parse to AST and back (validates structure)
                tokens = md.parse(content)
                # For now, just validate that it parses
                changes_made.append("Validated using markdown-it-py")

                # Basic formatting fixes
                corrected_content = content

                # Fix link spacing [text] (url) -> [text](url)
                link_pattern = r'\[([^\]]+)\]\s+\(([^)]+)\)'
                if re.search(link_pattern, corrected_content):
                    corrected_content = re.sub(link_pattern, r'[\1](\2)', corrected_content)
                    changes_made.append("Fixed link formatting (removed spaces between text and URL)")

                # Fix trailing whitespace
                lines = corrected_content.split('\n')
                fixed_lines = [line.rstrip() for line in lines]
                if fixed_lines != lines:
                    changes_made.append("Removed trailing whitespace")
                    corrected_content = '\n'.join(fixed_lines)

                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("markdown-it-py not available")
            except Exception as e:
                warnings.append(f"markdown-it-py error: {str(e)}")

            # Method 3: Use markdownlint via subprocess if available
            if self._check_tool_available('markdownlint'):
                success, stdout, stderr = self._run_external_tool(['markdownlint', '--stdin'], content)
                if stderr:  # markdownlint outputs issues to stderr
                    warnings.append(f"markdownlint found issues: {stderr}")

            # Fallback: Basic corrections
            corrected_content = content
            lines = corrected_content.split('\n')

            # Fix 1: Header spacing
            header_fixes = 0
            corrected_lines = []
            for line in lines:
                if line.strip().startswith('#') and not re.match(r'^#+\s+', line.strip()):
                    header_level = len(line.strip()) - len(line.strip().lstrip('#'))
                    if header_level <= 6:
                        indent = len(line) - len(line.lstrip())
                        header_text = line.strip()[header_level:].strip()
                        corrected_line = ' ' * indent + '#' * header_level + ' ' + header_text
                        corrected_lines.append(corrected_line)
                        header_fixes += 1
                        continue
                corrected_lines.append(line)

            if header_fixes > 0:
                changes_made.append(f"Fixed {header_fixes} header spacing issues")

            # Fix 2: Remove trailing whitespace
            final_lines = [line.rstrip() for line in corrected_lines]
            trailing_fixes = sum(1 for orig, fixed in zip(corrected_lines, final_lines)
                               if orig != fixed and orig.strip())

            if trailing_fixes > 0:
                changes_made.append(f"Removed trailing whitespace from {trailing_fixes} lines")

            # Fix 3: Fix link formatting [text] (url) -> [text](url)
            corrected_content = '\n'.join(final_lines)
            link_pattern = r'\[([^\]]+)\]\s+\(([^)]+)\)'
            if re.search(link_pattern, corrected_content):
                corrected_content = re.sub(link_pattern, r'[\1](\2)', corrected_content)
                changes_made.append("Fixed link formatting (removed spaces between text and URL)")

            # Fix 4: Standardize list markers (*, +, - → -)
            lines = corrected_content.split('\n')
            marker_fixes = 0
            corrected_lines = []

            for line in lines:
                # Standardize list markers to use dash (-)
                if re.match(r'^(\s*)[\*\+]\s+', line):
                    # Replace * or + with -
                    fixed_line = re.sub(r'^(\s*)[\*\+](\s+)', r'\1-\2', line)
                    corrected_lines.append(fixed_line)
                    marker_fixes += 1
                else:
                    corrected_lines.append(line)

            if marker_fixes > 0:
                changes_made.append(f"Standardized {marker_fixes} list markers to use dashes")
                corrected_content = '\n'.join(corrected_lines)

            # Fix 5: Fix list formatting (ensure space after dash)
            list_pattern = r'^(\s*)-([^\s])'
            lines = corrected_content.split('\n')
            list_fixes = 0
            corrected_lines = []

            for line in lines:
                if re.match(list_pattern, line):
                    corrected_line = re.sub(list_pattern, r'\1- \2', line)
                    corrected_lines.append(corrected_line)
                    list_fixes += 1
                else:
                    corrected_lines.append(line)

            if list_fixes > 0:
                corrected_content = '\n'.join(corrected_lines)
                changes_made.append(f"Fixed {list_fixes} list formatting issues")

            # Fix 5: Balance code block markers
            code_block_count = corrected_content.count('```')
            if code_block_count % 2 != 0:
                corrected_content += '\n```'
                changes_made.append("Added missing closing code block marker")

            return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"Markdown correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_xlsx(self, content: str) -> CorrectionResult:
        """Correct XLSX/Excel format issues using professional tools.

        Args:
            content: XLSX content (base64 encoded or file path)

        Returns:
            CorrectionResult with corrected content and changes made
        """
        changes_made = []
        errors = []
        warnings = []

        try:
            # Method 1: Use pandas for Excel file handling
            try:
                import pandas as pd
                from io import BytesIO
                import base64

                # Try to decode if it's base64 encoded content
                try:
                    if isinstance(content, str) and len(content) > 100:
                        # Assume it's base64 encoded
                        excel_data = base64.b64decode(content)
                        excel_file = BytesIO(excel_data)
                    else:
                        # Assume it's a file path
                        excel_file = content

                    # Read Excel file
                    df = pd.read_excel(excel_file)

                    # Basic cleanup
                    # Remove completely empty rows
                    df_cleaned = df.dropna(how='all')

                    # Remove completely empty columns
                    df_cleaned = df_cleaned.dropna(axis=1, how='all')

                    if len(df_cleaned) != len(df):
                        changes_made.append(f"Removed {len(df) - len(df_cleaned)} empty rows")

                    if len(df_cleaned.columns) != len(df.columns):
                        changes_made.append(f"Removed {len(df.columns) - len(df_cleaned.columns)} empty columns")

                    # Convert back to Excel format
                    output = BytesIO()
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        df_cleaned.to_excel(writer, index=False)

                    # Return as base64 if input was base64
                    if isinstance(content, str) and len(content) > 100:
                        corrected_content = base64.b64encode(output.getvalue()).decode('utf-8')
                    else:
                        corrected_content = output.getvalue()

                    changes_made.append("Processed using pandas DataFrame")
                    return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

                except Exception as e:
                    warnings.append(f"pandas Excel processing error: {str(e)}")

            except ImportError:
                warnings.append("pandas not available for Excel processing")

            # Method 2: Use openpyxl directly
            try:
                import openpyxl
                from io import BytesIO
                import base64

                if isinstance(content, str) and len(content) > 100:
                    excel_data = base64.b64decode(content)
                    excel_file = BytesIO(excel_data)
                else:
                    excel_file = content

                workbook = openpyxl.load_workbook(excel_file)

                # Basic cleanup for each worksheet
                for sheet_name in workbook.sheetnames:
                    sheet = workbook[sheet_name]

                    # Remove empty rows from the end
                    max_row = sheet.max_row
                    while max_row > 1:
                        if all(cell.value is None for cell in sheet[max_row]):
                            sheet.delete_rows(max_row)
                            max_row -= 1
                        else:
                            break

                    # Remove empty columns from the end
                    max_col = sheet.max_column
                    while max_col > 1:
                        if all(sheet.cell(row, max_col).value is None for row in range(1, sheet.max_row + 1)):
                            sheet.delete_cols(max_col)
                            max_col -= 1
                        else:
                            break

                # Save the cleaned workbook
                output = BytesIO()
                workbook.save(output)

                if isinstance(content, str) and len(content) > 100:
                    corrected_content = base64.b64encode(output.getvalue()).decode('utf-8')
                else:
                    corrected_content = output.getvalue()

                changes_made.append("Cleaned using openpyxl")
                return CorrectionResult(True, corrected_content, changes_made, errors, warnings)

            except ImportError:
                warnings.append("openpyxl not available for Excel processing")
            except Exception as e:
                warnings.append(f"openpyxl error: {str(e)}")

            errors.append("No Excel processing libraries available (pandas, openpyxl)")
            return CorrectionResult(False, content, changes_made, errors, warnings)

        except Exception as e:
            errors.append(f"XLSX correction error: {str(e)}")
            return CorrectionResult(False, content, changes_made, errors, warnings)

    def correct_format(self, content: str, file_type: str) -> CorrectionResult:
        """Correct content based on file type.
        
        Args:
            content: Content to correct
            file_type: Type of file (json, yaml, csv, python, etc.)
            
        Returns:
            CorrectionResult with corrected content and changes made
        """
        if file_type.lower() == 'json':
            return self.correct_json(content)
        elif file_type.lower() in ['yaml', 'yml']:
            return self.correct_yaml(content)
        elif file_type.lower() == 'csv':
            return self.correct_csv(content)
        elif file_type.lower() in ['python', 'py']:
            return self.correct_python(content)
        elif file_type.lower() == 'xml':
            return self.correct_xml(content)
        elif file_type.lower() in ['markdown', 'md']:
            return self.correct_markdown(content)
        elif file_type.lower() in ['xlsx', 'excel']:
            return self.correct_xlsx(content)
        else:
            return CorrectionResult(
                False,
                content,
                [],
                [f"No correction available for {file_type} files"],
                []
            )
