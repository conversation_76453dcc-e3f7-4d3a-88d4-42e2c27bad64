#!/usr/bin/env python3
"""
Test the format correction system on the user's JSON content.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_user_json():
    """Test format correction on the user's JSON content."""
    
    # The user's JSON content (appears to have some issues)
    content = '''"sale_date": "2025-06-25",
"sale_price": 230000.0,
"improvements": 15000.0,
"depreciation_taken": 30132.0,
"monthly_depreciation": 243.0,
"other_income": 150000.0,
"filing_status": "Married Filing Jointly",
"state": "Massachusetts"'''
    
    print("🧪 Testing User's JSON Content")
    print("=" * 50)
    print("Original content:")
    print(content)
    print("\n" + "=" * 50)
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # First, let's validate the content
    print("📋 VALIDATION RESULTS:")
    validation_result = validator.validate_format(content, 'json')
    
    print(f"  Valid: {validation_result.is_valid}")
    print(f"  Correctable: {validation_result.is_correctable}")
    
    if validation_result.errors:
        print("  Errors:")
        for error in validation_result.errors:
            print(f"    - {error}")
    
    if validation_result.warnings:
        print("  Warnings:")
        for warning in validation_result.warnings:
            print(f"    - {warning}")
    
    if validation_result.correction_suggestions:
        print("  Suggestions:")
        for suggestion in validation_result.correction_suggestions:
            print(f"    - {suggestion}")
    
    print("\n" + "=" * 50)
    
    # Now let's try to correct it
    if validation_result.is_correctable:
        print("🔧 CORRECTION ATTEMPT:")
        correction_result = corrector.correct_format(content, 'json')
        
        print(f"  Success: {correction_result.success}")
        
        if correction_result.success:
            print("  Changes made:")
            for change in correction_result.changes_made:
                print(f"    - {change}")
            
            print("\n  Corrected content:")
            print(correction_result.corrected_content)
            
            # Verify the corrected content is valid JSON
            try:
                import json
                parsed = json.loads(correction_result.corrected_content)
                print(f"\n✅ Corrected JSON is valid! Contains {len(parsed)} properties.")
            except json.JSONDecodeError as e:
                print(f"\n❌ Corrected JSON is still invalid: {e}")
                
        else:
            print("  Correction failed:")
            for error in correction_result.errors:
                print(f"    - {error}")
    else:
        print("🚫 Content is not detected as correctable")
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")

if __name__ == "__main__":
    test_user_json()
