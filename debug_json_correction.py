#!/usr/bin/env python3
"""
Debug the JSON correction issue with the user's content.
"""

import os
import sys
import json

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def debug_json_correction():
    """Debug why JSON correction isn't working."""
    
    # User's actual content (missing braces)
    user_content = '''"sale_date": "2025-06-25",
"sale_price": 230000.0,
"improvements": 15000.0,
"depreciation_taken": 30132.0,
"monthly_depreciation": 243.0,
"other_income": 150000.0,
"filing_status": "Married Filing Jointly",
"state": "Massachusetts"'''
    
    print("🐛 DEBUGGING JSON CORRECTION")
    print("=" * 60)
    print("User's content:")
    print(repr(user_content))
    print("\nContent preview:")
    print(user_content[:200] + "..." if len(user_content) > 200 else user_content)
    print("\n" + "=" * 60)
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # Step 1: Test validation
    print("STEP 1: Validation")
    try:
        validation_result = validator.validate_format(user_content, 'json')
        print(f"  Valid: {validation_result.is_valid}")
        print(f"  Correctable: {validation_result.is_correctable}")
        print(f"  Errors: {validation_result.errors}")
        print(f"  Warnings: {validation_result.warnings}")
        print(f"  Suggestions: {validation_result.correction_suggestions}")
    except Exception as e:
        print(f"  ERROR in validation: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "-" * 40)
    
    # Step 2: Test what JSON parser says
    print("STEP 2: Direct JSON parsing test")
    try:
        json.loads(user_content)
        print("  JSON is valid (unexpected!)")
    except json.JSONDecodeError as e:
        print(f"  JSON error: {e}")
        print(f"  Error position: line {e.lineno}, column {e.colno}")
        print(f"  Error message: {e.msg}")
    
    print("\n" + "-" * 40)
    
    # Step 3: Test correction
    print("STEP 3: Correction attempt")
    try:
        correction_result = corrector.correct_format(user_content, 'json')
        print(f"  Success: {correction_result.success}")
        print(f"  Changes: {correction_result.changes_made}")
        print(f"  Errors: {correction_result.errors}")
        print(f"  Warnings: {correction_result.warnings}")
        
        if correction_result.success:
            print(f"  Corrected content preview:")
            preview = correction_result.corrected_content[:300]
            print(f"    {repr(preview)}...")
            
            # Test if corrected content is valid
            try:
                json.loads(correction_result.corrected_content)
                print("  ✅ Corrected content is valid JSON!")
            except json.JSONDecodeError as e:
                print(f"  ❌ Corrected content still invalid: {e}")
        
    except Exception as e:
        print(f"  ERROR in correction: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    
    # Step 4: Test with a simple case that should work
    print("STEP 4: Test with simple trailing comma (known working case)")
    simple_case = '{"name": "test", "value": 123,}'
    print(f"Simple case: {simple_case}")
    
    try:
        simple_validation = validator.validate_format(simple_case, 'json')
        print(f"  Simple case correctable: {simple_validation.is_correctable}")
        
        if simple_validation.is_correctable:
            simple_correction = corrector.correct_format(simple_case, 'json')
            print(f"  Simple case success: {simple_correction.success}")
            print(f"  Simple case changes: {simple_correction.changes_made}")
    except Exception as e:
        print(f"  ERROR with simple case: {e}")
    
    print("\n✅ Debug completed!")

if __name__ == "__main__":
    debug_json_correction()
