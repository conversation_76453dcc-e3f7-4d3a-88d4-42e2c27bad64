"""Text Filter Dialog for advanced text filtering functionality."""

import wx
from typing import Optional, Dict, Any
from .filter_criteria import TextFilterCriterion
import logging

logger = logging.getLogger(__name__)


class TextFilterDialog(wx.Dialog):
    """Dialog for creating advanced text filters."""
    
    def __init__(self, parent: wx.Window, column_name: str, current_criterion: Optional[TextFilterCriterion] = None):
        """
        Initialize the text filter dialog.
        
        Args:
            parent: Parent window
            column_name: Name of the column being filtered
            current_criterion: Existing text filter criterion (if any)
        """
        super().__init__(parent, title=f"Text Filters - {column_name}", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.column_name = column_name
        self.current_criterion = current_criterion
        self.filter_result: Optional[TextFilterCriterion] = None
        
        self._init_ui()
        self._bind_events()
        self._populate_current_values()
        
        # Set initial size and center
        self.SetSize((400, 300))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title_label = wx.StaticText(self, label=f"Show rows where '{self.column_name}':")
        title_font = title_label.GetFont()
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL, 10)
        
        # Filter condition panel
        condition_panel = self._create_condition_panel()
        main_sizer.Add(condition_panel, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Options panel
        options_panel = self._create_options_panel()
        main_sizer.Add(options_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
    
    def _create_condition_panel(self) -> wx.Panel:
        """Create the condition selection panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operator choice
        operator_label = wx.StaticText(panel, label="Condition:")
        sizer.Add(operator_label, 0, wx.BOTTOM, 5)
        
        self.operator_choice = wx.Choice(panel, choices=[
            "Contains",
            "Does Not Contain", 
            "Equals",
            "Does Not Equal",
            "Begins With",
            "Does Not Begin With",
            "Ends With", 
            "Does Not End With",
            "Is Empty",
            "Is Not Empty",
            "Matches Pattern (wildcards: * ?)"
        ])
        self.operator_choice.SetSelection(0)  # Default to "Contains"
        sizer.Add(self.operator_choice, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Value input
        value_label = wx.StaticText(panel, label="Value:")
        sizer.Add(value_label, 0, wx.BOTTOM, 5)
        
        self.value_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE)
        self.value_text.SetMinSize((-1, 60))
        sizer.Add(self.value_text, 1, wx.EXPAND | wx.BOTTOM, 10)
        
        # Help text
        self.help_text = wx.StaticText(panel, label="")
        self.help_text.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(self.help_text, 0, wx.EXPAND)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_options_panel(self) -> wx.Panel:
        """Create the options panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Case sensitivity option
        self.case_sensitive_cb = wx.CheckBox(panel, label="Case sensitive")
        sizer.Add(self.case_sensitive_cb, 0, wx.BOTTOM, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 5)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.operator_choice.Bind(wx.EVT_CHOICE, self._on_operator_change)
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _populate_current_values(self) -> None:
        """Populate the dialog with current criterion values."""
        if self.current_criterion:
            # Map operators to choice indices
            operator_map = {
                TextFilterCriterion.CONTAINS: 0,
                TextFilterCriterion.NOT_CONTAINS: 1,
                TextFilterCriterion.EQUALS: 2,
                TextFilterCriterion.NOT_EQUALS: 3,
                TextFilterCriterion.BEGINS_WITH: 4,
                TextFilterCriterion.NOT_BEGINS_WITH: 5,
                TextFilterCriterion.ENDS_WITH: 6,
                TextFilterCriterion.NOT_ENDS_WITH: 7,
                TextFilterCriterion.IS_EMPTY: 8,
                TextFilterCriterion.IS_NOT_EMPTY: 9,
                TextFilterCriterion.MATCHES_PATTERN: 10
            }
            
            index = operator_map.get(self.current_criterion.operator, 0)
            self.operator_choice.SetSelection(index)
            
            self.value_text.SetValue(self.current_criterion.value)
            self.case_sensitive_cb.SetValue(self.current_criterion.case_sensitive)
        
        self._update_ui_state()
    
    def _on_operator_change(self, event: wx.CommandEvent) -> None:
        """Handle operator selection changes."""
        self._update_ui_state()
    
    def _update_ui_state(self) -> None:
        """Update UI state based on selected operator."""
        selection = self.operator_choice.GetSelection()
        
        # Operators that don't need a value
        no_value_operators = [8, 9]  # "Is Empty", "Is Not Empty"
        
        if selection in no_value_operators:
            self.value_text.Enable(False)
            self.value_text.SetValue("")
            self.case_sensitive_cb.Enable(False)
        else:
            self.value_text.Enable(True)
            self.case_sensitive_cb.Enable(True)
        
        # Update help text
        help_texts = {
            0: "Show rows that contain the specified text anywhere in the cell",
            1: "Show rows that do not contain the specified text",
            2: "Show rows where the cell exactly matches the specified text", 
            3: "Show rows where the cell does not exactly match the specified text",
            4: "Show rows where the cell starts with the specified text",
            5: "Show rows where the cell does not start with the specified text",
            6: "Show rows where the cell ends with the specified text",
            7: "Show rows where the cell does not end with the specified text", 
            8: "Show rows where the cell is empty or contains only whitespace",
            9: "Show rows where the cell is not empty",
            10: "Show rows matching wildcard pattern (* = any text, ? = any character)"
        }
        
        help_text = help_texts.get(selection, "")
        self.help_text.SetLabel(help_text)
        self.help_text.Wrap(350)
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        selection = self.operator_choice.GetSelection()
        
        # Map choice indices to operators
        operators = [
            TextFilterCriterion.CONTAINS,
            TextFilterCriterion.NOT_CONTAINS,
            TextFilterCriterion.EQUALS,
            TextFilterCriterion.NOT_EQUALS,
            TextFilterCriterion.BEGINS_WITH,
            TextFilterCriterion.NOT_BEGINS_WITH,
            TextFilterCriterion.ENDS_WITH,
            TextFilterCriterion.NOT_ENDS_WITH,
            TextFilterCriterion.IS_EMPTY,
            TextFilterCriterion.IS_NOT_EMPTY,
            TextFilterCriterion.MATCHES_PATTERN
        ]
        
        operator = operators[selection]
        value = self.value_text.GetValue()
        case_sensitive = self.case_sensitive_cb.GetValue()
        
        # Validate input
        if operator not in [TextFilterCriterion.IS_EMPTY, TextFilterCriterion.IS_NOT_EMPTY]:
            if not value:
                wx.MessageBox("Please enter a value for the filter.", "Input Required", 
                            wx.OK | wx.ICON_WARNING)
                self.value_text.SetFocus()
                return
            
            # Validate wildcard patterns
            if operator == TextFilterCriterion.MATCHES_PATTERN:
                try:
                    # Test the pattern by converting wildcards to regex
                    import re
                    pattern = value.replace('*', '.*').replace('?', '.')
                    re.compile(pattern)
                except re.error as e:
                    wx.MessageBox(f"Invalid wildcard pattern: {value}\n\nError: {str(e)}\n\nUse * for any text and ? for any single character.", 
                                "Invalid Pattern", wx.OK | wx.ICON_WARNING)
                    self.value_text.SetFocus()
                    return
            
            # Check for very long values that might cause performance issues
            if len(value) > 1000:
                result = wx.MessageBox(f"The filter value is very long ({len(value)} characters).\nThis might slow down filtering.\n\nContinue anyway?", 
                                     "Long Filter Value", wx.YES_NO | wx.ICON_QUESTION)
                if result != wx.YES:
                    self.value_text.SetFocus()
                    return
        
        # Create the filter criterion
        self.filter_result = TextFilterCriterion(
            operator=operator,
            value=value,
            case_sensitive=case_sensitive
        )
        
        self.EndModal(wx.ID_OK)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filter_result = None
        self.EndModal(wx.ID_CANCEL)
    
    def get_filter_result(self) -> Optional[TextFilterCriterion]:
        """Get the filter result after dialog is closed."""
        return self.filter_result 