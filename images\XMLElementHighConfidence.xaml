<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
      <SolidColorBrush x:Key="white" Color="#ffffff" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M12,3V6.979a5.04,5.04,0,0,0-5,5L7,12H2V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7,12a5.07,5.07,0,0,0,.1,1H1.5L1,12.5V2.5L1.5,2h11l.5.5V7.08a5,5,0,0,0-1-.1V3H2v9Zm3.921-4.9L8.811,5l-.7.709L9.875,7.47A5.009,5.009,0,0,1,10.923,7.1ZM5.276,5,3.106,7.179v.707L5.277,10.06l.707-.707L4.168,7.533,5.985,5.705Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M16,11.979a4,4,0,1,1-4-4A4,4,0,0,1,16,11.979Z" />
              <GeometryDrawing Brush="{DynamicResource white}" Geometry="F1M14.208,11.15l-2.365,2.364-.711,0-1.34-1.372.715-.7.987,1.011L13.5,10.443Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
