"""Memory management module for JEdit2.

This module provides memory management functionality for JEdit2.
"""

import time
import threading
# import psutil # Moved to _update_stats to avoid import-time crash
import gc
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Iterator
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict
import bisect
import logging
from datetime import datetime
from jedit2.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorLevel, ErrorCategory


class MemoryPoolType(Enum):
    """Type of memory pool."""
    SMALL = "small"  # < 1MB
    MEDIUM = "medium"  # 1MB - 10MB
    LARGE = "large"  # 10MB - 100MB
    HUGE = "huge"  # > 100MB


@dataclass
class MemoryBlock:
    """Memory block in a pool."""
    address: int
    size: int
    used: bool
    last_used: float
    data: Any = None


@dataclass
class MemoryPool:
    """Memory pool for efficient allocation."""
    type: MemoryPoolType
    blocks: List[MemoryBlock]
    total_size: int
    used_size: int
    fragmentation: float


@dataclass
class MemoryStats:
    """Memory statistics."""
    total_memory: int
    used_memory: int
    free_memory: int
    swap_used: int
    swap_free: int
    fragmentation: float
    gc_count: int
    gc_time: float
    pool_stats: Dict[MemoryPoolType, MemoryPool]


class MemoryManager:
    """Memory manager for JEdit2."""
    
    def __init__(
        self,
        memory_limit: Optional[int] = None,
        gc_threshold: int = 80,
        defrag_threshold: float = 0.3,
        pool_sizes: Optional[Dict[MemoryPoolType, int]] = None
    ) -> None:
        """Initialize the memory manager.
        
        Args:
            memory_limit: Memory limit in bytes
            gc_threshold: Garbage collection threshold percentage
            defrag_threshold: Defragmentation threshold
            pool_sizes: Pool sizes in bytes
        """
        self.memory_limit = memory_limit
        self.gc_threshold = gc_threshold
        self.defrag_threshold = defrag_threshold
        
        # Initialize memory pools
        self.pools: Dict[MemoryPoolType, MemoryPool] = {}
        if pool_sizes is None:
            pool_sizes = {
                MemoryPoolType.SMALL: 1024 * 1024,  # 1MB
                MemoryPoolType.MEDIUM: 10 * 1024 * 1024,  # 10MB
                MemoryPoolType.LARGE: 100 * 1024 * 1024,  # 100MB
                MemoryPoolType.HUGE: 1024 * 1024 * 1024  # 1GB
            }
        
        for pool_type, size in pool_sizes.items():
            self.pools[pool_type] = MemoryPool(
                type=pool_type,
                blocks=[],
                total_size=size,
                used_size=0,
                fragmentation=0.0
            )
        
        # Initialize statistics
        self.stats = MemoryStats(
            total_memory=0,
            used_memory=0,
            free_memory=0,
            swap_used=0,
            swap_free=0,
            fragmentation=0.0,
            gc_count=0,
            gc_time=0.0,
            pool_stats={}
        )
        
        # Initialize monitoring
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        self._lock = threading.Lock()
        
        # Initialize logger
        self._logger = logging.getLogger("MemoryManager")
        
        self._memory_limit = 1024 * 1024 * 1024  # 1GB default limit
        self._cache_limit = 256 * 1024 * 1024  # 256MB default cache limit
        self._gc_threshold = 0.8  # 80% of memory limit
        self._monitoring = False
        self._memory_usage = 0
        self._cache_size = 0
        self._memory_stats = {
            "peak_usage": 0,
            "current_usage": 0,
            "cache_size": 0,
            "gc_count": 0,
            "gc_time": 0,
            "allocations": 0,
            "deallocations": 0
        }
    
    def start_monitoring(self) -> None:
        """Start memory monitoring."""
        if self._monitor_thread is None or not self._monitor_thread.is_alive():
            self._stop_monitoring.clear()
            self._monitor_thread = threading.Thread(
                target=self._monitor_memory,
                daemon=True
            )
            self._monitor_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop memory monitoring."""
        if self._monitor_thread is not None and self._monitor_thread.is_alive():
            self._stop_monitoring.set()
            self._monitor_thread.join()
    
    def _monitor_memory(self) -> None:
        """Monitor memory usage."""
        while not self._stop_monitoring.is_set():
            try:
                self._update_stats()
                self._check_memory_usage()
                time.sleep(1)
            except Exception as e:
                self._logger.error(f"Error monitoring memory: {e}")
    
    def _update_stats(self) -> None:
        """Update memory statistics."""
        import psutil  # Import locally to prevent startup crash
        with self._lock:
            # Get system memory info
            mem = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Update basic stats
            self.stats.total_memory = mem.total
            self.stats.used_memory = mem.used
            self.stats.free_memory = mem.free
            self.stats.swap_used = swap.used
            self.stats.swap_free = swap.free
            
            # Update pool stats
            for pool in self.pools.values():
                self.stats.pool_stats[pool.type] = pool
                
                # Calculate fragmentation
                if pool.total_size > 0:
                    free_blocks = [b for b in pool.blocks if not b.used]
                    if free_blocks:
                        max_free = max(b.size for b in free_blocks)
                        total_free = sum(b.size for b in free_blocks)
                        pool.fragmentation = 1 - (max_free / total_free)
                    else:
                        pool.fragmentation = 0.0
            
            # Update overall fragmentation
            total_frag = sum(p.fragmentation for p in self.pools.values())
            self.stats.fragmentation = total_frag / len(self.pools)
    
    def _check_memory_usage(self) -> None:
        """Check memory usage and take action if needed."""
        with self._lock:
            # Check memory limit
            if self.memory_limit is not None:
                if self.stats.used_memory > self.memory_limit:
                    self._logger.warning("Memory limit exceeded")
                    self.optimize_memory()
            
            # Check GC threshold
            if self.stats.used_memory / self.stats.total_memory * 100 > self.gc_threshold:
                self._logger.info("GC threshold exceeded")
                self._run_gc()
            
            # Check fragmentation
            if self.stats.fragmentation > self.defrag_threshold:
                self._logger.info("Fragmentation threshold exceeded")
                self.defragment_memory()
    
    def _run_gc(self) -> None:
        """Run garbage collection."""
        start_time = time.time()
        gc.collect()
        end_time = time.time()
        
        self.stats.gc_count += 1
        self.stats.gc_time += end_time - start_time
    
    def allocate_memory(self, size: int) -> Optional[MemoryBlock]:
        """Allocate memory from a pool.
        
        Args:
            size: Size in bytes
        
        Returns:
            Memory block or None if allocation failed
        """
        with self._lock:
            # Determine pool type
            if size < 1024 * 1024:  # < 1MB
                pool_type = MemoryPoolType.SMALL
            elif size < 10 * 1024 * 1024:  # < 10MB
                pool_type = MemoryPoolType.MEDIUM
            elif size < 100 * 1024 * 1024:  # < 100MB
                pool_type = MemoryPoolType.LARGE
            else:  # >= 100MB
                pool_type = MemoryPoolType.HUGE
            
            pool = self.pools[pool_type]
            
            # Try to find a free block
            for block in pool.blocks:
                if not block.used and block.size >= size:
                    block.used = True
                    block.last_used = time.time()
                    pool.used_size += size
                    return block
            
            # Create new block if possible
            if pool.used_size + size <= pool.total_size:
                block = MemoryBlock(
                    address=id(pool) + len(pool.blocks),
                    size=size,
                    used=True,
                    last_used=time.time()
                )
                pool.blocks.append(block)
                pool.used_size += size
                return block
            
            return None
    
    def free_memory(self, block: MemoryBlock) -> None:
        """Free memory block.
        
        Args:
            block: Memory block to free
        """
        with self._lock:
            for pool in self.pools.values():
                if block in pool.blocks:
                    block.used = False
                    pool.used_size -= block.size
                    break
    
    def defragment_memory(self) -> None:
        """Defragment memory pools."""
        with self._lock:
            for pool in self.pools.values():
                # Sort blocks by address
                pool.blocks.sort(key=lambda b: b.address)
                
                # Merge adjacent free blocks
                i = 0
                while i < len(pool.blocks) - 1:
                    if not pool.blocks[i].used and not pool.blocks[i + 1].used:
                        pool.blocks[i].size += pool.blocks[i + 1].size
                        pool.blocks.pop(i + 1)
                    else:
                        i += 1
    
    def optimize_memory(self) -> None:
        """Optimize memory usage."""
        with self._lock:
            # Run garbage collection
            self._run_gc()
            
            # Defragment memory
            self.defragment_memory()
            
            # Clear unused blocks
            for pool in self.pools.values():
                pool.blocks = [b for b in pool.blocks if b.used]
    
    def get_stats(self) -> MemoryStats:
        """Get memory statistics.
        
        Returns:
            Memory statistics
        """
        with self._lock:
            return self.stats
    
    def get_memory_stats(self) -> MemoryStats:
        """Get memory statistics (alias for get_stats).
        
        Returns:
            Memory statistics
        """
        return self.get_stats()
    
    def set_memory_limit(self, limit: Optional[int]) -> None:
        """Set memory limit.
        
        Args:
            limit: Memory limit in bytes
        """
        with self._lock:
            self.memory_limit = limit
    
    def set_gc_threshold(self, threshold: int) -> None:
        """Set garbage collection threshold.
        
        Args:
            threshold: Threshold percentage
        """
        with self._lock:
            self.gc_threshold = threshold
    
    def set_defrag_threshold(self, threshold: float) -> None:
        """Set defragmentation threshold.
        
        Args:
            threshold: Threshold value
        """
        with self._lock:
            self.defrag_threshold = threshold
    
    def __del__(self) -> None:
        """Clean up when destroyed."""
        self.stop_monitoring()
    
    def track_allocation(self, size: int) -> None:
        """Track memory allocation."""
        with self._lock:
            self._memory_stats["allocations"] += 1
            self._memory_usage += size
    
    def track_deallocation(self, size: int) -> None:
        """Track memory deallocation."""
        with self._lock:
            self._memory_stats["deallocations"] += 1
            self._memory_usage -= size
    
    def check_memory_leak(self) -> bool:
        """Check for potential memory leaks."""
        with self._lock:
            # If allocations are much higher than deallocations, there might be a leak
            return self._memory_stats["allocations"] > self._memory_stats["deallocations"] * 1.5
    
    def get_memory_usage(self) -> int:
        """Get current memory usage in bytes."""
        with self._lock:
            return self._memory_usage
    
    def get_cache_size(self) -> int:
        """Get current cache size in bytes."""
        with self._lock:
            return self._cache_size
    
    def is_memory_critical(self) -> bool:
        """Check if memory usage is critical."""
        with self._lock:
            return self._memory_usage > self._memory_limit * 0.9  # 90% of limit 