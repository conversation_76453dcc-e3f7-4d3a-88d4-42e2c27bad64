#!/usr/bin/env python3
"""
Production Monitoring System for JEdit2 AI

Provides real-time monitoring, alerting, and feedback loops
for the AI system in production environments.
"""

import logging
import json
import time
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from collections import deque, defaultdict


class AlertLevel(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MonitoringMetric(Enum):
    """Types of metrics to monitor."""

    SUCCESS_RATE = "success_rate"
    RESPONSE_TIME = "response_time"
    ERROR_RATE = "error_rate"
    QUERY_COMPLEXITY = "query_complexity"
    PATTERN_DETECTION = "pattern_detection"
    THROUGHPUT = "throughput"
    RESOURCE_USAGE = "resource_usage"


@dataclass
class MonitoringAlert:
    """Represents a monitoring alert."""

    alert_id: str
    level: AlertLevel
    metric: MonitoringMetric
    message: str
    value: float
    threshold: float
    timestamp: str
    resolved: bool = False
    resolution_time: Optional[str] = None


@dataclass
class PerformanceMetrics:
    """Real-time performance metrics."""

    timestamp: str
    success_rate: float
    average_response_time: float
    error_rate: float
    total_queries: int
    successful_queries: int
    failed_queries: int
    patterns_detected: int
    active_users: int
    system_load: float


@dataclass
class FeedbackAction:
    """Represents an automated feedback action."""

    action_id: str
    trigger_condition: str
    action_type: str
    parameters: Dict[str, Any]
    success: bool
    timestamp: str
    result_message: str


class ProductionMonitor:
    """
    Production monitoring system for AI performance and reliability.

    Features:
    - Real-time performance tracking
    - Automated alerting system
    - Adaptive feedback loops
    - Performance analytics
    - System health monitoring
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the production monitoring system.

        Args:
            config: Monitoring configuration
        """
        self.logger = logging.getLogger(__name__)

        # Configuration
        self.config = self._initialize_config(config or {})

        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None

        # Data storage
        self.metrics_history = deque(maxlen=self.config["metrics_history_size"])
        self.active_alerts: Dict[str, MonitoringAlert] = {}
        self.alert_history = deque(maxlen=self.config["alert_history_size"])
        self.feedback_actions: List[FeedbackAction] = []

        # Real-time counters
        self.query_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.pattern_count = 0
        self.response_times = deque(maxlen=100)  # Last 100 response times

        # Time windows for rate calculations
        self.time_windows = {
            "1min": deque(maxlen=60),
            "5min": deque(maxlen=300),
            "15min": deque(maxlen=900),
            "1hour": deque(maxlen=3600),
        }

        # Alert handlers
        self.alert_handlers: Dict[AlertLevel, List[Callable]] = {
            AlertLevel.INFO: [],
            AlertLevel.WARNING: [],
            AlertLevel.ERROR: [],
            AlertLevel.CRITICAL: [],
        }

        # Feedback loop handlers
        self.feedback_handlers: Dict[str, Callable] = {}

        self._initialize_default_feedback_loops()

    def _initialize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize monitoring configuration with defaults."""
        default_config = {
            "monitoring_interval": 30.0,  # seconds
            "metrics_history_size": 1000,
            "alert_history_size": 500,
            "performance_thresholds": {
                MonitoringMetric.SUCCESS_RATE: {
                    "warning": 0.95,
                    "error": 0.90,
                    "critical": 0.85,
                },
                MonitoringMetric.RESPONSE_TIME: {
                    "warning": 5.0,
                    "error": 10.0,
                    "critical": 15.0,
                },
                MonitoringMetric.ERROR_RATE: {
                    "warning": 0.05,
                    "error": 0.10,
                    "critical": 0.15,
                },
                MonitoringMetric.THROUGHPUT: {
                    "warning": 10.0,  # queries per minute
                    "error": 5.0,
                    "critical": 2.0,
                },
            },
            "enable_automated_feedback": True,
            "enable_adaptive_thresholds": True,
            "enable_performance_analytics": True,
            "alert_cooldown_period": 300,  # 5 minutes
            "export_metrics_interval": 3600,  # 1 hour
            "metrics_export_path": "monitoring/metrics",
            "enable_real_time_alerts": True,
        }

        # Merge with provided config
        default_config.update(config)
        return default_config

    def _initialize_default_feedback_loops(self) -> None:
        """Initialize default automated feedback loops."""

        # Register default feedback handlers
        self.register_feedback_handler("high_error_rate", self._handle_high_error_rate)
        self.register_feedback_handler(
            "slow_response_time", self._handle_slow_response_time
        )
        self.register_feedback_handler(
            "low_success_rate", self._handle_low_success_rate
        )
        self.register_feedback_handler("pattern_anomaly", self._handle_pattern_anomaly)

    def register_alert_handler(self, level: AlertLevel, handler: Callable) -> None:
        """Register a custom alert handler."""
        self.alert_handlers[level].append(handler)
        self.logger.info(f"Registered alert handler for {level.value}")

    def register_feedback_handler(self, condition: str, handler: Callable) -> None:
        """Register a custom feedback loop handler."""
        self.feedback_handlers[condition] = handler
        self.logger.info(f"Registered feedback handler for {condition}")

    def record_query_result(
        self,
        query: str,
        success: bool,
        response_time: float,
        analysis_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Record the result of an AI query for monitoring.

        Args:
            query: The user query
            success: Whether the query was successful
            response_time: Time taken to process the query
            analysis_data: Additional analysis data
        """
        timestamp = time.time()

        # Update counters
        self.query_count += 1
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1

        # Record response time
        self.response_times.append(response_time)

        # Record patterns if available
        if analysis_data and analysis_data.get("patterns_detected"):
            self.pattern_count += len(analysis_data["patterns_detected"])

        # Update time windows
        query_data = {
            "timestamp": timestamp,
            "success": success,
            "response_time": response_time,
            "query_length": len(query),
        }

        for window in self.time_windows.values():
            window.append(query_data)

        # Check for immediate alerts
        if self.config["enable_real_time_alerts"]:
            self._check_real_time_alerts()

    def _check_real_time_alerts(self) -> None:
        """Check for alert conditions in real-time."""
        current_metrics = self.get_current_metrics()

        # Check each metric against thresholds
        for metric, value in [
            (MonitoringMetric.SUCCESS_RATE, current_metrics.success_rate),
            (MonitoringMetric.RESPONSE_TIME, current_metrics.average_response_time),
            (MonitoringMetric.ERROR_RATE, current_metrics.error_rate),
        ]:
            self._check_metric_threshold(metric, value)

    def _check_metric_threshold(self, metric: MonitoringMetric, value: float) -> None:
        """Check a specific metric against its thresholds."""
        if metric not in self.config["performance_thresholds"]:
            return

        thresholds = self.config["performance_thresholds"][metric]
        alert_level = None
        threshold_value = None

        # Determine alert level (check from most severe to least)
        if metric in [MonitoringMetric.SUCCESS_RATE, MonitoringMetric.THROUGHPUT]:
            # For these metrics, lower values are worse
            if value <= thresholds.get("critical", 0):
                alert_level = AlertLevel.CRITICAL
                threshold_value = thresholds["critical"]
            elif value <= thresholds.get("error", 0):
                alert_level = AlertLevel.ERROR
                threshold_value = thresholds["error"]
            elif value <= thresholds.get("warning", 0):
                alert_level = AlertLevel.WARNING
                threshold_value = thresholds["warning"]
        else:
            # For these metrics, higher values are worse
            if value >= thresholds.get("critical", float("inf")):
                alert_level = AlertLevel.CRITICAL
                threshold_value = thresholds["critical"]
            elif value >= thresholds.get("error", float("inf")):
                alert_level = AlertLevel.ERROR
                threshold_value = thresholds["error"]
            elif value >= thresholds.get("warning", float("inf")):
                alert_level = AlertLevel.WARNING
                threshold_value = thresholds["warning"]

        if alert_level:
            self._create_alert(metric, alert_level, value, threshold_value)

    def _create_alert(
        self,
        metric: MonitoringMetric,
        level: AlertLevel,
        value: float,
        threshold: float,
    ) -> None:
        """Create and process a new alert."""
        alert_id = f"{metric.value}_{level.value}_{int(time.time())}"

        # Check if we already have an active alert for this metric
        existing_alert_key = f"{metric.value}_{level.value}"
        if existing_alert_key in self.active_alerts:
            # Check cooldown period
            existing_alert = self.active_alerts[existing_alert_key]
            last_alert_time = datetime.fromisoformat(existing_alert.timestamp)
            if datetime.now() - last_alert_time < timedelta(
                seconds=self.config["alert_cooldown_period"]
            ):
                return  # Skip due to cooldown

        # Create new alert
        alert = MonitoringAlert(
            alert_id=alert_id,
            level=level,
            metric=metric,
            message=self._generate_alert_message(metric, level, value, threshold),
            value=value,
            threshold=threshold,
            timestamp=datetime.now().isoformat(),
        )

        # Store alert
        self.active_alerts[existing_alert_key] = alert
        self.alert_history.append(alert)

        # Process alert
        self._process_alert(alert)

    def _generate_alert_message(
        self,
        metric: MonitoringMetric,
        level: AlertLevel,
        value: float,
        threshold: float,
    ) -> str:
        """Generate a human-readable alert message."""
        metric_name = metric.value.replace("_", " ").title()

        if metric in [MonitoringMetric.SUCCESS_RATE]:
            return f"{metric_name} {level.value.upper()}: {value:.1%} (threshold: {threshold:.1%})"
        elif metric in [MonitoringMetric.RESPONSE_TIME]:
            return f"{metric_name} {level.value.upper()}: {value:.2f}s (threshold: {threshold:.2f}s)"
        elif metric in [MonitoringMetric.ERROR_RATE]:
            return f"{metric_name} {level.value.upper()}: {value:.1%} (threshold: {threshold:.1%})"
        else:
            return f"{metric_name} {level.value.upper()}: {value:.2f} (threshold: {threshold:.2f})"

    def _process_alert(self, alert: MonitoringAlert) -> None:
        """Process an alert by calling registered handlers."""
        try:
            # Log the alert
            log_method = getattr(
                self.logger, alert.level.value.lower(), self.logger.info
            )
            log_method(f"ALERT: {alert.message}")

            # Call registered alert handlers
            for handler in self.alert_handlers[alert.level]:
                try:
                    handler(alert)
                except Exception as e:
                    self.logger.error(f"Alert handler failed: {e}")

            # Trigger automated feedback if enabled
            if self.config["enable_automated_feedback"]:
                self._trigger_feedback_loops(alert)

        except Exception as e:
            self.logger.error(f"Failed to process alert: {e}")

    def _trigger_feedback_loops(self, alert: MonitoringAlert) -> None:
        """Trigger appropriate feedback loops based on alert."""
        feedback_conditions = []

        # Map alerts to feedback conditions
        if alert.metric == MonitoringMetric.ERROR_RATE and alert.level in [
            AlertLevel.ERROR,
            AlertLevel.CRITICAL,
        ]:
            feedback_conditions.append("high_error_rate")
        elif alert.metric == MonitoringMetric.RESPONSE_TIME and alert.level in [
            AlertLevel.WARNING,
            AlertLevel.ERROR,
        ]:
            feedback_conditions.append("slow_response_time")
        elif alert.metric == MonitoringMetric.SUCCESS_RATE and alert.level in [
            AlertLevel.ERROR,
            AlertLevel.CRITICAL,
        ]:
            feedback_conditions.append("low_success_rate")

        # Execute feedback handlers
        for condition in feedback_conditions:
            if condition in self.feedback_handlers:
                try:
                    result = self.feedback_handlers[condition](alert)
                    self._record_feedback_action(condition, result, alert)
                except Exception as e:
                    self.logger.error(f"Feedback handler '{condition}' failed: {e}")
                    self._record_feedback_action(condition, False, alert, str(e))

    def _record_feedback_action(
        self,
        condition: str,
        success: bool,
        alert: MonitoringAlert,
        error_message: str = "",
    ) -> None:
        """Record the result of a feedback action."""
        action = FeedbackAction(
            action_id=f"feedback_{int(time.time())}_{len(self.feedback_actions)}",
            trigger_condition=condition,
            action_type="automated_response",
            parameters={"alert_id": alert.alert_id, "metric": alert.metric.value},
            success=success,
            timestamp=datetime.now().isoformat(),
            result_message=(
                error_message if error_message else "Action completed successfully"
            ),
        )

        self.feedback_actions.append(action)
        self.logger.info(
            f"Feedback action recorded: {condition} - {'Success' if success else 'Failed'}"
        )

    def _handle_high_error_rate(self, alert: MonitoringAlert) -> bool:
        """Handle high error rate feedback loop."""
        try:
            self.logger.warning("Automated response: High error rate detected")

            # Could implement actions like:
            # - Reduce query complexity thresholds
            # - Enable additional validation
            # - Switch to simpler AI models temporarily
            # - Alert human operators

            # For demo, just log the action
            self.logger.info("Feedback action: Error rate monitoring increased")
            return True

        except Exception as e:
            self.logger.error(f"High error rate feedback failed: {e}")
            return False

    def _handle_slow_response_time(self, alert: MonitoringAlert) -> bool:
        """Handle slow response time feedback loop."""
        try:
            self.logger.warning("Automated response: Slow response time detected")

            # Could implement actions like:
            # - Optimize query processing
            # - Reduce batch sizes
            # - Scale up resources
            # - Enable caching

            self.logger.info("Feedback action: Response time optimization enabled")
            return True

        except Exception as e:
            self.logger.error(f"Slow response time feedback failed: {e}")
            return False

    def _handle_low_success_rate(self, alert: MonitoringAlert) -> bool:
        """Handle low success rate feedback loop."""
        try:
            self.logger.warning("Automated response: Low success rate detected")

            # Could implement actions like:
            # - Enable more robust parsing
            # - Increase retry attempts
            # - Switch to backup AI models
            # - Enable human oversight

            self.logger.info(
                "Feedback action: Success rate improvement measures enabled"
            )
            return True

        except Exception as e:
            self.logger.error(f"Low success rate feedback failed: {e}")
            return False

    def _handle_pattern_anomaly(self, alert: MonitoringAlert) -> bool:
        """Handle pattern detection anomaly feedback loop."""
        try:
            self.logger.warning("Automated response: Pattern anomaly detected")

            # Could implement actions like:
            # - Update pattern detection models
            # - Retrain on recent data
            # - Alert data scientists

            self.logger.info("Feedback action: Pattern analysis updated")
            return True

        except Exception as e:
            self.logger.error(f"Pattern anomaly feedback failed: {e}")
            return False

    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current real-time performance metrics."""
        timestamp = datetime.now().isoformat()

        # Calculate success rate
        total_queries = self.query_count
        success_rate = (
            (self.success_count / total_queries) if total_queries > 0 else 1.0
        )
        error_rate = (self.failure_count / total_queries) if total_queries > 0 else 0.0

        # Calculate average response time
        avg_response_time = (
            sum(self.response_times) / len(self.response_times)
            if self.response_times
            else 0.0
        )

        # Get system metrics (simplified)
        import psutil

        system_load = psutil.cpu_percent()

        return PerformanceMetrics(
            timestamp=timestamp,
            success_rate=success_rate,
            average_response_time=avg_response_time,
            error_rate=error_rate,
            total_queries=total_queries,
            successful_queries=self.success_count,
            failed_queries=self.failure_count,
            patterns_detected=self.pattern_count,
            active_users=1,  # Simplified
            system_load=system_load,
        )

    def get_metrics_history(
        self, time_range: str = "1hour"
    ) -> List[PerformanceMetrics]:
        """Get historical metrics for a time range."""
        if time_range not in self.time_windows:
            time_range = "1hour"

        # Convert recent data points to metrics
        historical_metrics = []
        window_data = list(self.time_windows[time_range])

        # Group by time intervals and calculate metrics
        if window_data:
            # For demo, return current metrics
            historical_metrics.append(self.get_current_metrics())

        return historical_metrics

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of current alerts and alert history."""
        active_count_by_level = defaultdict(int)
        for alert in self.active_alerts.values():
            if not alert.resolved:
                active_count_by_level[alert.level.value] += 1

        recent_alerts = list(self.alert_history)[-10:]  # Last 10 alerts

        return {
            "active_alerts": len(
                [a for a in self.active_alerts.values() if not a.resolved]
            ),
            "active_by_level": dict(active_count_by_level),
            "total_alerts_today": len(
                [
                    a
                    for a in self.alert_history
                    if datetime.fromisoformat(a.timestamp).date()
                    == datetime.now().date()
                ]
            ),
            "recent_alerts": [asdict(alert) for alert in recent_alerts],
            "alert_trends": self._calculate_alert_trends(),
        }

    def _calculate_alert_trends(self) -> Dict[str, Any]:
        """Calculate alert trends over time."""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_day = now - timedelta(days=1)

        hour_alerts = [
            a
            for a in self.alert_history
            if datetime.fromisoformat(a.timestamp) > last_hour
        ]
        day_alerts = [
            a
            for a in self.alert_history
            if datetime.fromisoformat(a.timestamp) > last_day
        ]

        return {
            "alerts_last_hour": len(hour_alerts),
            "alerts_last_day": len(day_alerts),
            "trending_metrics": [
                metric.value
                for metric, count in defaultdict(
                    int, [(a.metric, 1) for a in hour_alerts]
                ).items()
                if count >= 2
            ],
        }

    def get_feedback_summary(self) -> Dict[str, Any]:
        """Get summary of feedback loop actions."""
        recent_actions = self.feedback_actions[-20:]  # Last 20 actions

        success_rate = (
            sum(1 for a in recent_actions if a.success) / len(recent_actions)
            if recent_actions
            else 1.0
        )

        action_types = defaultdict(int)
        for action in recent_actions:
            action_types[action.trigger_condition] += 1

        return {
            "total_actions": len(self.feedback_actions),
            "recent_actions": len(recent_actions),
            "success_rate": success_rate,
            "action_types": dict(action_types),
            "recent_actions_detail": [asdict(action) for action in recent_actions],
        }

    def start_monitoring(self) -> None:
        """Start the production monitoring system."""
        if self.is_monitoring:
            self.logger.warning("Monitoring is already running")
            return

        self.is_monitoring = True

        def monitoring_loop():
            while self.is_monitoring:
                try:
                    # Collect current metrics
                    current_metrics = self.get_current_metrics()
                    self.metrics_history.append(current_metrics)

                    # Check adaptive thresholds
                    if self.config["enable_adaptive_thresholds"]:
                        self._update_adaptive_thresholds()

                    # Export metrics if scheduled
                    if self.config["export_metrics_interval"]:
                        self._maybe_export_metrics()

                    # Cleanup old data
                    self._cleanup_old_data()

                except Exception as e:
                    self.logger.error(f"Monitoring loop error: {e}")

                time.sleep(self.config["monitoring_interval"])

        self.monitor_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitor_thread.start()

        self.logger.info("Production monitoring started")

    def stop_monitoring(self) -> None:
        """Stop the production monitoring system."""
        self.is_monitoring = False

        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)

        self.logger.info("Production monitoring stopped")

    def _update_adaptive_thresholds(self) -> None:
        """Update thresholds based on historical performance."""
        # Simplified adaptive threshold logic
        if len(self.metrics_history) < 10:
            return

        # Calculate baseline metrics from recent history
        recent_metrics = list(self.metrics_history)[-50:]  # Last 50 data points

        if recent_metrics:
            avg_success_rate = sum(m.success_rate for m in recent_metrics) / len(
                recent_metrics
            )
            avg_response_time = sum(
                m.average_response_time for m in recent_metrics
            ) / len(recent_metrics)

            # Adjust thresholds slightly based on recent performance
            current_success_threshold = self.config["performance_thresholds"][
                MonitoringMetric.SUCCESS_RATE
            ]["warning"]
            current_response_threshold = self.config["performance_thresholds"][
                MonitoringMetric.RESPONSE_TIME
            ]["warning"]

            # Gentle adaptive adjustment (move 10% toward recent average)
            new_success_threshold = (
                current_success_threshold * 0.9 + avg_success_rate * 0.1
            )
            new_response_threshold = (
                current_response_threshold * 0.9 + avg_response_time * 1.2 * 0.1
            )

            # Apply reasonable bounds
            new_success_threshold = max(0.8, min(0.98, new_success_threshold))
            new_response_threshold = max(1.0, min(30.0, new_response_threshold))

            # Update thresholds
            self.config["performance_thresholds"][MonitoringMetric.SUCCESS_RATE][
                "warning"
            ] = new_success_threshold
            self.config["performance_thresholds"][MonitoringMetric.RESPONSE_TIME][
                "warning"
            ] = new_response_threshold

    def _maybe_export_metrics(self) -> None:
        """Export metrics if it's time to do so."""
        # Simplified export logic - could be enhanced with actual file export
        if len(self.metrics_history) > 0:
            self.logger.debug(
                f"Metrics export: {len(self.metrics_history)} data points available"
            )

    def _cleanup_old_data(self) -> None:
        """Clean up old monitoring data to prevent memory issues."""
        # Cleanup old alerts (keep active ones)
        cutoff_time = datetime.now() - timedelta(days=1)

        self.active_alerts = {
            k: v
            for k, v in self.active_alerts.items()
            if not v.resolved or datetime.fromisoformat(v.timestamp) > cutoff_time
        }

        # Cleanup old feedback actions
        if len(self.feedback_actions) > 1000:
            self.feedback_actions = self.feedback_actions[-500:]  # Keep last 500

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring system status."""
        return {
            "is_monitoring": self.is_monitoring,
            "uptime": "monitoring_started",  # Could calculate actual uptime
            "metrics_collected": len(self.metrics_history),
            "active_alerts": len(
                [a for a in self.active_alerts.values() if not a.resolved]
            ),
            "feedback_actions": len(self.feedback_actions),
            "current_metrics": asdict(self.get_current_metrics()),
            "config": self.config,
        }

    def export_monitoring_report(self, filepath: str) -> bool:
        """Export comprehensive monitoring report."""
        try:
            report = {
                "monitoring_status": self.get_monitoring_status(),
                "current_metrics": asdict(self.get_current_metrics()),
                "alert_summary": self.get_alert_summary(),
                "feedback_summary": self.get_feedback_summary(),
                "metrics_history": [
                    asdict(m) for m in list(self.metrics_history)[-100:]
                ],  # Last 100
                "export_timestamp": datetime.now().isoformat(),
            }

            with open(filepath, "w") as f:
                json.dump(report, f, indent=2, default=str)

            self.logger.info(f"Monitoring report exported to {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export monitoring report: {e}")
            return False


def main():
    """Demo the production monitoring system."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    print("📊 Production Monitoring System - Demo Mode")
    print("=" * 60)

    # Create monitoring system
    monitor = ProductionMonitor(
        config={
            "monitoring_interval": 1.0,  # Fast for demo
            "enable_automated_feedback": True,
            "performance_thresholds": {
                MonitoringMetric.SUCCESS_RATE: {
                    "warning": 0.9,
                    "error": 0.8,
                    "critical": 0.7,
                }
            },
        }
    )

    print("✅ Production monitor initialized")

    # Start monitoring
    monitor.start_monitoring()
    print("✅ Monitoring started")

    # Simulate some AI queries
    import random

    print("\n🔄 Simulating AI queries...")
    for i in range(20):
        success = random.random() > 0.2  # 80% success rate
        response_time = random.uniform(0.5, 3.0)

        monitor.record_query_result(
            query=f"test query {i}",
            success=success,
            response_time=response_time,
            analysis_data={
                "patterns_detected": ["test_pattern"] if random.random() > 0.5 else []
            },
        )

        time.sleep(0.1)  # Small delay

    # Wait for monitoring cycle
    time.sleep(2)

    # Get status
    status = monitor.get_monitoring_status()
    print(f"\nMonitoring Status:")
    print(f"Metrics Collected: {status['metrics_collected']}")
    print(f"Active Alerts: {status['active_alerts']}")
    print(f"Success Rate: {status['current_metrics']['success_rate']:.1%}")
    print(
        f"Avg Response Time: {status['current_metrics']['average_response_time']:.2f}s"
    )

    # Get alert summary
    alert_summary = monitor.get_alert_summary()
    print(f"\nAlert Summary:")
    print(f"Active Alerts: {alert_summary['active_alerts']}")
    print(f"Alerts Today: {alert_summary['total_alerts_today']}")

    # Get feedback summary
    feedback_summary = monitor.get_feedback_summary()
    print(f"\nFeedback Summary:")
    print(f"Total Actions: {feedback_summary['total_actions']}")
    print(f"Success Rate: {feedback_summary['success_rate']:.1%}")

    # Export report
    report_path = "monitoring_demo_report.json"
    if monitor.export_monitoring_report(report_path):
        print(f"✅ Report exported to {report_path}")

    # Stop monitoring
    monitor.stop_monitoring()
    print("✅ Monitoring stopped")

    print("\n✅ Production monitoring demo completed")


if __name__ == "__main__":
    main()
