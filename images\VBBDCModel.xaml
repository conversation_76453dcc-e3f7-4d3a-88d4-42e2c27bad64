<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4,3.97V6H5V5.261a7.863,7.863,0,0,0,3.5.709A7.863,7.863,0,0,0,12,5.261V12c0,.29-1.227,1-3.5,1-.177,0-.336-.011-.5-.019v1c.166.009.332.019.5.019,2.236,0,4.5-.686,4.5-2V3.97C13,1.343,4,1.343,4,3.97Zm4.5,1c-2.273,0-3.5-.71-3.5-1s1.227-1,3.5-1,3.5.71,3.5,1S10.773,4.97,8.5,4.97Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M.3,15.5v-8H6.7v8Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M12.5,4v8c0,.829-1.791,1.5-4,1.5-.17,0-.5-.012-.5-.012v-6.4L6.914,6H4.5V4c0-.828,1.791-1.5,4-1.5S12.5,3.172,12.5,4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5,12H4V11H5ZM5,9H4v1H5ZM3,9H2v1H3Zm0,2H2v1H3ZM7,7.5V16H0V7.5L.5,7h6ZM6,8H1v7H3V13H4v2H6Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
