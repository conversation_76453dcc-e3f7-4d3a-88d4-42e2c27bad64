#!/usr/bin/env python3
"""
Test Case Generator for JEdit2 AI System

Automatically generates comprehensive test cases for command combinations,
semantic ambiguity patterns, and structural state scenarios.
"""

import logging
import itertools
import random
from dataclasses import dataclass
from typing import Dict, List, Tuple, Any, Optional
from enum import Enum

try:
    from .command_combination_tester import CommandCategory
except ImportError:
    from command_combination_tester import CommandCategory


class TestCaseType(Enum):
    """Types of test cases that can be generated."""

    COMMAND_COMBINATION = "command_combination"
    SEMANTIC_AMBIGUITY = "semantic_ambiguity"
    STRUCTURAL_STATE = "structural_state"
    BOUNDARY_CONDITION = "boundary_condition"
    ERROR_CONDITION = "error_condition"


@dataclass
class GeneratedTestCase:
    """Represents a generated test case."""

    test_id: str
    test_type: TestCaseType
    query: str
    expected_commands: List[Dict[str, Any]]
    risk_level: str
    category_combination: Tuple[str, ...]
    expected_ambiguities: List[str]
    description: str
    complexity_score: int  # 1-10 scale


@dataclass
class TestGenerationConfig:
    """Configuration for test case generation."""

    max_command_combinations: int = 100
    max_query_variations: int = 50
    include_boundary_tests: bool = True
    include_error_conditions: bool = True
    complexity_range: Tuple[int, int] = (1, 8)
    focus_areas: List[str] = None


class TestCaseGenerator:
    """
    Comprehensive test case generation system.

    Automatically creates test cases based on command combinations,
    semantic patterns, and known failure scenarios.
    """

    def __init__(self, config: Optional[TestGenerationConfig] = None):
        """
        Initialize the test case generator.

        Args:
            config: Generation configuration (uses defaults if None)
        """
        self.config = config or TestGenerationConfig()
        self.logger = logging.getLogger(__name__)

        # Initialize command templates and patterns
        self.command_templates = self._initialize_command_templates()
        self.connector_variations = self._initialize_connectors()
        self.ambiguity_patterns = self._initialize_ambiguity_patterns()
        self.boundary_conditions = self._initialize_boundary_conditions()

        # Generated test cases storage
        self.generated_tests: List[GeneratedTestCase] = []

    def _initialize_command_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize command templates for test generation."""
        return {
            # File operations
            "OPEN_FILE": {
                "templates": [
                    "open {file}",
                    "load {file}",
                    "open file {file}",
                    "load the file {file}",
                ],
                "params": {
                    "file": ["price.json", "data.csv", "results.xlsx", "test.txt"]
                },
                "category": CommandCategory.FILE_OPS,
            },
            # Column operations
            "INSERT_COLUMN_RIGHT": {
                "templates": [
                    "insert column after {column}",
                    "add column to the right of {column}",
                    "create new column after column {column}",
                    "add a new column after {column}",
                ],
                "params": {"column": ["A", "B", "C", "D"]},
                "category": CommandCategory.STRUCTURAL,
            },
            "DELETE_COLUMN": {
                "templates": [
                    "delete column {column}",
                    "remove column {column}",
                    "delete the {column} column",
                    "remove the column {column}",
                ],
                "params": {"column": ["A", "B", "C", "D"]},
                "category": CommandCategory.STRUCTURAL,
            },
            "COPY_COLUMN_TO_NEW_COLUMN_AFTER": {
                "templates": [
                    "copy column {source} to new column after {target}",
                    "copy data from column {source} into new column after {target}",
                    "copy the information from column {source} to the new column",
                    "copy column {source} data to column after {target}",
                ],
                "params": {"source": ["A", "B", "C", "D"], "target": ["A", "B", "C"]},
                "category": CommandCategory.REFERENCE,
            },
            "SORT_COLUMN_ASCENDING": {
                "templates": [
                    "sort column {column} ascending",
                    "sort by column {column}",
                    "arrange column {column} in ascending order",
                    "sort the data by column {column}",
                ],
                "params": {"column": ["A", "B", "C", "D"]},
                "category": CommandCategory.REFERENCE,
            },
            "FILTER_COLUMN": {
                "templates": [
                    "filter column {column} where {condition}",
                    "filter by column {column} {condition}",
                    "show only rows where column {column} {condition}",
                    "filter data in column {column} {condition}",
                ],
                "params": {
                    "column": ["A", "B", "C", "D"],
                    "condition": [
                        "contains 'test'",
                        "is greater than 100",
                        "equals 'active'",
                        "is not empty",
                    ],
                },
                "category": CommandCategory.FILTER,
            },
            # Row operations
            "INSERT_ROW_BELOW": {
                "templates": [
                    "insert row below row {row}",
                    "add new row after row {row}",
                    "create row below {row}",
                    "insert a new row after row {row}",
                ],
                "params": {"row": [1, 5, 10, 15]},
                "category": CommandCategory.STRUCTURAL,
            },
            # Formatting operations
            "APPLY_BOLD_FORMATTING": {
                "templates": [
                    "make column {column} bold",
                    "format column {column} as bold",
                    "apply bold formatting to column {column}",
                    "bold the text in column {column}",
                ],
                "params": {"column": ["A", "B", "C", "D"]},
                "category": CommandCategory.FORMAT,
            },
        }

    def _initialize_connectors(self) -> List[Dict[str, Any]]:
        """Initialize connector word variations for query generation."""
        return [
            {"connector": "then", "type": "sequential", "weight": 3},
            {"connector": "and then", "type": "sequential", "weight": 2},
            {"connector": "after that", "type": "sequential", "weight": 1},
            {"connector": "next", "type": "sequential", "weight": 1},
            {"connector": "and", "type": "ambiguous", "weight": 2},
            {"connector": ", then", "type": "sequential", "weight": 2},
            {"connector": "followed by", "type": "sequential", "weight": 1},
        ]

    def _initialize_ambiguity_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns that create semantic ambiguity."""
        return {
            "column_reference_ambiguity": [
                "the new column {column}",
                "new column {column}",
                "column {column} after the insert",
                "the {column} column",
            ],
            "positional_ambiguity": [
                "to the right of {column}",
                "next to {column}",
                "beside {column}",
                "after {column}",
                "following {column}",
            ],
            "temporal_ambiguity": [
                "and then",
                "while",
                "during",
                "at the same time",
                "simultaneously",
            ],
            "implicit_operations": [
                "copy to {file}",
                "save as {file}",
                "make it bold",
                "format it",
            ],
        }

    def _initialize_boundary_conditions(self) -> Dict[str, List[Dict[str, Any]]]:
        """Initialize boundary condition test scenarios."""
        return {
            "empty_state": [
                {"description": "Operations on empty spreadsheet", "file": None},
                {"description": "Operations on empty column", "column": "empty"},
                {"description": "Operations on empty row", "row": "empty"},
            ],
            "edge_columns": [
                {"description": "Operations on first column", "column": "A"},
                {"description": "Operations on last column", "column": "Z"},
                {"description": "Operations beyond visible columns", "column": "AA"},
            ],
            "edge_rows": [
                {"description": "Operations on first row", "row": 1},
                {"description": "Operations on row 1000", "row": 1000},
                {"description": "Operations on very large row", "row": 100000},
            ],
            "file_states": [
                {
                    "description": "Operations before file is open",
                    "file_state": "closed",
                },
                {
                    "description": "Operations on non-existent file",
                    "file": "nonexistent.csv",
                },
                {"description": "Operations on corrupt file", "file": "corrupt.json"},
            ],
        }

    def generate_command_combination_tests(
        self, max_combinations: int = 50
    ) -> List[GeneratedTestCase]:
        """
        Generate test cases for command combinations.

        Args:
            max_combinations: Maximum number of combinations to generate

        Returns:
            List of generated test cases
        """
        test_cases = []
        command_names = list(self.command_templates.keys())

        # Generate 2-command combinations
        combinations = list(itertools.combinations(command_names, 2))

        # Prioritize high-risk combinations
        high_risk_combinations = self._prioritize_combinations(combinations)

        for i, (cmd1, cmd2) in enumerate(high_risk_combinations[:max_combinations]):
            # Generate multiple query variations for each combination
            variations = self._generate_query_variations(cmd1, cmd2)

            for j, (query, expected_cmds) in enumerate(
                variations[:3]
            ):  # Max 3 per combo
                test_id = f"combo_{i:03d}_{j}"

                # Assess risk level
                risk_level = self._assess_combination_risk(cmd1, cmd2)

                # Calculate complexity
                complexity = self._calculate_query_complexity(query, expected_cmds)

                test_case = GeneratedTestCase(
                    test_id=test_id,
                    test_type=TestCaseType.COMMAND_COMBINATION,
                    query=query,
                    expected_commands=expected_cmds,
                    risk_level=risk_level,
                    category_combination=(
                        self.command_templates[cmd1]["category"].value,
                        self.command_templates[cmd2]["category"].value,
                    ),
                    expected_ambiguities=[],
                    description=f"Combination: {cmd1} + {cmd2}",
                    complexity_score=complexity,
                )
                test_cases.append(test_case)

        self.logger.info(f"Generated {len(test_cases)} command combination test cases")
        return test_cases

    def generate_semantic_ambiguity_tests(
        self, max_tests: int = 30
    ) -> List[GeneratedTestCase]:
        """
        Generate test cases specifically for semantic ambiguity detection.

        Args:
            max_tests: Maximum number of tests to generate

        Returns:
            List of semantic ambiguity test cases
        """
        test_cases = []

        # Generate tests for each ambiguity type
        for ambiguity_type, patterns in self.ambiguity_patterns.items():
            for i, pattern in enumerate(patterns):
                if len(test_cases) >= max_tests:
                    break

                # Create ambiguous query
                query = self._create_ambiguous_query(ambiguity_type, pattern)

                test_id = f"ambig_{ambiguity_type}_{i:02d}"

                test_case = GeneratedTestCase(
                    test_id=test_id,
                    test_type=TestCaseType.SEMANTIC_AMBIGUITY,
                    query=query,
                    expected_commands=[],  # To be determined by AI
                    risk_level="HIGH",  # Ambiguity tests are high risk
                    category_combination=("ambiguity",),
                    expected_ambiguities=[ambiguity_type],
                    description=f"Semantic ambiguity: {ambiguity_type}",
                    complexity_score=6,  # Medium-high complexity
                )
                test_cases.append(test_case)

        self.logger.info(f"Generated {len(test_cases)} semantic ambiguity test cases")
        return test_cases

    def generate_boundary_condition_tests(
        self, max_tests: int = 20
    ) -> List[GeneratedTestCase]:
        """
        Generate test cases for boundary conditions.

        Args:
            max_tests: Maximum number of boundary tests to generate

        Returns:
            List of boundary condition test cases
        """
        test_cases = []

        for condition_type, conditions in self.boundary_conditions.items():
            for i, condition in enumerate(conditions):
                if len(test_cases) >= max_tests:
                    break

                query = self._create_boundary_query(condition_type, condition)

                test_id = f"boundary_{condition_type}_{i:02d}"

                test_case = GeneratedTestCase(
                    test_id=test_id,
                    test_type=TestCaseType.BOUNDARY_CONDITION,
                    query=query,
                    expected_commands=[],
                    risk_level="MEDIUM",
                    category_combination=("boundary",),
                    expected_ambiguities=[],
                    description=condition["description"],
                    complexity_score=4,  # Medium complexity
                )
                test_cases.append(test_case)

        self.logger.info(f"Generated {len(test_cases)} boundary condition test cases")
        return test_cases

    def _prioritize_combinations(
        self, combinations: List[Tuple[str, str]]
    ) -> List[Tuple[str, str]]:
        """Prioritize command combinations by risk level."""

        def risk_score(combo):
            cmd1, cmd2 = combo
            cat1 = self.command_templates[cmd1]["category"]
            cat2 = self.command_templates[cmd2]["category"]

            # High risk: structural + reference
            if cat1 == CommandCategory.STRUCTURAL and cat2 == CommandCategory.REFERENCE:
                return 5
            # Medium-high risk: file ops + structural
            elif (
                cat1 == CommandCategory.FILE_OPS and cat2 == CommandCategory.STRUCTURAL
            ):
                return 4
            # Medium risk: multiple structural
            elif (
                cat1 == CommandCategory.STRUCTURAL
                and cat2 == CommandCategory.STRUCTURAL
            ):
                return 3
            else:
                return 1

        return sorted(combinations, key=risk_score, reverse=True)

    def _generate_query_variations(
        self, cmd1: str, cmd2: str
    ) -> List[Tuple[str, List[Dict[str, Any]]]]:
        """Generate natural language variations for command combinations."""
        variations = []

        cmd1_template = self.command_templates[cmd1]
        cmd2_template = self.command_templates[cmd2]

        # Get sample templates and parameters
        for template1 in cmd1_template["templates"][:2]:  # Max 2 per command
            for template2 in cmd2_template["templates"][:2]:
                # Select compatible parameters
                params1 = self._select_parameters(cmd1_template["params"])
                params2 = self._select_parameters(cmd2_template["params"])

                # Format templates
                query1 = template1.format(**params1)
                query2 = template2.format(**params2)

                # Combine with different connectors
                for connector_info in self.connector_variations[:3]:  # Max 3 connectors
                    connector = connector_info["connector"]
                    complete_query = f"{query1} {connector} {query2}"

                    # Create expected command structure
                    expected_commands = [
                        {"command": cmd1, "params": params1},
                        {"command": cmd2, "params": params2},
                    ]

                    variations.append((complete_query, expected_commands))

        return variations

    def _select_parameters(self, param_options: Dict[str, List]) -> Dict[str, Any]:
        """Select compatible parameters for command templates."""
        params = {}
        for param_name, options in param_options.items():
            params[param_name] = random.choice(options)
        return params

    def _assess_combination_risk(self, cmd1: str, cmd2: str) -> str:
        """Assess risk level of command combination."""
        cat1 = self.command_templates[cmd1]["category"]
        cat2 = self.command_templates[cmd2]["category"]

        if cat1 == CommandCategory.STRUCTURAL and cat2 == CommandCategory.REFERENCE:
            return "HIGH"
        elif cat1 == CommandCategory.FILE_OPS and cat2 == CommandCategory.STRUCTURAL:
            return "MEDIUM"
        elif cmd1 == cmd2:
            return "LOW"  # Same command repeated
        else:
            return "MEDIUM"

    def _calculate_query_complexity(
        self, query: str, expected_commands: List[Dict[str, Any]]
    ) -> int:
        """Calculate complexity score for a query (1-10 scale)."""
        complexity = 0

        # Base complexity from number of commands
        complexity += len(expected_commands)

        # Add complexity for query length
        complexity += min(len(query.split()) // 5, 3)

        # Add complexity for ambiguous connectors
        if " and " in query and " then " in query:
            complexity += 2

        # Add complexity for structural operations
        if any(
            "INSERT" in cmd["command"] or "DELETE" in cmd["command"]
            for cmd in expected_commands
        ):
            complexity += 2

        return min(complexity, 10)

    def _create_ambiguous_query(self, ambiguity_type: str, pattern: str) -> str:
        """Create a query with specific semantic ambiguity."""
        if ambiguity_type == "column_reference_ambiguity":
            return f"insert column after A then copy {pattern.format(column='C')} data"
        elif ambiguity_type == "positional_ambiguity":
            return f"add column {pattern.format(column='B')} then sort column A"
        elif ambiguity_type == "temporal_ambiguity":
            return f"open file.csv {pattern} sort column A and filter column B"
        else:
            return f"generic ambiguous query with {pattern}"

    def _create_boundary_query(
        self, condition_type: str, condition: Dict[str, Any]
    ) -> str:
        """Create a query for boundary condition testing."""
        if condition_type == "empty_state":
            return (
                f"copy column A data then format column B - {condition['description']}"
            )
        elif condition_type == "edge_columns":
            column = condition["column"]
            return f"insert column after {column} then copy column {column} data"
        elif condition_type == "edge_rows":
            row = condition["row"]
            return f"insert row below {row} then copy row {row} data"
        else:
            return f"boundary test: {condition['description']}"

    def generate_comprehensive_test_suite(self) -> Dict[str, Any]:
        """
        Generate a comprehensive test suite covering all test types.

        Returns:
            Dictionary containing all generated test cases and metadata
        """
        all_tests = []

        # Generate different types of tests
        combo_tests = self.generate_command_combination_tests(
            self.config.max_command_combinations
        )
        all_tests.extend(combo_tests)

        ambiguity_tests = self.generate_semantic_ambiguity_tests(30)
        all_tests.extend(ambiguity_tests)

        if self.config.include_boundary_tests:
            boundary_tests = self.generate_boundary_condition_tests(20)
            all_tests.extend(boundary_tests)

        # Filter by complexity if specified
        if self.config.complexity_range:
            min_complexity, max_complexity = self.config.complexity_range
            all_tests = [
                test
                for test in all_tests
                if min_complexity <= test.complexity_score <= max_complexity
            ]

        # Store generated tests
        self.generated_tests = all_tests

        # Generate summary
        summary = self._generate_test_suite_summary(all_tests)

        return {"test_cases": all_tests, "summary": summary, "config": self.config}

    def _generate_test_suite_summary(
        self, tests: List[GeneratedTestCase]
    ) -> Dict[str, Any]:
        """Generate summary statistics for the test suite."""
        total_tests = len(tests)

        # Count by type
        type_counts = {}
        for test in tests:
            test_type = test.test_type.value
            type_counts[test_type] = type_counts.get(test_type, 0) + 1

        # Count by risk level
        risk_counts = {}
        for test in tests:
            risk_counts[test.risk_level] = risk_counts.get(test.risk_level, 0) + 1

        # Complexity distribution
        complexity_dist = {}
        for test in tests:
            complexity_dist[test.complexity_score] = (
                complexity_dist.get(test.complexity_score, 0) + 1
            )

        return {
            "total_tests": total_tests,
            "type_distribution": type_counts,
            "risk_distribution": risk_counts,
            "complexity_distribution": complexity_dist,
            "avg_complexity": (
                sum(t.complexity_score for t in tests) / total_tests
                if total_tests > 0
                else 0
            ),
        }


def main():
    """Main function for testing the TestCaseGenerator."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    # Create generator with custom config
    config = TestGenerationConfig(
        max_command_combinations=20,
        include_boundary_tests=True,
        complexity_range=(2, 8),
    )
    generator = TestCaseGenerator(config)

    print("🧪 Test Case Generator - Planning Mode")
    print("=" * 50)

    # Generate comprehensive test suite
    results = generator.generate_comprehensive_test_suite()

    print("Test Suite Generation Results:")
    print(f"✅ Total Tests Generated: {results['summary']['total_tests']}")
    print(f"✅ Test Types: {list(results['summary']['type_distribution'].keys())}")
    print(f"✅ Risk Levels: {list(results['summary']['risk_distribution'].keys())}")
    print(f"✅ Average Complexity: {results['summary']['avg_complexity']:.1f}")

    # Show sample test cases
    print("\nSample Test Cases:")
    for i, test in enumerate(results["test_cases"][:5], 1):
        print(f"{i}. [{test.risk_level}] {test.test_type.value}")
        print(f'   Query: "{test.query[:60]}..."')
        print(f"   Complexity: {test.complexity_score}/10")
        print()

    print("✅ TestCaseGenerator ready for deployment")


if __name__ == "__main__":
    main()
