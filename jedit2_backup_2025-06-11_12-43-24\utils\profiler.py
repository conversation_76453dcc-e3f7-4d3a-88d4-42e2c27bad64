"""Performance profiling module for JEdit2 using cProfile.

This module provides utilities to profile functions and code blocks using cProfile.
"""

import cProfile
import pstats
import io
import functools
import os
from typing import Optional, Callable, Any


class Profiler:
    """Context manager for profiling a block of code using cProfile."""

    def __init__(self, output_file: Optional[str] = None):
        self.profiler = cProfile.Profile()
        self.output_file = output_file

    def __enter__(self):
        self.profiler.enable()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.profiler.disable()
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s).sort_stats("cumulative")
        ps.print_stats()
        if self.output_file:
            with open(self.output_file, "w") as f:
                f.write(s.getvalue())
        else:
            print(s.getvalue())


def profile(func: Callable) -> Callable:
    """Decorator to profile a function using cProfile.

    Args:
        func: The function to profile.
    Returns:
        The wrapped function.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with Profiler():
            return func(*args, **kwargs)
    return wrapper 