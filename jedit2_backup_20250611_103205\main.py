"""Main application entry point for JEdit2.

This module provides the main application entry point for JEdit2.
"""

import wx
import traceback
from jedit2.main_window import MainWindow
from jedit2.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorCategory, ErrorLevel
from jedit2.utils.memory_manager import MemoryManager


class JEdit2App(wx.App):
    """Main application class for JEdit2."""

    def OnInit(self) -> bool:
        """Initialize the application."""
        try:
            # First, create the managers
            self.memory_manager = MemoryManager()
            self.error_handler = ErrorHandler()

            # Now, create the main window and pass the managers to it
            self.frame = MainWindow(
                parent=None,
                title="JEdit2",
                memory_manager=self.memory_manager,
                error_handler=self.error_handler,
            )
            self.frame.Show()
            self.frame.Layout()

            # Use wx.CallAfter to start background services after the event loop has started
            wx.CallAfter(self.start_background_services)

            self.SetTopWindow(self.frame)
            return True
        except Exception as e:
            # Use the handler to log the error, if possible
            try:
                self.error_handler.log_error(e, ErrorLevel.CRITICAL, ErrorCategory.APPLICATION)
            except:
                 print(f"A critical error occurred on startup: {e}")
                 import traceback
                 traceback.print_exc()
            return False

    def start_background_services(self) -> None:
        """Start background services after the GUI is up."""
        ErrorHandler.setup_logging()
        self.memory_manager.start_monitoring()

    def OnExit(self) -> int:
        """Clean up on application exit."""
        try:
            self.memory_manager.stop_monitoring()
            stats = self.memory_manager.get_memory_stats()
            self.error_handler.log_info(
                f"Application exit. Memory stats: {stats}", ErrorCategory.MEMORY
            )
        except Exception as e:
            # If the error handler is available, use it
            try:
                self.error_handler.log_error(e, ErrorLevel.ERROR, ErrorCategory.APPLICATION)
            except:
                print(f"Error during application exit: {e}")

        return super().OnExit()


def main() -> None:
    """Main application entry point."""
    try:
        app = JEdit2App()
        app.MainLoop()
    except Exception as e:
        print(f"An unhandled exception occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 