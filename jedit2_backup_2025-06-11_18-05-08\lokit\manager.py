"""LibreOfficeKit Manager for JEdit2.

This module provides the main LibreOffice integration manager that handles
starting LibreOffice in headless mode and communicating via UNO API.
"""

import os
import sys
import subprocess
import time
import logging
import platform
from typing import Optional, Dict, Any, List
from pathlib import Path
import tempfile

try:
    import uno
    from com.sun.star.connection import NoConnectException
    from com.sun.star.beans import PropertyValue
    from com.sun.star.lang import DisposedException
    UNO_AVAILABLE = True
except ImportError as e:
    UNO_AVAILABLE = False
    # Add LibreOffice program directory to path if possible
    import platform
    potential_paths = []
    if platform.system() == "Windows":
        potential_paths = [
            r"C:\Program Files\LibreOffice\program",
            r"C:\Program Files (x86)\LibreOffice\program"
        ]
    elif platform.system() == "Darwin":
        potential_paths = [
            "/Applications/LibreOffice.app/Contents/Resources",
            "/Applications/LibreOffice.app/Contents/MacOS"
        ]
    else:
        potential_paths = [
            "/usr/lib/libreoffice/program",
            "/opt/libreoffice/program"
        ]
    
    for path in potential_paths:
        if os.path.exists(os.path.join(path, "uno.py")):
            sys.path.insert(0, path)
            try:
                import uno
                from com.sun.star.connection import NoConnectException
                from com.sun.star.beans import PropertyValue
                from com.sun.star.lang import DisposedException
                UNO_AVAILABLE = True
                break
            except ImportError:
                continue
    
    if not UNO_AVAILABLE:
        logging.warning(f"UNO not available: {e}")
        logging.warning("Run 'python setup_libreoffice.py' to configure UNO environment")


class LOKitManager:
    """Main LibreOfficeKit integration manager."""
    
    def __init__(self, libreoffice_path: Optional[str] = None):
        """Initialize LibreOffice manager.
        
        Args:
            libreoffice_path: Path to LibreOffice installation
        """
        self.lo_path = libreoffice_path or self._find_libreoffice_path()
        self.lo_process: Optional[subprocess.Popen] = None
        self.desktop = None
        self.component_context = None
        self.service_manager = None
        self.documents: Dict[str, Any] = {}
        self.port = 8100
        self.initialized = False
        
        self.logger = logging.getLogger(__name__)
        
    def _find_libreoffice_path(self) -> str:
        """Find LibreOffice installation path."""
        system = platform.system()
        
        if system == "Windows":
            # Common Windows paths
            paths = [
                r"C:\Program Files\LibreOffice\program\soffice.exe",
                r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            ]
        elif system == "Darwin":  # macOS
            paths = [
                "/Applications/LibreOffice.app/Contents/MacOS/soffice",
                "/opt/homebrew/bin/libreoffice",
                "/usr/local/bin/libreoffice",
            ]
        else:  # Linux
            paths = [
                "/usr/bin/libreoffice",
                "/usr/local/bin/libreoffice",
                "/opt/libreoffice/program/soffice",
            ]
            
        for path in paths:
            if os.path.exists(path):
                return path
                
        raise RuntimeError(f"LibreOffice not found on {system}")
    
    def initialize(self) -> bool:
        """Initialize LibreOffice connection."""
        if not UNO_AVAILABLE:
            self.logger.error("UNO not available - cannot initialize LibreOffice")
            return False
            
        try:
            # Start LibreOffice in headless mode
            if not self._start_libreoffice():
                return False
            
            # Connect to LibreOffice via UNO
            if not self._connect_uno():
                return False
                
            self.initialized = True
            self.logger.info("LibreOffice initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"LibreOffice initialization failed: {e}")
            return False
    
    def _start_libreoffice(self) -> bool:
        """Start LibreOffice in headless mode."""
        try:
            cmd = [
                self.lo_path,
                "--headless",
                "--invisible",
                "--nodefault",
                "--nolockcheck",
                "--nologo",
                "--norestore",
                f"--accept=socket,host=localhost,port={self.port};urp;StarOffice.ServiceManager"
            ]
            
            self.lo_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == "Windows" else 0
            )
            
            # Wait for LibreOffice to start
            time.sleep(3)
            
            if self.lo_process.poll() is not None:
                stdout, stderr = self.lo_process.communicate()
                self.logger.error(f"LibreOffice failed to start: {stderr.decode()}")
                return False
                
            self.logger.info("LibreOffice started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start LibreOffice: {e}")
            return False
    
    def _connect_uno(self) -> bool:
        """Connect to LibreOffice via UNO API."""
        max_attempts = 10
        
        for attempt in range(max_attempts):
            try:
                # Get component context
                local_context = uno.getComponentContext()
                resolver = local_context.ServiceManager.createInstanceWithContext(
                    "com.sun.star.bridge.UnoUrlResolver", local_context
                )
                
                # Connect to LibreOffice
                context = resolver.resolve(
                    f"uno:socket,host=localhost,port={self.port};urp;StarOffice.ComponentContext"
                )
                
                self.component_context = context
                self.service_manager = context.ServiceManager
                
                # Get desktop
                self.desktop = self.service_manager.createInstanceWithContext(
                    "com.sun.star.frame.Desktop", context
                )
                
                self.logger.info("UNO connection established")
                return True
                
            except NoConnectException:
                if attempt < max_attempts - 1:
                    time.sleep(1)
                    continue
                else:
                    self.logger.error("Failed to connect to LibreOffice after multiple attempts")
                    return False
            except Exception as e:
                self.logger.error(f"UNO connection error: {e}")
                return False
                
        return False
    
    def create_document(self, doc_type: str = "calc") -> str:
        """Create new document.
        
        Args:
            doc_type: Document type ('calc' for spreadsheet, 'writer' for text)
            
        Returns:
            Document ID
        """
        if not self.initialized:
            raise RuntimeError("LibreOffice not initialized")
            
        try:
            doc_id = f"doc_{len(self.documents)}"
            
            if doc_type == "calc":
                url = "private:factory/scalc"
            elif doc_type == "writer":
                url = "private:factory/swriter"
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")
            
            # Create document
            document = self.desktop.loadComponentFromURL(
                url, "_blank", 0, ()
            )
            
            self.documents[doc_id] = {
                'document': document,
                'type': doc_type,
                'path': None,
                'modified': False
            }
            
            self.logger.info(f"Created {doc_type} document: {doc_id}")
            return doc_id
            
        except Exception as e:
            self.logger.error(f"Failed to create document: {e}")
            raise
    
    def load_document(self, file_path: str) -> str:
        """Load existing document.
        
        Args:
            file_path: Path to document file
            
        Returns:
            Document ID
        """
        if not self.initialized:
            raise RuntimeError("LibreOffice not initialized")
            
        try:
            doc_id = f"doc_{len(self.documents)}"
            
            # Convert file path to URL
            file_url = Path(file_path).as_uri()
            
            # Load document
            document = self.desktop.loadComponentFromURL(
                file_url, "_blank", 0, ()
            )
            
            # Determine document type
            doc_type = self._determine_document_type(document)
            
            self.documents[doc_id] = {
                'document': document,
                'type': doc_type,
                'path': file_path,
                'modified': False
            }
            
            self.logger.info(f"Loaded document: {file_path} as {doc_id}")
            return doc_id
            
        except Exception as e:
            self.logger.error(f"Failed to load document {file_path}: {e}")
            raise
    
    def _determine_document_type(self, document) -> str:
        """Determine document type from LibreOffice document."""
        try:
            # Get document's service name
            service_name = document.getIdentifier()
            
            if "calc" in service_name.lower():
                return "calc"
            elif "writer" in service_name.lower():
                return "writer"
            else:
                return "unknown"
                
        except Exception:
            # Fallback: try to access Calc-specific interface
            try:
                document.getSheets()
                return "calc"
            except Exception:
                return "writer"
    
    def save_document(self, doc_id: str, file_path: Optional[str] = None) -> bool:
        """Save document.
        
        Args:
            doc_id: Document ID
            file_path: Optional new file path for Save As
            
        Returns:
            True if successful
        """
        if doc_id not in self.documents:
            raise ValueError(f"Document not found: {doc_id}")
            
        try:
            doc_info = self.documents[doc_id]
            document = doc_info['document']
            
            if file_path:
                # Save As
                file_url = Path(file_path).as_uri()
                document.storeAsURL(file_url, ())
                doc_info['path'] = file_path
            else:
                # Save
                if doc_info['path']:
                    document.store()
                else:
                    raise ValueError("No file path specified for new document")
            
            doc_info['modified'] = False
            self.logger.info(f"Saved document: {doc_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save document {doc_id}: {e}")
            return False
    
    def close_document(self, doc_id: str) -> bool:
        """Close document.
        
        Args:
            doc_id: Document ID
            
        Returns:
            True if successful
        """
        if doc_id not in self.documents:
            return False
            
        try:
            document = self.documents[doc_id]['document']
            document.close(False)  # Don't save changes
            del self.documents[doc_id]
            
            self.logger.info(f"Closed document: {doc_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to close document {doc_id}: {e}")
            return False
    
    def get_document(self, doc_id: str):
        """Get document object.
        
        Args:
            doc_id: Document ID
            
        Returns:
            LibreOffice document object
        """
        if doc_id not in self.documents:
            raise ValueError(f"Document not found: {doc_id}")
            
        return self.documents[doc_id]['document']
    
    def get_document_info(self, doc_id: str) -> Dict[str, Any]:
        """Get document information.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document information dict
        """
        if doc_id not in self.documents:
            raise ValueError(f"Document not found: {doc_id}")
            
        return self.documents[doc_id].copy()
    
    def list_documents(self) -> List[str]:
        """List all open document IDs.
        
        Returns:
            List of document IDs
        """
        return list(self.documents.keys())
    
    def shutdown(self):
        """Shutdown LibreOffice and cleanup."""
        try:
            # Close all documents
            for doc_id in list(self.documents.keys()):
                self.close_document(doc_id)
            
            # Shutdown LibreOffice
            if self.desktop:
                try:
                    self.desktop.terminate()
                except DisposedException:
                    pass  # Already disposed
            
            # Kill process if still running
            if self.lo_process and self.lo_process.poll() is None:
                self.lo_process.terminate()
                time.sleep(1)
                if self.lo_process.poll() is None:
                    self.lo_process.kill()
            
            self.initialized = False
            self.logger.info("LibreOffice shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        if not self.initialize():
            raise RuntimeError("Failed to initialize LibreOffice")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown() 