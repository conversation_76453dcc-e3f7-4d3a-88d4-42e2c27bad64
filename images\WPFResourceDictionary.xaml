<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13.5,3.707v7.586l-.207.207H10.659l-2-1.989-1.44,1.447H3.767L3.5,10.693V3.707L3.707,3.5H6.5a1.982,1.982,0,0,1,1.6.814l.4.539.4-.539A1.982,1.982,0,0,1,10.5,3.5h2.793Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,3.5v8l-.5.5H11.161l-.978-.974A2.557,2.557,0,0,1,10.5,11H13V4H10.5A1.5,1.5,0,0,0,9,5.5V9.847l-.337-.336L8,10.177V5.5A1.5,1.5,0,0,0,6.5,4H4v6.958H3.767L3,10.2V3.5L3.5,3h3a2.491,2.491,0,0,1,2,1.015A2.491,2.491,0,0,1,10.5,3h3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M1.207,13.469l1.816,1.82L2.316,16,.147,13.823v-.706l2.169-2.179.709.705Zm9.647-.351v.709L8.666,16l-.7-.709,1.831-1.819L7.961,11.647l.705-.709Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,11.971v3H4v-3Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
