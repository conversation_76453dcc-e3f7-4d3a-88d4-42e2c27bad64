<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey-25" Color="#212121" Opacity="0.25" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13.5,4.82v6.36L8,14.35,2.5,11.18,8,8V1.65Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2,11.18l.25.43,5.5,3.17h.5l5.5-3.17.25-.43V4.82l-.25-.43L8.25,1.22h-.5L8,1.65V8L2.5,11.18Zm5.5,2.31-4-2.31,4-2.31ZM9,8l4-2.31v4.62Zm3.5,3.18-4,2.31V8.87Zm-4-8.67,4,2.31-4,2.31Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M8,1.649V8L2.5,11.175V4.825Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-25}" Geometry="F1M8.25,7.567l-.5.866L2.25,5.258l.5-.866Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2.75,11.608,2,11.175V4.825l.25-.433,5.5-3.176.75.433V8l-.25.433ZM3,5.113v5.2l4.5-2.6v-5.2Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
