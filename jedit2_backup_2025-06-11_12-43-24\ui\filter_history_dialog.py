"""Filter History Dialog for managing multiple column filters."""

import wx
from typing import Dict, List, Optional, Any, Union
from .filter_criteria import FilterCriterion, ValueListCriterion, TextFilterCriterion, NumberFilterCriterion, DateFilterCriterion
import logging

logger = logging.getLogger(__name__)


class FilterHistoryDialog(wx.Dialog):
    """Dialog for managing multiple column filters and history."""
    
    def __init__(self, parent: wx.Window, active_filters: Dict[int, Union[str, FilterCriterion]], 
                 column_names: List[str]):
        """
        Initialize the filter history dialog.
        
        Args:
            parent: Parent window
            active_filters: Currently active filters {column_index: filter}
            column_names: List of column names for display
        """
        super().__init__(parent, title="Filter History", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.active_filters = active_filters.copy()
        self.column_names = column_names
        self.filters_to_remove: List[int] = []
        self.filter_modified = False
        
        self._init_ui()
        self._bind_events()
        self._populate_filters()
        
        # Set initial size and center
        self.SetSize((600, 400))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title_label = wx.StaticText(self, label="Active Filters")
        title_font = title_label.GetFont()
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title_font.SetPointSize(title_font.GetPointSize() + 2)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL, 10)
        
        # Instructions
        instructions = wx.StaticText(self, 
            label="Select filters to remove or clear all filters. Changes apply when you click OK.")
        instructions.SetForegroundColour(wx.Colour(100, 100, 100))
        main_sizer.Add(instructions, 0, wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Filters list
        self.filters_list = wx.CheckListBox(self, style=wx.LB_SINGLE)
        main_sizer.Add(self.filters_list, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Control buttons
        control_panel = self._create_control_panel()
        main_sizer.Add(control_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Dialog buttons
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
    
    def _create_control_panel(self) -> wx.Panel:
        """Create the control buttons panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.select_all_btn = wx.Button(panel, label="Select All")
        sizer.Add(self.select_all_btn, 0, wx.RIGHT, 5)
        
        self.select_none_btn = wx.Button(panel, label="Select None")
        sizer.Add(self.select_none_btn, 0, wx.RIGHT, 10)
        
        self.clear_all_btn = wx.Button(panel, label="Clear All Filters")
        sizer.Add(self.clear_all_btn, 0)
        
        sizer.AddStretchSpacer()
        
        # Statistics
        self.stats_label = wx.StaticText(panel, label="")
        self.stats_label.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(self.stats_label, 0, wx.ALIGN_CENTER_VERTICAL)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the dialog button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 5)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.select_all_btn.Bind(wx.EVT_BUTTON, self._on_select_all)
        self.select_none_btn.Bind(wx.EVT_BUTTON, self._on_select_none)
        self.clear_all_btn.Bind(wx.EVT_BUTTON, self._on_clear_all)
        self.filters_list.Bind(wx.EVT_CHECKLISTBOX, self._on_filter_check)
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _populate_filters(self) -> None:
        """Populate the filters list with current active filters."""
        self.filters_list.Clear()
        self.column_filter_map = {}  # Map list index to column index
        
        if not self.active_filters:
            # No filters active
            self.filters_list.Append("No active filters")
            self.filters_list.Enable(False)
            self.select_all_btn.Enable(False)
            self.select_none_btn.Enable(False)
            self.clear_all_btn.Enable(False)
            self._update_stats()
            return
        
        # Add each active filter
        list_index = 0
        for col_index, filter_obj in self.active_filters.items():
            # Get column name
            if col_index < len(self.column_names):
                col_name = self.column_names[col_index]
            else:
                col_name = f"Column {chr(ord('A') + col_index)}"
            
            # Get filter description
            filter_desc = self._get_filter_description(filter_obj)
            
            # Create display text
            display_text = f"{col_name}: {filter_desc}"
            
            self.filters_list.Append(display_text)
            self.column_filter_map[list_index] = col_index
            list_index += 1
        
        self._update_stats()
    
    def _get_filter_description(self, filter_obj: Union[str, FilterCriterion]) -> str:
        """Get a human-readable description of a filter."""
        if isinstance(filter_obj, str):
            # Simple string filter
            return f"Contains '{filter_obj}'"
        elif isinstance(filter_obj, ValueListCriterion):
            # Value list filter
            if len(filter_obj.selected_values) == 1:
                return f"Equals '{list(filter_obj.selected_values)[0]}'"
            else:
                return f"In {len(filter_obj.selected_values)} values"
        elif isinstance(filter_obj, TextFilterCriterion):
            # Text filter
            op_names = {
                TextFilterCriterion.CONTAINS: "Contains",
                TextFilterCriterion.NOT_CONTAINS: "Does not contain",
                TextFilterCriterion.EQUALS: "Equals",
                TextFilterCriterion.NOT_EQUALS: "Does not equal",
                TextFilterCriterion.BEGINS_WITH: "Begins with",
                TextFilterCriterion.NOT_BEGINS_WITH: "Does not begin with",
                TextFilterCriterion.ENDS_WITH: "Ends with",
                TextFilterCriterion.NOT_ENDS_WITH: "Does not end with",
                TextFilterCriterion.IS_EMPTY: "Is empty",
                TextFilterCriterion.IS_NOT_EMPTY: "Is not empty",
                TextFilterCriterion.MATCHES_PATTERN: "Matches pattern"
            }
            op_name = op_names.get(filter_obj.operator, "Unknown")
            if filter_obj.operator in [TextFilterCriterion.IS_EMPTY, TextFilterCriterion.IS_NOT_EMPTY]:
                return op_name
            else:
                return f"{op_name} '{filter_obj.value}'"
        elif isinstance(filter_obj, NumberFilterCriterion):
            # Number filter
            op_names = {
                NumberFilterCriterion.EQUALS: "Equals",
                NumberFilterCriterion.NOT_EQUALS: "Does not equal",
                NumberFilterCriterion.GREATER_THAN: "Greater than",
                NumberFilterCriterion.GREATER_EQUAL: "Greater than or equal",
                NumberFilterCriterion.LESS_THAN: "Less than",
                NumberFilterCriterion.LESS_EQUAL: "Less than or equal",
                NumberFilterCriterion.BETWEEN: "Between",
                NumberFilterCriterion.NOT_BETWEEN: "Not between",
                NumberFilterCriterion.TOP_N: "Top",
                NumberFilterCriterion.BOTTOM_N: "Bottom",
                NumberFilterCriterion.ABOVE_AVERAGE: "Above average",
                NumberFilterCriterion.BELOW_AVERAGE: "Below average"
            }
            op_name = op_names.get(filter_obj.operator, "Unknown")
            if filter_obj.operator in [NumberFilterCriterion.ABOVE_AVERAGE, NumberFilterCriterion.BELOW_AVERAGE]:
                return op_name
            elif filter_obj.operator in [NumberFilterCriterion.TOP_N, NumberFilterCriterion.BOTTOM_N]:
                return f"{op_name} {filter_obj.n} items"
            elif filter_obj.operator in [NumberFilterCriterion.BETWEEN, NumberFilterCriterion.NOT_BETWEEN]:
                return f"{op_name} {filter_obj.value1} and {filter_obj.value2}"
            else:
                return f"{op_name} {filter_obj.value1}"
        elif isinstance(filter_obj, DateFilterCriterion):
            # Date filter
            op_names = {
                DateFilterCriterion.EQUALS: "Equals",
                DateFilterCriterion.NOT_EQUALS: "Does not equal",
                DateFilterCriterion.BEFORE: "Before",
                DateFilterCriterion.AFTER: "After",
                DateFilterCriterion.BETWEEN: "Between",
                DateFilterCriterion.NOT_BETWEEN: "Not between",
                DateFilterCriterion.TODAY: "Today",
                DateFilterCriterion.YESTERDAY: "Yesterday",
                DateFilterCriterion.TOMORROW: "Tomorrow",
                DateFilterCriterion.THIS_WEEK: "This week",
                DateFilterCriterion.LAST_WEEK: "Last week",
                DateFilterCriterion.NEXT_WEEK: "Next week",
                DateFilterCriterion.THIS_MONTH: "This month",
                DateFilterCriterion.LAST_MONTH: "Last month",
                DateFilterCriterion.NEXT_MONTH: "Next month",
                DateFilterCriterion.THIS_QUARTER: "This quarter",
                DateFilterCriterion.LAST_QUARTER: "Last quarter",
                DateFilterCriterion.NEXT_QUARTER: "Next quarter",
                DateFilterCriterion.THIS_YEAR: "This year",
                DateFilterCriterion.LAST_YEAR: "Last year",
                DateFilterCriterion.NEXT_YEAR: "Next year"
            }
            op_name = op_names.get(filter_obj.operator, "Unknown")
            if filter_obj.operator in [DateFilterCriterion.TODAY, DateFilterCriterion.YESTERDAY, 
                                      DateFilterCriterion.TOMORROW, DateFilterCriterion.THIS_WEEK,
                                      DateFilterCriterion.LAST_WEEK, DateFilterCriterion.NEXT_WEEK,
                                      DateFilterCriterion.THIS_MONTH, DateFilterCriterion.LAST_MONTH,
                                      DateFilterCriterion.NEXT_MONTH, DateFilterCriterion.THIS_QUARTER,
                                      DateFilterCriterion.LAST_QUARTER, DateFilterCriterion.NEXT_QUARTER,
                                      DateFilterCriterion.THIS_YEAR, DateFilterCriterion.LAST_YEAR,
                                      DateFilterCriterion.NEXT_YEAR]:
                return op_name
            elif filter_obj.operator in [DateFilterCriterion.BETWEEN, DateFilterCriterion.NOT_BETWEEN]:
                date1_str = filter_obj.date1.strftime('%Y-%m-%d') if filter_obj.date1 else "?"
                date2_str = filter_obj.date2.strftime('%Y-%m-%d') if filter_obj.date2 else "?"
                return f"{op_name} {date1_str} and {date2_str}"
            else:
                date_str = filter_obj.date1.strftime('%Y-%m-%d') if filter_obj.date1 else "?"
                return f"{op_name} {date_str}"
        else:
            return f"Custom filter: {str(filter_obj)}"
    
    def _update_stats(self) -> None:
        """Update the statistics label."""
        total_filters = len(self.active_filters)
        if total_filters == 0:
            self.stats_label.SetLabel("")
        elif total_filters == 1:
            self.stats_label.SetLabel("1 active filter")
        else:
            self.stats_label.SetLabel(f"{total_filters} active filters")
    
    def _on_select_all(self, event: wx.CommandEvent) -> None:
        """Handle Select All button click."""
        for i in range(self.filters_list.GetCount()):
            self.filters_list.Check(i, True)
        self._on_filter_check(None)
    
    def _on_select_none(self, event: wx.CommandEvent) -> None:
        """Handle Select None button click."""
        for i in range(self.filters_list.GetCount()):
            self.filters_list.Check(i, False)
        self._on_filter_check(None)
    
    def _on_clear_all(self, event: wx.CommandEvent) -> None:
        """Handle Clear All Filters button click."""
        if self.active_filters:
            dlg = wx.MessageDialog(self, 
                "Are you sure you want to clear all filters?", 
                "Confirm Clear All",
                wx.YES_NO | wx.ICON_QUESTION)
            
            if dlg.ShowModal() == wx.ID_YES:
                # Mark all filters for removal
                self.filters_to_remove = list(self.active_filters.keys())
                self.filter_modified = True
                # Select all items to show what will be removed
                for i in range(self.filters_list.GetCount()):
                    self.filters_list.Check(i, True)
                self._on_filter_check(None)
            
            dlg.Destroy()
    
    def _on_filter_check(self, event: Optional[wx.CommandEvent]) -> None:
        """Handle filter checkbox changes."""
        # Update the list of filters to remove
        self.filters_to_remove = []
        for i in range(self.filters_list.GetCount()):
            if self.filters_list.IsChecked(i) and i in self.column_filter_map:
                col_index = self.column_filter_map[i]
                self.filters_to_remove.append(col_index)
        
        # Update button states
        has_checked = len(self.filters_to_remove) > 0
        if has_checked:
            self.filter_modified = True
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        self.EndModal(wx.ID_OK)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filters_to_remove = []
        self.filter_modified = False
        self.EndModal(wx.ID_CANCEL)
    
    def get_filters_to_remove(self) -> List[int]:
        """Get the list of column indices for filters to remove."""
        return self.filters_to_remove
    
    def was_modified(self) -> bool:
        """Check if any filters were modified."""
        return self.filter_modified
