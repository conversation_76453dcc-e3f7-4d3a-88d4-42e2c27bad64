<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M4.326,10,6.5,6.667V1.5h3V6.667L11.674,10Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6,6.519V2H5V1h6V2H10V6.519l2.419,3.708,0,0L12,10h-.923l-2-3.06L9,6.667V2H7V6.667l-.081.273-2,3.06H4l-.416.232,0,0Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M12.675,14.5H3.325a1,1,0,0,1-.843-1.538L4,10.5h8l1.518,2.462A1,1,0,0,1,12.675,14.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12.676,15H3.324A1.5,1.5,0,0,1,2.06,12.693l1.514-2.456L4,10h8l.426.237L13.944,12.7A1.5,1.5,0,0,1,12.676,15Zm-8.4-4L2.907,13.225A.5.5,0,0,0,3.324,14h9.352a.5.5,0,0,0,.421-.77L11.721,11Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
