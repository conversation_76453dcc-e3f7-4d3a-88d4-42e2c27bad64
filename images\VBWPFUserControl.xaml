<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10,6.5A2.5,2.5,0,1,1,12.5,4,2.5,2.5,0,0,1,10,6.5Zm0,0A4.5,4.5,0,0,0,5.62,10H5.114A4.918,4.918,0,0,0,5,11h9.5A4.5,4.5,0,0,0,10,6.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11.832,6.359a3,3,0,1,0-3.664,0A5.006,5.006,0,0,0,5.114,10H6.142A3.992,3.992,0,0,1,14,11h1A5,5,0,0,0,11.832,6.359ZM10,6a2,2,0,1,1,2-2A2,2,0,0,1,10,6Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,12v3H4V12Zm3.828,1.854v-.708L8.707,11.025,8,11.732,9.768,13.5,8,15.268l.707.707ZM.172,13.146v.708l2.121,2.121L3,15.268,1.232,13.5,3,11.732l-.707-.707Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
