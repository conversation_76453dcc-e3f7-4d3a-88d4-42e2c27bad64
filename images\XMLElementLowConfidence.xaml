<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="white" Color="#ffffff" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M12,3V6.979a5.04,5.04,0,0,0-5,5L7,12H2V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13,2.5V7.08a5,5,0,0,0-1-.1V3H2v9H7a5.07,5.07,0,0,0,.1,1H1.5L1,12.5V2.5L1.5,2h11ZM5.276,5,3.106,7.179v.707L5.277,10.06l.707-.707L4.168,7.533,5.985,5.705Zm2.83.709L9.875,7.47A5.009,5.009,0,0,1,10.923,7.1L8.811,5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12,16a4,4,0,1,1,4-4A4,4,0,0,1,12,16Z" />
              <GeometryDrawing Brush="{DynamicResource white}" Geometry="F1M12.625,14.5A.625.625,0,1,1,12,13.875.625.625,0,0,1,12.625,14.5Zm.587-4.842a1.534,1.534,0,0,1,.5,1.137c0,.775-.546,1.116-1.085,1.638a.543.543,0,0,0-.167.264,1.55,1.55,0,0,0-.033.508h-.858v-.3a1.29,1.29,0,0,1,.094-.508A1.692,1.692,0,0,1,11.9,12c.3-.373.426-.315.863-.879a.457.457,0,0,0,.094-.326.859.859,0,0,0-1.714,0h-.857A1.731,1.731,0,0,1,13.212,9.658Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
