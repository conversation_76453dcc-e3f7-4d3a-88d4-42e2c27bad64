#!/usr/bin/env python3
"""
Test script to verify that the recent AI commands feature is working in the main program.
This script simulates AI command execution and checks if commands are being tracked.
"""

import sys
import os
import json
import tempfile
from unittest.mock import Mock, patch

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_recent_commands_integration():
    """Test that recent commands integration is working."""
    print("🧪 Testing Recent AI Commands Integration")
    print("=" * 50)
    
    try:
        # Import the main window class
        from jedit2.main_window import MainWindow
        from jedit2.utils.config_manager import ConfigManager
        
        print("✅ Successfully imported MainWindow")
        
        # Create a temporary config directory
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Using temporary config directory: {temp_dir}")
            
            # Create a mock config manager
            config_manager = ConfigManager(config_dir=temp_dir)
            
            # Test the recent AI commands methods directly
            print("\n🔧 Testing Recent AI Commands Methods")
            
            # Create a minimal mock main window with just the methods we need
            class MockMainWindow:
                def __init__(self):
                    self.config_manager = config_manager
                    self.recent_ai_commands = []
                    self.max_recent_ai_commands = 10
                    self.ai_recent_combo = Mock()
                    
                    # Load methods from the real MainWindow
                    from jedit2.main_window import MainWindow
                    real_window = MainWindow.__new__(MainWindow)
                    
                    # Copy the methods we need
                    self._load_recent_ai_commands = real_window._load_recent_ai_commands.__get__(self, MockMainWindow)
                    self._save_recent_ai_commands = real_window._save_recent_ai_commands.__get__(self, MockMainWindow)
                    self._add_to_recent_ai_commands = real_window._add_to_recent_ai_commands.__get__(self, MockMainWindow)
                    self._update_recent_ai_commands_dropdown = real_window._update_recent_ai_commands_dropdown.__get__(self, MockMainWindow)
                    
                    # Initialize
                    self._load_recent_ai_commands()
            
            # Create mock window
            mock_window = MockMainWindow()
            
            # Test 1: Initial state
            print("  ✅ Test 1: Initial state")
            assert len(mock_window.recent_ai_commands) == 0, "Should start with no recent commands"
            print("     - Recent commands list is empty: ✓")
            
            # Test 2: Add commands
            print("  ✅ Test 2: Add commands")
            test_commands = [
                "open price.json",
                "sort column A ascending", 
                "make column B bold",
                "delete row 5"
            ]
            
            for cmd in test_commands:
                mock_window._add_to_recent_ai_commands(cmd)
            
            assert len(mock_window.recent_ai_commands) == 4, f"Should have 4 commands, got {len(mock_window.recent_ai_commands)}"
            assert mock_window.recent_ai_commands[0] == "delete row 5", "Most recent should be first"
            print(f"     - Added {len(test_commands)} commands: ✓")
            print(f"     - Most recent command is first: ✓")
            
            # Test 3: Persistence
            print("  ✅ Test 3: Persistence")
            mock_window2 = MockMainWindow()
            assert len(mock_window2.recent_ai_commands) == 4, "Commands should persist"
            assert mock_window2.recent_ai_commands[0] == "delete row 5", "Order should be preserved"
            print("     - Commands persist across instances: ✓")
            
            # Test 4: Check configuration storage
            print("  ✅ Test 4: Configuration storage")
            recent_commands_str = config_manager.get_setting("recent_ai_commands", "")
            assert recent_commands_str != "", "Commands should be stored in config"
            
            stored_commands = json.loads(recent_commands_str)
            assert len(stored_commands) == 4, "All commands should be stored"
            assert stored_commands[0] == "delete row 5", "Order should be preserved in storage"
            print("     - Commands stored in configuration: ✓")
            print("     - Correct order preserved: ✓")
            
            print(f"\n📊 Final state: {len(mock_window.recent_ai_commands)} recent commands")
            print("Recent commands:")
            for i, cmd in enumerate(mock_window.recent_ai_commands):
                print(f"   {i+1}. {cmd}")
            
            print(f"\n🎉 All integration tests passed!")
            return True
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ai_submit_integration():
    """Test that AI submit handlers include recent commands tracking."""
    print("\n🔧 Testing AI Submit Handler Integration")
    print("=" * 50)
    
    try:
        # Check production AI manager
        from jedit2.utils.ai_manager_production import integrate_production_ai_manager
        print("✅ Production AI manager import successful")
        
        # Check AI system integrator
        from jedit2.utils.ai_system_integrator import AISystemIntegrator
        print("✅ AI system integrator import successful")
        
        # Check that the methods contain recent commands tracking
        import inspect
        
        # Check production manager source
        prod_source = inspect.getsource(integrate_production_ai_manager)
        if "_add_to_recent_ai_commands" in prod_source:
            print("✅ Production AI manager includes recent commands tracking")
        else:
            print("❌ Production AI manager missing recent commands tracking")
            return False
        
        # Check system integrator source  
        integrator_source = inspect.getsource(AISystemIntegrator)
        if "_add_to_recent_ai_commands" in integrator_source:
            print("✅ AI system integrator includes recent commands tracking")
        else:
            print("❌ AI system integrator missing recent commands tracking")
            return False
            
        print("🎉 AI submit handler integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ AI submit integration test failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Recent AI Commands Integration Test Suite")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_recent_commands_integration()
    test2_passed = test_ai_submit_integration()
    
    print(f"\n📊 Test Results:")
    print(f"   Recent Commands Methods: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   AI Submit Integration:   {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 All tests passed! Recent AI commands feature is properly integrated.")
    else:
        print(f"\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)
