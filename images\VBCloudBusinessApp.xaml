<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12,7.429l-3.295,3.3H8L4.629,7.356,1,10.985V9.571L4.275,6.3h.707L8.351,9.664,12,6.015ZM15,2V3.414l-2,2V4Z" />
              </DrawingGroup>
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M15.5,1.5v7H13v-5L12.5,3h-4V1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M16,1.5v7l-.5.5H13V8h2V2H9V3H8V1.5L8.5,1h7Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M12.5,13.5H.5V3.5h12Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12.5,14H.5L0,13.5V3.5L.5,3h12l.5.5v10ZM1,13H12V4H1Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
