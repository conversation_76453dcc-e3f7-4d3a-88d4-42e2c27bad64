<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M2.5,5.5h11v9H2.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M11.9,5a1.816,1.816,0,0,0,.432-.878,1.913,1.913,0,0,0-1.563-2.2C9.588,1.722,9,2.575,8.612,3.342c-.076.151-.295.641-.526,1.16L8,4.685l-.1-.217Q7.657,3.9,7.4,3.342c-.386-.767-.971-1.62-2.157-1.423a1.91,1.91,0,0,0-1.564,2.2A1.816,1.816,0,0,0,4.106,5H2.5L2,5.5v9l.5.5h11l.5-.5v-9L13.5,5ZM9.506,3.791c.458-.913.764-.942,1.1-.885a.91.91,0,0,1,.744,1.049,1.071,1.071,0,0,1-.516.68A7.276,7.276,0,0,1,10.187,5H8.958L9,4.91C9.222,4.409,9.432,3.937,9.506,3.791Zm-4.33.843a1.07,1.07,0,0,1-.515-.679A.908.908,0,0,1,5.4,2.906h0c.33-.057.638-.026,1.095.886.072.143.274.6.492,1.085L7.048,5H5.819A7.287,7.287,0,0,1,5.176,4.634ZM7.5,14H3V6H7.5ZM13,14H8.5V6H13Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
