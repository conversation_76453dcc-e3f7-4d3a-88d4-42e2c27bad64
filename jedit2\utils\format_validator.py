"""Format validation for different file types in JEdit2.

This module provides validation for various file formats including JSON, YAML, CSV, etc.
"""

import json
import yaml
import csv
import io
import re
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Result of format validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    line_number: Optional[int] = None
    column_number: Optional[int] = None
    is_correctable: bool = False
    correction_suggestions: List[str] = None

    def __post_init__(self):
        """Initialize correction_suggestions if None."""
        if self.correction_suggestions is None:
            self.correction_suggestions = []


class FormatValidator:
    """Validator for different file formats."""
    
    def validate_json(self, content: str) -> ValidationResult:
        """Validate JSON format.
        
        Args:
            content: JSON content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        line_number = None
        column_number = None
        
        try:
            json.loads(content)
            # Additional JSON style checks
            warnings.extend(self._check_json_style(content))
            # JSON is valid but may have style issues that can be corrected
            is_correctable = len(warnings) > 0
            suggestions = ["Format and fix style issues"] if is_correctable else []
            return ValidationResult(True, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)
        except json.JSONDecodeError as e:
            errors.append(f"JSON Syntax Error: {e.msg}")
            line_number = e.lineno
            column_number = e.colno
            # JSON syntax errors are often correctable
            suggestions = ["Remove trailing commas", "Fix quote consistency", "Add missing brackets", "Fix indentation"]
            return ValidationResult(False, errors, warnings, line_number, column_number, is_correctable=True, correction_suggestions=suggestions)
        except Exception as e:
            errors.append(f"JSON Error: {str(e)}")
            return ValidationResult(False, errors, warnings)
    
    def validate_yaml(self, content: str) -> ValidationResult:
        """Validate YAML format.
        
        Args:
            content: YAML content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        line_number = None
        
        try:
            yaml.safe_load(content)
            # Additional YAML style checks
            warnings.extend(self._check_yaml_style(content))
            # YAML is valid but may have style issues that can be corrected
            is_correctable = len(warnings) > 0
            suggestions = ["Fix indentation and style issues"] if is_correctable else []
            return ValidationResult(True, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)
        except yaml.YAMLError as e:
            if hasattr(e, 'problem_mark'):
                line_number = e.problem_mark.line + 1
                column_number = e.problem_mark.column + 1
                errors.append(f"YAML Error at line {line_number}, column {column_number}: {e.problem}")
            else:
                errors.append(f"YAML Error: {str(e)}")
            # YAML syntax errors are often correctable
            suggestions = ["Convert tabs to spaces", "Fix indentation", "Add missing colons"]
            return ValidationResult(False, errors, warnings, line_number, is_correctable=True, correction_suggestions=suggestions)
        except Exception as e:
            errors.append(f"YAML Error: {str(e)}")
            return ValidationResult(False, errors, warnings)
    
    def validate_csv(self, content: str) -> ValidationResult:
        """Validate CSV format.
        
        Args:
            content: CSV content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        
        try:
            # Try to parse as CSV
            reader = csv.reader(io.StringIO(content))
            rows = list(reader)
            
            if not rows:
                warnings.append("CSV file is empty")
                return ValidationResult(True, errors, warnings)
            
            # Check for consistent column counts
            if len(rows) > 1:
                first_row_cols = len(rows[0])
                inconsistent_rows = []
                
                for i, row in enumerate(rows[1:], 2):
                    if len(row) != first_row_cols:
                        inconsistent_rows.append(f"Row {i}: {len(row)} columns (expected {first_row_cols})")
                
                if inconsistent_rows:
                    warnings.append("Inconsistent column counts:")
                    warnings.extend(inconsistent_rows)
            
            # Check for common CSV issues
            warnings.extend(self._check_csv_style(content))

            # Check for missing headers (heuristic: first row contains only numbers/dates)
            missing_headers = False
            if rows and len(rows) > 1:
                first_row = rows[0]
                # More aggressive detection: if most cells in first row look like data
                data_like_cells = 0
                total_non_empty_cells = 0

                for cell in first_row:
                    if cell.strip():
                        total_non_empty_cells += 1
                        # Check if cell looks like data rather than a header
                        if (cell.strip().replace('.', '').replace('-', '').replace('/', '').isdigit() or
                            re.match(r'^\d{4}-\d{2}-\d{2}$', cell.strip()) or  # Date pattern
                            re.match(r'^\d+\.\d+$', cell.strip()) or  # Decimal number
                            cell.strip().lower() in ['true', 'false', 'yes', 'no']):  # Boolean-like values
                            data_like_cells += 1

                # If more than half the cells look like data, probably missing headers
                if total_non_empty_cells > 1 and data_like_cells >= total_non_empty_cells / 2:
                    warnings.append("First row appears to be data rather than headers")
                    missing_headers = True

            # CSV is valid but may have inconsistencies that can be corrected
            is_correctable = len(warnings) > 0 or len(inconsistent_rows) > 0 or missing_headers
            suggestions = ["Standardize column counts", "Fix formatting", "Add headers"] if is_correctable else []
            return ValidationResult(True, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)

        except csv.Error as e:
            errors.append(f"CSV Error: {str(e)}")
            # CSV errors are often correctable
            suggestions = ["Fix quote escaping", "Standardize delimiters"]
            return ValidationResult(False, errors, warnings, is_correctable=True, correction_suggestions=suggestions)
        except Exception as e:
            errors.append(f"CSV Error: {str(e)}")
            return ValidationResult(False, errors, warnings)
    
    def validate_python(self, content: str) -> ValidationResult:
        """Validate Python syntax.
        
        Args:
            content: Python content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        line_number = None
        
        try:
            compile(content, '<string>', 'exec')
            # Additional Python style checks
            warnings.extend(self._check_python_style(content))
            # Python is valid but may have style issues that can be corrected
            is_correctable = len(warnings) > 0
            suggestions = ["Fix style issues", "Convert tabs to spaces", "Fix line length"] if is_correctable else []
            return ValidationResult(True, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)
        except SyntaxError as e:
            errors.append(f"Python Syntax Error: {e.msg}")
            line_number = e.lineno
            # Python syntax errors are sometimes correctable
            suggestions = ["Fix indentation", "Convert tabs to spaces", "Add missing colons"]
            return ValidationResult(False, errors, warnings, line_number, is_correctable=True, correction_suggestions=suggestions)
        except Exception as e:
            errors.append(f"Python Error: {str(e)}")
            return ValidationResult(False, errors, warnings)
    
    def validate_xml(self, content: str) -> ValidationResult:
        """Validate XML format.
        
        Args:
            content: XML content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        line_number = None
        
        try:
            import xml.etree.ElementTree as ET
            ET.fromstring(content)
            # Additional XML style checks
            warnings.extend(self._check_xml_style(content))
            # XML is valid but may have style issues that can be corrected
            is_correctable = len(warnings) > 0
            suggestions = ["Add XML declaration", "Fix formatting"] if is_correctable else []
            return ValidationResult(True, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)
        except ET.ParseError as e:
            errors.append(f"XML Parse Error: {str(e)}")
            # Try to extract line number from error message
            match = re.search(r'line (\d+)', str(e))
            if match:
                line_number = int(match.group(1))
            # XML parse errors are often correctable
            suggestions = ["Add XML declaration", "Fix attribute quoting", "Balance tags"]
            return ValidationResult(False, errors, warnings, line_number, is_correctable=True, correction_suggestions=suggestions)
        except Exception as e:
            errors.append(f"XML Error: {str(e)}")
            return ValidationResult(False, errors, warnings)
    
    def validate_markdown(self, content: str) -> ValidationResult:
        """Validate Markdown format.
        
        Args:
            content: Markdown content to validate
            
        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []
        
        lines = content.split('\n')
        
        # Check for common Markdown issues
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Check for malformed headers
            if line_stripped.startswith('#'):
                # Headers should have space after #
                if not re.match(r'^#+\s+', line_stripped):
                    warnings.append(f"Line {i}: Header should have space after # symbols")
                
                # Check for inconsistent header levels (skipping levels)
                header_level = len(line_stripped) - len(line_stripped.lstrip('#'))
                if header_level > 6:
                    warnings.append(f"Line {i}: Header level {header_level} exceeds maximum (6)")
            
            # Check for malformed links
            link_pattern = r'\[([^\]]*)\]\(([^)]*)\)'
            links = re.findall(link_pattern, line)
            for link_text, link_url in links:
                if not link_text.strip():
                    warnings.append(f"Line {i}: Link has empty text")
                if not link_url.strip():
                    errors.append(f"Line {i}: Link has empty URL")

            # Check for spaced link formatting [text] (url) instead of [text](url)
            spaced_link_pattern = r'\[([^\]]+)\]\s+\(([^)]+)\)'
            if re.search(spaced_link_pattern, line):
                warnings.append(f"Line {i}: Link has space between text and URL (should be [text](url))")
            
            # Check for images without alt text
            img_pattern = r'!\[([^\]]*)\]\(([^)]*)\)'
            images = re.findall(img_pattern, line)
            for alt_text, img_url in images:
                if not alt_text.strip():
                    warnings.append(f"Line {i}: Image missing alt text (accessibility)")
                if not img_url.strip():
                    errors.append(f"Line {i}: Image has empty URL")
            
            # Check for malformed lists
            if re.match(r'^[\s]*[-*+]\s*$', line):
                warnings.append(f"Line {i}: Empty list item")
            elif re.match(r'^[\s]*[-*+](?!\s)', line):
                warnings.append(f"Line {i}: List item should have space after marker")
            
            # Check for inconsistent list markers
            if re.match(r'^[\s]*[-*+]\s', line):
                # Check if we have mixed list markers in the document
                # This is a simplified check - could be enhanced for section-based consistency
                pass
            
            # Check for malformed tables
            if '|' in line and line.strip():
                if line.count('|') < 2:
                    warnings.append(f"Line {i}: Possible malformed table (insufficient pipes)")
                elif not line.strip().startswith('|') or not line.strip().endswith('|'):
                    warnings.append(f"Line {i}: Table row should start and end with |")
        
        # Check for code blocks
        code_block_count = content.count('```')
        if code_block_count % 2 != 0:
            errors.append("Unmatched code block markers (```)")
        
        # Check for inline code
        inline_code_count = content.count('`')
        # This is trickier because ` can be escaped, but basic check
        if inline_code_count % 2 != 0:
            warnings.append("Possibly unmatched inline code markers (`)")
        
        # Check for mixed list markers
        list_markers = set()
        for line in lines:
            if re.match(r'^[\s]*[-*+]\s', line):
                marker = re.match(r'^[\s]*([-*+])', line).group(1)
                list_markers.add(marker)

        if len(list_markers) > 1:
            warnings.append(f"Mixed list markers detected: {', '.join(sorted(list_markers))} (consider standardizing)")

        # Additional style checks
        warnings.extend(self._check_markdown_style(content))

        is_valid = len(errors) == 0
        # Markdown issues are often correctable
        is_correctable = len(errors) > 0 or len(warnings) > 0
        suggestions = ["Fix header spacing", "Remove trailing whitespace", "Fix list formatting", "Balance code blocks", "Standardize list markers"] if is_correctable else []
        return ValidationResult(is_valid, errors, warnings, is_correctable=is_correctable, correction_suggestions=suggestions)

    def validate_xlsx(self, content: str) -> ValidationResult:
        """Validate XLSX format.

        Args:
            content: XLSX content to validate (binary data as string)

        Returns:
            ValidationResult with validation status and any errors
        """
        errors = []
        warnings = []

        try:
            # For XLSX files, we need to handle binary content
            # This is a basic validation - in practice, you'd want to use openpyxl
            import io
            import zipfile

            # XLSX files are ZIP archives
            if isinstance(content, str):
                # If content is string, it might be base64 encoded or we need to read as binary
                warnings.append("XLSX validation requires binary file content")
                return ValidationResult(True, errors, warnings)

            # Try to open as ZIP (XLSX is a ZIP file)
            try:
                with zipfile.ZipFile(io.BytesIO(content), 'r') as zip_file:
                    # Check for required XLSX structure
                    required_files = ['[Content_Types].xml', '_rels/.rels']
                    missing_files = []

                    for required_file in required_files:
                        if required_file not in zip_file.namelist():
                            missing_files.append(required_file)

                    if missing_files:
                        errors.append(f"Missing required XLSX files: {', '.join(missing_files)}")

                    # Check for worksheets
                    worksheet_files = [f for f in zip_file.namelist() if f.startswith('xl/worksheets/')]
                    if not worksheet_files:
                        warnings.append("No worksheets found in XLSX file")

            except zipfile.BadZipFile:
                errors.append("File is not a valid ZIP archive (XLSX files must be ZIP format)")

        except Exception as e:
            errors.append(f"XLSX validation error: {str(e)}")

        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings)

    def validate_format(self, content: str, file_type: str) -> ValidationResult:
        """Validate content based on file type.
        
        Args:
            content: Content to validate
            file_type: Type of file (json, yaml, csv, python, xml, etc.)
            
        Returns:
            ValidationResult with validation status and any errors
        """
        if file_type.lower() == 'json':
            return self.validate_json(content)
        elif file_type.lower() in ['yaml', 'yml']:
            return self.validate_yaml(content)
        elif file_type.lower() == 'csv':
            return self.validate_csv(content)
        elif file_type.lower() in ['python', 'py']:
            return self.validate_python(content)
        elif file_type.lower() == 'xml':
            return self.validate_xml(content)
        elif file_type.lower() in ['markdown', 'md']:
            return self.validate_markdown(content)
        elif file_type.lower() in ['xlsx', 'xls']:
            return self.validate_xlsx(content)
        else:
            return ValidationResult(True, [], [f"No specific validation available for {file_type} files"])
    
    def _check_json_style(self, content: str) -> List[str]:
        """Check JSON style and best practices."""
        warnings = []
        
        # Check for trailing commas (common JSON error)
        if re.search(r',\s*[}\]]', content):
            warnings.append("Trailing commas detected (not valid in JSON)")
        
        # Check for single quotes (should be double quotes)
        if "'" in content and '"' in content:
            warnings.append("Mix of single and double quotes detected (JSON requires double quotes)")
        
        return warnings
    
    def _check_yaml_style(self, content: str) -> List[str]:
        """Check YAML style and best practices."""
        warnings = []

        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # Check for tabs (YAML should use spaces)
            if '\t' in line:
                warnings.append(f"Line {i}: Tab characters detected (YAML should use spaces)")

            # Check for missing space after colon
            if ':' in line and not line.strip().startswith('#'):
                # Look for pattern key:value (no space after colon)
                if re.search(r':\S', line):
                    warnings.append(f"Line {i}: Missing space after colon (should be 'key: value')")

        return warnings
    
    def _check_csv_style(self, content: str) -> List[str]:
        """Check CSV style and best practices."""
        warnings = []

        lines = content.split('\n')
        if lines:
            # Check if first row looks like a header
            first_row = lines[0]
            if not re.match(r'^[A-Za-z]', first_row.strip()):
                warnings.append("First row may not be a proper header (consider adding column names)")

        # Check for tab delimiters (often from Excel exports)
        if '\t' in content and content.count('\t') > content.count(','):
            warnings.append("Tab-delimited format detected (can be converted to comma-delimited)")

        # Check for quote escaping issues
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if '"' in line:
                # Look for unescaped quotes within quoted fields
                # Pattern: "text"more"text" (quotes inside quoted field)
                if re.search(r'"[^"]*"[^",\n]*"[^"]*"', line):
                    warnings.append(f"Line {i}: Possible unescaped quotes in quoted field")
                # Pattern: field with quotes but not properly quoted
                elif re.search(r'[^,"]+"[^"]*"[^",]+', line):
                    warnings.append(f"Line {i}: Field contains quotes but is not properly quoted")

        return warnings
    
    def _check_python_style(self, content: str) -> List[str]:
        """Check Python style and best practices."""
        warnings = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # Check for tabs and spaces mixing
            if '\t' in line and '    ' in line:
                warnings.append(f"Line {i}: Mixed tabs and spaces detected")
            
            # Check line length (PEP 8 recommends max 79 characters)
            if len(line) > 88:  # Using 88 as per user's black configuration
                warnings.append(f"Line {i}: Line too long ({len(line)} > 88 characters)")
        
        return warnings
    
    def _check_xml_style(self, content: str) -> List[str]:
        """Check XML style and best practices."""
        warnings = []
        
        # Check for XML declaration
        if not content.strip().startswith('<?xml'):
            warnings.append("Missing XML declaration")
        
        return warnings
    
    def _check_markdown_style(self, content: str) -> List[str]:
        """Check Markdown style and best practices."""
        warnings = []
        
        lines = content.split('\n')
        
        # Check for trailing whitespace
        for i, line in enumerate(lines, 1):
            if line.rstrip() != line and line.strip():  # Has trailing whitespace and isn't empty
                warnings.append(f"Line {i}: Trailing whitespace detected")
        
        # Check for multiple consecutive blank lines
        blank_count = 0
        for i, line in enumerate(lines, 1):
            if not line.strip():
                blank_count += 1
            else:
                if blank_count > 2:
                    warnings.append(f"Line {i-blank_count}: Multiple consecutive blank lines (consider using just one)")
                blank_count = 0
        
        # Check for inconsistent heading style (ATX vs Setext)
        has_atx = any(line.strip().startswith('#') for line in lines)
        has_setext = any(i < len(lines) - 1 and lines[i+1].strip() and 
                        all(c in '=-' for c in lines[i+1].strip()) 
                        for i, line in enumerate(lines) if line.strip())
        
        if has_atx and has_setext:
            warnings.append("Mixed heading styles detected (ATX # and Setext underline)")
        
        # Check for long lines (readability)
        for i, line in enumerate(lines, 1):
            if len(line) > 100:  # Common Markdown line length recommendation
                warnings.append(f"Line {i}: Line is quite long ({len(line)} characters, consider breaking for readability)")
        
        return warnings 