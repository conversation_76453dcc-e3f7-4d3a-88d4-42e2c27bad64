<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,2.5v12l-.5.5H1.5L1,14.5V2.5L1.5,2h12Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11,3V2h1V3ZM10,3V2H9V3ZM8,3V2H7V3ZM6,3V2H5V3ZM4,3V2H3V3Zm8,12V14H11v1Zm-2,0V14H9v1ZM8,15V14H7v1ZM6,15V14H5v1ZM4,15V14H3v1Zm9-1v1h.5l.5-.5V14ZM2,14H1v.5l.5.5H2ZM14,4H13V5h1Zm0,2H13V7h1Zm0,2H13V9h1Zm0,2H13v1h1Zm0,2H13v1h1ZM2,4H1V5H2ZM2,6H1V7H2ZM2,8H1V9H2Zm0,2H1v1H2Zm0,2H1v1H2ZM13,3h1V2.5L13.5,2H13ZM2,3V2H1.5L1,2.5V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4,8v3H8.293L7.146,9.854l.708-.708,2,2v.708l-2,2-.708-.708L8.293,12H3.5L3,11.5v-4L3.5,7H11V5H3V4h8.5l.5.5v3l-.5.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
