"""Format validation dialog for JEdit2.

This module provides a dialog for displaying format validation results.
"""

import wx
from typing import Op<PERSON>
from .format_validator import ValidationResult


class FormatValidationDialog(wx.Dialog):
    """Dialog for displaying format validation results."""
    
    def __init__(self, parent: wx.Window, result: ValidationResult, file_type: str) -> None:
        """Initialize the format validation dialog.
        
        Args:
            parent: Parent window
            result: Validation result to display
            file_type: Type of file that was validated
        """
        title = f"{file_type.upper()} Format Validation"
        super().__init__(
            parent,
            title=title,
            size=(600, 400),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.result = result
        self.file_type = file_type
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Status header
        status_panel = self._create_status_panel()
        main_sizer.Add(status_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Results area
        if self.result.errors or self.result.warnings:
            results_text = self._create_results_text()
            main_sizer.Add(results_text, 1, wx.EXPAND | wx.ALL, 5)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
    
    def _create_status_panel(self) -> wx.Panel:
        """Create the status panel showing overall validation result."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Status icon
        if self.result.is_valid:
            if self.result.warnings:
                icon = wx.ArtProvider.GetBitmap(wx.ART_WARNING, wx.ART_MESSAGE_BOX, (32, 32))
                status_text = f"{self.file_type.upper()} format is valid with warnings"
            else:
                icon = wx.ArtProvider.GetBitmap(wx.ART_TICK_MARK, wx.ART_MESSAGE_BOX, (32, 32))
                status_text = f"{self.file_type.upper()} format is valid"
            status_color = wx.Colour(0, 128, 0)  # Green
        else:
            icon = wx.ArtProvider.GetBitmap(wx.ART_ERROR, wx.ART_MESSAGE_BOX, (32, 32))
            status_text = f"{self.file_type.upper()} format has errors"
            status_color = wx.Colour(200, 0, 0)  # Red
        
        # Icon
        icon_bitmap = wx.StaticBitmap(panel, bitmap=icon)
        sizer.Add(icon_bitmap, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        # Status text
        status_label = wx.StaticText(panel, label=status_text)
        font = status_label.GetFont()
        font.SetWeight(wx.FONTWEIGHT_BOLD)
        font.SetPointSize(font.GetPointSize() + 2)
        status_label.SetFont(font)
        status_label.SetForegroundColour(status_color)
        sizer.Add(status_label, 1, wx.ALIGN_CENTER_VERTICAL)
        
        # Summary info
        summary_parts = []
        if self.result.errors:
            summary_parts.append(f"{len(self.result.errors)} error(s)")
        if self.result.warnings:
            summary_parts.append(f"{len(self.result.warnings)} warning(s)")
        
        if summary_parts:
            summary_text = " • ".join(summary_parts)
            summary_label = wx.StaticText(panel, label=summary_text)
            summary_label.SetForegroundColour(wx.Colour(100, 100, 100))
            sizer.Add(summary_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.LEFT, 10)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_results_text(self) -> wx.TextCtrl:
        """Create the text control showing detailed results."""
        text_ctrl = wx.TextCtrl(
            self,
            style=wx.TE_MULTILINE | wx.TE_READONLY | wx.TE_RICH2
        )
        
        # Set font to monospace for better formatting
        font = wx.Font(10, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
        text_ctrl.SetFont(font)
        
        # Build content
        content = []
        
        if self.result.errors:
            content.append("ERRORS:")
            content.append("=" * 50)
            for i, error in enumerate(self.result.errors, 1):
                if self.result.line_number:
                    content.append(f"{i}. Line {self.result.line_number}: {error}")
                else:
                    content.append(f"{i}. {error}")
            content.append("")  # Empty line
        
        if self.result.warnings:
            content.append("WARNINGS:")
            content.append("=" * 50)
            for i, warning in enumerate(self.result.warnings, 1):
                content.append(f"{i}. {warning}")
        
        text_ctrl.SetValue("\n".join(content))
        
        # Color the errors and warnings
        if self.result.errors:
            # Find and color error section
            text = text_ctrl.GetValue()
            error_start = text.find("ERRORS:")
            if error_start != -1:
                error_end = text.find("WARNINGS:")
                if error_end == -1:
                    error_end = len(text)
                
                text_ctrl.SetStyle(error_start, error_end, 
                                 wx.TextAttr(wx.Colour(200, 0, 0)))
        
        if self.result.warnings:
            # Find and color warning section
            text = text_ctrl.GetValue()
            warning_start = text.find("WARNINGS:")
            if warning_start != -1:
                text_ctrl.SetStyle(warning_start, len(text), 
                                 wx.TextAttr(wx.Colour(200, 100, 0)))
        
        return text_ctrl
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Add stretch space
        sizer.AddStretchSpacer()
        
        # Close button
        close_button = wx.Button(panel, wx.ID_CLOSE, "Close")
        close_button.Bind(wx.EVT_BUTTON, self._on_close)
        sizer.Add(close_button, 0, wx.ALL, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button click."""
        self.EndModal(wx.ID_CLOSE) 