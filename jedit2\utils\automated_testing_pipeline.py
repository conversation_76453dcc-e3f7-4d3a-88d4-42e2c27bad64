#!/usr/bin/env python3
"""
Automated Testing Pipeline for JEdit2 AI System

Provides continuous testing, validation, and quality assurance
for AI command processing and system reliability.
"""

import logging
import json
import time
import threading
import schedule
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

try:
    from .test_case_generator import TestCaseGenerator, TestGenerationConfig
    from .failure_pattern_analyzer import FailurePatternAnalyzer
    from .state_validation_framework import StateValidationFramework
except ImportError:
    try:
        from test_case_generator import TestCaseGenerator, TestGenerationConfig
        from failure_pattern_analyzer import FailurePatternAnalyzer
        from state_validation_framework import StateValidationFramework
    except ImportError:
        # Create minimal stubs if components are missing
        class TestCaseGenerator:
            def __init__(self, *args, **kwargs):
                pass
            def generate_command_combination_tests(self, count):
                return []
            def generate_semantic_ambiguity_tests(self, count):
                return []
            def generate_boundary_condition_tests(self, count):
                return []

        class TestGenerationConfig:
            def __init__(self, *args, **kwargs):
                pass

        class FailurePatternAnalyzer:
            def __init__(self, *args, **kwargs):
                pass

        class StateValidationFramework:
            def __init__(self, *args, **kwargs):
                pass


class TestExecutionStatus(Enum):
    """Status of test execution."""

    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestPriority(Enum):
    """Priority levels for test execution."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TestExecution:
    """Represents a test execution."""

    test_id: str
    test_description: str
    query: str
    expected_commands: List[Dict[str, Any]]
    priority: TestPriority
    status: TestExecutionStatus
    execution_time: Optional[float]
    error_message: Optional[str]
    actual_commands: Optional[List[Dict[str, Any]]]
    validation_results: Dict[str, Any]
    timestamp: str


@dataclass
class PipelineReport:
    """Comprehensive pipeline execution report."""

    pipeline_id: str
    execution_start: str
    execution_end: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    error_tests: int
    skipped_tests: int
    success_rate: float
    average_execution_time: float
    critical_failures: List[str]
    performance_metrics: Dict[str, float]
    recommendations: List[str]


class AutomatedTestingPipeline:
    """
    Automated testing pipeline for continuous AI validation.

    Features:
    - Scheduled test execution
    - Priority-based test ordering
    - Parallel test execution
    - Real-time monitoring
    - Automated reporting
    - Failure alert system
    """

    def __init__(self, ai_manager, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the automated testing pipeline.

        Args:
            ai_manager: AI manager instance for testing
            config: Pipeline configuration
        """
        self.logger = logging.getLogger(__name__)
        self.ai_manager = ai_manager

        # Configuration
        self.config = self._initialize_config(config or {})

        # Testing components
        self.test_generator = None
        self.failure_analyzer = None
        self.state_validator = None
        self._initialize_testing_components()

        # Test management
        self.test_queue: List[TestExecution] = []
        self.executed_tests: List[TestExecution] = []
        self.active_tests: Dict[str, TestExecution] = {}

        # Pipeline state
        self.is_running = False
        self.pipeline_thread = None
        self.last_execution_time = None

        # Metrics and monitoring
        self.metrics = {
            "total_executions": 0,
            "total_tests_run": 0,
            "cumulative_success_rate": 0.0,
            "average_execution_time": 0.0,
            "critical_failures_count": 0,
            "last_24h_success_rate": 0.0,
        }

        # Setup scheduled tasks
        self._setup_scheduled_tasks()

    def _initialize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize pipeline configuration with defaults."""
        default_config = {
            "execution_schedule": "hourly",  # hourly, daily, weekly, manual
            "parallel_execution": True,
            "max_parallel_tests": 5,
            "test_timeout": 30.0,  # seconds
            "critical_failure_threshold": 0.1,  # 10%
            "success_rate_threshold": 0.95,  # 95%
            "enable_performance_monitoring": True,
            "enable_automated_reporting": True,
            "report_directory": "tests/reports",
            "enable_failure_alerts": True,
            "test_priorities": {
                "command_combinations": TestPriority.HIGH,
                "semantic_ambiguity": TestPriority.MEDIUM,
                "boundary_conditions": TestPriority.LOW,
                "regression_tests": TestPriority.CRITICAL,
            },
        }

        # Merge with provided config
        default_config.update(config)
        return default_config

    def _initialize_testing_components(self) -> None:
        """Initialize the testing framework components."""
        try:
            # Test case generator
            test_config = TestGenerationConfig(
                max_command_combinations=50,
                include_boundary_tests=True,
                complexity_range=(2, 8),
            )
            self.test_generator = TestCaseGenerator(test_config)

            # Failure pattern analyzer
            self.failure_analyzer = FailurePatternAnalyzer()

            # State validation framework
            self.state_validator = StateValidationFramework()

            self.logger.info("Testing components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize testing components: {e}")
            raise

    def _setup_scheduled_tasks(self) -> None:
        """Setup scheduled testing tasks."""
        schedule_type = self.config["execution_schedule"]

        if schedule_type == "hourly":
            schedule.every().hour.do(self.run_scheduled_tests)
        elif schedule_type == "daily":
            schedule.every().day.at("02:00").do(self.run_scheduled_tests)
        elif schedule_type == "weekly":
            schedule.every().monday.at("02:00").do(self.run_scheduled_tests)

        # Always schedule maintenance tasks
        schedule.every().day.at("01:00").do(self.run_maintenance_tasks)
        schedule.every().hour.do(self.update_metrics)

    def generate_test_suite(
        self, suite_type: str = "comprehensive"
    ) -> List[TestExecution]:
        """
        Generate a test suite for execution.

        Args:
            suite_type: Type of test suite (comprehensive, regression, smoke)

        Returns:
            List of test executions ready for pipeline
        """
        test_executions = []

        try:
            if suite_type == "comprehensive":
                # Generate all types of tests
                combo_tests = self.test_generator.generate_command_combination_tests(30)
                ambiguity_tests = self.test_generator.generate_semantic_ambiguity_tests(
                    20
                )
                boundary_tests = self.test_generator.generate_boundary_condition_tests(
                    15
                )

                all_tests = combo_tests + ambiguity_tests + boundary_tests

            elif suite_type == "regression":
                # Generate tests for known failure patterns
                combo_tests = self.test_generator.generate_command_combination_tests(20)
                all_tests = combo_tests

            elif suite_type == "smoke":
                # Generate basic smoke tests
                combo_tests = self.test_generator.generate_command_combination_tests(10)
                all_tests = combo_tests

            elif suite_type == "startup":
                # Generate minimal startup tests
                combo_tests = self.test_generator.generate_command_combination_tests(5)
                all_tests = combo_tests

            elif suite_type == "hourly":
                # Generate hourly monitoring tests
                combo_tests = self.test_generator.generate_command_combination_tests(15)
                all_tests = combo_tests

            else:
                self.logger.warning(f"Unknown suite type: {suite_type}")
                return []

            # Convert to test executions
            for test_case in all_tests:
                priority = self._determine_test_priority(test_case)

                execution = TestExecution(
                    test_id=f"auto_{test_case.test_id}_{int(time.time())}",
                    test_description=test_case.description,
                    query=test_case.query,
                    expected_commands=test_case.expected_commands,
                    priority=priority,
                    status=TestExecutionStatus.PENDING,
                    execution_time=None,
                    error_message=None,
                    actual_commands=None,
                    validation_results={},
                    timestamp=datetime.now().isoformat(),
                )
                test_executions.append(execution)

            self.logger.info(
                f"Generated {len(test_executions)} tests for {suite_type} suite"
            )
            return test_executions

        except Exception as e:
            self.logger.error(f"Failed to generate test suite: {e}")
            return []

    def _determine_test_priority(self, test_case) -> TestPriority:
        """Determine priority for a test case."""
        # Map test types to priorities from config
        test_type = test_case.test_type.value
        priority_map = self.config["test_priorities"]

        if test_type in priority_map:
            return priority_map[test_type]
        elif test_case.risk_level == "HIGH":
            return TestPriority.HIGH
        elif test_case.complexity_score >= 8:
            return TestPriority.MEDIUM
        else:
            return TestPriority.LOW

    def add_tests_to_queue(self, test_executions: List[TestExecution]) -> None:
        """Add tests to the execution queue with priority ordering."""
        self.test_queue.extend(test_executions)

        # Sort by priority (CRITICAL > HIGH > MEDIUM > LOW)
        priority_order = {
            TestPriority.CRITICAL: 0,
            TestPriority.HIGH: 1,
            TestPriority.MEDIUM: 2,
            TestPriority.LOW: 3,
        }

        self.test_queue.sort(key=lambda t: priority_order[t.priority])
        self.logger.info(f"Added {len(test_executions)} tests to queue")

    def execute_test(self, test_execution: TestExecution) -> TestExecution:
        """
        Execute a single test.

        Args:
            test_execution: Test to execute

        Returns:
            Updated test execution with results
        """
        start_time = time.time()
        test_execution.status = TestExecutionStatus.RUNNING

        try:
            self.logger.info(f"Executing test: {test_execution.test_id}")

            # Execute the AI query
            if hasattr(self.ai_manager, "get_ai_response_enhanced"):
                # Use enhanced manager if available
                commands, raw_response, analysis = (
                    self.ai_manager.get_ai_response_enhanced(test_execution.query)
                )
            else:
                # Fall back to basic AI manager
                commands = self.ai_manager.get_ai_response(test_execution.query)
                raw_response = str(commands)
                analysis = {}

            test_execution.actual_commands = commands
            execution_time = time.time() - start_time
            test_execution.execution_time = execution_time

            # Validate results
            validation_results = self._validate_test_results(
                test_execution, commands, analysis
            )
            test_execution.validation_results = validation_results

            # Determine test status
            if commands and validation_results.get("validation_passed", False):
                test_execution.status = TestExecutionStatus.PASSED
            elif commands:
                test_execution.status = TestExecutionStatus.FAILED
                test_execution.error_message = validation_results.get(
                    "error_message", "Validation failed"
                )
            else:
                test_execution.status = TestExecutionStatus.FAILED
                test_execution.error_message = f"No commands generated: {raw_response}"

            self.logger.info(
                f"Test {test_execution.test_id} completed: {test_execution.status.value} "
                f"in {execution_time:.2f}s"
            )

        except Exception as e:
            test_execution.status = TestExecutionStatus.ERROR
            test_execution.error_message = str(e)
            test_execution.execution_time = time.time() - start_time

            self.logger.error(f"Test {test_execution.test_id} error: {e}")

        return test_execution

    def _validate_test_results(
        self,
        test_execution: TestExecution,
        actual_commands: Optional[List[Dict[str, Any]]],
        analysis: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Validate test execution results."""
        validation_results = {
            "validation_passed": False,
            "issues_found": [],
            "performance_metrics": {},
            "analysis_data": analysis,
        }

        try:
            # Basic validation - commands generated
            if not actual_commands:
                validation_results["issues_found"].append("No commands generated")
                return validation_results

            # Command count validation
            expected_count = len(test_execution.expected_commands)
            actual_count = len(actual_commands)

            if actual_count != expected_count:
                validation_results["issues_found"].append(
                    f"Command count mismatch: expected {expected_count}, got {actual_count}"
                )

            # Command type validation
            expected_types = [
                cmd.get("command", "") for cmd in test_execution.expected_commands
            ]
            actual_types = [cmd.get("command", "") for cmd in actual_commands]

            for i, (expected, actual) in enumerate(zip(expected_types, actual_types)):
                if expected != actual:
                    validation_results["issues_found"].append(
                        f"Command {i+1} type mismatch: expected {expected}, got {actual}"
                    )

            # Performance validation
            execution_time = test_execution.execution_time
            if execution_time and execution_time > self.config["test_timeout"]:
                validation_results["issues_found"].append(
                    f"Execution time exceeded timeout: {execution_time:.2f}s > {self.config['test_timeout']}s"
                )

            # Overall validation result
            if not validation_results["issues_found"]:
                validation_results["validation_passed"] = True
            else:
                validation_results["error_message"] = "; ".join(
                    validation_results["issues_found"]
                )

        except Exception as e:
            validation_results["issues_found"].append(f"Validation error: {e}")

        return validation_results

    def run_test_batch(self, batch_size: Optional[int] = None) -> List[TestExecution]:
        """
        Run a batch of tests from the queue.

        Args:
            batch_size: Number of tests to run (None for all)

        Returns:
            List of executed tests
        """
        if not self.test_queue:
            self.logger.info("No tests in queue")
            return []

        batch_size = batch_size or len(self.test_queue)
        batch_tests = self.test_queue[:batch_size]
        self.test_queue = self.test_queue[batch_size:]

        executed_tests = []

        if self.config["parallel_execution"]:
            executed_tests = self._run_parallel_tests(batch_tests)
        else:
            executed_tests = self._run_sequential_tests(batch_tests)

        # Store executed tests
        self.executed_tests.extend(executed_tests)

        # Update metrics
        self._update_execution_metrics(executed_tests)

        return executed_tests

    def _run_sequential_tests(self, tests: List[TestExecution]) -> List[TestExecution]:
        """Run tests sequentially."""
        executed_tests = []

        for test in tests:
            executed_test = self.execute_test(test)
            executed_tests.append(executed_test)

        return executed_tests

    def _run_parallel_tests(self, tests: List[TestExecution]) -> List[TestExecution]:
        """Run tests in parallel."""
        import concurrent.futures

        max_workers = min(self.config["max_parallel_tests"], len(tests))
        executed_tests = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tests for execution
            future_to_test = {
                executor.submit(self.execute_test, test): test for test in tests
            }

            # Collect results
            for future in concurrent.futures.as_completed(future_to_test):
                test = future_to_test[future]
                try:
                    executed_test = future.result(timeout=self.config["test_timeout"])
                    executed_tests.append(executed_test)
                except Exception as e:
                    # Handle timeout or other execution errors
                    test.status = TestExecutionStatus.ERROR
                    test.error_message = f"Execution error: {e}"
                    executed_tests.append(test)
                    self.logger.error(f"Parallel test execution failed: {e}")

        return executed_tests

    def _update_execution_metrics(self, executed_tests: List[TestExecution]) -> None:
        """Update pipeline metrics based on execution results."""
        if not executed_tests:
            return

        # Count results
        passed = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.PASSED
        )
        failed = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.FAILED
        )
        errors = sum(1 for t in executed_tests if t.status == TestExecutionStatus.ERROR)

        # Update cumulative metrics
        self.metrics["total_executions"] += 1
        self.metrics["total_tests_run"] += len(executed_tests)

        # Calculate success rate
        total_successful = passed
        total_tests = len(executed_tests)
        current_success_rate = total_successful / total_tests if total_tests > 0 else 0

        # Update cumulative success rate (weighted average)
        previous_total = self.metrics["total_tests_run"] - len(executed_tests)
        if previous_total > 0:
            weighted_success_rate = (
                (self.metrics["cumulative_success_rate"] * previous_total)
                + (current_success_rate * len(executed_tests))
            ) / self.metrics["total_tests_run"]
            self.metrics["cumulative_success_rate"] = weighted_success_rate
        else:
            self.metrics["cumulative_success_rate"] = current_success_rate

        # Update average execution time
        execution_times = [t.execution_time for t in executed_tests if t.execution_time]
        if execution_times:
            avg_time = sum(execution_times) / len(execution_times)
            if self.metrics["average_execution_time"] == 0:
                self.metrics["average_execution_time"] = avg_time
            else:
                # Weighted average
                self.metrics["average_execution_time"] = (
                    (self.metrics["average_execution_time"] * previous_total)
                    + (avg_time * len(executed_tests))
                ) / self.metrics["total_tests_run"]

        # Count critical failures
        critical_failures = sum(
            1
            for t in executed_tests
            if t.status in [TestExecutionStatus.FAILED, TestExecutionStatus.ERROR]
            and t.priority == TestPriority.CRITICAL
        )
        self.metrics["critical_failures_count"] += critical_failures

    def generate_execution_report(
        self, executed_tests: List[TestExecution]
    ) -> PipelineReport:
        """Generate a comprehensive execution report."""
        if not executed_tests:
            return PipelineReport(
                pipeline_id=f"pipeline_{int(time.time())}",
                execution_start=datetime.now().isoformat(),
                execution_end=datetime.now().isoformat(),
                total_tests=0,
                passed_tests=0,
                failed_tests=0,
                error_tests=0,
                skipped_tests=0,
                success_rate=0.0,
                average_execution_time=0.0,
                critical_failures=[],
                performance_metrics={},
                recommendations=[],
            )

        # Calculate statistics
        total_tests = len(executed_tests)
        passed_tests = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.PASSED
        )
        failed_tests = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.FAILED
        )
        error_tests = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.ERROR
        )
        skipped_tests = sum(
            1 for t in executed_tests if t.status == TestExecutionStatus.SKIPPED
        )

        success_rate = passed_tests / total_tests if total_tests > 0 else 0

        # Calculate average execution time
        execution_times = [t.execution_time for t in executed_tests if t.execution_time]
        avg_execution_time = (
            sum(execution_times) / len(execution_times) if execution_times else 0
        )

        # Identify critical failures
        critical_failures = [
            f"{t.test_id}: {t.error_message}"
            for t in executed_tests
            if t.status in [TestExecutionStatus.FAILED, TestExecutionStatus.ERROR]
            and t.priority == TestPriority.CRITICAL
        ]

        # Performance metrics
        performance_metrics = {
            "min_execution_time": min(execution_times) if execution_times else 0,
            "max_execution_time": max(execution_times) if execution_times else 0,
            "std_execution_time": (
                self._calculate_std_dev(execution_times) if execution_times else 0
            ),
            "timeout_count": sum(
                1
                for t in executed_tests
                if t.execution_time and t.execution_time > self.config["test_timeout"]
            ),
        }

        # Generate recommendations
        recommendations = self._generate_execution_recommendations(
            executed_tests, success_rate, performance_metrics
        )

        # Determine execution timeframe
        timestamps = [t.timestamp for t in executed_tests if t.timestamp]
        execution_start = min(timestamps) if timestamps else datetime.now().isoformat()
        execution_end = max(timestamps) if timestamps else datetime.now().isoformat()

        return PipelineReport(
            pipeline_id=f"pipeline_{int(time.time())}",
            execution_start=execution_start,
            execution_end=execution_end,
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            error_tests=error_tests,
            skipped_tests=skipped_tests,
            success_rate=success_rate,
            average_execution_time=avg_execution_time,
            critical_failures=critical_failures,
            performance_metrics=performance_metrics,
            recommendations=recommendations,
        )

    def _calculate_std_dev(self, values: List[float]) -> float:
        """Calculate standard deviation of values."""
        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance**0.5

    def _generate_execution_recommendations(
        self,
        executed_tests: List[TestExecution],
        success_rate: float,
        performance_metrics: Dict[str, float],
    ) -> List[str]:
        """Generate recommendations based on execution results."""
        recommendations = []

        # Success rate recommendations
        if success_rate < self.config["success_rate_threshold"]:
            recommendations.append(
                f"SUCCESS RATE ALERT: {success_rate:.1%} below threshold "
                f"({self.config['success_rate_threshold']:.1%})"
            )

        # Performance recommendations
        avg_time = performance_metrics.get("average_execution_time", 0)
        if avg_time > self.config["test_timeout"] * 0.8:
            recommendations.append(
                f"PERFORMANCE WARNING: Average execution time {avg_time:.2f}s "
                f"approaching timeout {self.config['test_timeout']}s"
            )

        # Critical failure recommendations
        critical_failures = [
            t
            for t in executed_tests
            if t.status in [TestExecutionStatus.FAILED, TestExecutionStatus.ERROR]
            and t.priority == TestPriority.CRITICAL
        ]

        if critical_failures:
            recommendations.append(
                f"CRITICAL FAILURES: {len(critical_failures)} critical tests failed"
            )

        # Pattern-based recommendations
        failed_tests = [
            t for t in executed_tests if t.status == TestExecutionStatus.FAILED
        ]
        if failed_tests:
            # Analyze failure patterns
            command_failures = {}
            for test in failed_tests:
                if test.expected_commands:
                    for cmd in test.expected_commands:
                        cmd_name = cmd.get("command", "UNKNOWN")
                        command_failures[cmd_name] = (
                            command_failures.get(cmd_name, 0) + 1
                        )

            if command_failures:
                most_failed = max(command_failures.items(), key=lambda x: x[1])
                recommendations.append(
                    f"PATTERN DETECTED: Command '{most_failed[0]}' failed in {most_failed[1]} tests"
                )

        return recommendations

    def run_scheduled_tests(self) -> None:
        """Run scheduled tests (called by scheduler)."""
        if self.is_running:
            self.logger.warning(
                "Pipeline already running, skipping scheduled execution"
            )
            return

        self.logger.info("Starting scheduled test execution")

        try:
            # Generate test suite
            test_suite = self.generate_test_suite("comprehensive")

            if not test_suite:
                self.logger.warning("No tests generated for scheduled execution")
                return

            # Add to queue and execute
            self.add_tests_to_queue(test_suite)
            executed_tests = self.run_test_batch()

            # Generate report
            report = self.generate_execution_report(executed_tests)

            # Save report if enabled
            if self.config["enable_automated_reporting"]:
                self._save_execution_report(report)

            # Check for alerts
            if self.config["enable_failure_alerts"]:
                self._check_failure_alerts(report)

            self.last_execution_time = datetime.now()
            self.logger.info(
                f"Scheduled execution completed: {report.success_rate:.1%} success rate"
            )

        except Exception as e:
            self.logger.error(f"Scheduled test execution failed: {e}")

    def run_maintenance_tasks(self) -> None:
        """Run maintenance tasks (cleanup, archiving, etc.)."""
        try:
            # Clean up old test results
            cutoff_date = datetime.now() - timedelta(days=7)
            initial_count = len(self.executed_tests)

            self.executed_tests = [
                test
                for test in self.executed_tests
                if datetime.fromisoformat(test.timestamp) > cutoff_date
            ]

            cleaned_count = initial_count - len(self.executed_tests)
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} old test results")

            # Archive old reports
            self._archive_old_reports()

        except Exception as e:
            self.logger.error(f"Maintenance tasks failed: {e}")

    def update_metrics(self) -> None:
        """Update pipeline metrics (called by scheduler)."""
        try:
            # Calculate 24-hour success rate
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_tests = [
                test
                for test in self.executed_tests
                if datetime.fromisoformat(test.timestamp) > cutoff_time
            ]

            if recent_tests:
                passed_recent = sum(
                    1 for t in recent_tests if t.status == TestExecutionStatus.PASSED
                )
                self.metrics["last_24h_success_rate"] = passed_recent / len(
                    recent_tests
                )

        except Exception as e:
            self.logger.error(f"Metrics update failed: {e}")

    def _save_execution_report(self, report: PipelineReport) -> None:
        """Save execution report to file."""
        try:
            report_dir = Path(self.config["report_directory"])
            report_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"pipeline_report_{timestamp}.json"

            with open(report_file, "w") as f:
                json.dump(asdict(report), f, indent=2, default=str)

            self.logger.info(f"Execution report saved to {report_file}")

        except Exception as e:
            self.logger.error(f"Failed to save execution report: {e}")

    def _check_failure_alerts(self, report: PipelineReport) -> None:
        """Check for failure conditions that require alerts."""
        try:
            # Critical failure alert
            if report.critical_failures:
                self.logger.error(
                    f"CRITICAL ALERT: {len(report.critical_failures)} critical test failures"
                )

            # Success rate alert
            if report.success_rate < self.config["success_rate_threshold"]:
                self.logger.error(
                    f"SUCCESS RATE ALERT: {report.success_rate:.1%} below threshold"
                )

            # Performance alert
            if report.performance_metrics.get("timeout_count", 0) > 0:
                self.logger.warning(
                    f"PERFORMANCE ALERT: {report.performance_metrics['timeout_count']} tests timed out"
                )

        except Exception as e:
            self.logger.error(f"Alert checking failed: {e}")

    def _archive_old_reports(self) -> None:
        """Archive old reports to prevent disk space issues."""
        try:
            report_dir = Path(self.config["report_directory"])
            if not report_dir.exists():
                return

            # Find reports older than 30 days
            cutoff_date = datetime.now() - timedelta(days=30)
            old_reports = []

            for report_file in report_dir.glob("pipeline_report_*.json"):
                if report_file.stat().st_mtime < cutoff_date.timestamp():
                    old_reports.append(report_file)

            # Archive old reports
            if old_reports:
                archive_dir = report_dir / "archive"
                archive_dir.mkdir(exist_ok=True)

                for report_file in old_reports:
                    archive_path = archive_dir / report_file.name
                    report_file.rename(archive_path)

                self.logger.info(f"Archived {len(old_reports)} old reports")

        except Exception as e:
            self.logger.error(f"Report archiving failed: {e}")

    def start_pipeline(self) -> None:
        """Start the automated testing pipeline."""
        if self.is_running:
            self.logger.warning("Pipeline is already running")
            return

        self.is_running = True

        def run_scheduler():
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

        self.pipeline_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.pipeline_thread.start()

        self.logger.info("Automated testing pipeline started")

    def stop_pipeline(self) -> None:
        """Stop the automated testing pipeline."""
        self.is_running = False

        if self.pipeline_thread and self.pipeline_thread.is_alive():
            self.pipeline_thread.join(timeout=5)

        self.logger.info("Automated testing pipeline stopped")

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status and metrics."""
        return {
            "is_running": self.is_running,
            "tests_in_queue": len(self.test_queue),
            "tests_executed": len(self.executed_tests),
            "last_execution": (
                self.last_execution_time.isoformat()
                if self.last_execution_time
                else None
            ),
            "metrics": self.metrics,
            "config": self.config,
        }


def main():
    """Demo the automated testing pipeline."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    print("🔄 Automated Testing Pipeline - Demo Mode")
    print("=" * 60)

    # Mock AI manager for demo
    class MockAIManager:
        def get_ai_response(self, query):
            import random

            if random.random() > 0.1:  # 90% success rate
                return [{"command": "TEST", "params": {"query": query}}]
            else:
                return None

    # Create pipeline
    pipeline = AutomatedTestingPipeline(
        ai_manager=MockAIManager(),
        config={
            "execution_schedule": "manual",
            "parallel_execution": True,
            "max_parallel_tests": 3,
        },
    )

    print("✅ Pipeline initialized")

    # Generate and run test suite
    test_suite = pipeline.generate_test_suite("smoke")
    print(f"✅ Generated {len(test_suite)} test cases")

    pipeline.add_tests_to_queue(test_suite)
    print(f"✅ Added tests to queue")

    # Execute tests
    executed_tests = pipeline.run_test_batch(5)  # Run 5 tests
    print(f"✅ Executed {len(executed_tests)} tests")

    # Generate report
    report = pipeline.generate_execution_report(executed_tests)

    print(f"\nExecution Report:")
    print(f"Total Tests: {report.total_tests}")
    print(f"Success Rate: {report.success_rate:.1%}")
    print(f"Average Time: {report.average_execution_time:.2f}s")
    print(f"Critical Failures: {len(report.critical_failures)}")

    if report.recommendations:
        print(f"Recommendations:")
        for rec in report.recommendations[:3]:
            print(f"  • {rec}")

    # Show pipeline status
    status = pipeline.get_pipeline_status()
    print(f"\nPipeline Status:")
    print(f"Running: {status['is_running']}")
    print(f"Queue: {status['tests_in_queue']} tests")
    print(f"Executed: {status['tests_executed']} tests")

    print("\n✅ Automated Testing Pipeline demo completed")


if __name__ == "__main__":
    main()
