<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-lightblue-10" Color="#0077a0" Opacity="0.1" />
      <SolidColorBrush x:Key="light-lightblue" Color="#0077a0" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M15.35,7.15v.7l-2,2-.7-.7L13.79,8H9.75V7h4.04L12.65,5.85l.7-.7ZM9.5,3.97V5.2L12,2.71V4h1V1.5L12.5,1H10V2h1.29L9.48,3.82A.772.772,0,0,1,9.5,3.97ZM12,12.29,9.5,9.8v1.4L11.29,13H10v1h2.5l.5-.5V11H12Z" />
              <GeometryDrawing Brush="{DynamicResource light-lightblue-10}" Geometry="F1M9.5,3.97V12c0,.828-1.791,1.5-4,1.5s-4-.672-4-1.5V3.97c0-.828,1.791-1.5,4-1.5S9.5,3.142,9.5,3.97Z" />
              <GeometryDrawing Brush="{DynamicResource light-lightblue}" Geometry="F1M10,3.97c0-2.627-9-2.627-9,0H1V12c0,1.313,2.264,2,4.5,2s4.5-.687,4.5-2V3.97Zm-4.5-1c2.273,0,3.5.71,3.5,1s-1.227,1-3.5,1S2,4.26,2,3.97,3.227,2.97,5.5,2.97ZM9,12c0,.29-1.227,1-3.5,1S2,12.29,2,12V5.261H2a7.863,7.863,0,0,0,3.5.709A7.863,7.863,0,0,0,9,5.261H9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
