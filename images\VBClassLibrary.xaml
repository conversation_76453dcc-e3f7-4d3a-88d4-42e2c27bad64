<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,2.5h2V8.412l-.084-.084H6.234L5.562,9H5.5Zm6.042-.121-1.931.517,2.847,10.626L14.389,13ZM1.5,7.607l.6-.6H3.278l.222.221V2.5h-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.5,2l.5.5V8.917l-.584-.584H7V3H6V8.567l-.438.438H5V2.5L5.5,2Zm-6,0L1,2.5V8.112l1-1V3H3V6.75l.018.266h.26L4,7.737V2.5L3.5,2ZM14.873,12.869l-.353.613L12.588,14l-.613-.354L9.127,3.021l.353-.613,1.932-.517.613.353Zm-1.1-.223-2.589-9.66-.965.259L12.812,12.9Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.023,11.307V11H4v3H5.976l.672-.672H7l.625.625v.354L6,15.932H5.648l-.625-.625V15H3.5L3,14.5V11H2.229l-.615.614H1.261l-1-1v-.353l2.25-2.25h.353l1,1v.353L3.229,10H5.976l.672-.672H7l.625.625v.354L6,11.932H5.648Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
