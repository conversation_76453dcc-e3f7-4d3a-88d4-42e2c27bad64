"""LibreOffice UI Controller for JEdit2.

This module provides UI state management for LibreOffice integration.
"""

import logging
from typing import Dict, Any, Optional, List


class LibreOfficeUI:
    """UI controller for LibreOffice integration."""
    
    def __init__(self, lokit_manager):
        """Initialize LibreOffice UI controller.
        
        Args:
            lokit_manager: LOKit manager instance
        """
        self.lokit_manager = lokit_manager
        self.logger = logging.getLogger(__name__)
        
        # UI state tracking
        self.ui_state = {
            'active_document': None,
            'selection': None,
            'zoom_level': 100,
            'view_mode': 'normal'
        }
        
    def set_active_document(self, doc_id: str):
        """Set the active document.
        
        Args:
            doc_id: Document ID
        """
        try:
            if doc_id in self.lokit_manager.documents:
                self.ui_state['active_document'] = doc_id
                self.logger.info(f"Set active document: {doc_id}")
            else:
                self.logger.warning(f"Document not found: {doc_id}")
                
        except Exception as e:
            self.logger.error(f"Failed to set active document: {e}")
    
    def get_active_document(self) -> Optional[str]:
        """Get the active document ID.
        
        Returns:
            Active document ID or None
        """
        return self.ui_state.get('active_document')
    
    def update_selection(self, selection_info: Dict[str, Any]):
        """Update current selection information.
        
        Args:
            selection_info: Selection information
        """
        self.ui_state['selection'] = selection_info
        self.logger.debug(f"Updated selection: {selection_info}")
    
    def get_selection(self) -> Optional[Dict[str, Any]]:
        """Get current selection information.
        
        Returns:
            Selection information or None
        """
        return self.ui_state.get('selection')
    
    def set_zoom_level(self, zoom: int):
        """Set zoom level.
        
        Args:
            zoom: Zoom level percentage
        """
        if 25 <= zoom <= 500:  # Reasonable zoom range
            self.ui_state['zoom_level'] = zoom
            self.logger.info(f"Set zoom level: {zoom}%")
        else:
            self.logger.warning(f"Invalid zoom level: {zoom}")
    
    def get_zoom_level(self) -> int:
        """Get current zoom level.
        
        Returns:
            Zoom level percentage
        """
        return self.ui_state.get('zoom_level', 100)
    
    def set_view_mode(self, mode: str):
        """Set view mode.
        
        Args:
            mode: View mode ('normal', 'outline', 'print', etc.)
        """
        valid_modes = ['normal', 'outline', 'print', 'web']
        if mode in valid_modes:
            self.ui_state['view_mode'] = mode
            self.logger.info(f"Set view mode: {mode}")
        else:
            self.logger.warning(f"Invalid view mode: {mode}")
    
    def get_view_mode(self) -> str:
        """Get current view mode.
        
        Returns:
            Current view mode
        """
        return self.ui_state.get('view_mode', 'normal')
    
    def get_ui_state(self) -> Dict[str, Any]:
        """Get complete UI state.
        
        Returns:
            UI state dictionary
        """
        return self.ui_state.copy()
    
    def restore_ui_state(self, state: Dict[str, Any]):
        """Restore UI state.
        
        Args:
            state: UI state dictionary
        """
        try:
            self.ui_state.update(state)
            self.logger.info("UI state restored")
        except Exception as e:
            self.logger.error(f"Failed to restore UI state: {e}") 