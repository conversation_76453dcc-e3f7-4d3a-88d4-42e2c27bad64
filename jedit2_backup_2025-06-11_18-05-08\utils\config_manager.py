"""Configuration Manager for JEdit2.

This module handles the loading and saving of application settings,
including the secure storage of the user's API key.
"""

import json
import os
from typing import Optional, Dict, Any

class ConfigManager:
    """Manages application configuration."""

    def __init__(self, config_file: str = "config.json"):
        """
        Initializes the ConfigManager.
        
        Args:
            config_file: The name of the configuration file.
        """
        self._config_file = config_file
        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Loads the configuration file."""
        if os.path.exists(self._config_file):
            with open(self._config_file, 'r') as f:
                return json.load(f)
        return {}

    def get_setting(self, key: str, default: Any = None) -> Optional[Any]:
        """
        Retrieves a setting from the configuration.
        
        Args:
            key: The key of the setting to retrieve.
            default: Default value to return if setting not found.
            
        Returns:
            The value of the setting, or default if not found.
        """
        return self._config.get(key, default)

    def set_setting(self, key: str, value: Any) -> None:
        """
        Saves a setting to the configuration.
        
        Args:
            key: The key of the setting to save.
            value: The value of the setting.
        """
        self._config[key] = value
        self._save_config()

    def _save_config(self) -> None:
        """Saves the current configuration to the file."""
        with open(self._config_file, 'w') as f:
            json.dump(self._config, f, indent=4) 