#!/usr/bin/env python3
"""
Enhanced AI Manager with Integrated Testing Framework

Combines production AI manager with comprehensive testing infrastructure
for real-time validation, failure pattern detection, and continuous improvement.
"""

import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import asdict

try:
    from .ai_manager_production import ProductionAIManager
    from .test_case_generator import TestCaseGenerator, TestGenerationConfig
    from .failure_pattern_analyzer import FailurePatternAnalyzer
    from .state_validation_framework import StateValidationFramework
except ImportError:
    # Fallback for direct execution
    from ai_manager_production import ProductionAIManager
    from test_case_generator import TestCaseGenerator, TestGenerationConfig
    from failure_pattern_analyzer import FailurePatternAnalyzer
    from state_validation_framework import StateValidationFramework


class EnhancedAIManager:
    """
    Enhanced AI Manager with integrated testing framework.

    Features:
    - Real-time failure detection and analysis
    - Automated test case generation from live queries
    - State validation for command execution
    - Continuous improvement feedback loops
    - Production monitoring and alerting
    """

    def __init__(
        self,
        base_ai_manager,
        enable_testing: bool = True,
        testing_config: Optional[Dict] = None,
    ):
        """
        Initialize the enhanced AI manager.

        Args:
            base_ai_manager: The base AI manager instance
            enable_testing: Whether to enable testing framework
            testing_config: Configuration for testing components
        """
        self.logger = logging.getLogger(__name__)

        # Core production AI manager
        self.production_manager = ProductionAIManager(
            base_ai_manager, rate_limit_delay=4.0
        )

        # Testing framework components
        self.enable_testing = enable_testing
        if enable_testing:
            self._initialize_testing_framework(testing_config or {})

        # Performance metrics
        self.metrics = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "average_response_time": 0.0,
            "patterns_detected": 0,
            "tests_generated": 0,
        }

        # Real-time monitoring
        self.monitoring_config = {
            "failure_threshold": 0.05,  # Alert if failure rate > 5%
            "response_time_threshold": 10.0,  # Alert if response time > 10s
            "enable_auto_testing": True,
            "min_queries_for_pattern_detection": 5,
        }

    def _initialize_testing_framework(self, config: Dict[str, Any]) -> None:
        """Initialize the testing framework components."""
        try:
            # Test case generator
            test_config = TestGenerationConfig(
                max_command_combinations=config.get("max_test_combinations", 50),
                include_boundary_tests=config.get("include_boundary_tests", True),
                complexity_range=config.get("complexity_range", (2, 8)),
            )
            self.test_generator = TestCaseGenerator(test_config)

            # Failure pattern analyzer
            self.failure_analyzer = FailurePatternAnalyzer()

            # State validation framework
            self.state_validator = StateValidationFramework()

            self.logger.info("Testing framework initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize testing framework: {e}")
            self.enable_testing = False

    def get_ai_response_enhanced(
        self,
        query: str,
        validate_state: bool = True,
        before_state: Optional[Dict] = None,
    ) -> Tuple[Optional[List[Dict[str, Any]]], str, Dict[str, Any]]:
        """
        Enhanced AI response with integrated testing and validation.

        Args:
            query: User query
            validate_state: Whether to perform state validation
            before_state: Application state before command execution

        Returns:
            Tuple of (commands, raw_response, analysis_data)
        """
        start_time = time.time()
        analysis_data = {
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "testing_enabled": self.enable_testing,
            "validation_performed": False,
            "patterns_detected": [],
            "recommendations": [],
        }

        try:
            # Get AI response using production manager
            commands, raw_response = self.production_manager.get_ai_response_robust(
                query
            )

            # Update metrics
            self.metrics["total_queries"] += 1
            response_time = time.time() - start_time
            self._update_response_time_metric(response_time)

            # Analyze response
            if commands:
                self.metrics["successful_queries"] += 1
                analysis_data["success"] = True
                analysis_data["commands_generated"] = len(commands)

                # Perform testing if enabled
                if self.enable_testing:
                    testing_results = self._perform_real_time_testing(
                        query, commands, before_state, validate_state
                    )
                    analysis_data.update(testing_results)

            else:
                self.metrics["failed_queries"] += 1
                analysis_data["success"] = False
                analysis_data["error_details"] = raw_response

                # Analyze failure if testing enabled
                if self.enable_testing:
                    failure_analysis = self._analyze_failure(query, raw_response)
                    analysis_data.update(failure_analysis)

            # Check monitoring thresholds
            self._check_monitoring_alerts(analysis_data)

            # Generate insights and recommendations
            analysis_data["insights"] = self._generate_insights(analysis_data)

            return commands, raw_response, analysis_data

        except Exception as e:
            self.logger.error(f"Enhanced AI response failed: {e}")
            self.metrics["failed_queries"] += 1
            analysis_data["success"] = False
            analysis_data["error_details"] = str(e)

            return None, str(e), analysis_data

    def _perform_real_time_testing(
        self,
        query: str,
        commands: List[Dict[str, Any]],
        before_state: Optional[Dict],
        validate_state: bool,
    ) -> Dict[str, Any]:
        """Perform real-time testing and validation."""
        testing_results = {
            "validation_performed": False,
            "patterns_detected": [],
            "test_cases_generated": 0,
            "validation_results": {},
            "recommendations": [],
        }

        try:
            # Generate test cases based on this query
            if self.monitoring_config["enable_auto_testing"]:
                similar_tests = self._generate_similar_test_cases(query, commands)
                testing_results["test_cases_generated"] = len(similar_tests)
                self.metrics["tests_generated"] += len(similar_tests)

            # Perform state validation if requested and state provided
            if validate_state and before_state:
                validation_results = self._validate_command_execution(
                    query, commands, before_state
                )
                testing_results["validation_performed"] = True
                testing_results["validation_results"] = validation_results

            # Detect patterns in successful queries
            patterns = self._detect_success_patterns(query, commands)
            testing_results["patterns_detected"] = patterns
            self.metrics["patterns_detected"] += len(patterns)

        except Exception as e:
            self.logger.error(f"Real-time testing failed: {e}")
            testing_results["testing_error"] = str(e)

        return testing_results

    def _analyze_failure(self, query: str, error_details: str) -> Dict[str, Any]:
        """Analyze failure using the failure pattern analyzer."""
        failure_analysis = {
            "failure_analyzed": False,
            "failure_categories": [],
            "severity": "unknown",
            "patterns_matched": [],
            "recommendations": [],
        }

        try:
            # Analyze the failure
            analysis = self.failure_analyzer.analyze_failure(
                query=query, actual_response=None, error_details=error_details
            )

            failure_analysis.update(
                {
                    "failure_analyzed": True,
                    "failure_categories": [
                        cat.value for cat in analysis.failure_categories
                    ],
                    "severity": analysis.severity.value,
                    "patterns_matched": analysis.pattern_matches,
                    "specific_issues": analysis.specific_issues,
                }
            )

            # Generate recommendations based on failure analysis
            recommendations = self._generate_failure_recommendations(analysis)
            failure_analysis["recommendations"] = recommendations

        except Exception as e:
            self.logger.error(f"Failure analysis failed: {e}")
            failure_analysis["analysis_error"] = str(e)

        return failure_analysis

    def _generate_similar_test_cases(
        self, query: str, commands: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate test cases similar to the current successful query."""
        test_cases = []

        try:
            # Extract command types from successful response
            command_types = [cmd.get("command", "") for cmd in commands]

            # Generate variations based on command patterns
            if len(command_types) >= 2:
                # Multi-command scenario - generate combination tests
                combo_tests = self.test_generator.generate_command_combination_tests(5)
                test_cases.extend([asdict(test) for test in combo_tests[:3]])

            # Generate semantic ambiguity tests if query has ambiguous patterns
            if any(
                phrase in query.lower() for phrase in ["new column", "the column", "it"]
            ):
                ambiguity_tests = self.test_generator.generate_semantic_ambiguity_tests(
                    3
                )
                test_cases.extend([asdict(test) for test in ambiguity_tests])

        except Exception as e:
            self.logger.error(f"Test case generation failed: {e}")

        return test_cases

    def _validate_command_execution(
        self, query: str, commands: List[Dict[str, Any]], before_state: Dict
    ) -> Dict[str, Any]:
        """Validate command execution using state validation framework."""
        validation_results = {
            "validation_completed": False,
            "issues_found": [],
            "severity": "unknown",
            "validation_checks": {},
        }

        try:
            # Convert before_state to ApplicationState format
            before_app_state = self.state_validator.capture_state(
                grid_data=before_state.get("grid_data"),
                file_info=before_state.get("file_info"),
                active_command=commands[0].get("command") if commands else None,
            )

            # Simulate after state (would be captured after actual execution)
            # For now, we'll create a mock after state for demonstration
            after_app_state = before_app_state  # Simplified for demo

            # Validate each command
            for command in commands:
                validation = self.state_validator.validate_command_execution(
                    command=command,
                    before_state=before_app_state,
                    after_state=after_app_state,
                    test_description=f"Real-time validation: {query}",
                )

                validation_results.update(
                    {
                        "validation_completed": True,
                        "issues_found": validation.issues_found,
                        "severity": validation.severity,
                        "validation_checks": {
                            check: result.value
                            for check, result in validation.validation_results.items()
                        },
                    }
                )

                break  # For demo, just validate first command

        except Exception as e:
            self.logger.error(f"State validation failed: {e}")
            validation_results["validation_error"] = str(e)

        return validation_results

    def _detect_success_patterns(
        self, query: str, commands: List[Dict[str, Any]]
    ) -> List[str]:
        """Detect patterns in successful queries for learning."""
        patterns = []

        try:
            # Pattern: Successful multi-command sequences
            if len(commands) > 1:
                cmd_sequence = " -> ".join([cmd.get("command", "") for cmd in commands])
                patterns.append(f"successful_sequence: {cmd_sequence}")

            # Pattern: Successful handling of ambiguous references
            if "new column" in query.lower() and len(commands) > 1:
                patterns.append("successful_ambiguous_reference_handling")

            # Pattern: Successful complex operations
            if len(query.split()) > 10 and commands:
                patterns.append("successful_complex_query")

            # Pattern: Successful structural + reference operations
            has_structural = any(
                "INSERT" in cmd.get("command", "") or "DELETE" in cmd.get("command", "")
                for cmd in commands
            )
            has_reference = any(
                "COPY" in cmd.get("command", "") or "SORT" in cmd.get("command", "")
                for cmd in commands
            )
            if has_structural and has_reference:
                patterns.append("successful_structural_reference_combination")

        except Exception as e:
            self.logger.error(f"Pattern detection failed: {e}")

        return patterns

    def _generate_failure_recommendations(self, analysis) -> List[str]:
        """Generate actionable recommendations based on failure analysis."""
        recommendations = []

        try:
            # Recommendations based on failure categories
            for category in analysis.failure_categories:
                if category.value == "format_error":
                    recommendations.append("Try rephrasing with simpler language")
                elif category.value == "semantic_ambiguity":
                    recommendations.append(
                        "Use specific column references (A, B, C) instead of 'new column'"
                    )
                elif category.value == "reference_error":
                    recommendations.append(
                        "Check column references after structural operations"
                    )
                elif category.value == "parameter_validation":
                    recommendations.append(
                        "Ensure all required parameters are provided"
                    )

            # Recommendations based on severity
            if analysis.severity.value == "critical":
                recommendations.append(
                    "URGENT: Consider manual command entry as fallback"
                )
            elif analysis.severity.value == "high":
                recommendations.append("Try breaking complex query into simpler steps")

            # Generic recommendations
            if not recommendations:
                recommendations.append(
                    "Try rephrasing the query with different wording"
                )
                recommendations.append("Consider using menu commands as alternative")

        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")

        return recommendations

    def _update_response_time_metric(self, response_time: float) -> None:
        """Update the running average response time."""
        total_queries = self.metrics["total_queries"]
        current_avg = self.metrics["average_response_time"]

        # Calculate new average
        new_avg = ((current_avg * (total_queries - 1)) + response_time) / total_queries
        self.metrics["average_response_time"] = new_avg

    def _check_monitoring_alerts(self, analysis_data: Dict[str, Any]) -> None:
        """Check if any monitoring thresholds are exceeded."""
        try:
            # Check failure rate
            if self.metrics["total_queries"] >= 10:  # Minimum sample size
                failure_rate = (
                    self.metrics["failed_queries"] / self.metrics["total_queries"]
                )
                if failure_rate > self.monitoring_config["failure_threshold"]:
                    self.logger.warning(
                        f"High failure rate detected: {failure_rate:.1%} "
                        f"(threshold: {self.monitoring_config['failure_threshold']:.1%})"
                    )
                    analysis_data["alert"] = "high_failure_rate"

            # Check response time
            avg_time = self.metrics["average_response_time"]
            threshold = self.monitoring_config["response_time_threshold"]
            if avg_time > threshold:
                self.logger.warning(
                    f"High response time detected: {avg_time:.1f}s "
                    f"(threshold: {threshold}s)"
                )
                analysis_data["alert"] = "high_response_time"

        except Exception as e:
            self.logger.error(f"Monitoring alert check failed: {e}")

    def _generate_insights(self, analysis_data: Dict[str, Any]) -> List[str]:
        """Generate insights based on analysis data."""
        insights = []

        try:
            # Success rate insights
            if self.metrics["total_queries"] >= 5:
                success_rate = (
                    self.metrics["successful_queries"] / self.metrics["total_queries"]
                )
                insights.append(f"Current session success rate: {success_rate:.1%}")

            # Pattern insights
            if analysis_data.get("patterns_detected"):
                insights.append(
                    f"Detected {len(analysis_data['patterns_detected'])} patterns in this query"
                )

            # Testing insights
            if analysis_data.get("test_cases_generated", 0) > 0:
                insights.append(
                    f"Generated {analysis_data['test_cases_generated']} test cases from this query"
                )

            # Validation insights
            if analysis_data.get("validation_performed"):
                insights.append("Real-time state validation performed")

            # Performance insights
            avg_time = self.metrics["average_response_time"]
            if avg_time > 0:
                insights.append(f"Average response time: {avg_time:.1f}s")

        except Exception as e:
            self.logger.error(f"Insight generation failed: {e}")

        return insights

    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Generate a comprehensive testing and performance report."""
        try:
            report = {
                "session_metrics": self.metrics.copy(),
                "timestamp": datetime.now().isoformat(),
                "testing_enabled": self.enable_testing,
            }

            if self.enable_testing:
                # Failure analysis report
                failure_report = self.failure_analyzer.generate_analysis_report()
                report["failure_analysis"] = asdict(failure_report)

                # State validation report
                validation_report = self.state_validator.generate_validation_report()
                report["state_validation"] = validation_report

                # Test generation summary
                if hasattr(self.test_generator, "generated_tests"):
                    report["test_generation"] = {
                        "total_tests_generated": len(
                            self.test_generator.generated_tests
                        ),
                        "test_types": list(
                            set(
                                test.test_type.value
                                for test in self.test_generator.generated_tests
                            )
                        ),
                    }

            return report

        except Exception as e:
            self.logger.error(f"Report generation failed: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

    def export_session_data(self, filepath: str) -> bool:
        """Export all session data for analysis."""
        try:
            report = self.get_comprehensive_report()

            with open(filepath, "w") as f:
                json.dump(report, f, indent=2, default=str)

            self.logger.info(f"Session data exported to {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export session data: {e}")
            return False


def integrate_enhanced_ai_manager(main_window, testing_config: Optional[Dict] = None):
    """
    Integrate the enhanced AI manager with testing framework into the main window.

    Args:
        main_window: The MainWindow instance to integrate with
        testing_config: Configuration for testing components
    """
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Create the enhanced AI manager
        enhanced_manager = EnhancedAIManager(
            base_ai_manager=main_window.ai_manager,
            enable_testing=True,
            testing_config=testing_config or {},
        )

        # Store reference to enhanced manager
        main_window.enhanced_ai_manager = enhanced_manager

        # Update the AI submit handler to use the enhanced version
        original_ai_submit = main_window._on_ai_submit

        def enhanced_ai_submit(event):
            """Enhanced AI submit handler with integrated testing."""
            query = main_window.ai_input.GetValue()
            if not query:
                import wx
                wx.MessageBox(
                    "Please enter a command in the AI input field.",
                    "No Command",
                    wx.OK | wx.ICON_WARNING,
                )
                return
            
            # Capture before state (simplified for demo)
            before_state = {
                "grid_data": getattr(main_window, 'current_grid_data', None),
                "file_info": getattr(main_window, 'current_file_info', {}),
                "timestamp": datetime.now().isoformat()
            }
            
            # Use the enhanced manager for comprehensive handling
            commands, raw_response, analysis_data = enhanced_manager.get_ai_response_enhanced(
                query=query,
                validate_state=True,
                before_state=before_state
            )
            
            if commands:
                # Process commands using existing infrastructure
                for command_dict in commands:
                    main_window._process_command(command_dict)
                
                # Log the response with analysis
                logger.info(f"AI Response: {commands}")
                logger.info(f"Analysis: {analysis_data}")
                
                print(f"DEBUG - AI Query: '{query}'")
                print(f"DEBUG - AI Response: {commands}")
                print(f"DEBUG - Analysis: {analysis_data.get('insights', [])}")
                
                # Show insights if any
                if analysis_data.get('insights'):
                    insights_text = '\n'.join(analysis_data['insights'])
                    print(f"INSIGHTS: {insights_text}")
                
            else:
                # Show enhanced error message with recommendations
                import wx
                
                error_message = f"Failed to process AI command: {raw_response}\n"
                
                if analysis_data.get('recommendations'):
                    error_message += "\nRecommendations:\n"
                    error_message += '\n'.join(f"• {rec}" for rec in analysis_data['recommendations'])
                
                wx.MessageBox(
                    error_message, "AI Processing Error", wx.OK | wx.ICON_ERROR
                )
                
                logger.error(f"AI processing failed for query: {query}")
                logger.error(f"Analysis: {analysis_data}")
        
        # Replace the submit handler
        main_window._on_ai_submit = enhanced_ai_submit

        # Add method to get testing report
        def get_testing_report():
            """Get comprehensive testing report."""
            return enhanced_manager.get_comprehensive_report()

        main_window.get_testing_report = get_testing_report

        logger.info(
            "Enhanced AI Manager with testing framework successfully integrated"
        )
        return True

    except Exception as e:
        logger.error(f"Failed to integrate enhanced AI manager: {e}")
        return False


def main():
    """Demo the enhanced AI manager capabilities."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    print("🚀 Enhanced AI Manager with Testing Framework")
    print("=" * 60)

    # Mock AI manager for demo
    class MockAIManager:
        def get_ai_response(self, query):
            return [{"command": "TEST", "params": {"query": query}}]

    # Create enhanced manager
    enhanced_manager = EnhancedAIManager(
        base_ai_manager=MockAIManager(),
        enable_testing=True,
        testing_config={"max_test_combinations": 10},
    )

    # Test queries
    test_queries = [
        "open price.json then add column after A",
        "copy column C to new column B",
        "make column A bold then sort by column A",
    ]

    print("Testing Enhanced AI Manager:")
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        commands, response, analysis = enhanced_manager.get_ai_response_enhanced(query)
        print(f"Commands: {len(commands) if commands else 0}")
        print(f"Success: {analysis.get('success', False)}")
        print(f"Patterns: {len(analysis.get('patterns_detected', []))}")
        print(f"Tests Generated: {analysis.get('test_cases_generated', 0)}")

    # Show final report
    print("\nFinal Report:")
    report = enhanced_manager.get_comprehensive_report()
    print(f"Total Queries: {report['session_metrics']['total_queries']}")
    success_rate = report['session_metrics']['successful_queries'] / report['session_metrics']['total_queries']
    print(f"Success Rate: {success_rate:.1%}")
    print(f"Patterns Detected: {report['session_metrics']['patterns_detected']}")

    print("\n✅ Enhanced AI Manager demo completed")


if __name__ == "__main__":
    main()
