<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,11.5v4H.5v-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M0,11.5v4l.5.5h5l.5-.5v-4L5.5,11H5V10a2,2,0,0,0-4,0v1H.5ZM4,11H2V10a1,1,0,0,1,2,0ZM1,12H5v3H1Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M6.5,1.5a5,5,0,0,0-5,5,4.913,4.913,0,0,0,.08.86,2.765,2.765,0,0,1,.47-.2A2.986,2.986,0,0,1,6,10v.09l1,1v.38A5,5,0,0,0,6.5,1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.72,10.02a5.491,5.491,0,1,0-9.6-2.36,3.637,3.637,0,0,1,.46-.3,2.765,2.765,0,0,1,.47-.2A4.651,4.651,0,0,1,2,6.5a4.5,4.5,0,1,1,4.89,4.48l.11.11v.89a5.458,5.458,0,0,0,3.02-1.26l5.13,5.13.7-.7Z" />
              </DrawingGroup>
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
