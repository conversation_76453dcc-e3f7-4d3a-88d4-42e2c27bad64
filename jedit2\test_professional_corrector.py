#!/usr/bin/env python3
"""Test script for the professional format corrector."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.format_corrector import FormatCorrector

def test_json_correction():
    """Test JSON correction with professional tools."""
    print("=== Testing JSON Correction ===")
    
    # Test case with missing comma
    bad_json = """{
    "name": "John"
    "age": 30,
    "city": "New York"
}"""
    
    corrector = FormatCorrector()
    result = corrector.correct_json(bad_json)
    
    print(f"Success: {result.success}")
    print(f"Changes made: {result.changes_made}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Corrected content:\n{result.corrected_content}")
    print()

def test_yaml_correction():
    """Test YAML correction with professional tools."""
    print("=== Testing YAML Correction ===")
    
    # Test case with tabs
    bad_yaml = """name: John
	age: 30
	address:
		street: 123 Main St
		city: New York"""
    
    corrector = FormatCorrector()
    result = corrector.correct_yaml(bad_yaml)
    
    print(f"Success: {result.success}")
    print(f"Changes made: {result.changes_made}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Corrected content:\n{result.corrected_content}")
    print()

def test_csv_correction():
    """Test CSV correction with professional tools."""
    print("=== Testing CSV Correction ===")
    
    # Test case with inconsistent columns
    bad_csv = """name,age,city
John,30
Jane,25,Boston,Extra
Bob,35,Chicago"""
    
    corrector = FormatCorrector()
    result = corrector.correct_csv(bad_csv)
    
    print(f"Success: {result.success}")
    print(f"Changes made: {result.changes_made}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Corrected content:\n{result.corrected_content}")
    print()

def test_python_correction():
    """Test Python correction with professional tools."""
    print("=== Testing Python Correction ===")
    
    # Test case with tabs and long lines
    bad_python = """def hello_world():
	print("Hello, World!")
	very_long_line_that_exceeds_the_recommended_length = "This is a very long string that should be wrapped"
	return True"""
    
    corrector = FormatCorrector()
    result = corrector.correct_python(bad_python)
    
    print(f"Success: {result.success}")
    print(f"Changes made: {result.changes_made}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Corrected content:\n{result.corrected_content}")
    print()

def test_markdown_correction():
    """Test Markdown correction with professional tools."""
    print("=== Testing Markdown Correction ===")
    
    # Test case with header spacing and trailing whitespace
    bad_markdown = """#Header Without Space   
##Another Header   

- List item without space
-Another list item

```python
print("hello")
# Missing closing code block"""
    
    corrector = FormatCorrector()
    result = corrector.correct_markdown(bad_markdown)
    
    print(f"Success: {result.success}")
    print(f"Changes made: {result.changes_made}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Corrected content:\n{result.corrected_content}")
    print()

def check_available_tools():
    """Check which professional tools are available."""
    print("=== Checking Available Professional Tools ===")
    
    corrector = FormatCorrector()
    
    tools_to_check = [
        'python',
        'jq',
        'yamllint',
        'csvclean',
        'black',
        'autopep8',
        'yapf',
        'mdformat',
        'markdownlint'
    ]
    
    for tool in tools_to_check:
        available = corrector._check_tool_available(tool)
        status = "✓ Available" if available else "✗ Not found"
        print(f"{tool:15} {status}")
    
    print()
    print("To install missing tools:")
    print("pip install jq black autopep8 yapf mdformat")
    print("npm install -g markdownlint-cli")
    print("pip install csvkit ruamel.yaml pandas openpyxl")
    print()

if __name__ == "__main__":
    check_available_tools()
    test_json_correction()
    test_yaml_correction()
    test_csv_correction()
    test_python_correction()
    test_markdown_correction()
    
    print("=== Summary ===")
    print("The new format corrector uses professional tools when available:")
    print("• JSON: python -m json.tool, jq, jsonschema")
    print("• YAML: ruamel.yaml, PyYAML, yamllint")
    print("• CSV: csvkit, pandas")
    print("• Python: black, autopep8, yapf")
    print("• Markdown: mdformat, markdown-it-py, markdownlint")
    print("• Excel: pandas, openpyxl")
    print()
    print("This approach is much more reliable than custom correction routines!")
