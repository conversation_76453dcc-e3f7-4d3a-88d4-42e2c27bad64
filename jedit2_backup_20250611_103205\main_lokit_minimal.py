"""Minimal main entry point for JEdit2 LibreOffice Edition.

This version avoids AI manager to work around Windows networking issues.
"""

import sys
import logging
import tkinter as tk
from tkinter import messagebox, filedialog, simpledialog
from pathlib import Path

# Import existing utilities (avoiding AI manager for now)
from jedit2.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorCategory, ErrorLevel
from jedit2.utils.memory_manager import MemoryManager
from jedit2.utils.cache import CacheManager
from jedit2.utils.config_manager import ConfigManager

# Import LibreOffice integration
from jedit2.lokit.manager import LOKitManager, UNO_AVAILABLE
from jedit2.adapters.calc import CalcAdapter


class JEdit2LibreOfficeMinimalApp:
    """Minimal application class for JEdit2 LibreOffice Edition."""

    def __init__(self):
        """Initialize the application."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize managers (without AI for now)
        self.error_handler = ErrorHandler()
        self.memory_manager = MemoryManager()
        self.config_manager = ConfigManager()
        self.cache_manager = CacheManager()
        
        # Initialize LibreOffice integration
        self.lokit_manager = None
        self.adapters = {}
        
        # Track open documents
        self.open_documents = {}
        self.active_document = None
        
        # Initialize UI
        self.root = tk.Tk()
        self.root.title("JEdit2 - LibreOffice Edition (Minimal)")
        self.root.geometry("1200x800")
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Initializing LibreOffice...")
        self.status_bar = tk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create main area
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Add some basic info
        info_label = tk.Label(
            self.main_frame,
            text="JEdit2 LibreOffice Edition\n\n"
                 "This interface integrates with LibreOffice for professional\n"
                 "data editing capabilities.\n\n"
                 "Use File menu to create or open documents.",
            justify=tk.CENTER,
            font=("Arial", 12)
        )
        info_label.pack(expand=True)
        
        # Initialize LibreOffice
        self.root.after(100, self.initialize_libreoffice)
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('jedit2_lokit_minimal.log'),
                logging.StreamHandler()
            ]
        )
        
    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Spreadsheet", command=self.new_spreadsheet)
        file_menu.add_command(label="New Text Document", command=self.new_text_document)
        file_menu.add_separator()
        file_menu.add_command(label="Open", command=self.open_file)
        file_menu.add_command(label="Save", command=self.save_file)
        file_menu.add_command(label="Save As", command=self.save_file_as)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_exit)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo)
        edit_menu.add_command(label="Redo", command=self.redo)
        edit_menu.add_separator()
        edit_menu.add_command(label="Cut", command=self.cut)
        edit_menu.add_command(label="Copy", command=self.copy)
        edit_menu.add_command(label="Paste", command=self.paste)
        
        # Format menu
        format_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Format", menu=format_menu)
        format_menu.add_command(label="Bold", command=self.format_bold)
        format_menu.add_command(label="Italic", command=self.format_italic)
        format_menu.add_command(label="Underline", command=self.format_underline)
        format_menu.add_separator()
        format_menu.add_command(label="Currency", command=self.format_currency)
        format_menu.add_command(label="Percentage", command=self.format_percentage)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Memory Manager", command=self.show_memory_manager)
        tools_menu.add_command(label="Cache Manager", command=self.show_cache_manager)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def initialize_libreoffice(self):
        """Initialize LibreOffice integration."""
        try:
            if not UNO_AVAILABLE:
                self.status_var.set("Warning - UNO not available (LibreOffice features limited)")
                messagebox.showwarning(
                    "UNO Not Available",
                    "LibreOffice UNO bindings not found.\n\n"
                    "File operations will be limited.\n"
                    "Run 'python setup_libreoffice.py' to configure UNO."
                )
                return
            
            self.status_var.set("Initializing LibreOffice...")
            self.root.update()
            
            # Initialize LibreOffice manager
            self.lokit_manager = LOKitManager()
            
            if not self.lokit_manager.initialize():
                raise RuntimeError("Failed to initialize LibreOffice")
            
            self.status_var.set("Ready - LibreOffice initialized")
            self.logger.info("LibreOffice integration initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LibreOffice: {e}")
            self.status_var.set("Error - LibreOffice initialization failed")
            messagebox.showerror(
                "LibreOffice Error",
                f"Failed to initialize LibreOffice:\n{e}\n\n"
                "Please ensure LibreOffice is installed and try again."
            )
    
    def new_spreadsheet(self):
        """Create new spreadsheet document."""
        try:
            if not self.lokit_manager:
                messagebox.showwarning("Warning", "LibreOffice not initialized")
                return
                
            doc_id = self.lokit_manager.create_document("calc")
            document = self.lokit_manager.get_document(doc_id)
            
            # Create adapter for the document
            calc_adapter = CalcAdapter(document)
            self.adapters[doc_id] = calc_adapter
            
            # Track the document
            self.open_documents[doc_id] = {
                'type': 'calc',
                'path': None,
                'modified': False,
                'title': f"Spreadsheet {len(self.open_documents) + 1}"
            }
            
            self.active_document = doc_id
            self.status_var.set(f"Created new spreadsheet: {doc_id}")
            self.logger.info(f"Created new spreadsheet document: {doc_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to create new spreadsheet: {e}")
            messagebox.showerror("Error", f"Failed to create new spreadsheet:\n{e}")
    
    def new_text_document(self):
        """Create new text document."""
        try:
            if not self.lokit_manager:
                messagebox.showwarning("Warning", "LibreOffice not initialized")
                return
                
            doc_id = self.lokit_manager.create_document("writer")
            
            # Track the document
            self.open_documents[doc_id] = {
                'type': 'writer',
                'path': None,
                'modified': False,
                'title': f"Document {len(self.open_documents) + 1}"
            }
            
            self.active_document = doc_id
            self.status_var.set(f"Created new text document: {doc_id}")
            self.logger.info(f"Created new text document: {doc_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to create new text document: {e}")
            messagebox.showerror("Error", f"Failed to create new text document:\n{e}")
    
    def open_file(self):
        """Open existing file."""
        try:
            file_path = filedialog.askopenfilename(
                title="Open File",
                filetypes=[
                    ("All Supported", "*.ods;*.xlsx;*.xls;*.csv;*.odt;*.docx;*.doc;*.txt"),
                    ("Spreadsheets", "*.ods;*.xlsx;*.xls;*.csv"),
                    ("Documents", "*.odt;*.docx;*.doc;*.txt"),
                    ("All Files", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            if not self.lokit_manager:
                messagebox.showwarning("Warning", "LibreOffice not initialized")
                return
            
            doc_id = self.lokit_manager.load_document(file_path)
            doc_info = self.lokit_manager.get_document_info(doc_id)
            
            # Create appropriate adapter
            if doc_info['type'] == 'calc':
                document = self.lokit_manager.get_document(doc_id)
                calc_adapter = CalcAdapter(document)
                self.adapters[doc_id] = calc_adapter
            
            # Track the document
            self.open_documents[doc_id] = {
                'type': doc_info['type'],
                'path': file_path,
                'modified': False,
                'title': Path(file_path).name
            }
            
            self.active_document = doc_id
            self.status_var.set(f"Opened: {Path(file_path).name}")
            self.logger.info(f"Opened file: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to open file: {e}")
            messagebox.showerror("Error", f"Failed to open file:\n{e}")
    
    def save_file(self):
        """Save current file."""
        if not self.active_document:
            messagebox.showwarning("Warning", "No active document to save")
            return
        
        try:
            doc_info = self.open_documents[self.active_document]
            
            if doc_info['path']:
                # Save existing file
                self.lokit_manager.save_document(self.active_document)
                doc_info['modified'] = False
                self.status_var.set(f"Saved: {doc_info['title']}")
            else:
                # New file - prompt for save location
                self.save_file_as()
                
        except Exception as e:
            self.logger.error(f"Failed to save file: {e}")
            messagebox.showerror("Error", f"Failed to save file:\n{e}")
    
    def save_file_as(self):
        """Save current file with new name."""
        if not self.active_document:
            messagebox.showwarning("Warning", "No active document to save")
            return
        
        try:
            doc_info = self.open_documents[self.active_document]
            
            # Determine file types based on document type
            if doc_info['type'] == 'calc':
                filetypes = [
                    ("OpenDocument Spreadsheet", "*.ods"),
                    ("Excel Workbook", "*.xlsx"),
                    ("CSV", "*.csv"),
                ]
            else:
                filetypes = [
                    ("OpenDocument Text", "*.odt"),
                    ("Word Document", "*.docx"),
                    ("Text File", "*.txt"),
                ]
            
            file_path = filedialog.asksaveasfilename(
                title="Save As",
                filetypes=filetypes,
                defaultextension=".ods" if doc_info['type'] == 'calc' else ".odt"
            )
            
            if not file_path:
                return
            
            self.lokit_manager.save_document(self.active_document, file_path)
            
            # Update document info
            doc_info['path'] = file_path
            doc_info['title'] = Path(file_path).name
            doc_info['modified'] = False
            
            self.status_var.set(f"Saved as: {doc_info['title']}")
            self.logger.info(f"Saved file as: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save file as: {e}")
            messagebox.showerror("Error", f"Failed to save file:\n{e}")
    
    def format_bold(self):
        """Apply bold formatting."""
        if self.active_document and self.active_document in self.adapters:
            try:
                adapter = self.adapters[self.active_document]
                adapter.apply_bold_formatting(0, 0, 0, 0)
                self.status_var.set("Applied bold formatting")
            except Exception as e:
                self.logger.error(f"Failed to apply bold formatting: {e}")
    
    def format_currency(self):
        """Apply currency formatting."""
        if self.active_document and self.active_document in self.adapters:
            try:
                adapter = self.adapters[self.active_document]
                adapter.format_as_currency(0, 0, 0, 0)
                self.status_var.set("Applied currency formatting")
            except Exception as e:
                self.logger.error(f"Failed to apply currency formatting: {e}")
    
    def show_memory_manager(self):
        """Show memory manager dialog."""
        try:
            stats = self.memory_manager.get_memory_stats()
            messagebox.showinfo("Memory Statistics", f"Memory Stats:\n{stats}")
        except Exception as e:
            messagebox.showerror("Error", f"Memory manager error:\n{e}")
    
    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo(
            "About JEdit2 LibreOffice Edition (Minimal)",
            "JEdit2 LibreOffice Edition\n\n"
            "Advanced data editor with LibreOffice integration.\n"
            "This is the minimal version for compatibility.\n\n"
            "Version: 2.0.0-minimal"
        )
    
    # Placeholder methods for menu items
    def undo(self): pass
    def redo(self): pass
    def cut(self): pass
    def copy(self): pass
    def paste(self): pass
    def format_italic(self): pass
    def format_underline(self): pass
    def format_percentage(self): pass
    def show_cache_manager(self): pass
    
    def on_exit(self):
        """Handle application exit."""
        try:
            # Save any modified documents
            for doc_id, doc_info in self.open_documents.items():
                if doc_info['modified']:
                    response = messagebox.askyesnocancel(
                        "Save Changes",
                        f"Save changes to {doc_info['title']}?"
                    )
                    if response is True:
                        self.lokit_manager.save_document(doc_id)
                    elif response is None:  # Cancel
                        return
            
            # Shutdown LibreOffice
            if self.lokit_manager:
                self.lokit_manager.shutdown()
            
            # Stop managers
            if self.memory_manager:
                self.memory_manager.stop_monitoring()
                stats = self.memory_manager.get_memory_stats()
                self.logger.info(f"Application exit. Memory stats: {stats}")
            
            self.root.quit()
            
        except Exception as e:
            self.logger.error(f"Error during exit: {e}")
            self.root.quit()
    
    def run(self):
        """Run the application."""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_exit)
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application runtime error: {e}")
        finally:
            if self.lokit_manager:
                self.lokit_manager.shutdown()


def main():
    """Main entry point."""
    try:
        app = JEdit2LibreOfficeMinimalApp()
        app.run()
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 