<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>IconLightXSLTTemplate</title>
  <g id="canvas" class="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <g class="cls-1">
      <path class="light-defaultgrey-10" d="M13.5,4.5v10H2.5V1.5h8Z" />
      <path class="light-defaultgrey" d="M13.854,4.146l-3-3L10.5,1h-8L2,1.5v13l.5.5h11l.5-.5V4.5ZM13,14H3V2h7V5h3Z" />
      <path class="light-defaultgrey" d="M4,3H6V4H4ZM7,3V4H9V3ZM4,6H6V5H4ZM7,6H9V5H7ZM4,8H6V7H4ZM7,8H9V7H7Z" />
    </g>
    <path class="light-defaultgrey" d="M10.5,7.4,6.473,13H7.84l.838-1.246H10.67L10.625,13H12V7.4Zm.289,1.5-.068,1.883H9.292l1.284-1.9a2.935,2.935,0,0,0,.271-.489h.032A2.412,2.412,0,0,0,10.793,8.9Z" />
  </g>
</svg>
