"""Filter criteria classes for advanced filtering functionality."""

import re
from abc import ABC, abstractmethod
from datetime import datetime, date, timedelta
from typing import List, Any, Union, Optional, Set
from .filter_utils import parse_number, parse_date, is_blank, normalize_value
import logging

logger = logging.getLogger(__name__)


class FilterCriterion(ABC):
    """Base class for all filter criteria."""
    
    def __init__(self, name: str, description: str):
        """
        Initialize the filter criterion.
        
        Args:
            name: Short name for the criterion
            description: Human-readable description
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    def matches(self, value: str, data_type: str) -> bool:
        """
        Check if a value matches this criterion.
        
        Args:
            value: Value to check
            data_type: Type of data being filtered
            
        Returns:
            True if the value matches the criterion
        """
        pass
    
    def get_description(self) -> str:
        """Get human-readable description of this criterion."""
        return self.description
    
    @abstractmethod
    def to_dict(self) -> dict:
        """Convert criterion to dictionary for serialization."""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: dict) -> 'FilterCriterion':
        """Create criterion from dictionary."""
        pass


class TextFilterCriterion(FilterCriterion):
    """Filter criterion for text-based filtering."""
    
    # Text filter operators
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    BEGINS_WITH = "begins_with"
    NOT_BEGINS_WITH = "not_begins_with"
    ENDS_WITH = "ends_with"
    NOT_ENDS_WITH = "not_ends_with"
    IS_EMPTY = "is_empty"
    IS_NOT_EMPTY = "is_not_empty"
    MATCHES_PATTERN = "matches_pattern"
    
    def __init__(self, operator: str, value: str = "", case_sensitive: bool = False):
        """
        Initialize text filter criterion.
        
        Args:
            operator: Text filter operator
            value: Value to compare against
            case_sensitive: Whether comparison is case sensitive
        """
        super().__init__("text_filter", f"Text {operator}")
        self.operator = operator
        self.value = value
        self.case_sensitive = case_sensitive
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value matches text criterion."""
        if self.operator in [self.IS_EMPTY, self.IS_NOT_EMPTY]:
            is_empty = is_blank(value)
            return is_empty if self.operator == self.IS_EMPTY else not is_empty
        
        # Normalize case if needed
        check_value = value if self.case_sensitive else value.lower()
        filter_value = self.value if self.case_sensitive else self.value.lower()
        
        if self.operator == self.CONTAINS:
            return filter_value in check_value
        elif self.operator == self.NOT_CONTAINS:
            return filter_value not in check_value
        elif self.operator == self.EQUALS:
            return check_value == filter_value
        elif self.operator == self.NOT_EQUALS:
            return check_value != filter_value
        elif self.operator == self.BEGINS_WITH:
            return check_value.startswith(filter_value)
        elif self.operator == self.NOT_BEGINS_WITH:
            return not check_value.startswith(filter_value)
        elif self.operator == self.ENDS_WITH:
            return check_value.endswith(filter_value)
        elif self.operator == self.NOT_ENDS_WITH:
            return not check_value.endswith(filter_value)
        elif self.operator == self.MATCHES_PATTERN:
            try:
                # Convert wildcards to regex
                pattern = filter_value.replace('*', '.*').replace('?', '.')
                flags = 0 if self.case_sensitive else re.IGNORECASE
                return bool(re.search(pattern, check_value, flags))
            except re.error:
                return False
        
        return False
    
    def get_description(self) -> str:
        """Get human-readable description of this text filter."""
        if self.operator == self.CONTAINS:
            return f"Contains '{self.value}'"
        elif self.operator == self.NOT_CONTAINS:
            return f"Does not contain '{self.value}'"
        elif self.operator == self.EQUALS:
            return f"Equals '{self.value}'"
        elif self.operator == self.NOT_EQUALS:
            return f"Does not equal '{self.value}'"
        elif self.operator == self.BEGINS_WITH:
            return f"Begins with '{self.value}'"
        elif self.operator == self.NOT_BEGINS_WITH:
            return f"Does not begin with '{self.value}'"
        elif self.operator == self.ENDS_WITH:
            return f"Ends with '{self.value}'"
        elif self.operator == self.NOT_ENDS_WITH:
            return f"Does not end with '{self.value}'"
        elif self.operator == self.IS_EMPTY:
            return "Is empty"
        elif self.operator == self.IS_NOT_EMPTY:
            return "Is not empty"
        elif self.operator == self.MATCHES_PATTERN:
            return f"Matches pattern '{self.value}'"
        else:
            return f"Text {self.operator} '{self.value}'"
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'text',
            'operator': self.operator,
            'value': self.value,
            'case_sensitive': self.case_sensitive
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TextFilterCriterion':
        """Create from dictionary."""
        return cls(
            operator=data['operator'],
            value=data.get('value', ''),
            case_sensitive=data.get('case_sensitive', False)
        )


class NumberFilterCriterion(FilterCriterion):
    """Filter criterion for number-based filtering."""
    
    # Number filter operators
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    GREATER_THAN = "greater_than"
    GREATER_EQUAL = "greater_equal"
    LESS_THAN = "less_than"
    LESS_EQUAL = "less_equal"
    BETWEEN = "between"
    NOT_BETWEEN = "not_between"
    TOP_N = "top_n"
    BOTTOM_N = "bottom_n"
    ABOVE_AVERAGE = "above_average"
    BELOW_AVERAGE = "below_average"
    
    def __init__(self, operator: str, value1: float = 0.0, value2: Optional[float] = None, n: int = 10):
        """
        Initialize number filter criterion.
        
        Args:
            operator: Number filter operator
            value1: Primary comparison value
            value2: Secondary value for range operations
            n: Count for top/bottom N operations
        """
        super().__init__("number_filter", f"Number {operator}")
        self.operator = operator
        self.value1 = value1
        self.value2 = value2
        self.n = n
        self._average: Optional[float] = None
    
    def set_average(self, average: float) -> None:
        """Set the average value for above/below average operations."""
        self._average = average
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value matches number criterion."""
        num_value = parse_number(value)
        if num_value is None:
            return False
        
        if self.operator == self.EQUALS:
            return num_value == self.value1
        elif self.operator == self.NOT_EQUALS:
            return num_value != self.value1
        elif self.operator == self.GREATER_THAN:
            return num_value > self.value1
        elif self.operator == self.GREATER_EQUAL:
            return num_value >= self.value1
        elif self.operator == self.LESS_THAN:
            return num_value < self.value1
        elif self.operator == self.LESS_EQUAL:
            return num_value <= self.value1
        elif self.operator == self.BETWEEN:
            if self.value2 is not None:
                min_val = min(self.value1, self.value2)
                max_val = max(self.value1, self.value2)
                return min_val <= num_value <= max_val
        elif self.operator == self.NOT_BETWEEN:
            if self.value2 is not None:
                min_val = min(self.value1, self.value2)
                max_val = max(self.value1, self.value2)
                return not (min_val <= num_value <= max_val)
        elif self.operator == self.ABOVE_AVERAGE:
            return self._average is not None and num_value > self._average
        elif self.operator == self.BELOW_AVERAGE:
            return self._average is not None and num_value < self._average
        # TOP_N and BOTTOM_N require special handling in the filter manager
        
        return False
    
    def get_description(self) -> str:
        """Get human-readable description of this number filter."""
        if self.operator == self.EQUALS:
            return f"Equals {self.value1}"
        elif self.operator == self.NOT_EQUALS:
            return f"Does not equal {self.value1}"
        elif self.operator == self.GREATER_THAN:
            return f"Greater than {self.value1}"
        elif self.operator == self.GREATER_EQUAL:
            return f"Greater than or equal to {self.value1}"
        elif self.operator == self.LESS_THAN:
            return f"Less than {self.value1}"
        elif self.operator == self.LESS_EQUAL:
            return f"Less than or equal to {self.value1}"
        elif self.operator == self.BETWEEN:
            return f"Between {self.value1} and {self.value2}"
        elif self.operator == self.NOT_BETWEEN:
            return f"Not between {self.value1} and {self.value2}"
        elif self.operator == self.TOP_N:
            return f"Top {self.n} values"
        elif self.operator == self.BOTTOM_N:
            return f"Bottom {self.n} values"
        elif self.operator == self.ABOVE_AVERAGE:
            return "Above average"
        elif self.operator == self.BELOW_AVERAGE:
            return "Below average"
        else:
            return f"Number {self.operator} {self.value1}"
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'number',
            'operator': self.operator,
            'value1': self.value1,
            'value2': self.value2,
            'n': self.n
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'NumberFilterCriterion':
        """Create from dictionary."""
        return cls(
            operator=data['operator'],
            value1=data.get('value1', 0.0),
            value2=data.get('value2'),
            n=data.get('n', 10)
        )


class DateFilterCriterion(FilterCriterion):
    """Filter criterion for date-based filtering."""
    
    # Date filter operators
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    BEFORE = "before"
    AFTER = "after"
    BETWEEN = "between"
    NOT_BETWEEN = "not_between"
    TODAY = "today"
    YESTERDAY = "yesterday"
    TOMORROW = "tomorrow"
    THIS_WEEK = "this_week"
    LAST_WEEK = "last_week"
    NEXT_WEEK = "next_week"
    THIS_MONTH = "this_month"
    LAST_MONTH = "last_month"
    NEXT_MONTH = "next_month"
    THIS_QUARTER = "this_quarter"
    LAST_QUARTER = "last_quarter"
    NEXT_QUARTER = "next_quarter"
    THIS_YEAR = "this_year"
    LAST_YEAR = "last_year"
    NEXT_YEAR = "next_year"
    
    def __init__(self, operator: str, date1: Optional[datetime] = None, date2: Optional[datetime] = None):
        """
        Initialize date filter criterion.
        
        Args:
            operator: Date filter operator
            date1: Primary comparison date
            date2: Secondary date for range operations
        """
        super().__init__("date_filter", f"Date {operator}")
        self.operator = operator
        self.date1 = date1
        self.date2 = date2
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value matches date criterion."""
        date_value = parse_date(value)
        if date_value is None:
            return False
        
        # Get current date for relative comparisons
        now = datetime.now()
        today = now.date()
        
        if self.operator == self.EQUALS:
            return self.date1 is not None and date_value.date() == self.date1.date()
        elif self.operator == self.NOT_EQUALS:
            return self.date1 is None or date_value.date() != self.date1.date()
        elif self.operator == self.BEFORE:
            return self.date1 is not None and date_value < self.date1
        elif self.operator == self.AFTER:
            return self.date1 is not None and date_value > self.date1
        elif self.operator == self.BETWEEN:
            if self.date1 is not None and self.date2 is not None:
                start_date = min(self.date1, self.date2)
                end_date = max(self.date1, self.date2)
                return start_date <= date_value <= end_date
        elif self.operator == self.NOT_BETWEEN:
            if self.date1 is not None and self.date2 is not None:
                start_date = min(self.date1, self.date2)
                end_date = max(self.date1, self.date2)
                return not (start_date <= date_value <= end_date)
        elif self.operator == self.TODAY:
            return date_value.date() == today
        elif self.operator == self.YESTERDAY:
            return date_value.date() == today - timedelta(days=1)
        elif self.operator == self.TOMORROW:
            return date_value.date() == today + timedelta(days=1)
        elif self.operator == self.THIS_WEEK:
            week_start = today - timedelta(days=today.weekday())
            week_end = week_start + timedelta(days=6)
            return week_start <= date_value.date() <= week_end
        elif self.operator == self.THIS_MONTH:
            return (date_value.year == today.year and 
                   date_value.month == today.month)
        elif self.operator == self.THIS_YEAR:
            return date_value.year == today.year
        # Add more relative date implementations as needed
        
        return False
    
    def get_description(self) -> str:
        """Get human-readable description of this date filter."""
        if self.operator == self.EQUALS:
            return f"Equals {self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'}"
        elif self.operator == self.NOT_EQUALS:
            return f"Does not equal {self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'}"
        elif self.operator == self.BEFORE:
            return f"Before {self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'}"
        elif self.operator == self.AFTER:
            return f"After {self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'}"
        elif self.operator == self.BETWEEN:
            date1_str = self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'
            date2_str = self.date2.strftime('%Y-%m-%d') if self.date2 else 'None'
            return f"Between {date1_str} and {date2_str}"
        elif self.operator == self.NOT_BETWEEN:
            date1_str = self.date1.strftime('%Y-%m-%d') if self.date1 else 'None'
            date2_str = self.date2.strftime('%Y-%m-%d') if self.date2 else 'None'
            return f"Not between {date1_str} and {date2_str}"
        elif self.operator == self.TODAY:
            return "Today"
        elif self.operator == self.YESTERDAY:
            return "Yesterday"
        elif self.operator == self.TOMORROW:
            return "Tomorrow"
        elif self.operator == self.THIS_WEEK:
            return "This week"
        elif self.operator == self.LAST_WEEK:
            return "Last week"
        elif self.operator == self.NEXT_WEEK:
            return "Next week"
        elif self.operator == self.THIS_MONTH:
            return "This month"
        elif self.operator == self.LAST_MONTH:
            return "Last month"
        elif self.operator == self.NEXT_MONTH:
            return "Next month"
        elif self.operator == self.THIS_QUARTER:
            return "This quarter"
        elif self.operator == self.LAST_QUARTER:
            return "Last quarter"
        elif self.operator == self.NEXT_QUARTER:
            return "Next quarter"
        elif self.operator == self.THIS_YEAR:
            return "This year"
        elif self.operator == self.LAST_YEAR:
            return "Last year"
        elif self.operator == self.NEXT_YEAR:
            return "Next year"
        else:
            return f"Date {self.operator}"
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'date',
            'operator': self.operator,
            'date1': self.date1.isoformat() if self.date1 else None,
            'date2': self.date2.isoformat() if self.date2 else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'DateFilterCriterion':
        """Create from dictionary."""
        date1 = datetime.fromisoformat(data['date1']) if data.get('date1') else None
        date2 = datetime.fromisoformat(data['date2']) if data.get('date2') else None
        return cls(
            operator=data['operator'],
            date1=date1,
            date2=date2
        )


class ValueListCriterion(FilterCriterion):
    """Filter criterion for value list selection."""
    
    def __init__(self, selected_values: Set[str], include_blanks: bool = False):
        """
        Initialize value list criterion.
        
        Args:
            selected_values: Set of values to include
            include_blanks: Whether to include blank values
        """
        super().__init__("value_list", "Selected values")
        self.selected_values = set(selected_values)
        self.include_blanks = include_blanks
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value is in the selected list."""
        if is_blank(value):
            return self.include_blanks
        
        # Exact match for value lists
        return value.strip() in self.selected_values
    
    def get_description(self) -> str:
        """Get human-readable description of this value list filter."""
        value_count = len(self.selected_values)
        if value_count == 1:
            value_name = next(iter(self.selected_values))
            desc = f"Equals '{value_name}'"
        else:
            desc = f"{value_count} selected values"
        
        if self.include_blanks:
            desc += " (including blanks)"
            
        return desc
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'value_list',
            'selected_values': list(self.selected_values),
            'include_blanks': self.include_blanks
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ValueListCriterion':
        """Create from dictionary."""
        return cls(
            selected_values=set(data['selected_values']),
            include_blanks=data.get('include_blanks', False)
        )


class BlanksCriterion(FilterCriterion):
    """Filter criterion for blank/non-blank values."""
    
    def __init__(self, show_blanks: bool = True):
        """
        Initialize blanks criterion.
        
        Args:
            show_blanks: True to show only blanks, False to show only non-blanks
        """
        super().__init__("blanks", "Blanks only" if show_blanks else "Non-blanks only")
        self.show_blanks = show_blanks
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value matches blank criterion."""
        is_value_blank = is_blank(value)
        return is_value_blank if self.show_blanks else not is_value_blank
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'blanks',
            'show_blanks': self.show_blanks
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'BlanksCriterion':
        """Create from dictionary."""
        return cls(show_blanks=data.get('show_blanks', True))


class CompoundCriterion(FilterCriterion):
    """Filter criterion combining multiple criteria with AND/OR logic."""
    
    def __init__(self, criteria: List[FilterCriterion], use_and: bool = True):
        """
        Initialize compound criterion.
        
        Args:
            criteria: List of criteria to combine
            use_and: True for AND logic, False for OR logic
        """
        operator = "AND" if use_and else "OR"
        super().__init__("compound", f"Compound ({operator})")
        self.criteria = criteria
        self.use_and = use_and
    
    def matches(self, value: str, data_type: str) -> bool:
        """Check if value matches compound criterion."""
        if not self.criteria:
            return True
        
        if self.use_and:
            return all(criterion.matches(value, data_type) for criterion in self.criteria)
        else:
            return any(criterion.matches(value, data_type) for criterion in self.criteria)
    
    def to_dict(self) -> dict:
        """Convert to dictionary."""
        return {
            'type': 'compound',
            'criteria': [criterion.to_dict() for criterion in self.criteria],
            'use_and': self.use_and
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'CompoundCriterion':
        """Create from dictionary."""
        criteria = []
        for criterion_data in data['criteria']:
            criterion_type = criterion_data['type']
            if criterion_type == 'text':
                criteria.append(TextFilterCriterion.from_dict(criterion_data))
            elif criterion_type == 'number':
                criteria.append(NumberFilterCriterion.from_dict(criterion_data))
            elif criterion_type == 'date':
                criteria.append(DateFilterCriterion.from_dict(criterion_data))
            elif criterion_type == 'value_list':
                criteria.append(ValueListCriterion.from_dict(criterion_data))
            elif criterion_type == 'blanks':
                criteria.append(BlanksCriterion.from_dict(criterion_data))
            elif criterion_type == 'compound':
                criteria.append(CompoundCriterion.from_dict(criterion_data))
        
        return cls(criteria=criteria, use_and=data.get('use_and', True)) 