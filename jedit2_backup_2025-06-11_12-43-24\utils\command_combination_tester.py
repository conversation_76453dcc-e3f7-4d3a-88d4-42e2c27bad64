#!/usr/bin/env python3
"""
Command Combination Tester for JEdit2 AI System

Systematically tests 2-command combinations to discover hidden failure patterns,
particularly focusing on state dependency issues and reference ambiguities.
"""

import logging
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum


class CommandCategory(Enum):
    """Categorize commands by their impact on application state."""

    STRUCTURAL = "structural"  # INSERT, DELETE operations
    REFERENCE = "reference"  # COPY, MOVE operations using column/row references
    FILE_OPS = "file_ops"  # OPEN, SAVE, CLOSE operations
    FORMAT = "format"  # BOLD, ITALIC, ALIGN operations
    FILTER = "filter"  # All filtering operations
    DATA_TRANSFORM = "data"  # SORT, TRANSPOSE operations


@dataclass
class CommandTest:
    """Represents a single command combination test case."""

    command1: str
    command2: str
    category1: CommandCategory
    category2: CommandCategory
    risk_level: str  # "HIGH", "MEDIUM", "LOW"
    description: str
    expected_failure_patterns: List[str]


@dataclass
class TestResult:
    """Results from a command combination test."""

    test_case: CommandTest
    success: bool
    ai_response: Optional[List[Dict[str, Any]]]
    raw_response: str
    execution_time: float
    failure_reason: Optional[str]
    state_inconsistency: bool = False
    semantic_ambiguity: bool = False


class CommandCombinationTester:
    """
    Systematic testing framework for 2-command combinations.

    Focuses on discovering state dependency issues and semantic ambiguities
    that could cause AI interpretation failures.
    """

    def __init__(self, ai_manager=None):
        """
        Initialize the command combination tester.

        Args:
            ai_manager: AI manager instance for testing (optional for planning)
        """
        self.ai_manager = ai_manager
        self.logger = logging.getLogger(__name__)

        # Command categorization for systematic testing
        self.command_categories = self._initialize_command_categories()

        # High-risk patterns identified from analysis
        self.high_risk_patterns = self._initialize_high_risk_patterns()

        # Test results storage
        self.test_results: List[TestResult] = []

    def _initialize_command_categories(self) -> Dict[str, Dict[str, Any]]:
        """Initialize command categorization for systematic testing."""
        return {
            # Structural commands that change grid layout
            "INSERT_COLUMN_RIGHT": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "column",
                "common_params": [{"column_index": "A"}, {"column_index": "B"}],
            },
            "INSERT_COLUMN_LEFT": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "column",
                "common_params": [{"column_index": "B"}, {"column_index": "C"}],
            },
            "INSERT_ROW_ABOVE": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "row",
                "common_params": [{"row_index": 5}, {"row_index": 10}],
            },
            "INSERT_ROW_BELOW": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "row",
                "common_params": [{"row_index": 5}, {"row_index": 10}],
            },
            "DELETE_COLUMN": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "column",
                "common_params": [{"column_index": "B"}, {"column_index": "C"}],
            },
            "DELETE_ROW": {
                "category": CommandCategory.STRUCTURAL,
                "affects_references": True,
                "reference_shift": "row",
                "common_params": [{"row_index": 5}, {"row_index": 10}],
            },
            # Reference commands that use column/row positions
            "COPY_COLUMN_TO_NEW_COLUMN_AFTER": {
                "category": CommandCategory.REFERENCE,
                "uses_references": True,
                "reference_type": "column",
                "common_params": [
                    {"source_column": "C", "target_column": "A", "apply_bold": False},
                    {"source_column": "B", "target_column": "C", "apply_bold": True},
                ],
            },
            "SORT_COLUMN_ASCENDING": {
                "category": CommandCategory.REFERENCE,
                "uses_references": True,
                "reference_type": "column",
                "common_params": [{"column_index": "B"}, {"column_index": "C"}],
            },
            "SORT_COLUMN_DESCENDING": {
                "category": CommandCategory.REFERENCE,
                "uses_references": True,
                "reference_type": "column",
                "common_params": [{"column_index": "B"}, {"column_index": "C"}],
            },
            "FILTER_COLUMN": {
                "category": CommandCategory.FILTER,
                "uses_references": True,
                "reference_type": "column",
                "common_params": [
                    {
                        "column_index": "B",
                        "filter_type": "text",
                        "operator": "contains",
                        "value": "test",
                    },
                    {
                        "column_index": "C",
                        "filter_type": "number",
                        "operator": "greater_than",
                        "value": 100,
                    },
                ],
            },
            # File operations
            "OPEN_FILE": {
                "category": CommandCategory.FILE_OPS,
                "affects_references": False,
                "common_params": [
                    {"file_path": "price.json"},
                    {"file_path": "data.csv"},
                ],
            },
            "SAVE_FILE": {
                "category": CommandCategory.FILE_OPS,
                "affects_references": False,
                "common_params": [{}],
            },
            "NEW_SPREADSHEET": {
                "category": CommandCategory.FILE_OPS,
                "affects_references": False,
                "common_params": [{}],
            },
        }

    def _initialize_high_risk_patterns(self) -> List[Dict[str, Any]]:
        """Initialize high-risk command combination patterns."""
        return [
            {
                "pattern": "STRUCTURAL → REFERENCE",
                "description": "Insert/delete followed by column/row reference",
                "risk_level": "HIGH",
                "examples": [
                    ("INSERT_COLUMN_RIGHT", "COPY_COLUMN_TO_NEW_COLUMN_AFTER"),
                    ("DELETE_COLUMN", "SORT_COLUMN_ASCENDING"),
                    ("INSERT_ROW_ABOVE", "FILTER_COLUMN"),
                ],
            },
            {
                "pattern": "FILE_OPS → STRUCTURAL",
                "description": "File operations followed by structural changes",
                "risk_level": "MEDIUM",
                "examples": [
                    ("OPEN_FILE", "INSERT_COLUMN_RIGHT"),
                    ("NEW_SPREADSHEET", "DELETE_ROW"),
                ],
            },
            {
                "pattern": "MULTIPLE_STRUCTURAL",
                "description": "Multiple structural operations in sequence",
                "risk_level": "HIGH",
                "examples": [
                    ("INSERT_COLUMN_RIGHT", "INSERT_COLUMN_RIGHT"),
                    ("DELETE_COLUMN", "INSERT_ROW_BELOW"),
                ],
            },
        ]

    def generate_critical_test_cases(self, max_cases: int = 20) -> List[CommandTest]:
        """
        Generate the most critical test cases for immediate testing.

        Args:
            max_cases: Maximum number of test cases to generate

        Returns:
            List of high-priority test cases
        """
        test_cases = []

        # HIGH RISK: Structural operations followed by reference operations
        structural_commands = [
            "INSERT_COLUMN_RIGHT",
            "INSERT_COLUMN_LEFT",
            "DELETE_COLUMN",
        ]
        reference_commands = [
            "COPY_COLUMN_TO_NEW_COLUMN_AFTER",
            "SORT_COLUMN_ASCENDING",
        ]

        for structural in structural_commands:
            for reference in reference_commands:
                if len(test_cases) >= max_cases:
                    break

                test_case = CommandTest(
                    command1=structural,
                    command2=reference,
                    category1=CommandCategory.STRUCTURAL,
                    category2=CommandCategory.REFERENCE,
                    risk_level="HIGH",
                    description=(
                        f"{structural} followed by {reference} - "
                        "potential reference shift issue"
                    ),
                    expected_failure_patterns=[
                        "column_reference_ambiguity",
                        "state_tracking_error",
                    ],
                )
                test_cases.append(test_case)

        # MEDIUM RISK: File operations followed by structural changes
        file_commands = ["OPEN_FILE", "NEW_SPREADSHEET"]
        for file_cmd in file_commands:
            for structural in structural_commands[:2]:  # Limit to avoid too many cases
                if len(test_cases) >= max_cases:
                    break

                test_case = CommandTest(
                    command1=file_cmd,
                    command2=structural,
                    category1=CommandCategory.FILE_OPS,
                    category2=CommandCategory.STRUCTURAL,
                    risk_level="MEDIUM",
                    description=(
                        f"{file_cmd} followed by {structural} - "
                        "state initialization issue"
                    ),
                    expected_failure_patterns=[
                        "empty_state_handling",
                        "file_context_loss",
                    ],
                )
                test_cases.append(test_case)

        return test_cases[:max_cases]

    def generate_natural_language_variations(self, test_case: CommandTest) -> List[str]:
        """
        Generate natural language variations for testing semantic ambiguity.

        Args:
            test_case: The test case to generate variations for

        Returns:
            List of natural language query variations
        """
        variations = []

        # Basic template
        if (
            test_case.command1 == "INSERT_COLUMN_RIGHT"
            and test_case.command2 == "COPY_COLUMN_TO_NEW_COLUMN_AFTER"
        ):
            variations = [
                "insert column after A then copy column C to the new column",
                "add new column to the right of A, then copy column C into it",
                "create column after A and copy the C column data to it",
                "insert column after A then copy information from column C",
                "add column after A then copy column C into new column B",  # Semantic ambiguity
                "insert column right of A then copy data from new column C",  # Reference confusion
            ]
        elif (
            test_case.command1 == "OPEN_FILE"
            and test_case.command2 == "INSERT_COLUMN_RIGHT"
        ):
            variations = [
                "open price.json then add column after A",
                "open price.json and insert new column to the right of A",
                "load price.json then create column after A",
                "open price.json then add new column after column A",
            ]
        else:
            # Generic template
            cmd1_clean = test_case.command1.lower().replace("_", " ")
            cmd2_clean = test_case.command2.lower().replace("_", " ")
            variations = [
                f"{cmd1_clean} then {cmd2_clean}",
                f"{cmd1_clean} and {cmd2_clean}",
            ]

        return variations

    def test_command_combination(
        self, test_case: CommandTest, query: str
    ) -> TestResult:
        """
        Test a specific command combination with a natural language query.

        Args:
            test_case: The test case definition
            query: Natural language query to test

        Returns:
            Test result with success/failure information
        """
        start_time = time.time()

        if not self.ai_manager:
            # Planning mode - simulate test
            return TestResult(
                test_case=test_case,
                success=True,  # Assume success in planning mode
                ai_response=None,
                raw_response="PLANNING_MODE",
                execution_time=0.0,
                failure_reason=None,
            )

        try:
            # Get AI response
            commands, raw_response = self.ai_manager.get_ai_response_robust(query)
            execution_time = time.time() - start_time

            # Analyze the response
            success = commands is not None and len(commands) >= 2
            failure_reason = None
            state_inconsistency = False
            semantic_ambiguity = False

            if not success:
                if commands is None:
                    failure_reason = "No commands generated"
                elif len(commands) < 2:
                    failure_reason = (
                        f"Only {len(commands)} command(s) generated, expected 2"
                    )
            else:
                # Check for semantic ambiguity in reference-based commands
                if test_case.category2 == CommandCategory.REFERENCE:
                    semantic_ambiguity = self._check_semantic_ambiguity(
                        commands, test_case
                    )

                # Check for state inconsistency
                state_inconsistency = self._check_state_consistency(commands, test_case)

            return TestResult(
                test_case=test_case,
                success=success,
                ai_response=commands,
                raw_response=raw_response,
                execution_time=execution_time,
                failure_reason=failure_reason,
                state_inconsistency=state_inconsistency,
                semantic_ambiguity=semantic_ambiguity,
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return TestResult(
                test_case=test_case,
                success=False,
                ai_response=None,
                raw_response=str(e),
                execution_time=execution_time,
                failure_reason=f"Exception: {str(e)}",
            )

    def _check_semantic_ambiguity(
        self, commands: List[Dict[str, Any]], test_case: CommandTest
    ) -> bool:
        """Check if the AI response shows semantic ambiguity issues."""
        if len(commands) < 2:
            return False

        # Look for the "new column X" pattern issue
        second_command = commands[1]
        if second_command.get("command") == "COPY_COLUMN_TO_NEW_COLUMN_AFTER":
            source_column = second_command.get("params", {}).get("source_column")
            # If source column is C after an insert operation, this might be wrong
            if source_column == "C" and test_case.command1.startswith("INSERT_COLUMN"):
                return True

        return False

    def _check_state_consistency(
        self, commands: List[Dict[str, Any]], test_case: CommandTest
    ) -> bool:
        """Check if the command sequence shows state consistency issues."""
        # This is a placeholder for more sophisticated state tracking
        # For now, just check basic command structure
        if len(commands) != 2:
            return True  # Wrong number of commands indicates state issue

        return False

    def run_test_suite(self, test_cases: List[CommandTest]) -> Dict[str, Any]:
        """
        Run a complete test suite and analyze results.

        Args:
            test_cases: List of test cases to execute

        Returns:
            Summary of test results and analysis
        """
        self.test_results = []
        total_tests = 0

        for test_case in test_cases:
            # Generate natural language variations
            variations = self.generate_natural_language_variations(test_case)

            for query in variations:
                result = self.test_command_combination(test_case, query)
                self.test_results.append(result)
                total_tests += 1

                self.logger.info(
                    f"Test {total_tests}: {query} -> "
                    f"{'✅' if result.success else '❌'}"
                )

        # Analyze results
        analysis = self._analyze_results()

        return {
            "total_tests": total_tests,
            "success_rate": analysis["success_rate"],
            "failure_patterns": analysis["failure_patterns"],
            "recommendations": analysis["recommendations"],
            "detailed_results": self.test_results,
        }

    def _analyze_results(self) -> Dict[str, Any]:
        """Analyze test results to identify patterns and recommendations."""
        if not self.test_results:
            return {"success_rate": 0.0, "failure_patterns": [], "recommendations": []}

        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.success)
        success_rate = successful_tests / total_tests

        # Categorize failures
        failure_patterns = {}
        semantic_ambiguity_count = 0
        state_inconsistency_count = 0

        for result in self.test_results:
            if not result.success:
                pattern = f"{result.test_case.category1.value}_to_{result.test_case.category2.value}"
                failure_patterns[pattern] = failure_patterns.get(pattern, 0) + 1

            if result.semantic_ambiguity:
                semantic_ambiguity_count += 1
            if result.state_inconsistency:
                state_inconsistency_count += 1

        # Generate recommendations
        recommendations = []
        if semantic_ambiguity_count > 0:
            recommendations.append(
                f"Enhance semantic ambiguity detection ({semantic_ambiguity_count} cases)"
            )
        if state_inconsistency_count > 0:
            recommendations.append(
                f"Improve state tracking logic ({state_inconsistency_count} cases)"
            )
        if failure_patterns:
            top_failure = max(failure_patterns, key=failure_patterns.get)
            recommendations.append(
                f"Focus on {top_failure} combinations ({failure_patterns[top_failure]} failures)"
            )

        return {
            "success_rate": success_rate,
            "failure_patterns": failure_patterns,
            "semantic_ambiguity_rate": semantic_ambiguity_count / total_tests,
            "state_inconsistency_rate": state_inconsistency_count / total_tests,
            "recommendations": recommendations,
        }

    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a comprehensive test report."""
        report = f"""
# Command Combination Test Report

## Summary
- **Total Tests**: {results['total_tests']}
- **Success Rate**: {results['success_rate']:.2%}
- **Semantic Ambiguity Rate**: {results.get('semantic_ambiguity_rate', 0):.2%}
- **State Inconsistency Rate**: {results.get('state_inconsistency_rate', 0):.2%}

## Failure Patterns
"""
        for pattern, count in results["failure_patterns"].items():
            report += f"- **{pattern}**: {count} failures\n"

        report += "\n## Recommendations\n"
        for i, rec in enumerate(results["recommendations"], 1):
            report += f"{i}. {rec}\n"

        report += "\n## High-Risk Test Cases\n"
        high_risk_failures = [
            r
            for r in self.test_results
            if not r.success and r.test_case.risk_level == "HIGH"
        ]
        for result in high_risk_failures[:5]:  # Show top 5
            desc = result.test_case.description
            reason = result.failure_reason
            report += f"- **{desc}**: {reason}\n"

        return report


def main():
    """Main function for testing the CommandCombinationTester."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    # Initialize tester in planning mode
    tester = CommandCombinationTester()

    # Generate critical test cases
    test_cases = tester.generate_critical_test_cases(20)

    print("🧪 Command Combination Tester - Planning Mode")
    print("=" * 50)
    print(f"Generated {len(test_cases)} critical test cases:")

    for i, test_case in enumerate(test_cases, 1):
        print(
            f"{i:2d}. [{test_case.risk_level}] {test_case.command1} → {test_case.command2}"
        )
        print(f"    {test_case.description}")

        # Show sample queries
        variations = tester.generate_natural_language_variations(test_case)
        print(f"    Sample queries: {len(variations)} variations")
        for var in variations[:2]:  # Show first 2
            print(f'      - "{var}"')
        print()

    print("✅ CommandCombinationTester ready for deployment")


if __name__ == "__main__":
    main()
