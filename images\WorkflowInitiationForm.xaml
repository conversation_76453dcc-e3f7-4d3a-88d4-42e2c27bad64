<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-green-10" Color="#1f801f" Opacity="0.1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3.5,9A3.5,3.5,0,0,1,6,10.06V9H7v2.5l-.5.5H4V11H5.5a2.5,2.5,0,1,0,.167,2.75l.865.5A3.5,3.5,0,1,1,3.5,9Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13,5v9H7.832l.065-.113L6.366,13h.548L8,11.914V8H5v.263A4.443,4.443,0,0,0,3.5,8,4.488,4.488,0,0,0,2,8.257V8L9.016,4l-3.5-2H10V5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.854,4.146l-3-3L10.5,1H3.766l1.75,1H10V5h3v9H7.832l-.435.752c-.05.087-.111.165-.167.248H13.5l.5-.5V4.5Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-green-10}" Geometry="F1M6,4,.5,7V1Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M0,0V8L7,4ZM1,1.723,4.984,4,1,6.277Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
