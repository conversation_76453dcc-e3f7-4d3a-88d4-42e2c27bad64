#!/usr/bin/env python3
"""
Complete JEdit2 AI System Integrator
====================================

This module provides complete integration of all AI system components:
- Enhanced AI Manager with testing framework
- Automated Testing Pipeline with continuous monitoring
- Production Monitor with real-time alerts
- System Integration with unified reporting

Week 4 - Complete System Integration & Deployment
"""

import os
import sys
import time
import logging
import threading
import wx
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple, Callable

# Import all our testing and monitoring components
try:
    from .ai_manager_enhanced import EnhancedAIManager
    from .automated_testing_pipeline import AutomatedTestingPipeline
    from .production_monitor import ProductionMonitor
    from .system_integration import JEdit2AISystem
    from .ai_manager_production import ProductionAIManager
except ImportError as e:
    # Fallback for direct execution or missing components
    try:
        from ai_manager_enhanced import EnhancedAIManager
        from automated_testing_pipeline import AutomatedTestingPipeline
        from production_monitor import ProductionMonitor
        from system_integration import JEdit2AISystem
        from ai_manager_production import ProductionAIManager
    except ImportError:
        # If components are missing, create minimal stubs
        class EnhancedAIManager:
            def __init__(self, *args, **kwargs):
                pass
            def get_ai_response_enhanced(self, query):
                return None, "Enhanced AI Manager not available", {}

        class AutomatedTestingPipeline:
            def __init__(self, *args, **kwargs):
                pass
            def generate_test_suite(self, suite_type="comprehensive"):
                return []
            def queue_tests(self, tests):
                pass
            def add_tests_to_queue(self, tests):
                pass
            def start_pipeline(self):
                pass
            def stop_pipeline(self):
                pass
            def execute_test_batch(self, tests):
                return []
            def get_pipeline_status(self):
                return {"is_running": False, "tests_in_queue": 0, "tests_executed": 0}

        class ProductionMonitor:
            def __init__(self, *args, **kwargs):
                pass
            def start_monitoring(self):
                pass
            def record_query(self, **kwargs):
                pass

        class JEdit2AISystem:
            def __init__(self, *args, **kwargs):
                self.enhanced_ai_manager = None
                self.testing_pipeline = None
                self.production_monitor = None
            def initialize(self):
                return False


class AISystemIntegrator:
    """
    Complete AI System Integrator for JEdit2.

    This class provides seamless integration of all AI system components
    with the main JEdit2 application, ensuring minimal disruption to
    existing functionality while providing comprehensive testing and monitoring.
    """

    def __init__(self, main_window, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the AI System Integrator.

        Args:
            main_window: JEdit2 main window instance
            config: Configuration for the AI system
        """
        self.main_window = main_window
        self.logger = self._setup_logging()

        # System components
        self.ai_system: Optional[JEdit2AISystem] = None
        self.enhanced_ai_manager: Optional[EnhancedAIManager] = None
        self.testing_pipeline: Optional[AutomatedTestingPipeline] = None
        self.production_monitor: Optional[ProductionMonitor] = None

        # Integration state
        self.is_integrated = False
        self.integration_mode = "production"  # "development", "testing", "production"
        self.background_thread: Optional[threading.Thread] = None

        # Default configuration
        default_config = {
            "integration_mode": "production",
            "enable_background_testing": False,  # Disable for faster startup
            "enable_real_time_monitoring": False,  # Disable for faster startup
            "enable_automatic_reports": False,  # Disable for faster startup
            "shadow_mode": False,  # Run alongside existing AI without replacing
            "testing_config": {
                "run_on_startup": True,
                "hourly_testing": True,
                "max_parallel_tests": 3,
                "auto_fix_enabled": False,  # Conservative for production
            },
            "monitoring_config": {
                "alert_thresholds": {
                    "success_rate_warning": 0.98,  # Alert if below 98%
                    "response_time_warning": 3.0,  # Alert if above 3 seconds
                    "error_rate_critical": 0.05,  # Critical if above 5% errors
                },
                "enable_adaptive_learning": True,
                "export_metrics": True,
            },
            "reporting_config": {
                "daily_reports": True,
                "weekly_summaries": True,
                "export_path": "reports/ai_system",
            },
        }

        # Merge with user config
        self.config = self._deep_merge(default_config, config or {})
        self.integration_mode = self.config["integration_mode"]

        self.logger.info(
            f"AI System Integrator initialized in {self.integration_mode} mode"
        )

    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the integrator."""
        logger = logging.getLogger(f"{__name__}.AISystemIntegrator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _deep_merge(self, default: Dict, override: Dict) -> Dict:
        """Deep merge configuration dictionaries."""
        result = default.copy()
        for key, value in override.items():
            if (
                key in result
                and isinstance(result[key], dict)
                and isinstance(value, dict)
            ):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result

    def integrate_complete_system(self) -> bool:
        """
        Integrate the complete AI system with JEdit2.

        Returns:
            bool: True if integration successful, False otherwise
        """
        try:
            self.logger.info("Starting complete AI system integration...")

            # Step 1: Initialize the unified AI system
            if not self._initialize_ai_system():
                self.logger.error("Failed to initialize AI system")
                return False

            # Step 2: Setup monitoring and testing
            if not self._setup_monitoring_and_testing():
                self.logger.error("Failed to setup monitoring and testing")
                return False

            # Step 3: Integrate with main window
            if not self._integrate_with_main_window():
                self.logger.error("Failed to integrate with main window")
                return False

            # Step 4: Start background services
            if not self._start_background_services():
                self.logger.error("Failed to start background services")
                return False

            # Step 5: Skip initial health check to avoid startup delays
            # Health checks will run in background services
            self.logger.info("Skipping initial health check for faster startup")

            self.is_integrated = True
            self.logger.info("✅ Complete AI system integration successful!")
            return True

        except Exception as e:
            self.logger.error(f"Integration failed: {e}")
            return False

    def _initialize_ai_system(self) -> bool:
        """Initialize the unified AI system."""
        try:
            # Create the complete AI system
            from .system_integration import integrate_ai_system
            self.ai_system = integrate_ai_system(self.main_window, self.config)

            if not self.ai_system.initialize():
                self.logger.error("AI system initialization failed")
                return False

            # Extract components for direct access
            self.enhanced_ai_manager = self.ai_system.enhanced_ai_manager
            self.testing_pipeline = self.ai_system.testing_pipeline
            self.production_monitor = self.ai_system.production_monitor

            self.logger.info("✅ AI system initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"AI system initialization error: {e}")
            return False

    def _setup_monitoring_and_testing(self) -> bool:
        """Setup monitoring and testing components."""
        try:
            # Configure testing pipeline
            if self.testing_pipeline and self.config["enable_background_testing"]:
                if self.config["testing_config"]["run_on_startup"]:
                    # Generate and queue initial test suite
                    test_suite = self.testing_pipeline.generate_test_suite("startup")
                    self.testing_pipeline.add_tests_to_queue(test_suite)
                    self.logger.info(f"Queued {len(test_suite)} startup tests")

            # Configure production monitor
            if self.production_monitor and self.config["enable_real_time_monitoring"]:
                self.production_monitor.start_monitoring()
                self.logger.info("✅ Production monitoring started")

            return True

        except Exception as e:
            self.logger.error(f"Monitoring and testing setup error: {e}")
            return False

    def _integrate_with_main_window(self) -> bool:
        """Integrate AI system with the main window."""
        try:
            if self.config["shadow_mode"]:
                # Shadow mode: Run alongside existing AI without replacing
                self._setup_shadow_mode_integration()
            else:
                # Full integration: Replace existing AI with enhanced version
                self._setup_full_integration()

            # Add system status to the main window
            self._add_system_status_indicators()

            # Add menu items for system management
            self._add_system_management_menu()

            self.logger.info("✅ Main window integration completed")
            return True

        except Exception as e:
            self.logger.error(f"Main window integration error: {e}")
            return False

    def _setup_shadow_mode_integration(self):
        """Setup shadow mode integration (non-disruptive)."""
        self.logger.info("Setting up shadow mode integration...")

        # Keep existing AI manager functional
        original_ai_manager = self.main_window.ai_manager

        # Add enhanced capabilities as additional methods
        def get_enhanced_ai_response(query: str):
            """Enhanced AI response with monitoring."""
            if self.enhanced_ai_manager:
                return self.enhanced_ai_manager.get_ai_response_enhanced(query)
            else:
                return original_ai_manager.get_ai_response(query)

        # Add the enhanced method without replacing existing functionality
        self.main_window.get_enhanced_ai_response = get_enhanced_ai_response

    def _setup_full_integration(self):
        """Setup full integration (replace existing AI manager)."""
        self.logger.info("Setting up full integration...")

        if self.enhanced_ai_manager:
            # Replace the AI manager completely
            original_ai_manager = self.main_window.ai_manager
            self.main_window.ai_manager = self.enhanced_ai_manager
            self.logger.info(f"Replaced AI manager: {type(original_ai_manager)} -> {type(self.enhanced_ai_manager)}")

            # Update AI submit handler
            def enhanced_ai_submit(event):
                """Enhanced AI submit with full monitoring and testing."""
                try:
                    self.logger.info("Enhanced AI submit called")

                    # Get the query from the correct input field
                    query = self.main_window.ai_input.GetValue().strip()
                    if not query:
                        self.logger.warning("No query provided")
                        return

                    self.logger.info(f"Processing query: {query}")

                    # Process with enhanced AI manager - with robust fallback
                    commands = None
                    raw_response = ""
                    analysis = {}

                    try:
                        # Try enhanced AI manager first
                        commands, raw_response, analysis = (
                            self.enhanced_ai_manager.get_ai_response_enhanced(query)
                        )
                        self.logger.info(f"Enhanced AI response: commands={commands is not None}, raw_length={len(raw_response) if raw_response else 0}")
                    except Exception as e:
                        self.logger.error(f"Enhanced AI manager failed: {e}")
                        try:
                            # Fallback to production AI manager
                            from jedit2.utils.ai_manager_production import ProductionAIManager
                            production_manager = ProductionAIManager(self.main_window.ai_manager)
                            commands, raw_response = production_manager.get_ai_response_robust(query)
                            analysis = {"fallback": "production", "error": str(e)}
                            self.logger.info(f"Production fallback AI response: commands={commands is not None}")
                        except Exception as e2:
                            self.logger.error(f"Production AI manager also failed: {e2}")
                            # Final fallback to basic AI manager
                            commands, raw_response = self.main_window.ai_manager.get_ai_response(query)
                            analysis = {"fallback": "basic", "error": str(e), "error2": str(e2)}
                            self.logger.info(f"Basic fallback AI response: commands={commands is not None}")

                    # Update debug display
                    try:
                        if hasattr(self.main_window, 'ai_debug_display'):
                            debug_text = f"Query: {query}\n\nEnhanced AI Response:\n{commands}\n\nRaw: {raw_response[:200] if raw_response else 'No response'}"
                            self.main_window.ai_debug_display.SetValue(debug_text)
                            self.logger.info("Debug display updated successfully")
                        else:
                            self.logger.warning("ai_debug_display not found on main window")
                    except Exception as debug_error:
                        self.logger.error(f"Failed to update debug display: {debug_error}")

                    # Process commands using existing handler
                    if commands:
                        for cmd in commands:
                            self.main_window._process_ai_command(cmd)
                    else:
                        self.logger.warning("No commands returned from enhanced AI manager")

                    # Clear input
                    self.main_window.ai_input.Clear()

                    # Log for monitoring
                    if self.production_monitor:
                        try:
                            # Try the correct method name
                            if hasattr(self.production_monitor, 'record_query_result'):
                                self.production_monitor.record_query_result(
                                    query=query,
                                    success=(commands is not None),
                                    response_time=analysis.get("response_time", 0.0),
                                    analysis_data=analysis,
                                )
                            else:
                                self.logger.debug("Production monitor doesn't have record_query_result method")
                        except Exception as monitor_error:
                            self.logger.debug(f"Monitoring error (non-critical): {monitor_error}")

                except Exception as e:
                    self.logger.error(f"Enhanced AI submit error: {e}")
                    import traceback
                    self.logger.error(f"Traceback: {traceback.format_exc()}")

            # Replace the AI submit handler
            self.logger.info("Replacing AI submit handler with enhanced version")
            self.main_window._on_ai_submit = enhanced_ai_submit

    def _add_system_status_indicators(self):
        """Add system status indicators to the main window."""
        try:
            # Add status bar indicators for AI system health
            status_bar = self.main_window.GetStatusBar()
            if status_bar and hasattr(status_bar, "SetFieldsCount"):
                # Extend status bar for AI system status
                current_fields = status_bar.GetFieldsCount()
                status_bar.SetFieldsCount(current_fields + 1)

                # Initial status
                self._update_status_indicator("AI System: Active")

        except Exception as e:
            self.logger.warning(f"Could not add status indicators: {e}")

    def _update_status_indicator(self, status_text: str):
        """Update the AI system status indicator."""
        try:
            status_bar = self.main_window.GetStatusBar()
            if status_bar:
                field_count = status_bar.GetFieldsCount()
                if field_count > 0:
                    status_bar.SetStatusText(status_text, field_count - 1)
        except:
            pass  # Silently ignore if status bar updates fail

    def _add_system_management_menu(self):
        """Add AI system management menu items."""
        try:
            # Add to Tools menu
            menu_bar = self.main_window.GetMenuBar()
            if menu_bar:
                tools_menu = None
                for i in range(menu_bar.GetMenuCount()):
                    if menu_bar.GetMenuLabelText(i) == "Tools":
                        tools_menu = menu_bar.GetMenu(i)
                        break

                if tools_menu:
                    tools_menu.AppendSeparator()

                    # AI System submenu
                    ai_system_menu = wx.Menu()
                    ai_system_menu.Append(
                        wx.ID_ANY, "System Status", "View AI system status"
                    )
                    ai_system_menu.Append(
                        wx.ID_ANY,
                        "Generate Report",
                        "Generate comprehensive system report",
                    )
                    ai_system_menu.Append(
                        wx.ID_ANY, "Run Tests", "Run AI system tests manually"
                    )
                    ai_system_menu.Append(
                        wx.ID_ANY, "Monitor Dashboard", "Open monitoring dashboard"
                    )

                    tools_menu.AppendSubMenu(ai_system_menu, "AI System")

        except Exception as e:
            self.logger.warning(f"Could not add system management menu: {e}")

    def _start_background_services(self) -> bool:
        """Start background services for testing and monitoring."""
        try:
            if (
                self.config["enable_background_testing"]
                or self.config["enable_real_time_monitoring"]
            ):
                self.background_thread = threading.Thread(
                    target=self._background_service_loop,
                    daemon=True,
                    name="AISystemBackgroundServices",
                )
                self.background_thread.start()
                self.logger.info("✅ Background services started")

            return True

        except Exception as e:
            self.logger.error(f"Background services startup error: {e}")
            return False

    def _background_service_loop(self):
        """Background service loop for testing and monitoring."""
        self.logger.info("Background services loop started")

        last_hourly_test = 0
        last_daily_report = 0
        last_weekly_summary = 0

        try:
            while self.is_integrated:
                current_time = time.time()

                # Hourly testing
                if (
                    self.config["testing_config"]["hourly_testing"]
                    and current_time - last_hourly_test > 3600
                ):  # 1 hour

                    self._run_hourly_tests()
                    last_hourly_test = current_time

                # Daily reports
                if (
                    self.config["reporting_config"]["daily_reports"]
                    and current_time - last_daily_report > 86400
                ):  # 24 hours

                    self._generate_daily_report()
                    last_daily_report = current_time

                # Weekly summaries
                if (
                    self.config["reporting_config"]["weekly_summaries"]
                    and current_time - last_weekly_summary > 604800
                ):  # 7 days

                    self._generate_weekly_summary()
                    last_weekly_summary = current_time

                # Update status indicators
                self._update_system_status()

                # Sleep for 60 seconds before next check
                time.sleep(60)

        except Exception as e:
            self.logger.error(f"Background service loop error: {e}")

    def _run_hourly_tests(self):
        """Run hourly automated tests."""
        try:
            if self.testing_pipeline:
                # Generate targeted test suite
                test_suite = self.testing_pipeline.generate_test_suite("hourly")

                # Execute tests
                results = self.testing_pipeline.execute_test_batch(test_suite)

                # Log results
                passed = sum(1 for r in results if r.status.name == "PASSED")
                total = len(results)
                success_rate = (passed / total) if total > 0 else 0

                self.logger.info(
                    f"Hourly tests completed: {passed}/{total} passed ({success_rate:.1%})"
                )

                # Alert on poor performance
                if success_rate < 0.95:  # Less than 95% success
                    self._send_alert(
                        f"Hourly test success rate low: {success_rate:.1%}"
                    )

        except Exception as e:
            self.logger.error(f"Hourly testing error: {e}")

    def _generate_daily_report(self):
        """Generate daily system report."""
        try:
            if self.ai_system:
                report = self.ai_system.generate_comprehensive_report()

                # Save report
                report_dir = Path(self.config["reporting_config"]["export_path"])
                report_dir.mkdir(parents=True, exist_ok=True)

                timestamp = datetime.now().strftime("%Y%m%d")
                report_file = report_dir / f"daily_report_{timestamp}.json"

                import json

                with open(report_file, "w") as f:
                    json.dump(report, f, indent=2, default=str)

                self.logger.info(f"Daily report saved: {report_file}")

        except Exception as e:
            self.logger.error(f"Daily report generation error: {e}")

    def _generate_weekly_summary(self):
        """Generate weekly system summary."""
        try:
            # Implementation for weekly summary generation
            self.logger.info("Weekly summary generated")

        except Exception as e:
            self.logger.error(f"Weekly summary generation error: {e}")

    def _update_system_status(self):
        """Update system status indicators."""
        try:
            if self.ai_system:
                health_status = self.ai_system.run_health_check()
                status_text = f"AI System: {health_status['overall_status'].title()}"

                # Add metrics if available
                if "metrics" in health_status:
                    metrics = health_status["metrics"]
                    if "success_rate" in metrics:
                        status_text += f" ({metrics['success_rate']:.1%})"

                self._update_status_indicator(status_text)

        except Exception as e:
            self.logger.debug(f"Status update error: {e}")

    def _send_alert(self, message: str):
        """Send system alert."""
        self.logger.warning(f"ALERT: {message}")
        # Could extend to send email, notifications, etc.

    def _run_initial_health_check(self) -> Dict[str, Any]:
        """Run initial system health check."""
        if self.ai_system:
            return self.ai_system.run_health_check()
        else:
            return {"overall_status": "unknown", "components": {}}

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        if self.ai_system:
            return self.ai_system.get_system_status()
        else:
            return {
                "integrated": self.is_integrated,
                "mode": self.integration_mode,
                "components": {},
            }

    def shutdown(self):
        """Shutdown the AI system integrator."""
        try:
            self.logger.info("Shutting down AI System Integrator...")

            # Stop background services
            self.is_integrated = False
            if self.background_thread and self.background_thread.is_alive():
                self.background_thread.join(timeout=5.0)

            # Shutdown AI system
            if self.ai_system:
                self.ai_system.shutdown()

            self.logger.info("✅ AI System Integrator shutdown complete")

        except Exception as e:
            self.logger.error(f"Shutdown error: {e}")


def integrate_complete_ai_system(
    main_window, config: Optional[Dict[str, Any]] = None
) -> AISystemIntegrator:
    """
    Main integration function for complete AI system.

    Args:
        main_window: JEdit2 main window instance
        config: Configuration for the AI system

    Returns:
        AISystemIntegrator: The integrator instance
    """
    integrator = AISystemIntegrator(main_window, config)

    if integrator.integrate_complete_system():
        return integrator
    else:
        raise RuntimeError("Failed to integrate complete AI system")


# Usage example and testing
if __name__ == "__main__":
    # Demo configuration for testing
    demo_config = {
        "integration_mode": "development",
        "shadow_mode": True,  # Safe for testing
        "testing_config": {
            "run_on_startup": True,
            "hourly_testing": False,  # Disable for demo
        },
        "monitoring_config": {
            "alert_thresholds": {
                "success_rate_warning": 0.90,  # Lower threshold for demo
            }
        },
    }

    print("🚀 AI System Integrator Demo")
    print("============================")
    print("Complete integration system for JEdit2 AI")
    print("✅ Enhanced AI Manager with testing framework")
    print("✅ Automated Testing Pipeline with continuous monitoring")
    print("✅ Production Monitor with real-time alerts")
    print("✅ System Integration with unified reporting")
    print("\nReady for integration with JEdit2!")
