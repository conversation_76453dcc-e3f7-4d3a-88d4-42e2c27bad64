#!/usr/bin/env python3
"""
Automated validation system for AI commands.
This script can be run as part of CI/CD or development workflow to catch
command mapping issues before they reach production.
"""

import sys
import json
from analyze_ai_commands import analyze_command_consistency


def generate_fixes_for_issues(issues):
    """Generate code fixes for the identified issues"""
    fixes = []
    
    for capability, expected, actual, issue_type in issues:
        if issue_type == "missing_method":
            if capability == "COPY_COLUMN_TO_NEW_COLUMN_AFTER":
                # This is already handled by ai_handler
                fix = f"""
# Add to _process_ai_command special cases:
if capability == "{capability}":
    self.ai_handler.execute_command(capability, **params)
    return"""
            else:
                # Generate a stub method
                fix = f"""
# Add this method to MainWindow class:
def {expected}(self, **params):
    '''Handle {capability} command.'''
    # TODO: Implement {capability} functionality
    wx.MessageBox("Command {capability} not yet implemented", "Info", wx.OK | wx.ICON_INFORMATION)"""
            
            fixes.append((capability, fix))
    
    return fixes


def validate_ai_commands():
    """Main validation function that can be used in CI/CD"""
    print("🔍 Validating AI Command Consistency...")
    
    issues, working, orphaned = analyze_command_consistency()
    
    # Filter out known issues that are intentionally handled differently
    known_exceptions = {
        "COPY_COLUMN_TO_NEW_COLUMN_AFTER",  # Handled by ai_handler
        "EXCEL_LIST_WORKSHEETS",           # Excel features not yet implemented
        "EXCEL_SWITCH_WORKSHEET",          # Excel features not yet implemented
        "EXCEL_SAVE_AS_CSV",               # Excel features not yet implemented
        "EXCEL_VALIDATE_FILE",             # Excel features not yet implemented
    }
    
    critical_issues = [
        issue for issue in issues 
        if issue[0] not in known_exceptions
    ]
    
    if critical_issues:
        print(f"\n❌ CRITICAL ISSUES FOUND ({len(critical_issues)}):")
        for capability, expected, actual, issue_type in critical_issues:
            print(f"  - {capability}: {issue_type}")
        
        print(f"\n🛠️  SUGGESTED FIXES:")
        fixes = generate_fixes_for_issues(critical_issues)
        for capability, fix in fixes:
            print(f"\n{capability}:")
            print(fix)
        
        return False
    else:
        print("✅ All AI commands are properly mapped!")
        return True


def generate_command_mapping_documentation():
    """Generate documentation for all command mappings"""
    issues, working, orphaned = analyze_command_consistency()
    
    doc = """# AI Command Mapping Documentation

This document shows how AI capabilities map to implementation methods.

## Working Commands

| Capability | Method | Mapping Type |
|------------|--------|--------------|
"""
    
    for capability, method, mapping_type in sorted(working):
        doc += f"| `{capability}` | `{method}` | {mapping_type} |\n"
    
    if issues:
        doc += "\n## Issues Found\n\n"
        for capability, expected, actual, issue_type in issues:
            doc += f"- **{capability}**: {issue_type}\n"
    
    if orphaned:
        doc += "\n## Orphaned Methods\n\nThese methods exist but have no corresponding capability:\n\n"
        for method in sorted(orphaned):
            doc += f"- `{method}`\n"
    
    return doc


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--docs":
        # Generate documentation
        doc = generate_command_mapping_documentation()
        with open("AI_COMMAND_MAPPING.md", "w") as f:
            f.write(doc)
        print("📝 Documentation generated: AI_COMMAND_MAPPING.md")
    else:
        # Run validation
        success = validate_ai_commands()
        sys.exit(0 if success else 1)
