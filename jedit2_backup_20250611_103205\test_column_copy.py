#!/usr/bin/env python3
"""
Test script to verify the column copy functionality works correctly.
This script tests the AI handler's copy_column_to_new_column_after method.
"""

import sys
import os
import wx

# Add the parent directory to the path so we can import jedit2 modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jedit2.main_window import MainWindow, ViewContainer
from jedit2.ai_handler import AICommandHandler


class TestApp(wx.App):
    def OnInit(self):
        # Create a main window
        self.main_window = MainWindow(None)
        
        # Create some test data
        test_data = [
            ["Key", "Value", "Price"],
            ["property_data.property_name", "Price", ""],
            ["property_data.purchase_date", "2015-03-15", ""],
            ["property_data.purchase_price", "77000.0", ""],
            ["property_data.sale_date", "2025-06-15", ""],
            ["property_data.sale_price", "230000.0", ""],
        ]
        
        # Get the current tab and switch to grid view
        current_tab = self.main_window.notebook.GetCurrentPage()
        if isinstance(current_tab, ViewContainer):
            # Switch to grid view
            current_tab.switch_view()

            # Get the grid and set up test data
            grid = current_tab.grid_view
            grid.CreateGrid(len(test_data), len(test_data[0]))

            # Set column labels A, B, C...
            for i in range(len(test_data[0])):
                grid.SetColLabelValue(i, chr(ord("A") + i))

            # Set the test data
            grid.set_data(test_data)
        
        # Create AI handler
        ai_handler = AICommandHandler(self.main_window)
        
        print("Before operation:")
        self.print_grid_state()
        
        # Test the copy operation: copy column C to new column after B
        print("\nTesting: Copy column C to new column after B with bold formatting...")
        try:
            ai_handler.copy_column_to_new_column_after(
                source_column='C',
                target_column='B', 
                apply_bold=True
            )
            print("✅ Operation completed successfully!")
        except Exception as e:
            print(f"❌ Operation failed: {e}")
            return False
        
        print("\nAfter operation:")
        self.print_grid_state()
        
        # Verify the results
        grid = self.main_window._get_current_grid()
        if not grid:
            print("❌ No active grid found for verification")
            return False
        num_cols = grid.GetNumberCols()
        
        print(f"\nVerification:")
        print(f"Number of columns: {num_cols} (should be 4)")
        
        if num_cols == 4:
            print("✅ Column was successfully inserted")
            
            # Check if data was copied correctly
            for row in range(grid.GetNumberRows()):
                original_c_value = test_data[row][2] if row < len(test_data) else ""
                new_col_value = grid.GetCellValue(row, 2)  # New column C (after insertion)
                print(f"Row {row}: Original C='{original_c_value}', New C='{new_col_value}'")
                
                if original_c_value == new_col_value:
                    print(f"  ✅ Row {row} data copied correctly")
                else:
                    print(f"  ❌ Row {row} data mismatch")
        else:
            print("❌ Column insertion failed")
        
        return True
    
    def print_grid_state(self):
        """Print the current state of the grid."""
        grid = self.main_window._get_current_grid()
        if not grid:
            print("No active grid found")
            return
        rows = grid.GetNumberRows()
        cols = grid.GetNumberCols()
        
        print(f"Grid state: {rows} rows x {cols} columns")
        
        # Print column headers
        headers = []
        for col in range(cols):
            headers.append(chr(ord('A') + col))
        print("   " + "  ".join(f"{h:>12}" for h in headers))
        
        # Print data
        for row in range(min(rows, 6)):  # Show first 6 rows
            row_data = []
            for col in range(cols):
                value = grid.GetCellValue(row, col)
                row_data.append(value[:12])  # Truncate long values
            print(f"{row+1:2} " + "  ".join(f"{v:>12}" for v in row_data))


if __name__ == '__main__':
    app = TestApp()
    app.MainLoop()
