#!/usr/bin/env python3
"""
Test the final push to fix the remaining 3 critical JSON cases.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_final_json_cases():
    """Test the final 3 JSON cases that are still failing."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # The 3 remaining JSON cases
    final_json_cases = [
        {
            'name': 'Mixed Array/Object',
            'content': '{"key": "value", "item2"}',
            'description': 'Object with orphaned string value'
        },
        {
            'name': 'Nested Object Fragment',
            'content': '"outer": {"inner": "value"',
            'description': 'Nested object missing closing braces'
        },
        {
            'name': 'Multiple Errors',
            'content': '{key1: "value1", "key2": value2,}',
            'description': 'Multiple issues: unquoted key, unquoted value, trailing comma'
        }
    ]
    
    print("🎯 FINAL JSON PUSH - TESTING REMAINING 3 CASES")
    print("=" * 70)
    print("These are the last 3 JSON cases preventing 100% JSON coverage...")
    print()
    
    results = []
    
    for i, case in enumerate(final_json_cases, 1):
        print(f"{i}. {case['name']}")
        print("-" * 60)
        print(f"   Description: {case['description']}")
        print(f"   Content: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], 'json')
        print(f"   Correctable: {validation_result.is_correctable}")
        
        if validation_result.warnings:
            print(f"   Warnings: {validation_result.warnings[:2]}...")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], 'json')
            
            if correction_result.success:
                print(f"   ✅ SUCCESS!")
                print(f"   Changes: {correction_result.changes_made}")
                
                # Verify the result is valid JSON
                try:
                    import json
                    parsed = json.loads(correction_result.corrected_content)
                    print(f"   ✅ Valid JSON!")
                    
                    # Show result
                    result_preview = correction_result.corrected_content[:100]
                    if len(correction_result.corrected_content) > 100:
                        result_preview += "..."
                    print(f"   Result: {repr(result_preview)}")
                    success = True
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid JSON: {e}")
                    
            else:
                print(f"   ❌ CORRECTION FAILED")
                print(f"   Errors: {correction_result.errors[:2]}...")  # Show first 2 errors
        else:
            print(f"   ❌ NOT DETECTED AS CORRECTABLE")
        
        results.append({
            'name': case['name'],
            'success': success
        })
        print()
    
    # Summary
    print("=" * 70)
    print("📊 FINAL JSON PUSH SUMMARY")
    print("=" * 70)
    
    total = len(results)
    passed = sum(1 for r in results if r['success'])
    
    print(f"Final JSON Cases: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL JSON CASES FIXED!")
        print("JSON coverage: 21/21 (100%)")
        print("Estimated total coverage boost: +5.4% (to ~71.5%)")
    elif passed >= 2:
        print("\n🚀 MAJOR PROGRESS!")
        print(f"JSON coverage: {18 + passed}/21 ({((18 + passed)/21)*100:.1f}%)")
        estimated_boost = passed * 1.8
        print(f"Estimated total coverage boost: +{estimated_boost:.1f}% (to ~{66.1 + estimated_boost:.1f}%)")
    else:
        print("\n🔧 MORE WORK NEEDED on these complex cases.")
        
        print("\nStill failing:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}")
    
    return passed, total

def analyze_remaining_gaps():
    """Analyze what's needed to reach 80% coverage."""
    print("\n" + "=" * 70)
    print("📈 PATH TO 80% COVERAGE ANALYSIS")
    print("=" * 70)
    
    current_coverage = 66.1
    target_coverage = 80.0
    total_cases = 56
    
    current_covered = int(current_coverage / 100 * total_cases)  # 37 cases
    target_covered = int(target_coverage / 100 * total_cases)   # 45 cases
    
    needed_fixes = target_covered - current_covered  # 8 more cases
    
    print(f"Current: {current_covered}/56 cases ({current_coverage}%)")
    print(f"Target:  {target_covered}/56 cases ({target_coverage}%)")
    print(f"Need to fix: {needed_fixes} more cases")
    
    print(f"\nRemaining gaps by format:")
    print(f"  JSON: 3 cases (if all fixed: +5.4%)")
    print(f"  YAML: 10 cases (if 5 fixed: +8.9%)")
    print(f"  CSV: 3 cases (if all fixed: +5.4%)")
    print(f"  Markdown: 3 cases (if all fixed: +5.4%)")
    
    print(f"\nRealistic path to 80%:")
    print(f"  1. Fix remaining 3 JSON cases: +5.4% → 71.5%")
    print(f"  2. Fix 3-4 YAML cases: +5-7% → 76-78%")
    print(f"  3. Fix 1-2 CSV/Markdown cases: +2-4% → 80%+")
    
    return needed_fixes

if __name__ == "__main__":
    print("🧪 FINAL JSON PUSH TEST")
    print("=" * 40)
    
    passed, total = test_final_json_cases()
    needed = analyze_remaining_gaps()
    
    print(f"\n🎯 RESULT: {passed}/{total} final JSON cases fixed")
    
    if passed == total:
        print("🎉 JSON is now 100% covered! Push for 80% total coverage!")
    elif passed >= 2:
        print("🚀 Major progress! Continue with YAML improvements.")
    else:
        print("🔧 Debug these complex JSON cases first.")
