<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,12.5H9.5L8.5,10V8.5h6V10Zm-13,0H5.5l1-2.5V8.5H.5V10Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,8h-6L8,8.5V9H6.989L7,8.5,6.5,8H.5L0,8.5l.01,1.6.5,2.5L1,13H5.5l.464-.314,1-2.5V10H8l1.036,2.686L9.5,13H14l.49-.4L15,10V8.5ZM6,9.9,5.162,12H1.408L1,10V9H6ZM13.59,12H9.838L9,9.9V9h5l.007.9Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.947,8.276l-2.5-5L12,3H10V4h1.691l2,4H14.5l.5.5ZM0,8.5.5,8h.809l2-4H5V3H3l-.447.276-2.5,5Z" />
              </DrawingGroup>
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
