<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,15.971H0v-16H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,4.5h-4v-3h4Zm5,10h-2v-2h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,12H14V8.5L13.5,8H8V5H9.5l.5-.5v-3L9.5,1h-4L5,1.5v3l.5.5H7V8H1.5L1,8.5v2.341l1-1V9H7v1.971h.223L8,10.19V9h5v3h-.5l-.5.5v2l.5.5h2l.5-.5v-2ZM6,4V2H9V4Zm8,10H13V13h1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M1.207,13.469l1.816,1.82L2.316,16,.147,13.823v-.706l2.169-2.179.709.705Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.854,13.118v.709L8.666,16l-.7-.709,1.831-1.819L7.961,11.647l.705-.709Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,11.971v3H4v-3Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
