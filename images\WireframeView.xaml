<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey-25" Color="#212121" Opacity="0.25" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.5,16C4.851,16,2,15.374,2,14V3H3V14c.076.27,1.576,1,4.5,1s4.424-.73,4.5-1.008V3h1V14C13,15.374,10.149,16,7.5,16Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,15H7V4H8ZM5,4H4V15H5Zm6,0H10V15h1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.5,5C4.851,5,2,4.374,2,3S4.851,1,7.5,1,13,1.626,13,3,10.149,5,7.5,5Zm0-3C4.576,2,3.076,2.73,3,3.008,3.076,3.27,4.576,4,7.5,4c2.9,0,4.4-.717,4.5-1C11.9,2.717,10.4,2,7.5,2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M3,3.009v0C3.08,2.728,4.579,2,7.5,2c2.9,0,4.4.717,4.5,1-.1.283-1.6,1-4.5,1C4.579,4,3.08,3.272,3,3.009Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-25}" Geometry="F1M12,4.214A11.051,11.051,0,0,1,7.5,5,11.051,11.051,0,0,1,3,4.214V14c.076.27,1.576,1,4.5,1s4.424-.73,4.5-1.008Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
