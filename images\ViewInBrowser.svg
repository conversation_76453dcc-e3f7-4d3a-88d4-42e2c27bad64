<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>IconLightViewInBrowser</title>
  <g id="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <g class="cls-1">
      <path class="light-defaultgrey-10" d="M3.5,4V5.2A2.784,2.784,0,0,1,4,5.09,5.47,5.47,0,0,1,5,5a6,6,0,0,1,6,6,6.634,6.634,0,0,1-.08,1H15.5V4Z" />
      <path class="light-defaultgrey" d="M15.5,4H3.5L3,4.5v.85a2.213,2.213,0,0,1,.5-.15A2.784,2.784,0,0,1,4,5.09V5H15v7H10.92a6.671,6.671,0,0,1-.26,1H15.5l.5-.5v-8Z" />
    </g>
    <path class="light-defaultgrey-10" d="M4,2V4H15V2Z" />
    <path class="light-defaultgrey" d="M15.5,1H3.5L3,1.5v3l.5.5h12l.5-.5v-3ZM4,4V2H15V4Z" />
    <path class="light-defaultgrey-10" d="M9.5,11A4.5,4.5,0,1,1,5,6.5,4.5,4.5,0,0,1,9.5,11Z" />
    <path class="light-defaultgrey" d="M9,14a5,5,0,1,0-4,2,4.974,4.974,0,0,0,4-2H9Zm0-3a4.024,4.024,0,0,1-.126,1H5V7A4,4,0,0,1,9,11ZM5,15A4,4,0,0,1,3,7.537V13l1,1H7.643A3.984,3.984,0,0,1,5,15Z" />
  </g>
</svg>
