"""CSV file handler for JEdit2."""
import csv
from typing import List, Any

def parse_csv(path: str) -> List[List[Any]]:
    """Parse a CSV file and return its contents as a list of rows.

    Args:
        path: Path to the CSV file.
    Returns:
        List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be parsed.
    """
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        return [row for row in reader]

def save_csv(path: str, data: List[List[Any]]) -> None:
    """Save data to a CSV file.

    Args:
        path: Path where to save the CSV file.
        data: List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be saved.
    """
    with open(path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(data) 