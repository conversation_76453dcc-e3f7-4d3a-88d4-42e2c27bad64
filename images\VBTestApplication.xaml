<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M2.528,8.5h2v3l1.25,2h-4.5l1.25-2Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2.03,9h-1V8h5V9h-1v2.356L6.056,13H4.878L4.03,11.644V9h-1v2.644L2.182,13H1L2.03,11.356Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M1.528,13.5h4l1,1.5-.5.5h-5l-.5-.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.911,16H1.148A1.12,1.12,0,0,1,.2,14.286L1,13H6.056l.8,1.287A1.12,1.12,0,0,1,5.911,16ZM1.557,14l-.511.816a.118.118,0,0,0,0,.122.116.116,0,0,0,.1.062H5.911a.116.116,0,0,0,.105-.062.112.112,0,0,0,0-.121L5.5,14Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,5v8H7.236l-.257-.412L6.03,11.069V10h1V7H1.5V5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,5.5v8l-.5.5H7.86l-.15-.24v0L7.236,13H14V6H2V7H1V5.5L1.5,5h13Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,3V5H2V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,6H1.5L1,5.5v-3L1.5,2h13l.5.5v3ZM2,5H14V3H2Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
