<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-red-10" Color="#c50b17" Opacity="0.1" />
      <SolidColorBrush x:Key="light-red" Color="#c50b17" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,6.5v6H4.5V7H5.914l.5-.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-red-10}" Geometry="F1M5.5,5.5H.5V.5h5Z" />
              <GeometryDrawing Brush="{DynamicResource light-red}" Geometry="F1M4,3A1,1,0,1,1,3,2,1,1,0,0,1,4,3Z" />
              <GeometryDrawing Brush="{DynamicResource light-red}" Geometry="F1M5.5,0H.5L0,.5v5L.5,6h5L6,5.5V.5ZM3,5A2,2,0,1,1,5,3,2,2,0,0,1,3,5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,6.5v6l-.5.5H10v1h2v1H7V14H9V13H4.5L4,12.5V7H5v5h9V7H5.914l1-1H14.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
