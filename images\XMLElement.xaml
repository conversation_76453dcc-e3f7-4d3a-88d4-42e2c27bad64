<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,3v9H8.306l-.153-.011L5.8,9.619,4.006,11.4,4,11.394V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,5.705,6.183,7.533,8,9.353l-.707.707L5.121,7.886V7.179L7.291,5ZM10.811,10,13,7.827V7.118L10.811,4.939l-.7.709,1.831,1.823-1.826,1.82ZM14.5,2H3.5L3,2.5v7.894l1,1V3H14v9H8.163l.771.776V13H14.5l.5-.5V2.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2.223,15.967.085,13.813l0-.707L2.193,11l.707.707L1.146,13.461l1.787,1.8ZM5.8,11.033l2.138,2.155v.706L5.826,16l-.707-.707,1.754-1.754-1.788-1.8Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
