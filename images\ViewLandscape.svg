<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>IconLightViewLandscape</title>
  <g id="canvas">
    <path class="canvas" d="M16,0V16H0V0Z" />
  </g>
  <g id="level-1">
    <path class="light-defaultgrey-10" d="M14.5,2.5v10H1.5V2.5Z" />
    <path class="light-defaultgrey" d="M14.5,13H1.5L1,12.5V2.5L1.5,2h13l.5.5v10ZM2,12H14V3H2Z" />
    <g class="cls-1">
      <path class="light-defaultgrey" d="M12.5,4.5v6h-9v-6Z" />
    </g>
    <path class="light-defaultgrey" d="M12.5,11h-9L3,10.5v-6L3.5,4h9l.5.5v6ZM4,10h8V5H4Z" />
  </g>
</svg>
