"""
Excel format handler for jedit2.

This module provides functionality to read from and write to Excel files (.xlsx)
using the OpenPyXL library. It maintains data integrity while converting between
Excel format and the internal jedit2 grid representation.
"""

import logging
import os
from typing import List, Optional, Any, Dict

# Set up logging
logger = logging.getLogger(__name__)

# OpenPyXL availability check
try:
    import openpyxl
    from openpyxl import Workbook
    OPENPYXL_AVAILABLE = True
    logger.info("OpenPyXL is available for Excel support")
except ImportError:
    OPENPYXL_AVAILABLE = False
    logger.warning("OpenPyXL not available - Excel support disabled")


def is_available() -> bool:
    """Check if Excel format support is available."""
    return OPENPYXL_AVAILABLE


def parse_xlsx(file_path: str) -> List[List[str]]:
    """
    Parse an Excel file and return the data as a list of lists.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        List of lists representing the spreadsheet data
        
    Raises:
        ImportError: If OpenPyXL is not available
        Exception: For other Excel parsing errors
    """
    if not OPENPYXL_AVAILABLE:
        raise ImportError(
            "OpenPyXL is required for Excel support. "
            "Please install it: pip install openpyxl"
        )

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    try:
        logger.info(f"Parsing Excel file: {file_path}")
        
        # Load the workbook in read-only mode for better performance
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        
        # Get the first worksheet
        worksheet = workbook.active
        
        data = []
        for row in worksheet.iter_rows(values_only=True):
            # Convert each row to strings, handling None values
            row_data = []
            for cell in row:
                if cell is None:
                    row_data.append("")
                else:
                    # Convert all values to strings, preserving data types
                    row_data.append(str(cell).strip())
            data.append(row_data)
        
        # Remove empty rows from the end
        while data and all(cell == "" for cell in data[-1]):
            data.pop()
        
        workbook.close()
        logger.info(f"Successfully parsed Excel file with {len(data)} rows")
        return data

    except Exception as e:
        logger.error(f"Error parsing Excel file {file_path}: {str(e)}")
        raise Exception(f"Failed to parse Excel file: {str(e)}")


def save_xlsx(
    file_path: str,
    data: List[List[str]],
    original_file: Optional[str] = None
) -> bool:
    """
    Save data to an Excel file.
    
    Args:
        file_path: Path where the Excel file should be saved
        data: List of lists representing the spreadsheet data
        original_file: Optional path to original file for structure preservation
        
    Returns:
        True if save was successful
        
    Raises:
        ImportError: If OpenPyXL is not available
        Exception: For other Excel saving errors
    """
    if not OPENPYXL_AVAILABLE:
        raise ImportError(
            "OpenPyXL is required for Excel support. "
            "Please install it: pip install openpyxl"
        )

    try:
        logger.info(f"Saving Excel file: {file_path}")

        # Try to preserve original workbook structure if available
        if original_file and os.path.exists(original_file):
            try:
                logger.info(f"Loading original file for structure: {original_file}")
                workbook = openpyxl.load_workbook(original_file)
                worksheet = workbook.active
            except Exception as e:
                logger.warning(
                    f"Could not load original file {original_file}: {e}. "
                    "Creating new workbook."
                )
                workbook = Workbook()
                worksheet = workbook.active
        else:
            workbook = Workbook()
            worksheet = workbook.active

        # Clear existing data in the worksheet
        worksheet.delete_rows(1, worksheet.max_row)

        # Write data to worksheet
        for row_idx, row_data in enumerate(data, start=1):
            for col_idx, cell_value in enumerate(row_data, start=1):
                cell = worksheet.cell(row=row_idx, column=col_idx)

                # Try to preserve numeric types
                if str(cell_value).strip():
                    try:
                        # Try integer first
                        if isinstance(cell_value, (int, float)):
                            cell.value = cell_value
                        elif ('.' not in str(cell_value)
                              and str(cell_value).replace('-', '').isdigit()):
                            cell.value = int(cell_value)
                        elif (str(cell_value).replace('.', '').replace('-', '')
                              .isdigit()):
                            cell.value = float(cell_value)
                        else:
                            cell.value = cell_value
                    except (ValueError, TypeError):
                        cell.value = cell_value
                else:
                    cell.value = None

        # Save the workbook
        workbook.save(file_path)
        logger.info(f"Successfully saved Excel file with {len(data)} rows")
        return True

    except Exception as e:
        logger.error(f"Error saving Excel file {file_path}: {str(e)}")
        raise Exception(f"Failed to save Excel file: {str(e)}")


def get_worksheet_names(file_path: str) -> List[str]:
    """
    Get the names of all worksheets in an Excel file.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        List of worksheet names
        
    Raises:
        Exception if file cannot be read or is not a valid Excel file
    """
    if not OPENPYXL_AVAILABLE:
        raise Exception("OpenPyXL is not available")
    
    try:
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        worksheet_names = workbook.sheetnames
        workbook.close()
        return worksheet_names
    except Exception as e:
        logger.error(f"Error getting worksheet names from {file_path}: {e}")
        raise Exception(f"Failed to read Excel file: {e}")


def validate_excel_file(file_path: str) -> Dict[str, Any]:
    """
    Validate an Excel file and return structure information.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        Dictionary containing validation results and file structure info
    """
    if not OPENPYXL_AVAILABLE:
        return {
            'valid': False,
            'error': 'OpenPyXL is not available',
            'worksheets': [],
            'row_count': 0,
            'column_count': 0,
            'has_data': False
        }
    
    try:
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        worksheet_names = workbook.sheetnames
        
        # Get data from the first worksheet for analysis
        if worksheet_names:
            worksheet = workbook[worksheet_names[0]]
            
            # Count rows and columns with data
            row_count = 0
            column_count = 0
            has_data = False
            
            for row in worksheet.iter_rows():
                if any(cell.value is not None for cell in row):
                    row_count += 1
                    has_data = True
                    # Update column count based on the last non-empty cell
                    current_col_count = 0
                    for cell in row:
                        if cell.value is not None:
                            current_col_count = cell.column
                    column_count = max(column_count, current_col_count)
        else:
            row_count = 0
            column_count = 0
            has_data = False
        
        workbook.close()
        
        return {
            'valid': True,
            'error': None,
            'worksheets': worksheet_names,
            'row_count': row_count,
            'column_count': column_count,
            'has_data': has_data
        }
        
    except Exception as e:
        logger.error(f"Error validating Excel file {file_path}: {e}")
        return {
            'valid': False,
            'error': str(e),
            'worksheets': [],
            'row_count': 0,
            'column_count': 0,
            'has_data': False
        }


def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get comprehensive information about an Excel file.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        Dictionary containing file information
    """
    import os
    
    if not os.path.exists(file_path):
        return {'error': 'File does not exist'}
    
    if not OPENPYXL_AVAILABLE:
        return {'error': 'OpenPyXL is not available'}
    
    try:
        file_size = os.path.getsize(file_path)
        file_info = {
            'file_name': os.path.basename(file_path),
            'file_size': file_size,
            'file_size_mb': round(file_size / (1024 * 1024), 2)
        }
        
        # Add validation info
        validation_result = validate_excel_file(file_path)
        file_info.update(validation_result)
        
        return file_info
        
    except Exception as e:
        logger.error(f"Error getting Excel file info for {file_path}: {e}")
        return {'error': str(e)} 