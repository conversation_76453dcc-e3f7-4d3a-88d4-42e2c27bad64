"""LibreOffice Writer Adapter for JEdit2.

This module provides an adapter for LibreOffice Writer text document operations.
"""

import logging
from typing import Any, Dict, Optional

try:
    from com.sun.star.beans import PropertyValue
    UNO_AVAILABLE = True
except ImportError:
    UNO_AVAILABLE = False


class WriterAdapter:
    """Adapter for LibreOffice Writer text document operations."""
    
    def __init__(self, document):
        """Initialize Writer adapter.
        
        Args:
            document: LibreOffice Writer document object
        """
        self.document = document
        self.controller = document.getCurrentController()
        self.text = document.getText()
        self.logger = logging.getLogger(__name__)
    
    def get_text_content(self) -> str:
        """Get all text content from document."""
        try:
            return self.text.getString()
        except Exception as e:
            self.logger.error(f"Failed to get text content: {e}")
            return ""
    
    def set_text_content(self, content: str):
        """Set document text content."""
        try:
            self.text.setString(content)
        except Exception as e:
            self.logger.error(f"Failed to set text content: {e}")
    
    def insert_text(self, text: str, position: Optional[int] = None):
        """Insert text at specified position or cursor."""
        try:
            if position is not None:
                cursor = self.text.createTextCursor()
                cursor.goAbsolute(position, False)
                self.text.insertString(cursor, text, False)
            else:
                # Insert at current cursor position
                view_cursor = self.controller.getViewCursor()
                self.text.insertString(view_cursor, text, False)
                
        except Exception as e:
            self.logger.error(f"Failed to insert text: {e}")
    
    def apply_bold_formatting(self):
        """Apply bold formatting to current selection."""
        try:
            selection = self.controller.getSelection()
            if hasattr(selection, 'setPropertyValue'):
                current_weight = selection.getPropertyValue("CharWeight")
                new_weight = 700 if current_weight < 700 else 400
                selection.setPropertyValue("CharWeight", new_weight)
        except Exception as e:
            self.logger.error(f"Failed to apply bold formatting: {e}")
    
    def apply_italic_formatting(self):
        """Apply italic formatting to current selection."""
        try:
            selection = self.controller.getSelection()
            if hasattr(selection, 'setPropertyValue'):
                from com.sun.star.awt.FontSlant import NONE, ITALIC
                current_slant = selection.getPropertyValue("CharPosture")
                new_slant = ITALIC if current_slant == NONE else NONE
                selection.setPropertyValue("CharPosture", new_slant)
        except Exception as e:
            self.logger.error(f"Failed to apply italic formatting: {e}")
    
    def find_and_replace(self, search_text: str, replace_text: str) -> int:
        """Find and replace text in document."""
        try:
            # Create search descriptor
            search_desc = self.document.createSearchDescriptor()
            search_desc.setSearchString(search_text)
            search_desc.setReplaceString(replace_text)
            
            # Perform replace all
            return self.document.replaceAll(search_desc)
            
        except Exception as e:
            self.logger.error(f"Failed to find and replace: {e}")
            return 0 