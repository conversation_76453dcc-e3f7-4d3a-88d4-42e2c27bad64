<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-blue{fill: #005dba; opacity: 1;}.light-defaultgrey{fill: #212121; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>XSLTransform</title>
  <g id="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <path class="light-defaultgrey-10" d="M12.5,1.5v2h-2v-2Z" />
    <path class="light-blue" d="M1,6V3.5L1.5,3H4.293L3.146,1.854l.708-.708,2,2v.708l-2,2-.708-.708L4.293,4H2V6Z" />
    <g class="cls-1">
      <path class="light-defaultgrey" d="M14,5.5v2H13V6H10V7.5H9v-2L9.5,5H11V3.5h1V5h1.5Z" />
    </g>
    <path class="light-defaultgrey" d="M13,1.5,12.5,1h-2l-.5.5v2l.5.5h2l.5-.5ZM12,3H11V2h1ZM9.5,7l.5.5v1L9.5,9h-1L8,8.5v-1L8.5,7Zm5.5.5v1l-.5.5h-1L13,8.5v-1l.5-.5h1Z" />
    <path class="light-defaultgrey" d="M7,10v1H1V10ZM5,12H1v1H5ZM1,15H6V14H1Z" />
  </g>
</svg>
