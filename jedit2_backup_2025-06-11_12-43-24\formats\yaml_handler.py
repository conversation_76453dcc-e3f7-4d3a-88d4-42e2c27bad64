"""YAML file handler for JEdit2."""
import yaml
from typing import List, Any, Dict

def _flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """Flatten a nested dictionary into a single level dictionary.
    
    Args:
        d: Dictionary to flatten
        parent_key: Parent key for nested items
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items: List[tuple] = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(_flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            # Handle lists by converting to a more readable format
            if all(isinstance(item, dict) for item in v):
                # List of dicts - format as structured text
                formatted_items = []
                for item in v:
                    if 'bracket' in item and 'rate' in item:
                        # Tax bracket format
                        bracket = item['bracket']
                        rate = item['rate']
                        if len(bracket) == 2:
                            formatted_items.append(f"{bracket[0]}-{bracket[1]}: {rate*100}%")
                        else:
                            formatted_items.append(f"{bracket}: {rate*100}%")
                    else:
                        # Generic dict format
                        formatted_items.append(", ".join(f"{k}: {v}" for k, v in item.items()))
                items.append((new_key, "; ".join(formatted_items)))
            else:
                # Simple list - join with commas
                items.append((new_key, ", ".join(str(item) for item in v)))
        else:
            items.append((new_key, v))
    return dict(items)

def _unflatten_dict(d: Dict[str, Any], sep: str = '.') -> Dict[str, Any]:
    """Convert a flattened dictionary back to a nested structure.
    
    Args:
        d: Flattened dictionary
        sep: Separator used in keys
        
    Returns:
        Nested dictionary
    """
    result: Dict[str, Any] = {}
    for k, v in d.items():
        parts = k.split(sep)
        current = result
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        current[parts[-1]] = v
    return result

def parse_yaml(path: str) -> List[List[Any]]:
    """Parse a YAML file and return its contents as a list of rows for tabular display.

    Args:
        path: Path to the YAML file.
    Returns:
        List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be parsed.
    """
    with open(path, encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    # Handle dictionary of dictionaries (like state rates)
    if isinstance(data, dict) and all(isinstance(v, dict) for v in data.values()):
        rows = [["State", "Type", "Rate"]]
        for state, info in data.items():
            rows.append([
                state,
                str(info.get('type', '')),
                str(info.get('rate', ''))
            ])
        return rows
    
    # Flatten nested structure
    if isinstance(data, dict):
        flat_data = _flatten_dict(data)
        return [["Key", "Value"]] + [[k, str(v)] for k, v in flat_data.items()]
    
    # Handle list of dicts
    if isinstance(data, list) and all(isinstance(row, dict) for row in data):
        if not data:
            return [["No data"]]
        headers = list(data[0].keys())
        rows = [headers]
        for row in data:
            rows.append([str(row.get(h, "")) for h in headers])
        return rows
    
    # Handle list of lists
    if isinstance(data, list) and all(isinstance(row, list) for row in data):
        return data
    
    raise Exception("Unsupported YAML structure for tabular display.")

def save_yaml(path: str, data: List[List[Any]]) -> None:
    """Save data to a YAML file.

    Args:
        path: Path where to save the YAML file.
        data: List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be saved.
    """
    if not data:
        raise Exception("No data to save")
    
    # If the data is in state rates format
    if len(data[0]) == 3 and data[0] == ["State", "Type", "Rate"]:
        result = {}
        for row in data[1:]:
            state, type_val, rate = row
            result[state] = {
                'type': type_val,
                'rate': float(rate) if rate else 0.0
            }
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(result, f, default_flow_style=False)
        return
    
    # If the data is in key-value format (from flattened dict)
    if len(data[0]) == 2 and data[0] == ["Key", "Value"]:
        flat_dict = {row[0]: row[1] for row in data[1:]}
        nested_dict = _unflatten_dict(flat_dict)
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(nested_dict, f, default_flow_style=False)
        return
    
    # If the data is in table format (headers + rows)
    headers = data[0]
    rows = data[1:]
    if all(len(row) == len(headers) for row in rows):
        result = []
        for row in rows:
            result.append(dict(zip(headers, row)))
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(result, f, default_flow_style=False)
        return
    
    # If the data is a simple list of lists
    with open(path, 'w', encoding='utf-8') as f:
        yaml.safe_dump(data, f, default_flow_style=False) 