#!/usr/bin/env python3
"""
Semantic Ambiguity Detector for JEdit2 AI System

Expands and enhances the "new column X" fix to handle broader semantic
ambiguity patterns in natural language queries.
"""

import logging
import re
from dataclasses import dataclass
from typing import Dict, List, Tuple
from enum import Enum


class AmbiguityType(Enum):
    """Types of semantic ambiguity patterns."""

    REFERENCE_AMBIGUITY = "reference_ambiguity"  # "new column C" vs "column C"
    POSITIONAL_AMBIGUITY = "positional_ambiguity"  # "after A" vs "right of A"
    TEMPORAL_AMBIGUITY = "temporal_ambiguity"  # "then" vs sequence implications
    IMPLICIT_OPERATIONS = "implicit_operations"  # Missing intermediate steps
    SCOPE_AMBIGUITY = "scope_ambiguity"  # Which file/tab is referenced


@dataclass
class AmbiguityPattern:
    """Represents a detected ambiguity pattern."""

    pattern_type: AmbiguityType
    original_text: str
    ambiguous_phrases: List[str]
    clarified_text: str
    confidence: float  # 0.0 to 1.0
    risk_level: str  # "HIGH", "MEDIUM", "LOW"


@dataclass
class DetectionResult:
    """Result of ambiguity detection analysis."""

    query: str
    ambiguities_found: List[AmbiguityPattern]
    clarified_query: str
    requires_user_confirmation: bool
    confidence_score: float


class SemanticAmbiguityDetector:
    """
    Comprehensive semantic ambiguity detection and resolution system.

    Extends the original "new column X" fix to handle multiple types of
    semantic ambiguity in natural language queries.
    """

    def __init__(self):
        """Initialize the semantic ambiguity detector."""
        self.logger = logging.getLogger(__name__)

        # Initialize pattern matching rules
        self.reference_patterns = self._initialize_reference_patterns()
        self.positional_patterns = self._initialize_positional_patterns()
        self.temporal_patterns = self._initialize_temporal_patterns()
        self.implicit_patterns = self._initialize_implicit_patterns()

        # State tracking for context-aware detection
        self.last_operations = []
        self.current_file_context = None

    def _initialize_reference_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for reference ambiguity detection."""
        return {
            "new_column_reference": {
                "pattern": r"(?:new\s+column\s+|the\s+new\s+column\s+)([A-Z])",
                "description": "References to 'new column X' when X might be ambiguous",
                "examples": [
                    "copy from new column C",
                    "the new column B",
                    "new column data",
                ],
                "clarification_template": (
                    "Note: When referring to 'new column {column}', "
                    "use original column positions before any insertions for source columns"
                ),
            },
            "ambiguous_column_after_insert": {
                "pattern": r"insert.+(?:column|after).+(?:copy|move).+column\s+([A-Z])",
                "description": "Column references that may be affected by previous insertions",
                "examples": [
                    "insert column after A then copy column C",
                    "add column then move column B data",
                ],
                "clarification_template": (
                    "Note: Column {column} refers to the original position "
                    "before the insert operation"
                ),
            },
            "shifted_reference": {
                "pattern": r"(?:delete|remove).+column.+(?:copy|move|sort).+column\s+([A-Z])",
                "description": "Column references after deletion that may have shifted",
                "examples": [
                    "delete column B then sort column C",
                    "remove column A then copy column B",
                ],
                "clarification_template": (
                    "Note: Column {column} position may have shifted due to deletion"
                ),
            },
        }

    def _initialize_positional_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for positional ambiguity detection."""
        return {
            "relative_position_variants": {
                "pattern": r"(?:after|right\s+of|next\s+to|beside|following)\s+column\s+([A-Z])",
                "description": "Different ways to express relative column positions",
                "standardization": "after column {column}",
                "examples": [
                    "right of column A",
                    "next to column B",
                    "beside column C",
                ],
            },
            "directional_ambiguity": {
                "pattern": r"(?:left|right)\s+(?:side|of)?.*column",
                "description": "Left/right directional references that may be unclear",
                "examples": ["left side of column A", "to the right of column B"],
            },
            "ordinal_references": {
                "pattern": r"(?:first|second|third|last|final)\s+column",
                "description": "Ordinal column references that may change with operations",
                "examples": ["first column", "last column", "second column"],
            },
        }

    def _initialize_temporal_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for temporal ambiguity detection."""
        return {
            "sequence_connectors": {
                "pattern": r"\b(?:then|and\s+then|after\s+that|next)\b",
                "description": "Temporal connectors that imply operation sequence",
                "implications": "Operations must be executed in strict order",
            },
            "simultaneous_vs_sequential": {
                "pattern": r"\b(?:and|while|during|as)\b",
                "description": "Connectors that may imply simultaneous vs sequential operations",
                "clarification_needed": True,
            },
            "conditional_operations": {
                "pattern": r"\b(?:if|when|unless|provided)\b",
                "description": "Conditional operations that require state checking",
                "examples": ["if column A exists", "when the file is open"],
            },
        }

    def _initialize_implicit_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for implicit operation detection."""
        return {
            "missing_file_operations": {
                "pattern": r"(?:copy|move|sort|filter).+(?:to|from|in).+(?:csv|json|xlsx|txt)",
                "description": "Operations on files that may not be open",
                "implicit_operation": "OPEN_FILE",
                "examples": ["copy data to results.csv", "sort data in data.xlsx"],
            },
            "missing_column_creation": {
                "pattern": r"copy.+(?:to|into).+(?:new\s+column|column\s+[A-Z])",
                "description": "Copy operations that may require creating target columns",
                "implicit_operation": "INSERT_COLUMN",
                "examples": ["copy to new column", "copy data into column E"],
            },
            "missing_formatting_context": {
                "pattern": r"make.+(?:bold|italic|colored)",
                "description": "Formatting operations that need target specification",
                "implicit_clarification": "Specify target range or column",
                "examples": ["make bold", "make it red"],
            },
        }

    def analyze_query(self, query: str) -> DetectionResult:
        """
        Comprehensive analysis of a query for semantic ambiguities.

        Args:
            query: The natural language query to analyze

        Returns:
            DetectionResult with detected ambiguities and clarifications
        """
        ambiguities = []
        clarified_query = query

        # Check for reference ambiguities
        ref_ambiguities = self._detect_reference_ambiguities(query)
        ambiguities.extend(ref_ambiguities)

        # Check for positional ambiguities
        pos_ambiguities = self._detect_positional_ambiguities(query)
        ambiguities.extend(pos_ambiguities)

        # Check for temporal ambiguities
        temp_ambiguities = self._detect_temporal_ambiguities(query)
        ambiguities.extend(temp_ambiguities)

        # Check for implicit operations
        impl_ambiguities = self._detect_implicit_operations(query)
        ambiguities.extend(impl_ambiguities)

        # Generate clarified query
        if ambiguities:
            clarified_query = self._generate_clarified_query(query, ambiguities)

        # Calculate overall confidence
        confidence_score = self._calculate_confidence_score(ambiguities)

        # Determine if user confirmation is needed
        requires_confirmation = any(
            amb.risk_level == "HIGH" or amb.confidence < 0.7 for amb in ambiguities
        )

        return DetectionResult(
            query=query,
            ambiguities_found=ambiguities,
            clarified_query=clarified_query,
            requires_user_confirmation=requires_confirmation,
            confidence_score=confidence_score,
        )

    def _detect_reference_ambiguities(self, query: str) -> List[AmbiguityPattern]:
        """Detect reference-based ambiguities in the query."""
        ambiguities = []

        for pattern_name, pattern_info in self.reference_patterns.items():
            matches = re.finditer(pattern_info["pattern"], query, re.IGNORECASE)

            for match in matches:
                column = match.group(1) if match.groups() else None

                # Determine risk level based on context
                risk_level = self._assess_reference_risk(query, pattern_name, column)

                # Generate clarification
                clarification = pattern_info["clarification_template"].format(
                    column=column or "X"
                )

                ambiguity = AmbiguityPattern(
                    pattern_type=AmbiguityType.REFERENCE_AMBIGUITY,
                    original_text=match.group(0),
                    ambiguous_phrases=[match.group(0)],
                    clarified_text=clarification,
                    confidence=0.8,  # High confidence for reference patterns
                    risk_level=risk_level,
                )
                ambiguities.append(ambiguity)

        return ambiguities

    def _detect_positional_ambiguities(self, query: str) -> List[AmbiguityPattern]:
        """Detect positional ambiguities in the query."""
        ambiguities = []

        for pattern_name, pattern_info in self.positional_patterns.items():
            matches = re.finditer(pattern_info["pattern"], query, re.IGNORECASE)

            for match in matches:
                column = match.group(1) if match.groups() else None

                # Generate standardized form if available
                clarified_text = pattern_info.get("standardization", "").format(
                    column=column or "X"
                )

                ambiguity = AmbiguityPattern(
                    pattern_type=AmbiguityType.POSITIONAL_AMBIGUITY,
                    original_text=match.group(0),
                    ambiguous_phrases=[match.group(0)],
                    clarified_text=clarified_text,
                    confidence=0.6,  # Medium confidence for positional
                    risk_level="MEDIUM",
                )
                ambiguities.append(ambiguity)

        return ambiguities

    def _detect_temporal_ambiguities(self, query: str) -> List[AmbiguityPattern]:
        """Detect temporal ambiguities in the query."""
        ambiguities = []

        # Count sequence indicators
        then_count = len(re.findall(r"\bthen\b", query, re.IGNORECASE))
        and_count = len(re.findall(r"\band\b", query, re.IGNORECASE))

        if then_count > 0 and and_count > 0:
            # Mixed temporal connectors - potential ambiguity
            ambiguity = AmbiguityPattern(
                pattern_type=AmbiguityType.TEMPORAL_AMBIGUITY,
                original_text=query,
                ambiguous_phrases=["mixed temporal connectors"],
                clarified_text="Operations will be executed in left-to-right sequence",
                confidence=0.7,
                risk_level="MEDIUM",
            )
            ambiguities.append(ambiguity)

        return ambiguities

    def _detect_implicit_operations(self, query: str) -> List[AmbiguityPattern]:
        """Detect implicit operations that may be missing."""
        ambiguities = []

        for pattern_name, pattern_info in self.implicit_patterns.items():
            if re.search(pattern_info["pattern"], query, re.IGNORECASE):
                implicit_op = pattern_info.get("implicit_operation", "")
                clarification = pattern_info.get("implicit_clarification", "")

                if implicit_op:
                    clarification = f"Note: May require {implicit_op} operation first"

                ambiguity = AmbiguityPattern(
                    pattern_type=AmbiguityType.IMPLICIT_OPERATIONS,
                    original_text=query,
                    ambiguous_phrases=[pattern_name],
                    clarified_text=clarification,
                    confidence=0.5,  # Lower confidence for implicit detection
                    risk_level="MEDIUM",
                )
                ambiguities.append(ambiguity)

        return ambiguities

    def _assess_reference_risk(self, query: str, pattern_name: str, column: str) -> str:
        """Assess the risk level of a reference ambiguity."""
        # High risk patterns
        if "new_column_reference" in pattern_name:
            return "HIGH"

        # Check if query contains structural operations
        structural_ops = ["insert", "delete", "remove", "add column"]
        if any(op in query.lower() for op in structural_ops):
            return "HIGH"

        return "MEDIUM"

    def _generate_clarified_query(
        self, query: str, ambiguities: List[AmbiguityPattern]
    ) -> str:
        """Generate a clarified version of the query."""
        clarified = query

        # Add clarifications as notes
        clarifications = []
        for ambiguity in ambiguities:
            if (
                ambiguity.clarified_text
                and ambiguity.clarified_text not in clarifications
            ):
                clarifications.append(ambiguity.clarified_text)

        if clarifications:
            clarified += " (" + "; ".join(clarifications) + ")"

        return clarified

    def _calculate_confidence_score(self, ambiguities: List[AmbiguityPattern]) -> float:
        """Calculate overall confidence score for the detection."""
        if not ambiguities:
            return 1.0  # High confidence if no ambiguities

        # Weighted average of individual confidences
        total_weight = 0
        weighted_sum = 0

        for ambiguity in ambiguities:
            weight = 1.0
            if ambiguity.risk_level == "HIGH":
                weight = 2.0
            elif ambiguity.risk_level == "LOW":
                weight = 0.5

            weighted_sum += ambiguity.confidence * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.5

    def enhance_query_preprocessing(self, query: str) -> str:
        """
        Enhanced version of the original context-aware preprocessing.

        This method provides backward compatibility while adding new capabilities.

        Args:
            query: Original natural language query

        Returns:
            Enhanced query with semantic clarifications
        """
        result = self.analyze_query(query)

        if result.ambiguities_found:
            self.logger.info(
                f"Detected {len(result.ambiguities_found)} ambiguities in query"
            )
            for ambiguity in result.ambiguities_found:
                self.logger.debug(
                    f"Ambiguity: {ambiguity.pattern_type.value} - {ambiguity.original_text}"
                )

        return result.clarified_query

    def generate_test_cases(self, num_cases: int = 20) -> List[Tuple[str, str]]:
        """
        Generate test cases for semantic ambiguity detection.

        Args:
            num_cases: Number of test cases to generate

        Returns:
            List of (original_query, expected_clarification) tuples
        """
        test_cases = [
            # Reference ambiguity test cases
            (
                "insert column after A then copy column C to new column",
                "Note: Column C refers to the original position before the insert operation",
            ),
            (
                "add new column then copy the new column C data",
                "Note: When referring to 'new column C', use original column positions",
            ),
            (
                "delete column B then sort column C ascending",
                "Note: Column C position may have shifted due to deletion",
            ),
            # Positional ambiguity test cases
            ("insert column to the right of A", "Standardized: after column A"),
            ("add column next to B", "Standardized: after column B"),
            # Temporal ambiguity test cases
            (
                "open file and then sort column A and filter column B",
                "Operations will be executed in left-to-right sequence",
            ),
            # Implicit operation test cases
            ("copy data to results.csv", "Note: May require OPEN_FILE operation first"),
            ("make column bold", "Note: Specify target range or column"),
            # Complex combination cases
            (
                "open price.json then add column after A and copy new column C into it",
                "Multiple clarifications needed",
            ),
            (
                "insert two columns then copy the first new column data to the second",
                "Complex structural state tracking required",
            ),
        ]

        return test_cases[:num_cases]


def main():
    """Main function for testing the SemanticAmbiguityDetector."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    detector = SemanticAmbiguityDetector()

    print("🔍 Semantic Ambiguity Detector - Test Mode")
    print("=" * 50)

    # Test with sample queries
    test_queries = [
        "insert column after A then copy column C to the new column",
        "open price.json then add column after A and copy new column C into it",
        "delete column B then sort column C and filter column D",
        "copy data to results.csv and make column A bold",
        "add column to the right of B then copy the first column data",
    ]

    for i, query in enumerate(test_queries, 1):
        print(f'\n{i}. Testing: "{query}"')
        result = detector.analyze_query(query)

        print(f"   Ambiguities found: {len(result.ambiguities_found)}")
        for amb in result.ambiguities_found:
            print(f"   - {amb.pattern_type.value}: {amb.original_text}")
            print(f"     Risk: {amb.risk_level}, Confidence: {amb.confidence:.2f}")

        print(f'   Clarified: "{result.clarified_query}"')
        print(f"   Confidence Score: {result.confidence_score:.2f}")
        print(f"   Needs Confirmation: {result.requires_user_confirmation}")

    print("\n✅ SemanticAmbiguityDetector ready for deployment")


if __name__ == "__main__":
    main()
