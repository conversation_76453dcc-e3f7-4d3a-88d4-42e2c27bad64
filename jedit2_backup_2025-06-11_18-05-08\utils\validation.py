"""Data validation for JEdit2.

This module provides data validation functionality for JEdit2.
"""

import re
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from enum import Enum
from dataclasses import dataclass


class ValidationType(Enum):
    """Validation types."""
    TYPE = "type"
    RANGE = "range"
    FORMAT = "format"
    CUSTOM = "custom"


class DataType(Enum):
    """Data types."""
    TEXT = "text"
    NUMBER = "number"
    DECIMAL = "decimal"
    INTEGER = "integer"
    DATE = "date"
    TIME = "time"
    DATETIME = "datetime"
    BOOLEAN = "boolean"
    EMAIL = "email"
    URL = "url"
    PHONE = "phone"
    CUSTOM = "custom"


@dataclass
class ValidationRule:
    """Validation rule."""
    name: str
    type: ValidationType
    data_type: DataType
    required: bool = False
    min_value: Optional[Union[int, float, Decimal, datetime]] = None
    max_value: Optional[Union[int, float, Decimal, datetime]] = None
    pattern: Optional[str] = None
    custom_validator: Optional[Callable[[Any], Tuple[bool, str]]] = None
    error_message: Optional[str] = None
    depends_on: Optional[List[str]] = None
    group: Optional[str] = None


class ValidationError(Exception):
    """Validation error."""
    
    def __init__(
        self,
        message: str,
        rule: ValidationRule,
        value: Any,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Initialize validation error.
        
        Args:
            message: Error message
            rule: Validation rule
            value: Invalid value
            context: Additional context
        """
        super().__init__(message)
        self.rule = rule
        self.value = value
        self.context = context or {}


class DataValidator:
    """Data validator for JEdit2."""
    
    def __init__(self) -> None:
        """Initialize the data validator."""
        self.rules: Dict[str, ValidationRule] = {}
        self.groups: Dict[str, Set[str]] = {}
        self.dependencies: Dict[str, Set[str]] = {}
    
    def add_rule(self, rule: ValidationRule) -> None:
        """Add a validation rule.
        
        Args:
            rule: Validation rule to add
        """
        self.rules[rule.name] = rule
        
        # Add to group if specified
        if rule.group:
            if rule.group not in self.groups:
                self.groups[rule.group] = set()
            self.groups[rule.group].add(rule.name)
        
        # Add dependencies if specified
        if rule.depends_on:
            for dep in rule.depends_on:
                if dep not in self.dependencies:
                    self.dependencies[dep] = set()
                self.dependencies[dep].add(rule.name)
    
    def remove_rule(self, name: str) -> None:
        """Remove a validation rule.
        
        Args:
            name: Name of rule to remove
        """
        if name in self.rules:
            rule = self.rules[name]
            
            # Remove from group
            if rule.group and rule.group in self.groups:
                self.groups[rule.group].discard(name)
                if not self.groups[rule.group]:
                    del self.groups[rule.group]
            
            # Remove dependencies
            if rule.depends_on:
                for dep in rule.depends_on:
                    if dep in self.dependencies:
                        self.dependencies[dep].discard(name)
                        if not self.dependencies[dep]:
                            del self.dependencies[dep]
            
            # Remove rule
            del self.rules[name]
    
    def validate(
        self,
        data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> List[ValidationError]:
        """Validate data against rules.
        
        Args:
            data: Data to validate
            context: Additional context
            
        Returns:
            List of validation errors
        """
        errors: List[ValidationError] = []
        context = context or {}
        
        # Validate each rule
        for name, rule in self.rules.items():
            # Skip if dependencies not met
            if rule.depends_on and not all(
                dep in data and data[dep] is not None
                for dep in rule.depends_on
            ):
                continue
            
            # Get value
            value = data.get(name)
            
            # Check required
            if rule.required and value is None:
                errors.append(ValidationError(
                    f"{name} is required",
                    rule,
                    value,
                    context
                ))
                continue
            
            # Skip if not required and value is None
            if not rule.required and value is None:
                continue
            
            # Validate value
            try:
                if rule.type == ValidationType.TYPE:
                    self._validate_type(rule, value, context)
                elif rule.type == ValidationType.RANGE:
                    self._validate_range(rule, value, context)
                elif rule.type == ValidationType.FORMAT:
                    self._validate_format(rule, value, context)
                elif rule.type == ValidationType.CUSTOM:
                    self._validate_custom(rule, value, context)
            except ValidationError as e:
                errors.append(e)
        
        return errors
    
    def _validate_type(
        self,
        rule: ValidationRule,
        value: Any,
        context: Dict[str, Any]
    ) -> None:
        """Validate data type.
        
        Args:
            rule: Validation rule
            value: Value to validate
            context: Additional context
            
        Raises:
            ValidationError: If validation fails
        """
        if rule.data_type == DataType.TEXT:
            if not isinstance(value, str):
                raise ValidationError(
                    f"{rule.name} must be text",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.NUMBER:
            try:
                float(value)
            except (TypeError, ValueError):
                raise ValidationError(
                    f"{rule.name} must be a number",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.DECIMAL:
            try:
                Decimal(str(value))
            except (TypeError, ValueError, InvalidOperation):
                raise ValidationError(
                    f"{rule.name} must be a decimal number",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.INTEGER:
            try:
                int(value)
            except (TypeError, ValueError):
                raise ValidationError(
                    f"{rule.name} must be an integer",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.DATE:
            try:
                if isinstance(value, str):
                    datetime.strptime(value, "%Y-%m-%d")
                elif not isinstance(value, datetime):
                    raise ValidationError(
                        f"{rule.name} must be a date",
                        rule,
                        value,
                        context
                    )
            except ValueError:
                raise ValidationError(
                    f"{rule.name} must be a valid date (YYYY-MM-DD)",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.TIME:
            try:
                if isinstance(value, str):
                    datetime.strptime(value, "%H:%M:%S")
                elif not isinstance(value, datetime):
                    raise ValidationError(
                        f"{rule.name} must be a time",
                        rule,
                        value,
                        context
                    )
            except ValueError:
                raise ValidationError(
                    f"{rule.name} must be a valid time (HH:MM:SS)",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.DATETIME:
            try:
                if isinstance(value, str):
                    datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                elif not isinstance(value, datetime):
                    raise ValidationError(
                        f"{rule.name} must be a datetime",
                        rule,
                        value,
                        context
                    )
            except ValueError:
                raise ValidationError(
                    f"{rule.name} must be a valid datetime (YYYY-MM-DD HH:MM:SS)",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.BOOLEAN:
            if not isinstance(value, bool):
                raise ValidationError(
                    f"{rule.name} must be a boolean",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.EMAIL:
            if not isinstance(value, str) or not re.match(
                r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
                value
            ):
                raise ValidationError(
                    f"{rule.name} must be a valid email address",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.URL:
            if not isinstance(value, str) or not re.match(
                r"^https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+$",
                value
            ):
                raise ValidationError(
                    f"{rule.name} must be a valid URL",
                    rule,
                    value,
                    context
                )
        elif rule.data_type == DataType.PHONE:
            if not isinstance(value, str) or not re.match(
                r"^\+?1?\d{9,15}$",
                value
            ):
                raise ValidationError(
                    f"{rule.name} must be a valid phone number",
                    rule,
                    value,
                    context
                )
    
    def _validate_range(
        self,
        rule: ValidationRule,
        value: Any,
        context: Dict[str, Any]
    ) -> None:
        """Validate value range.
        
        Args:
            rule: Validation rule
            value: Value to validate
            context: Additional context
            
        Raises:
            ValidationError: If validation fails
        """
        # First validate type
        self._validate_type(rule, value, context)
        
        # Convert value to appropriate type
        if rule.data_type == DataType.NUMBER:
            value = float(value)
        elif rule.data_type == DataType.DECIMAL:
            value = Decimal(str(value))
        elif rule.data_type == DataType.INTEGER:
            value = int(value)
        elif rule.data_type in (DataType.DATE, DataType.TIME, DataType.DATETIME):
            if isinstance(value, str):
                if rule.data_type == DataType.DATE:
                    value = datetime.strptime(value, "%Y-%m-%d")
                elif rule.data_type == DataType.TIME:
                    value = datetime.strptime(value, "%H:%M:%S")
                else:
                    value = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
        
        # Check range
        if rule.min_value is not None and value < rule.min_value:
            raise ValidationError(
                f"{rule.name} must be greater than or equal to {rule.min_value}",
                rule,
                value,
                context
            )
        
        if rule.max_value is not None and value > rule.max_value:
            raise ValidationError(
                f"{rule.name} must be less than or equal to {rule.max_value}",
                rule,
                value,
                context
            )
    
    def _validate_format(
        self,
        rule: ValidationRule,
        value: Any,
        context: Dict[str, Any]
    ) -> None:
        """Validate value format.
        
        Args:
            rule: Validation rule
            value: Value to validate
            context: Additional context
            
        Raises:
            ValidationError: If validation fails
        """
        # First validate type
        self._validate_type(rule, value, context)
        
        # Check format
        if rule.pattern and not re.match(rule.pattern, str(value)):
            raise ValidationError(
                f"{rule.name} must match pattern {rule.pattern}",
                rule,
                value,
                context
            )
    
    def _validate_custom(
        self,
        rule: ValidationRule,
        value: Any,
        context: Dict[str, Any]
    ) -> None:
        """Validate using custom validator.
        
        Args:
            rule: Validation rule
            value: Value to validate
            context: Additional context
            
        Raises:
            ValidationError: If validation fails
        """
        if rule.custom_validator:
            is_valid, message = rule.custom_validator(value)
            if not is_valid:
                raise ValidationError(
                    message or f"{rule.name} is invalid",
                    rule,
                    value,
                    context
                )
    
    def get_rule(self, name: str) -> Optional[ValidationRule]:
        """Get a validation rule.
        
        Args:
            name: Name of rule to get
            
        Returns:
            Validation rule if found, None otherwise
        """
        return self.rules.get(name)
    
    def get_rules_by_group(self, group: str) -> List[ValidationRule]:
        """Get validation rules by group.
        
        Args:
            group: Group name
            
        Returns:
            List of validation rules in group
        """
        if group not in self.groups:
            return []
        
        return [
            self.rules[name]
            for name in self.groups[group]
            if name in self.rules
        ]
    
    def get_dependent_rules(self, name: str) -> List[ValidationRule]:
        """Get rules that depend on a rule.
        
        Args:
            name: Name of rule
            
        Returns:
            List of dependent validation rules
        """
        if name not in self.dependencies:
            return []
        
        return [
            self.rules[dep]
            for dep in self.dependencies[name]
            if dep in self.rules
        ]
    
    def clear_rules(self) -> None:
        """Clear all validation rules."""
        self.rules.clear()
        self.groups.clear()
        self.dependencies.clear() 