"""Validation dialog for JEdit2.

This module provides a dialog for managing validation rules.
"""

import re
from decimal import Decimal, InvalidOperation
import wx
import wx.grid
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from .validation import (
    DataValidator,
    ValidationRule,
    ValidationType,
    DataType,
    ValidationError
)


class ValidationDialog(wx.Dialog):
    """Dialog for managing validation rules."""
    
    def __init__(self, parent: wx.Window, validator: DataValidator) -> None:
        """Initialize the validation dialog.
        
        Args:
            parent: Parent window
            validator: Data validator
        """
        super().__init__(
            parent,
            title="Validation Rules",
            size=(800, 600),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.validator = validator
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create rule grid
        self.grid = self._create_rule_grid()
        main_sizer.Add(self.grid, 1, wx.EXPAND | wx.ALL, 5)
        
        # Create button sizer
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        add_button = wx.Button(self, label="Add Rule")
        add_button.Bind(wx.EVT_BUTTON, self._on_add)
        button_sizer.Add(add_button, 0, wx.ALL, 5)
        
        edit_button = wx.Button(self, label="Edit Rule")
        edit_button.Bind(wx.EVT_BUTTON, self._on_edit)
        button_sizer.Add(edit_button, 0, wx.ALL, 5)
        
        remove_button = wx.Button(self, label="Remove Rule")
        remove_button.Bind(wx.EVT_BUTTON, self._on_remove)
        button_sizer.Add(remove_button, 0, wx.ALL, 5)
        
        button_sizer.AddStretchSpacer()
        
        close_button = wx.Button(self, label="Close")
        close_button.Bind(wx.EVT_BUTTON, self._on_close)
        button_sizer.Add(close_button, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        # Set main sizer
        self.SetSizer(main_sizer)
    
    def _create_rule_grid(self) -> wx.grid.Grid:
        """Create the rule grid.
        
        Returns:
            Rule grid
        """
        grid = wx.grid.Grid(self)
        
        # Set up grid
        grid.CreateGrid(0, 7)
        grid.SetColLabelValue(0, "Name")
        grid.SetColLabelValue(1, "Type")
        grid.SetColLabelValue(2, "Data Type")
        grid.SetColLabelValue(3, "Required")
        grid.SetColLabelValue(4, "Min Value")
        grid.SetColLabelValue(5, "Max Value")
        grid.SetColLabelValue(6, "Pattern")
        
        # Set column sizes
        grid.SetColSize(0, 150)  # Name
        grid.SetColSize(1, 100)  # Type
        grid.SetColSize(2, 100)  # Data Type
        grid.SetColSize(3, 80)   # Required
        grid.SetColSize(4, 100)  # Min Value
        grid.SetColSize(5, 100)  # Max Value
        grid.SetColSize(6, 150)  # Pattern
        
        # Enable sorting (if available in this wxPython version)
        try:
            grid.EnableSorting(True)
        except AttributeError:
            pass  # EnableSorting not available in this wxPython version
        
        # Populate grid
        self._populate_grid(grid)
        
        return grid
    
    def _populate_grid(self, grid: wx.grid.Grid) -> None:
        """Populate the grid with validation rules.
        
        Args:
            grid: Grid to populate
        """
        # Clear existing rows
        if grid.GetNumberRows() > 0:
            grid.DeleteRows(0, grid.GetNumberRows())
        
        # Add rules
        for rule in self.validator.rules.values():
            row = grid.GetNumberRows()
            grid.AppendRows(1)
            
            # Set cell values
            grid.SetCellValue(row, 0, rule.name)
            grid.SetCellValue(row, 1, rule.type.value)
            grid.SetCellValue(row, 2, rule.data_type.value)
            grid.SetCellValue(row, 3, str(rule.required))
            
            if rule.min_value is not None:
                grid.SetCellValue(row, 4, str(rule.min_value))
            
            if rule.max_value is not None:
                grid.SetCellValue(row, 5, str(rule.max_value))
            
            if rule.pattern:
                grid.SetCellValue(row, 6, rule.pattern)
    
    def _on_add(self, event: wx.CommandEvent) -> None:
        """Handle add button click.
        
        Args:
            event: Command event
        """
        dialog = RuleDialog(self)
        if dialog.ShowModal() == wx.ID_OK:
            rule = dialog.get_rule()
            self.validator.add_rule(rule)
            self._populate_grid(self.grid)
        dialog.Destroy()
    
    def _on_edit(self, event: wx.CommandEvent) -> None:
        """Handle edit button click.
        
        Args:
            event: Command event
        """
        # Get selected row
        row = self.grid.GetGridCursorRow()
        if row < 0:
            wx.MessageBox(
                "Please select a rule to edit.",
                "No Selection",
                wx.OK | wx.ICON_INFORMATION
            )
            return
        
        # Get rule name
        name = self.grid.GetCellValue(row, 0)
        rule = self.validator.get_rule(name)
        if not rule:
            return
        
        # Show edit dialog
        dialog = RuleDialog(self, rule)
        if dialog.ShowModal() == wx.ID_OK:
            # Remove old rule
            self.validator.remove_rule(name)
            
            # Add new rule
            new_rule = dialog.get_rule()
            self.validator.add_rule(new_rule)
            
            # Update grid
            self._populate_grid(self.grid)
        
        dialog.Destroy()
    
    def _on_remove(self, event: wx.CommandEvent) -> None:
        """Handle remove button click.
        
        Args:
            event: Command event
        """
        # Get selected row
        row = self.grid.GetGridCursorRow()
        if row < 0:
            wx.MessageBox(
                "Please select a rule to remove.",
                "No Selection",
                wx.OK | wx.ICON_INFORMATION
            )
            return
        
        # Get rule name
        name = self.grid.GetCellValue(row, 0)
        
        # Confirm removal
        result = wx.MessageBox(
            f"Are you sure you want to remove rule '{name}'?",
            "Confirm Removal",
            wx.YES_NO | wx.ICON_QUESTION
        )
        
        if result == wx.YES:
            self.validator.remove_rule(name)
            self._populate_grid(self.grid)
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button click.
        
        Args:
            event: Command event
        """
        self.EndModal(wx.ID_CLOSE)


class RuleDialog(wx.Dialog):
    """Dialog for editing validation rules."""
    
    def __init__(
        self,
        parent: wx.Window,
        rule: Optional[ValidationRule] = None
    ) -> None:
        """Initialize the rule dialog.
        
        Args:
            parent: Parent window
            rule: Rule to edit, or None for new rule
        """
        super().__init__(
            parent,
            title="Edit Rule" if rule else "Add Rule",
            size=(400, 500),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.rule = rule
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create form
        form_sizer = wx.FlexGridSizer(8, 2, 5, 5)
        form_sizer.AddGrowableCol(1)
        
        # Name
        name_label = wx.StaticText(self, label="Name:")
        self.name_text = wx.TextCtrl(self)
        if self.rule:
            self.name_text.SetValue(self.rule.name)
        form_sizer.Add(name_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.name_text, 0, wx.EXPAND)
        
        # Type
        type_label = wx.StaticText(self, label="Type:")
        self.type_choice = wx.Choice(
            self,
            choices=[t.value for t in ValidationType]
        )
        if self.rule:
            self.type_choice.SetSelection(
                list(ValidationType).index(self.rule.type)
            )
        else:
            self.type_choice.SetSelection(0)
        self.type_choice.Bind(wx.EVT_CHOICE, self._on_type_change)
        form_sizer.Add(type_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.type_choice, 0, wx.EXPAND)
        
        # Data Type
        data_type_label = wx.StaticText(self, label="Data Type:")
        self.data_type_choice = wx.Choice(
            self,
            choices=[t.value for t in DataType]
        )
        if self.rule:
            self.data_type_choice.SetSelection(
                list(DataType).index(self.rule.data_type)
            )
        else:
            self.data_type_choice.SetSelection(0)
        self.data_type_choice.Bind(wx.EVT_CHOICE, self._on_data_type_change)
        form_sizer.Add(data_type_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.data_type_choice, 0, wx.EXPAND)
        
        # Required
        required_label = wx.StaticText(self, label="Required:")
        self.required_check = wx.CheckBox(self)
        if self.rule:
            self.required_check.SetValue(self.rule.required)
        form_sizer.Add(required_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.required_check, 0, wx.EXPAND)
        
        # Min Value
        min_label = wx.StaticText(self, label="Min Value:")
        self.min_text = wx.TextCtrl(self)
        if self.rule and self.rule.min_value is not None:
            self.min_text.SetValue(str(self.rule.min_value))
        form_sizer.Add(min_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.min_text, 0, wx.EXPAND)
        
        # Max Value
        max_label = wx.StaticText(self, label="Max Value:")
        self.max_text = wx.TextCtrl(self)
        if self.rule and self.rule.max_value is not None:
            self.max_text.SetValue(str(self.rule.max_value))
        form_sizer.Add(max_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.max_text, 0, wx.EXPAND)
        
        # Pattern
        pattern_label = wx.StaticText(self, label="Pattern:")
        self.pattern_text = wx.TextCtrl(self)
        if self.rule and self.rule.pattern:
            self.pattern_text.SetValue(self.rule.pattern)
        form_sizer.Add(pattern_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.pattern_text, 0, wx.EXPAND)
        
        # Error Message
        error_label = wx.StaticText(self, label="Error Message:")
        self.error_text = wx.TextCtrl(self)
        if self.rule and self.rule.error_message:
            self.error_text.SetValue(self.rule.error_message)
        form_sizer.Add(error_label, 0, wx.ALIGN_CENTER_VERTICAL)
        form_sizer.Add(self.error_text, 0, wx.EXPAND)
        
        main_sizer.Add(form_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        # Create button sizer
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        ok_button = wx.Button(self, wx.ID_OK, "OK")
        ok_button.Bind(wx.EVT_BUTTON, self._on_ok)
        button_sizer.Add(ok_button, 0, wx.ALL, 5)
        
        cancel_button = wx.Button(self, wx.ID_CANCEL, "Cancel")
        button_sizer.Add(cancel_button, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.ALIGN_RIGHT | wx.ALL, 5)
        
        # Set main sizer
        self.SetSizer(main_sizer)
        
        # Update UI
        self._update_ui()
    
    def _on_type_change(self, event: wx.CommandEvent) -> None:
        """Handle type change.
        
        Args:
            event: Command event
        """
        self._update_ui()
    
    def _on_data_type_change(self, event: wx.CommandEvent) -> None:
        """Handle data type change.
        
        Args:
            event: Command event
        """
        self._update_ui()
    
    def _update_ui(self) -> None:
        """Update the user interface."""
        # Get selected type and data type
        type_index = self.type_choice.GetSelection()
        data_type_index = self.data_type_choice.GetSelection()
        
        if type_index < 0 or data_type_index < 0:
            return
        
        validation_type = list(ValidationType)[type_index]
        data_type = list(DataType)[data_type_index]
        
        # Update min/max value fields
        has_range = (
            validation_type == ValidationType.RANGE and
            data_type in (
                DataType.NUMBER,
                DataType.DECIMAL,
                DataType.INTEGER,
                DataType.DATE,
                DataType.TIME,
                DataType.DATETIME
            )
        )
        
        self.min_text.Enable(has_range)
        self.max_text.Enable(has_range)
        
        # Update pattern field
        has_pattern = (
            validation_type == ValidationType.FORMAT and
            data_type in (
                DataType.TEXT,
                DataType.EMAIL,
                DataType.URL,
                DataType.PHONE
            )
        )
        
        self.pattern_text.Enable(has_pattern)
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click.
        
        Args:
            event: Command event
        """
        # Validate input
        name = self.name_text.GetValue().strip()
        if not name:
            wx.MessageBox(
                "Please enter a rule name.",
                "Validation Error",
                wx.OK | wx.ICON_ERROR
            )
            return
        
        # Get selected type and data type
        type_index = self.type_choice.GetSelection()
        data_type_index = self.data_type_choice.GetSelection()
        
        if type_index < 0 or data_type_index < 0:
            wx.MessageBox(
                "Please select a type and data type.",
                "Validation Error",
                wx.OK | wx.ICON_ERROR
            )
            return
        
        validation_type = list(ValidationType)[type_index]
        data_type = list(DataType)[data_type_index]
        
        # Get min/max values
        min_value = None
        max_value = None
        
        if validation_type == ValidationType.RANGE:
            min_text = self.min_text.GetValue().strip()
            max_text = self.max_text.GetValue().strip()
            
            if min_text:
                try:
                    if data_type == DataType.NUMBER:
                        min_value = float(min_text)
                    elif data_type == DataType.DECIMAL:
                        min_value = Decimal(min_text)
                    elif data_type == DataType.INTEGER:
                        min_value = int(min_text)
                    elif data_type in (DataType.DATE, DataType.TIME, DataType.DATETIME):
                        if data_type == DataType.DATE:
                            min_value = datetime.strptime(min_text, "%Y-%m-%d")
                        elif data_type == DataType.TIME:
                            min_value = datetime.strptime(min_text, "%H:%M:%S")
                        else:
                            min_value = datetime.strptime(min_text, "%Y-%m-%d %H:%M:%S")
                except (ValueError, InvalidOperation):
                    wx.MessageBox(
                        f"Invalid min value for {data_type.value}.",
                        "Validation Error",
                        wx.OK | wx.ICON_ERROR
                    )
                    return
            
            if max_text:
                try:
                    if data_type == DataType.NUMBER:
                        max_value = float(max_text)
                    elif data_type == DataType.DECIMAL:
                        max_value = Decimal(max_text)
                    elif data_type == DataType.INTEGER:
                        max_value = int(max_text)
                    elif data_type in (DataType.DATE, DataType.TIME, DataType.DATETIME):
                        if data_type == DataType.DATE:
                            max_value = datetime.strptime(max_text, "%Y-%m-%d")
                        elif data_type == DataType.TIME:
                            max_value = datetime.strptime(max_text, "%H:%M:%S")
                        else:
                            max_value = datetime.strptime(max_text, "%Y-%m-%d %H:%M:%S")
                except (ValueError, InvalidOperation):
                    wx.MessageBox(
                        f"Invalid max value for {data_type.value}.",
                        "Validation Error",
                        wx.OK | wx.ICON_ERROR
                    )
                    return
        
        # Get pattern
        pattern = None
        if validation_type == ValidationType.FORMAT:
            pattern = self.pattern_text.GetValue().strip()
            if pattern:
                try:
                    re.compile(pattern)
                except re.error:
                    wx.MessageBox(
                        "Invalid regular expression pattern.",
                        "Validation Error",
                        wx.OK | wx.ICON_ERROR
                    )
                    return
        
        # Create rule
        self.rule = ValidationRule(
            name=name,
            type=validation_type,
            data_type=data_type,
            required=self.required_check.GetValue(),
            min_value=min_value,
            max_value=max_value,
            pattern=pattern,
            error_message=self.error_text.GetValue().strip() or None
        )
        
        self.EndModal(wx.ID_OK)
    
    def get_rule(self) -> ValidationRule:
        """Get the validation rule.
        
        Returns:
            Validation rule
        """
        return self.rule 