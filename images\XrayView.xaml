<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.05,5.5A2.071,2.071,0,0,0,7,6a3.009,3.009,0,0,0,3,3,2.071,2.071,0,0,0,.5-.05V5.5ZM10,1.5A4.489,4.489,0,0,0,5.97,4H11l1,1v5.03A4.5,4.5,0,0,0,10,1.5ZM10,12A6,6,0,0,1,4,6c0-.17.01-.34.02-.5H1.5v9h9V11.98C10.34,11.99,10.17,12,10,12Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10,11A5,5,0,1,0,5,6,5.006,5.006,0,0,0,10,11Zm0-1a4,4,0,1,1,4-4A4,4,0,0,1,10,10Zm0-4H7a2.772,2.772,0,0,1,.18-1H10.5l.5.5V8.82A2.772,2.772,0,0,1,10,9ZM1.5,5H4.09A5.47,5.47,0,0,0,4,6H2v8h8V12a5.47,5.47,0,0,0,1-.09V14.5l-.5.5h-9L1,14.5v-9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
