<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-yellow-10" Color="#996f00" Opacity="0.1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
      <SolidColorBrush x:Key="light-yellow" Color="#996f00" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,12.5h-3v3H.5v-4h3v-3h6Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.58,6H6.5V3.5h7.914L12.54,5.374,11.373,4.208Zm3.728,2.849H11.772L11,8.076V12.5h3.5V7.656Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9.5,7h-6L3,7.5V10H.5l-.5.5v5l.5.5h6l.5-.5V13H9.5l.5-.5v-5ZM6,15H1V12H3v.5l.5.5H6Zm0-3H4V11H6Zm3,0H7V10.5L6.5,10H4V9H9Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,7.156V12.5l-.5.5H10.914L11,12.914V12h3V8.156Zm-.293-3.949L14.5,3h-8L6,3.5V6H7V4h6.914Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-yellow-10}" Geometry="F1M6.5,10.5v2h-3v-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,3.914V2H12.5a2,2,0,0,0-4,0H7V4h6.914ZM10.5,3a1,1,0,1,1,1-1A1,1,0,0,1,10.5,3Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M15.871,4.871,12.894,7.849h-.707L10.666,6.328l.707-.707L12.54,6.788l2.624-2.624Z" />
              <GeometryDrawing Brush="{DynamicResource light-yellow}" Geometry="F1M7,12.5v-2L6.5,10h-3l-.5.5v2l.5.5h3ZM4,11H6v1H4Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
