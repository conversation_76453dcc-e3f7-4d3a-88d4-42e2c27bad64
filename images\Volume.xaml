<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M8.5,2V14l-4-3.5H.5v-5h4Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.5,8A2,2,0,0,1,9,9.929V6.071A2,2,0,0,1,10.5,8Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11,8a2.5,2.5,0,0,1-2,2.449V9.408A1.491,1.491,0,0,0,9,6.592V5.551A2.5,2.5,0,0,1,11,8Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8.171,1.624,4.313,5H.5L0,5.5v5l.5.5H4.313l3.858,3.376L9,14V2ZM4,10H1V6H4Zm4,2.9L5,10.273V5.727L8,3.1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.5,8a4.966,4.966,0,0,1-1.464,3.535l-.707-.707a4,4,0,0,0,0-5.656l.707-.707A4.966,4.966,0,0,1,13.5,8Zm.3-5.3L13.1,3.4a6.51,6.51,0,0,1,0,9.194l.707.707A7.511,7.511,0,0,0,13.8,2.7Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
