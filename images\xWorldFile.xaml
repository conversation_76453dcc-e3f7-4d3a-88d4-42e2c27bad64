<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,4.5v10l-.5.5H7.914l1-1H13V5H10V2H3V9.086l-1,1V1.5L2.5,1h8l.354.146,3,3Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13.5,4.5v10H8.414L9,13.914V12h.914L11,10.914V8.086L9.914,7H6.086L5,8.086V9H3.086L2.5,9.586V1.5h8Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M.5,15.5v-3h5v3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10,8.5v2l-.5.5H9V9H6V8.5L6.5,8h3ZM3.5,10l-.5.5V11H7v3h.5l.5-.5v-3L7.5,10Zm2,2,.5.5v3l-.5.5H.5L0,15.5v-3L.5,12ZM5,13H1v2H5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
