<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,3.008a1.978,1.978,0,0,0-1,.278V1H6V7H7V6.729a1.969,1.969,0,0,0,1,.279,2,2,0,0,0,0-4Zm.013,3.026a1,1,0,1,1,1-1A1,1,0,0,1,8.013,6.034ZM4,3.286a1.978,1.978,0,0,0-1-.278,2,2,0,0,0,0,4,1.969,1.969,0,0,0,1-.279V7H5V3H4ZM2.993,6.034a1,1,0,1,1,1-1A1,1,0,0,1,2.993,6.034Zm.711,5.7.719.7A2,2,0,1,1,4.393,9.6l-.7.714a1,1,0,1,0-.7,1.716A.992.992,0,0,0,3.7,11.734Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M15,5.5v6l-.5.5H7.679l1.677,1.678-.707.707-2.5-2.5v-.707l2.5-2.5.707.707L7.741,11H14V6H11V5h3.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
