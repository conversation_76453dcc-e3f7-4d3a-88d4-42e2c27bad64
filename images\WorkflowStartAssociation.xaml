<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-yellow" Color="#996f00" Opacity="1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.15,4.15,11.29,3H7.5a5.5,5.5,0,0,0,0,11h3.38l-.03.06A6.5,6.5,0,1,1,7.5,2h3.79L10.15.85l.7-.7,2,2v.7l-2,2Z" />
              <GeometryDrawing Brush="{DynamicResource light-yellow}" Geometry="F1M15.5,12,12,16H11l1.5-3h-2l2-4h3l-2.137,3Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M12,6.6l-4.25,4.25H7.043L4.793,8.6,5.5,7.892l1.9,1.9,3.9-3.9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
