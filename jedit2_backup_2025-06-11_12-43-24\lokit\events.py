"""Event Bridge for JEdit2 LibreOffice Integration.

This module provides event translation between LibreOffice UNO events
and existing JEdit2 Python handlers, preserving all functionality.
"""

import logging
from typing import Dict, Callable, Any, Optional, List
from ..utils.ai_manager import AIManager
from ..utils.memory_manager import MemoryManager
from ..utils.cache import CacheManager


class EventBridge:
    """Bridge LibreOffice events to JEdit2 handlers."""
    
    def __init__(self, ai_manager: AIManager, memory_manager: MemoryManager, 
                 cache_manager: CacheManager):
        """Initialize event bridge.
        
        Args:
            ai_manager: AI manager instance
            memory_manager: Memory manager instance  
            cache_manager: Cache manager instance
        """
        self.ai_manager = ai_manager
        self.memory_manager = memory_manager
        self.cache_manager = cache_manager
        self.logger = logging.getLogger(__name__)
        
        # Event handler registry
        self.event_handlers: Dict[str, Callable] = {}
        self._setup_event_handlers()
        
    def _setup_event_handlers(self):
        """Setup event handler mapping."""
        self.event_handlers.update({
            'cell_changed': self._handle_cell_change,
            'selection_changed': self._handle_selection_change,
            'document_modified': self._handle_document_modified,
            'format_applied': self._handle_format_applied,
            'ai_command': self._handle_ai_command,
            'save_requested': self._handle_save_requested,
            'undo_redo': self._handle_undo_redo,
            'find_replace': self._handle_find_replace,
            'memory_check': self._handle_memory_check,
            'cache_update': self._handle_cache_update,
        })
    
    def handle_event(self, event_type: str, data: Dict[str, Any]) -> Any:
        """Handle incoming event.
        
        Args:
            event_type: Type of event
            data: Event data
            
        Returns:
            Event handling result
        """
        try:
            if event_type in self.event_handlers:
                return self.event_handlers[event_type](data)
            else:
                self.logger.warning(f"Unhandled event type: {event_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error handling event {event_type}: {e}")
            return None
    
    def _handle_cell_change(self, data: Dict[str, Any]) -> None:
        """Handle cell content changes."""
        try:
            doc_id = data.get('doc_id')
            cell_data = data.get('cell_data', {})
            
            # Update cache with new cell data
            if doc_id and cell_data:
                self.cache_manager.update_cell_cache(doc_id, cell_data)
                
        except Exception as e:
            self.logger.error(f"Error handling cell change: {e}")
    
    def _handle_selection_change(self, data: Dict[str, Any]) -> None:
        """Handle selection changes."""
        try:
            doc_id = data.get('doc_id')
            selection_info = data.get('selection', {})
            
            # Update cache with selection state
            if doc_id:
                self.cache_manager.update_selection_cache(doc_id, selection_info)
                
        except Exception as e:
            self.logger.error(f"Error handling selection change: {e}")
    
    def _handle_document_modified(self, data: Dict[str, Any]) -> None:
        """Handle document modification events."""
        try:
            doc_id = data.get('doc_id')
            
            if doc_id:
                # Mark document as modified in cache
                self.cache_manager.mark_document_modified(doc_id)
                
                # Check memory usage if needed
                self.memory_manager.check_document_memory(doc_id)
                
        except Exception as e:
            self.logger.error(f"Error handling document modification: {e}")
    
    def _handle_format_applied(self, data: Dict[str, Any]) -> None:
        """Handle formatting application events."""
        try:
            doc_id = data.get('doc_id')
            format_info = data.get('format', {})
            
            if doc_id and format_info:
                # Cache formatting state
                self.cache_manager.update_format_cache(doc_id, format_info)
                
        except Exception as e:
            self.logger.error(f"Error handling format application: {e}")
    
    def _handle_ai_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle AI commands from UI."""
        try:
            doc_id = data.get('doc_id')
            command = data.get('command', '')
            
            if not doc_id or not command:
                return {'status': 'error', 'message': 'Missing doc_id or command'}
            
            # Process AI command
            result = self.ai_manager.process_command(command, doc_id)
            
            return {'status': 'success', 'ai_result': result}
            
        except Exception as e:
            self.logger.error(f"Error handling AI command: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_save_requested(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle save requests."""
        try:
            doc_id = data.get('doc_id')
            file_path = data.get('file_path')
            
            if doc_id:
                # Update cache before save
                self.cache_manager.prepare_for_save(doc_id)
                
                return {'status': 'ready_to_save'}
            
            return {'status': 'error', 'message': 'No document ID provided'}
            
        except Exception as e:
            self.logger.error(f"Error handling save request: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_undo_redo(self, data: Dict[str, Any]) -> None:
        """Handle undo/redo operations."""
        try:
            doc_id = data.get('doc_id')
            operation = data.get('operation')  # 'undo' or 'redo'
            
            if doc_id and operation:
                # Update cache for undo/redo state
                self.cache_manager.update_undo_state(doc_id, operation)
                
        except Exception as e:
            self.logger.error(f"Error handling undo/redo: {e}")
    
    def _handle_find_replace(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle find/replace operations."""
        try:
            doc_id = data.get('doc_id')
            search_text = data.get('search_text', '')
            replace_text = data.get('replace_text', '')
            
            if doc_id:
                # Cache search/replace operation
                operation = {
                    'search': search_text,
                    'replace': replace_text,
                    'timestamp': self.cache_manager.get_timestamp()
                }
                
                self.cache_manager.cache_operation(doc_id, 'find_replace', operation)
                
                return {'status': 'ready_to_execute'}
            
            return {'status': 'error', 'message': 'No document ID provided'}
            
        except Exception as e:
            self.logger.error(f"Error handling find/replace: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_memory_check(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle memory check requests."""
        try:
            memory_stats = self.memory_manager.get_memory_stats()
            
            return {'status': 'success', 'memory_stats': memory_stats}
            
        except Exception as e:
            self.logger.error(f"Error handling memory check: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_cache_update(self, data: Dict[str, Any]) -> None:
        """Handle cache update requests."""
        try:
            doc_id = data.get('doc_id')
            cache_data = data.get('cache_data', {})
            
            if doc_id and cache_data:
                # Update cache with provided data
                self.cache_manager.update_cache(doc_id, cache_data)
                
        except Exception as e:
            self.logger.error(f"Error handling cache update: {e}")
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register custom event handler.
        
        Args:
            event_type: Type of event to handle
            handler: Handler function
        """
        self.event_handlers[event_type] = handler
        self.logger.info(f"Registered handler for event type: {event_type}")
    
    def unregister_event_handler(self, event_type: str):
        """Unregister event handler.
        
        Args:
            event_type: Type of event to unregister
        """
        if event_type in self.event_handlers:
            del self.event_handlers[event_type]
            self.logger.info(f"Unregistered handler for event type: {event_type}")
    
    def get_registered_events(self) -> List[str]:
        """Get list of registered event types.
        
        Returns:
            List of event type strings
        """
        return list(self.event_handlers.keys()) 