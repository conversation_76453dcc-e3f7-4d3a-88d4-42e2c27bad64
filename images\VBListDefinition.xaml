<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,5v8H8V10.086L6.914,9H1.5V5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,5.5v8l-.5.5H8V13h6V6H2V9H1V5.5L1.5,5h13Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8,9h5v1H8ZM7,7V8h6V7ZM3,8H6V7H3Zm5,4h2V11H8Zm3,0h2V11H11Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,3V5H2V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,6H1.5L1,5.5v-3L1.5,2h13l.5.5v3ZM2,5H14V3H2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M6.5,15.5H.5v-5h6Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6.5,10H.5l-.5.5v5l.5.5h6l.5-.5v-5ZM4,12v1H3V12ZM1,12H2v1H1Zm0,3V14H2v1Zm2,0V14H4v1Zm3,0H5V14H6Zm0-2H5V12H6Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
