#!/usr/bin/env python3
"""
Comprehensive test of JSON correction fixes including the user's case.
"""

import os
import sys
import json

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_json_corrections():
    """Test all JSON correction scenarios."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    test_cases = [
        {
            'name': 'User Case: Missing Object Braces',
            'content': '''"sale_date": "2025-06-25",
"sale_price": 230000.0,
"improvements": 15000.0,
"depreciation_taken": 30132.0,
"monthly_depreciation": 243.0,
"other_income": 150000.0,
"filing_status": "Married Filing Jointly",
"state": "Massachusetts"''',
            'expected_fix': 'missing object braces'
        },
        {
            'name': 'Trailing Comma',
            'content': '{"name": "test", "value": 123,}',
            'expected_fix': 'trailing commas'
        },
        {
            'name': 'Missing Quotes',
            'content': '{name: "test", value: 123}',
            'expected_fix': 'unquoted property names'
        },
        {
            'name': 'Complex Case: Missing Braces + Trailing Comma',
            'content': '''"name": "test",
"value": 123,
"active": true,''',
            'expected_fix': 'missing object braces'
        }
    ]
    
    print("🧪 COMPREHENSIVE JSON CORRECTION TEST")
    print("=" * 60)
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {case['name']}")
        print("-" * 40)
        
        # Validate
        validation_result = validator.validate_format(case['content'], 'json')
        print(f"   Correctable: {validation_result.is_correctable}")
        
        if validation_result.is_correctable:
            # Correct
            correction_result = corrector.correct_format(case['content'], 'json')
            success = correction_result.success
            
            if success:
                print(f"   ✅ SUCCESS")
                print(f"   Changes: {correction_result.changes_made}")
                
                # Verify the result is valid JSON
                try:
                    parsed = json.loads(correction_result.corrected_content)
                    print(f"   ✅ Result is valid JSON with {len(parsed)} properties")
                    
                    # Show a preview of the corrected content
                    preview = correction_result.corrected_content
                    if len(preview) > 150:
                        preview = preview[:150] + "..."
                    print(f"   Preview: {repr(preview)}")
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ Result is still invalid: {e}")
                    success = False
            else:
                print(f"   ❌ FAILED")
                print(f"   Errors: {correction_result.errors}")
        else:
            print(f"   ❌ Not detected as correctable")
            success = False
        
        results.append({
            'name': case['name'],
            'success': success
        })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for r in results if r['success'])
    
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! JSON correction is working perfectly!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed:")
        for result in results:
            if not result['success']:
                print(f"   - {result['name']}")
    
    return passed == total

if __name__ == "__main__":
    test_json_corrections()
