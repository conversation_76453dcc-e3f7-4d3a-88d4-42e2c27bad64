#!/usr/bin/env python3
"""
Test script for format correction functionality.
"""

import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_format_corrector():
    """Test the FormatCorrector class."""
    print("🔧 Testing Format Corrector")
    print("=" * 50)
    
    try:
        from jedit2.utils.format_corrector import FormatCorrector
        
        corrector = FormatCorrector()
        print("✅ FormatCorrector imported successfully")
        
        # Test JSON correction
        print("\n📄 Testing JSON correction:")
        
        # Invalid JSON with trailing comma
        invalid_json = '{"name": "test", "value": 123,}'
        result = corrector.correct_json(invalid_json)
        print(f"   Trailing comma fix: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Invalid JSON with missing bracket
        invalid_json2 = '{"name": "test", "value": 123'
        result = corrector.correct_json(invalid_json2)
        print(f"   Missing bracket fix: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Test YAML correction
        print("\n📝 Testing YAML correction:")
        
        # YAML with tabs
        invalid_yaml = "name: test\n\tvalue: 123\n\tlist:\n\t\t- item1"
        result = corrector.correct_yaml(invalid_yaml)
        print(f"   Tab to spaces fix: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Test CSV correction
        print("\n📊 Testing CSV correction:")
        
        # CSV with inconsistent columns
        invalid_csv = "name,age,city\nJohn,25\nJane,30,LA,Extra"
        result = corrector.correct_csv(invalid_csv)
        print(f"   Column standardization: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Test Python correction
        print("\n🐍 Testing Python correction:")
        
        # Python with tabs
        invalid_python = "def test():\n\tprint('hello')\n\treturn True"
        result = corrector.correct_python(invalid_python)
        print(f"   Tab to spaces fix: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Test XML correction
        print("\n📋 Testing XML correction:")
        
        # XML without declaration
        invalid_xml = "<root><item>test</item></root>"
        result = corrector.correct_xml(invalid_xml)
        print(f"   XML declaration fix: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        # Test Markdown correction
        print("\n📝 Testing Markdown correction:")
        
        # Markdown with header spacing issues
        invalid_md = "#Header without space\n##Another header\n- List item without space"
        result = corrector.correct_markdown(invalid_md)
        print(f"   Header and list fixes: {'✅ PASS' if result.success else '❌ FAIL'}")
        if result.success:
            print(f"   Changes: {result.changes_made}")
        
        print("\n🎉 FormatCorrector tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ FormatCorrector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backup_manager():
    """Test the BackupManager class."""
    print("\n💾 Testing Backup Manager")
    print("=" * 50)
    
    try:
        from jedit2.utils.backup_manager import BackupManager
        
        backup_manager = BackupManager()
        print("✅ BackupManager imported successfully")
        
        # Test backup creation
        test_content = "This is test content for backup"
        backup_path = backup_manager.create_backup("test.txt", test_content)
        print(f"✅ Backup created: {backup_path}")
        
        # Test backup listing
        backups = backup_manager.list_backups("test.txt")
        print(f"✅ Found {len(backups)} backups for test.txt")
        
        # Test backup cleanup
        if backups:
            deleted = backup_manager.delete_backup(backups[0]['backup_path'])
            print(f"✅ Backup deletion: {'SUCCESS' if deleted else 'FAILED'}")
        
        print("🎉 BackupManager tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ BackupManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_validation_extensions():
    """Test the extended ValidationResult functionality."""
    print("\n🔍 Testing Validation Extensions")
    print("=" * 50)
    
    try:
        from jedit2.utils.format_validator import FormatValidator
        
        validator = FormatValidator()
        print("✅ FormatValidator imported successfully")
        
        # Test JSON validation with correction info
        invalid_json = '{"name": "test", "value": 123,}'
        result = validator.validate_json(invalid_json)
        print(f"✅ JSON validation with correction info:")
        print(f"   Is correctable: {result.is_correctable}")
        print(f"   Suggestions: {result.correction_suggestions}")
        
        # Test YAML validation with correction info
        invalid_yaml = "name: test\n\tvalue: 123"
        result = validator.validate_yaml(invalid_yaml)
        print(f"✅ YAML validation with correction info:")
        print(f"   Is correctable: {result.is_correctable}")
        print(f"   Suggestions: {result.correction_suggestions}")
        
        print("🎉 Validation extensions tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Validation extensions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_test_files_for_correction():
    """Create test files with various format issues."""
    print("\n📁 Creating Test Files for Manual Testing")
    print("=" * 50)
    
    test_files = {
        "test_json_errors.json": '''{
    "name": "Test File",
    "version": 1.0,
    "features": ["validation", "testing"],
    "valid": true,
}''',  # Trailing comma
        
        "test_yaml_errors.yaml": '''name: Test File
version: 1.0
features:
\t- validation
\t- testing''',  # Tabs instead of spaces
        
        "test_csv_errors.csv": '''name,age,city
John,25
Jane,30,Los Angeles,Extra
Bob,35,Chicago''',  # Inconsistent columns
        
        "test_python_errors.py": '''def test_function():
\tprint("Hello World")
\treturn True''',  # Tabs instead of spaces
        
        "test_xml_errors.xml": '''<root>
    <item name=value>test</item>
</root>''',  # Missing XML declaration and unquoted attribute
        
        "test_markdown_errors.md": '''#Header without space
##Another header
-List item without space
- Proper list item
```
Code block
```
Another code block without closing
```'''  # Various markdown issues
    }
    
    created_files = []
    
    for filename, content in test_files.items():
        try:
            with open(filename, 'w') as f:
                f.write(content)
            created_files.append(filename)
            print(f"✅ Created {filename}")
        except Exception as e:
            print(f"❌ Failed to create {filename}: {e}")
    
    if created_files:
        print(f"\n📋 Test files created: {len(created_files)}")
        print("   You can now:")
        print("   1. Open these files in JEdit2")
        print("   2. Go to Tools > Format Validation")
        print("   3. Test the correction functionality")
        print("   4. Try the 'Preview Corrections' and 'Apply Corrections' buttons")
        
        print(f"\n🧹 To clean up later, run:")
        print(f"   rm {' '.join(created_files)}")
    
    return len(created_files) > 0


if __name__ == "__main__":
    print("🚀 Format Correction Test Suite")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_format_corrector()
    test2_passed = test_backup_manager()
    test3_passed = test_validation_extensions()
    test4_passed = create_test_files_for_correction()
    
    print(f"\n📊 Test Results:")
    print(f"   Format Corrector:     {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Backup Manager:       {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Validation Extensions: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    print(f"   Test Files Created:   {'✅ PASS' if test4_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print(f"\n🎉 All tests passed! Format correction is ready to use.")
        print(f"💡 Try the manual test files to see the correction in action!")
    else:
        print(f"\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)
