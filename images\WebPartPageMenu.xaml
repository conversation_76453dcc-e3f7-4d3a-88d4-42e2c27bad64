<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2,5H1V4H2Zm7,8h1V12H9ZM2,6H1V7H2Zm6,6H7v1H8ZM4,2H3V3H4ZM8,2H7V3H8ZM6,2H5V3H6ZM2,2H1.5L1,2.5V3H2ZM13,7h1V6H13ZM9,3h1V2H9Zm4.5-1H13V3h1V2.5ZM13,5h1V4H13Zm0,6h1V10H13Zm0-2h1V8H13Zm-2,4h1V12H11Zm2,0h.5l.5-.5V12H13ZM6,12H5v1H6ZM2,10H1v1H2ZM2,8H1V9H2Zm2,4H3v1H4ZM2,12H1v.5l.5.5H2Zm9-9h1V2H11Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M11.5,9.5h-8v-4h8Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11.5,10h-8L3,9.5v-4L3.5,5h8l.5.5v4ZM4,9h7V6H4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5,8V7h5V8Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
