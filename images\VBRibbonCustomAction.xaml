<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8.5,7l.5.5v1L8.5,9h-1L7,8.5v-1L7.5,7Zm-5,0L3,7.5v1l.5.5h1L5,8.5v-1L4.5,7ZM13,8.5v-1L12.5,7h-1l-.5.5v1l.5.5h1ZM14.5,5H8V3.5L7.5,3h-4L3,3.5V5H1.5L1,5.5V10H2V6H3.5L4,5.5V4H7V5.5l.5.5H14v4H9v1h5.5l.5-.5v-5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,5.5v5H9V10H2V5.5H3.5v-2h4v2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,13.5l-3,2v-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2,11.5v4l.777.416,3-2v-.832l-3-2Zm1,.935L4.6,13.5,3,14.565ZM0,11H1v5H0Zm8,0v5H7V11Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
