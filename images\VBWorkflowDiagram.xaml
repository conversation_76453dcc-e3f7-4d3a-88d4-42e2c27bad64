<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M9.5,4.5h-4v-3h4Zm-7,10H.5v-2h2Zm6,0h-2v-2h2Zm6,0h-2v-2h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M14.5,12H14V8.5L13.5,8H8V5H9.5l.5-.5v-3L9.5,1h-4L5,1.5v3l.5.5H7V8H1.5L1,8.5V12H.5l-.5.5v2l.5.5h2l.5-.5v-2L2.5,12H2V9H7v3H6.5l-.5.5v2l.5.5h2l.5-.5v-2L8.5,12H8V9h5v3h-.5l-.5.5v2l.5.5h2l.5-.5v-2ZM2,14H1V13H2Zm6,0H7V13H8ZM6,4V2H9V4Zm8,10H13V13h1Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
