<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,5v8H9V10H1.5V5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,5.5v8l-.5.5H9V13h5V6H2v4H1V5.5L1.5,5h13Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,3V5H2V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,6H1.5L1,5.5v-3L1.5,2h13l.5.5v3ZM2,5H14V3H2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6,15H8v1H6ZM0,16H5V15H0Zm6-2H8V13H6ZM0,14H5V13H0Zm6-2H8V11H6ZM0,12H5V11H0Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
