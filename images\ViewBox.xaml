<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.5,5.5v5h-5v-5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,15H1.5L1,14.5V1.5L1.5,1h13l.5.5v13ZM2,14H14V2H2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6.1,5.4,5.4,6.1,4,4.707V6H3V3.5L3.5,3H6V4H4.707Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4.707,12H6v1H3.5L3,12.5V10H4v1.293L5.4,9.9,6.1,10.6Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M13,10v2.5l-.5.5H10V12h1.293L9.9,10.6,10.6,9.9l1.4,1.4V10Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M13,3.5V6H12V4.707L10.6,6.1,9.9,5.4l1.4-1.4H10V3h2.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.5,11h-5L5,10.5v-5L5.5,5h5l.5.5v5ZM6,10h4V6H6Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
