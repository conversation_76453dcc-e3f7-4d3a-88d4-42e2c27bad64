<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JEdit2 - Complete User Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
            scroll-behavior: smooth;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
        }
        .toc {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            color: #2980b9;
            text-decoration: none;
            padding: 5px 10px;
            display: block;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .toc a:hover {
            background-color: #bdc3c7;
        }
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        .back-to-top:hover {
            background: #2980b9;
        }
        .section {
            scroll-margin-top: 20px;
        }
        .toc ul li {
            border-left: 3px solid transparent;
            transition: border-color 0.3s;
        }
        .toc ul li:hover {
            border-left-color: #3498db;
        }
        .quick-nav {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 0.9em;
        }
        .quick-nav a {
            color: #2980b9;
            text-decoration: none;
            margin-right: 15px;
        }
        .quick-nav a:hover {
            text-decoration: underline;
        }
        .feature-box {
            background: #e8f6f3;
            border: 1px solid #1abc9c;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fdf2e9;
            border: 1px solid #e67e22;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box {
            background: #eaf2ff;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .keyboard-shortcut {
            background: #34495e;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .menu-path {
            background: #95a5a6;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .section {
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JEdit2 Complete User Guide</h1>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#overview">1. Overview</a></li>
                <li><a href="#getting-started">2. Getting Started</a></li>
                <li><a href="#file-operations">3. File Operations</a></li>
                <li><a href="#editing-features">4. Editing Features</a></li>
                <li><a href="#spreadsheet-features">5. Spreadsheet Features</a></li>
                <li><a href="#formatting-tools">6. Formatting Tools</a></li>
                <li><a href="#tools-menu">7. Tools Menu - Advanced Features</a></li>
                <li><a href="#ai-integration">8. AI Integration</a></li>
                <li><a href="#keyboard-shortcuts">9. Keyboard Shortcuts</a></li>
                <li><a href="#troubleshooting">10. Troubleshooting</a></li>
            </ul>
        </div>

        <div class="quick-nav">
            <strong>Quick Navigation:</strong>
            <a href="#overview">Overview</a>
            <a href="#getting-started">Getting Started</a>
            <a href="#file-operations">File Operations</a>
            <a href="#tools-menu">Tools Menu</a>
            <a href="#ai-integration">AI Integration</a>
            <a href="#keyboard-shortcuts">Shortcuts</a>
            <a href="#troubleshooting">Troubleshooting</a>
        </div>

        <div class="section" id="overview">
            <h2 id="overview">1. Overview</h2>
            <p>JEdit2 is a powerful, modern text editor and spreadsheet application that combines the best of both worlds. It provides advanced text editing capabilities with comprehensive spreadsheet functionality, all in a single, intuitive interface.</p>
            
            <div class="feature-box">
                <h4>Key Features:</h4>
                <ul>
                    <li><strong>Dual View System:</strong> Switch seamlessly between text and spreadsheet views</li>
                    <li><strong>Multi-Format Support:</strong> Excel (.xlsx), CSV, JSON, YAML, and text files</li>
                    <li><strong>Advanced Editing:</strong> Syntax highlighting, find & replace, undo/redo</li>
                    <li><strong>Spreadsheet Tools:</strong> Formatting, sorting, filtering, calculations</li>
                    <li><strong>AI Integration:</strong> Built-in AI assistant for enhanced productivity</li>
                    <li><strong>Professional Tools:</strong> Validation, optimization, and analysis features</li>
                </ul>
            </div>
        </div>

        <div class="section" id="getting-started">
            <h2 id="getting-started">2. Getting Started</h2>
            
            <h3>2.1 Interface Overview</h3>
            <p>JEdit2 features a modern ribbon interface with organized tool panels:</p>
            <ul>
                <li><strong>Clipboard Panel:</strong> Cut, Copy, Paste operations</li>
                <li><strong>Font Panel:</strong> Text formatting (Bold, Italic, Underline)</li>
                <li><strong>Alignment Panel:</strong> Text alignment and decimal controls</li>
                <li><strong>Number Panel:</strong> Number formatting (Currency, Percentage, Comma style)</li>
                <li><strong>Cells & View Panel:</strong> Row/column operations and view switching</li>
                <li><strong>Editing Panel:</strong> Find, Replace, Undo, Redo</li>
            </ul>

            <h3>2.2 Creating Your First Document</h3>
            <ol>
                <li>Launch JEdit2</li>
                <li>Choose <span class="menu-path">File → New</span> for a text document</li>
                <li>Or choose <span class="menu-path">File → New Spreadsheet</span> for a grid</li>
                <li>Start typing or entering data</li>
                <li>Save with <span class="keyboard-shortcut">Ctrl+S</span></li>
            </ol>

            <div class="tip-box">
                <strong>Tip:</strong> Use the View Switch button to toggle between text and spreadsheet views of the same data.
            </div>
        </div>

        <div class="section" id="file-operations">
            <h2 id="file-operations">3. File Operations</h2>
            
            <h3>3.1 Supported File Formats</h3>
            <table>
                <tr>
                    <th>Format</th>
                    <th>Extension</th>
                    <th>Description</th>
                    <th>Best View</th>
                </tr>
                <tr>
                    <td>Excel</td>
                    <td>.xlsx, .xls</td>
                    <td>Microsoft Excel spreadsheets</td>
                    <td>Grid View</td>
                </tr>
                <tr>
                    <td>CSV</td>
                    <td>.csv</td>
                    <td>Comma-separated values</td>
                    <td>Grid View</td>
                </tr>
                <tr>
                    <td>JSON</td>
                    <td>.json</td>
                    <td>JavaScript Object Notation</td>
                    <td>Both Views</td>
                </tr>
                <tr>
                    <td>YAML</td>
                    <td>.yaml, .yml</td>
                    <td>YAML Ain't Markup Language</td>
                    <td>Text View</td>
                </tr>
                <tr>
                    <td>Text</td>
                    <td>.txt, .py, .html, .css, .js</td>
                    <td>Plain text and code files</td>
                    <td>Text View</td>
                </tr>
            </table>

            <h3>3.2 Opening Files</h3>
            <ul>
                <li><span class="keyboard-shortcut">Ctrl+O</span> or <span class="menu-path">File → Open</span></li>
                <li>Select file type from the dropdown filter</li>
                <li>Files automatically open in the appropriate view</li>
                <li>Recent files are accessible via <span class="menu-path">File → Recent Files</span></li>
            </ul>

            <h3>3.3 Saving Files</h3>
            <ul>
                <li><span class="keyboard-shortcut">Ctrl+S</span> - Save current file</li>
                <li><span class="keyboard-shortcut">Ctrl+Shift+S</span> - Save As (choose format)</li>
                <li>Format is automatically detected from file extension</li>
                <li>Grid data can be saved as Excel, CSV, JSON, or YAML</li>
            </ul>
        </div>

        <div class="section" id="editing-features">
            <h2 id="editing-features">4. Editing Features</h2>

            <h3>4.1 Text Editing</h3>
            <ul>
                <li><strong>Syntax Highlighting:</strong> Automatic highlighting for Python, JavaScript, HTML, CSS, JSON, YAML</li>
                <li><strong>Auto-indentation:</strong> Smart indentation based on file type</li>
                <li><strong>Line Numbers:</strong> Always visible for easy navigation</li>
                <li><strong>Word Wrap:</strong> Automatic text wrapping for readability</li>
            </ul>

            <h3>4.2 Find & Replace</h3>
            <ul>
                <li>Access via <span class="keyboard-shortcut">Ctrl+F</span> or Find button</li>
                <li>Case-sensitive and whole-word options</li>
                <li>Regular expression support</li>
                <li>Replace all or replace individually</li>
                <li>Works in both text and grid views</li>
            </ul>

            <h3>4.3 Undo/Redo System</h3>
            <div class="feature-box">
                <p>JEdit2 features a comprehensive undo/redo system that tracks:</p>
                <ul>
                    <li>Text changes and formatting</li>
                    <li>Cell edits and formatting</li>
                    <li>Row and column operations (insert/delete)</li>
                    <li>Multi-row/column operations</li>
                    <li>Find and replace operations</li>
                </ul>
                <p>Use <span class="keyboard-shortcut">Ctrl+Z</span> to undo and <span class="keyboard-shortcut">Ctrl+Y</span> to redo.</p>
            </div>
        </div>

        <div class="section" id="spreadsheet-features">
            <h2 id="spreadsheet-features">5. Spreadsheet Features</h2>

            <h3>5.1 Grid Operations</h3>
            <h4>Row Operations:</h4>
            <ul>
                <li><strong>Insert Rows:</strong> Select rows and click Insert Row button</li>
                <li><strong>Delete Rows:</strong> Select rows and click Delete Row button</li>
                <li><strong>Multi-row Support:</strong> Operations work on multiple selected rows</li>
            </ul>

            <h4>Column Operations:</h4>
            <ul>
                <li><strong>Insert Columns:</strong> Select columns and click Insert Column button</li>
                <li><strong>Delete Columns:</strong> Select columns and click Delete Column button</li>
                <li><strong>Multi-column Support:</strong> Operations work on multiple selected columns</li>
            </ul>

            <h3>5.2 Data Formatting</h3>
            <table>
                <tr>
                    <th>Format Type</th>
                    <th>Button</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>Currency</td>
                    <td>$</td>
                    <td>Formats numbers as currency with $ symbol</td>
                </tr>
                <tr>
                    <td>Percentage</td>
                    <td>%</td>
                    <td>Converts decimals to percentage format</td>
                </tr>
                <tr>
                    <td>Comma Style</td>
                    <td>,</td>
                    <td>Adds thousand separators to numbers</td>
                </tr>
                <tr>
                    <td>Increase Decimal</td>
                    <td>.0↑</td>
                    <td>Increases decimal places shown</td>
                </tr>
                <tr>
                    <td>Decrease Decimal</td>
                    <td>.0↓</td>
                    <td>Decreases decimal places shown</td>
                </tr>
            </table>

            <h3>5.3 Text Formatting</h3>
            <ul>
                <li><strong>Bold:</strong> <span class="keyboard-shortcut">Ctrl+B</span> or Bold button</li>
                <li><strong>Italic:</strong> <span class="keyboard-shortcut">Ctrl+I</span> or Italic button</li>
                <li><strong>Underline:</strong> <span class="keyboard-shortcut">Ctrl+U</span> or Underline button</li>
                <li><strong>Alignment:</strong> Left, Center, Right alignment buttons</li>
            </ul>

            <h3>5.4 Copy & Paste</h3>
            <div class="tip-box">
                <strong>Advanced Clipboard:</strong> JEdit2 includes an intelligent clipboard manager that:
                <ul>
                    <li>Preserves formatting between applications</li>
                    <li>Maintains data structure when copying between views</li>
                    <li>Supports copying entire rows or columns</li>
                    <li>Handles mixed data types intelligently</li>
                </ul>
            </div>
        </div>

        <div class="section" id="formatting-tools">
            <h2 id="formatting-tools">6. Formatting Tools</h2>

            <h3>6.1 Cell Selection</h3>
            <ul>
                <li><strong>Single Cell:</strong> Click on any cell</li>
                <li><strong>Range Selection:</strong> Click and drag to select multiple cells</li>
                <li><strong>Row Selection:</strong> Click on row header</li>
                <li><strong>Column Selection:</strong> Click on column header</li>
                <li><strong>Multiple Ranges:</strong> Hold Ctrl while selecting</li>
            </ul>

            <h3>6.2 Transpose Data</h3>
            <p>The Transpose button swaps rows and columns in your data:</p>
            <ul>
                <li>Select the data range you want to transpose</li>
                <li>Click the Transpose button</li>
                <li>Data is automatically reorganized</li>
                <li>Useful for changing data orientation</li>
            </ul>

            <h3>6.3 View Switching</h3>
            <p>Switch between Text and Grid views to see your data differently:</p>
            <ul>
                <li><strong>Grid View:</strong> Spreadsheet-like interface for structured data</li>
                <li><strong>Text View:</strong> Raw text format with syntax highlighting</li>
                <li>Changes in one view are reflected in the other</li>
                <li>Choose the best view for your current task</li>
            </ul>
        </div>

        <div class="section" id="tools-menu">
            <h2 id="tools-menu">7. Tools Menu - Advanced Features</h2>

            <h3>7.1 Format Validation</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Validates and corrects file format issues</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Format Validation</span></p>
                <p><strong>Supported Formats:</strong> JSON, YAML, CSV, Markdown, Excel</p>

                <h4>How to Use:</h4>
                <ol>
                    <li>Open a file or have content in the editor</li>
                    <li>Select <span class="menu-path">Tools → Format Validation</span></li>
                    <li>The system analyzes your content for format errors</li>
                    <li>If errors are found, you'll see a correction dialog</li>
                    <li>Choose to apply corrections or save current version first</li>
                </ol>

                <h4>What It Checks:</h4>
                <ul>
                    <li><strong>JSON:</strong> Syntax errors, missing brackets, invalid escaping</li>
                    <li><strong>YAML:</strong> Indentation issues, invalid syntax, structure problems</li>
                    <li><strong>CSV:</strong> Inconsistent columns, encoding issues, delimiter problems</li>
                    <li><strong>Excel:</strong> Data integrity, format compatibility</li>
                    <li><strong>Markdown:</strong> Link validity, heading structure, formatting</li>
                </ul>
            </div>

            <h3>7.2 Index Manager</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Manages data indexing for improved performance</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Index Manager</span></p>

                <h4>Features:</h4>
                <ul>
                    <li>Creates searchable indexes of your data</li>
                    <li>Improves find operations on large datasets</li>
                    <li>Manages index storage and updates</li>
                    <li>Provides index statistics and health monitoring</li>
                </ul>

                <h4>When to Use:</h4>
                <ul>
                    <li>Working with large CSV or Excel files</li>
                    <li>Frequent search operations</li>
                    <li>Performance optimization needed</li>
                    <li>Data analysis tasks</li>
                </ul>
            </div>

            <h3>7.3 Memory Manager</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Optimizes memory usage for large files</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Memory Manager</span></p>

                <h4>Capabilities:</h4>
                <ul>
                    <li>Monitors current memory usage</li>
                    <li>Provides memory optimization suggestions</li>
                    <li>Manages garbage collection</li>
                    <li>Handles large file loading strategies</li>
                </ul>

                <h4>Best Practices:</h4>
                <ul>
                    <li>Check memory usage before opening very large files</li>
                    <li>Use optimization suggestions for better performance</li>
                    <li>Monitor memory during intensive operations</li>
                    <li>Clear unused data when working with multiple large files</li>
                </ul>
            </div>

            <h3>7.4 Cache Manager</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Manages application cache for improved performance</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Cache Manager</span></p>

                <h4>Functions:</h4>
                <ul>
                    <li>Displays cache usage statistics</li>
                    <li>Allows manual cache clearing</li>
                    <li>Configures cache size limits</li>
                    <li>Manages temporary file cleanup</li>
                </ul>

                <h4>When to Use:</h4>
                <ul>
                    <li>Application running slowly</li>
                    <li>Disk space concerns</li>
                    <li>After working with many large files</li>
                    <li>Troubleshooting performance issues</li>
                </ul>
            </div>

            <h3>7.5 Lazy Loading</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Enables efficient handling of very large files</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Lazy Loading</span></p>

                <h4>How It Works:</h4>
                <ul>
                    <li>Loads only visible portions of large files</li>
                    <li>Dynamically loads data as you scroll</li>
                    <li>Reduces initial loading time</li>
                    <li>Minimizes memory footprint</li>
                </ul>

                <h4>Configuration Options:</h4>
                <ul>
                    <li>Set file size threshold for lazy loading</li>
                    <li>Configure chunk size for loading</li>
                    <li>Adjust preload buffer size</li>
                    <li>Enable/disable for specific file types</li>
                </ul>

                <h4>Ideal For:</h4>
                <ul>
                    <li>Excel files with thousands of rows</li>
                    <li>Large CSV datasets</li>
                    <li>Log files and data exports</li>
                    <li>Any file over 10MB</li>
                </ul>
            </div>

            <h3>7.6 Error Report</h3>
            <div class="feature-box">
                <p><strong>Purpose:</strong> Comprehensive error tracking and reporting</p>
                <p><strong>Access:</strong> <span class="menu-path">Tools → Error Report</span></p>

                <h4>Features:</h4>
                <ul>
                    <li>Displays recent errors and warnings</li>
                    <li>Provides detailed error information</li>
                    <li>Suggests solutions for common problems</li>
                    <li>Exports error logs for troubleshooting</li>
                </ul>

                <h4>Information Provided:</h4>
                <ul>
                    <li>Error timestamp and severity</li>
                    <li>Detailed error messages</li>
                    <li>Stack traces for debugging</li>
                    <li>Suggested corrective actions</li>
                    <li>System information context</li>
                </ul>
            </div>
        </div>

        <div class="section" id="ai-integration">
            <h2 id="ai-integration">8. AI Integration</h2>

            <h3>8.1 AI Assistant Panel</h3>
            <p>JEdit2 includes a powerful AI assistant accessible from the ribbon:</p>
            <ul>
                <li><strong>AI Command Input:</strong> Type natural language commands</li>
                <li><strong>Recent Commands:</strong> Dropdown with previously used AI commands</li>
                <li><strong>AI Response Display:</strong> Shows AI analysis and suggestions</li>
            </ul>

            <h3>8.2 AI Commands</h3>
            <div class="tip-box">
                <strong>Usage:</strong> Type commands in natural language and press <span class="keyboard-shortcut">Ctrl+Enter</span>

                <h4>Example Commands:</h4>
                <ul>
                    <li>"Analyze this data for trends"</li>
                    <li>"Find errors in this JSON"</li>
                    <li>"Suggest improvements for this code"</li>
                    <li>"Format this data as a table"</li>
                    <li>"Explain what this formula does"</li>
                </ul>
            </div>

            <h3>8.3 AI Settings</h3>
            <p>Configure AI behavior via <span class="menu-path">Tools → AI Settings</span>:</p>
            <ul>
                <li>Set AI model preferences</li>
                <li>Configure response length</li>
                <li>Adjust analysis depth</li>
                <li>Enable/disable specific AI features</li>
            </ul>
        </div>

        <div class="section" id="keyboard-shortcuts">
            <h2 id="keyboard-shortcuts">9. Keyboard Shortcuts</h2>

            <table>
                <tr>
                    <th>Action</th>
                    <th>Shortcut</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>New File</td>
                    <td><span class="keyboard-shortcut">Ctrl+N</span></td>
                    <td>Create new text document</td>
                </tr>
                <tr>
                    <td>Open File</td>
                    <td><span class="keyboard-shortcut">Ctrl+O</span></td>
                    <td>Open existing file</td>
                </tr>
                <tr>
                    <td>Save</td>
                    <td><span class="keyboard-shortcut">Ctrl+S</span></td>
                    <td>Save current file</td>
                </tr>
                <tr>
                    <td>Save As</td>
                    <td><span class="keyboard-shortcut">Ctrl+Shift+S</span></td>
                    <td>Save with new name/format</td>
                </tr>
                <tr>
                    <td>Undo</td>
                    <td><span class="keyboard-shortcut">Ctrl+Z</span></td>
                    <td>Undo last action</td>
                </tr>
                <tr>
                    <td>Redo</td>
                    <td><span class="keyboard-shortcut">Ctrl+Y</span></td>
                    <td>Redo last undone action</td>
                </tr>
                <tr>
                    <td>Cut</td>
                    <td><span class="keyboard-shortcut">Ctrl+X</span></td>
                    <td>Cut selected content</td>
                </tr>
                <tr>
                    <td>Copy</td>
                    <td><span class="keyboard-shortcut">Ctrl+C</span></td>
                    <td>Copy selected content</td>
                </tr>
                <tr>
                    <td>Paste</td>
                    <td><span class="keyboard-shortcut">Ctrl+V</span></td>
                    <td>Paste from clipboard</td>
                </tr>
                <tr>
                    <td>Find</td>
                    <td><span class="keyboard-shortcut">Ctrl+F</span></td>
                    <td>Open find dialog</td>
                </tr>
                <tr>
                    <td>Bold</td>
                    <td><span class="keyboard-shortcut">Ctrl+B</span></td>
                    <td>Toggle bold formatting</td>
                </tr>
                <tr>
                    <td>Italic</td>
                    <td><span class="keyboard-shortcut">Ctrl+I</span></td>
                    <td>Toggle italic formatting</td>
                </tr>
                <tr>
                    <td>Underline</td>
                    <td><span class="keyboard-shortcut">Ctrl+U</span></td>
                    <td>Toggle underline formatting</td>
                </tr>
                <tr>
                    <td>AI Submit</td>
                    <td><span class="keyboard-shortcut">Ctrl+Enter</span></td>
                    <td>Submit AI command</td>
                </tr>
            </table>
        </div>

        <div class="section" id="troubleshooting">
            <h2 id="troubleshooting">10. Troubleshooting</h2>

            <h3>10.1 Common Issues</h3>

            <div class="warning-box">
                <h4>File Won't Open</h4>
                <ul>
                    <li>Check file format is supported</li>
                    <li>Verify file isn't corrupted</li>
                    <li>Try opening in text view first</li>
                    <li>Check file permissions</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Performance Issues</h4>
                <ul>
                    <li>Use Memory Manager to check usage</li>
                    <li>Enable Lazy Loading for large files</li>
                    <li>Clear cache via Cache Manager</li>
                    <li>Close unused tabs</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Excel Files Not Working</h4>
                <ul>
                    <li>Ensure OpenPyXL is installed: <code>pip install openpyxl</code></li>
                    <li>Check file isn't password protected</li>
                    <li>Try saving as .xlsx format</li>
                    <li>Verify file isn't open in Excel</li>
                </ul>
            </div>

            <h3>10.2 Getting Help</h3>
            <ul>
                <li>Use <span class="menu-path">Tools → Error Report</span> for detailed error information</li>
                <li>Check the AI Assistant for context-specific help</li>
                <li>Review this help file for feature explanations</li>
                <li>Restart the application if issues persist</li>
            </ul>

            <h3>10.3 Best Practices</h3>
            <ul>
                <li>Save your work frequently</li>
                <li>Use appropriate file formats for your data type</li>
                <li>Monitor memory usage with large files</li>
                <li>Keep the application updated</li>
                <li>Use the Tools menu features for optimization</li>
            </ul>
        </div>

        <div class="section">
            <h2>Support Information</h2>
            <p>For additional support or to report issues, please refer to the Error Report tool or contact support through the application.</p>
            <p><strong>Version:</strong> JEdit2 2025</p>
            <p><strong>Last Updated:</strong> December 2024</p>
        </div>

        <!-- Back to top link -->
        <a href="#overview" class="back-to-top">↑ Back to Top</a>
    </div>
</body>
</html>
