<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2.409,9.466a3,3,0,0,1,.923-.385l.591,1.427A1.48,1.48,0,0,0,3,10.891Zm1.668,4.026.591,1.427a2.967,2.967,0,0,0,.923-.386L5,13.109A1.485,1.485,0,0,1,4.077,13.492ZM2.508,11.923A1.485,1.485,0,0,1,2.891,11l-1.424-.59a2.977,2.977,0,0,0-.386.923Zm2.984.154A1.485,1.485,0,0,1,5.109,13l1.425.59a3,3,0,0,0,.385-.923Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M4,8.5A3.5,3.5,0,1,0,7.5,12,3.5,3.5,0,0,0,4,8.5ZM4,13a1,1,0,1,1,1-1A1,1,0,0,1,4,13Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4,13.5A1.5,1.5,0,1,1,5.5,12,1.5,1.5,0,0,1,4,13.5Zm0-2a.5.5,0,1,0,.5.5A.5.5,0,0,0,4,11.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4,16a4,4,0,1,1,4-4A4,4,0,0,1,4,16ZM4,9a3,3,0,1,0,3,3A3,3,0,0,0,4,9Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M14.5,8a6.448,6.448,0,0,1-6.181,6.477A4.952,4.952,0,0,0,9,12,4.967,4.967,0,0,0,1.523,7.681,6.492,6.492,0,0,1,14.5,8Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12.947,3.053A6.973,6.973,0,0,0,1.016,8.009a5.015,5.015,0,0,1,1.037-.616A5.976,5.976,0,0,1,2.35,6H4.7a9.923,9.923,0,0,0-.151,1.055,4.954,4.954,0,0,1,.993.212A9.518,9.518,0,0,1,5.722,6h4.556a9.12,9.12,0,0,1,0,4h-1.7A4.968,4.968,0,0,1,8.9,11H10.01a9.482,9.482,0,0,1-1.255,2.471,4.952,4.952,0,0,1-.764,1.512,6.984,6.984,0,0,0,4.956-11.93ZM4.946,5H2.812a6.025,6.025,0,0,1,3.4-2.727A10.522,10.522,0,0,0,4.946,5ZM6,5A9.479,9.479,0,0,1,7.628,2.019C7.752,2.011,7.874,2,8,2s.249.011.373.019A9.478,9.478,0,0,1,10.005,5ZM9.789,2.273A6.02,6.02,0,0,1,13.188,5H11.053A10.489,10.489,0,0,0,9.789,2.273Zm0,11.454A10.489,10.489,0,0,0,11.053,11h2.135A6.024,6.024,0,0,1,9.789,13.727ZM13.65,10H11.3a10.2,10.2,0,0,0,0-4h2.35A5.885,5.885,0,0,1,13.65,10Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
