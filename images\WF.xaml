<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M.5,12.5h4v2H.5Zm10,0h4v2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.5,8.5a3,3,0,1,1-3-3A3,3,0,0,1,10.5,8.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,1.5h4v2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,12H13V8.5L12.5,8H10.95A3.483,3.483,0,0,0,8,5.05V4H9.5l.5-.5v-2L9.5,1h-4L5,1.5v2l.5.5H7V5.05A3.483,3.483,0,0,0,4.05,8H2.5L2,8.5V12H.5l-.5.5v2l.5.5h4l.5-.5v-2L4.5,12H3V9H4.05a3.484,3.484,0,0,0,6.9,0H12v3H10.5l-.5.5v2l.5.5h4l.5-.5v-2ZM4,13v1H1V13ZM6,3V2H9V3Zm1.5,8A2.5,2.5,0,1,1,10,8.5,2.5,2.5,0,0,1,7.5,11ZM14,14H11V13h3Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
