#!/usr/bin/env python3
"""
Comprehensive analysis to identify uncovered format correction cases.
This script systematically tests edge cases and identifies gaps.
"""

import os
import sys
import json
import yaml
import csv
import io
from typing import List, Dict, Any

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

class CoverageAnalyzer:
    """Analyzes format correction coverage and identifies gaps."""
    
    def __init__(self):
        self.validator = FormatValidator()
        self.corrector = FormatCorrector()
        self.gaps = []
        self.covered_cases = []
        
    def analyze_json_coverage(self):
        """Analyze JSON format correction coverage."""
        print("🔍 ANALYZING JSON COVERAGE")
        print("=" * 50)
        
        # Known JSON error patterns from real-world usage
        json_test_cases = [
            # Basic syntax errors
            {"name": "Missing opening brace", "content": '"key": "value"}', "expected": "missing braces"},
            {"name": "Missing closing brace", "content": '{"key": "value"', "expected": "missing braces"},
            {"name": "Trailing comma", "content": '{"key": "value",}', "expected": "trailing comma"},
            {"name": "Missing comma", "content": '{"key1": "value1" "key2": "value2"}', "expected": "missing comma"},
            
            # Quote issues
            {"name": "Single quotes", "content": "{'key': 'value'}", "expected": "quote conversion"},
            {"name": "Unquoted keys", "content": '{key: "value"}', "expected": "unquoted keys"},
            {"name": "Unescaped quotes", "content": '{"message": "He said "hello""}', "expected": "escape quotes"},
            
            # Structural issues
            {"name": "Array without brackets", "content": '"item1", "item2", "item3"', "expected": "missing array brackets"},
            {"name": "Mixed array/object", "content": '{"key": "value", "item2"}', "expected": "mixed structure"},
            {"name": "Nested object fragment", "content": '"outer": {"inner": "value"', "expected": "nested structure"},
            
            # Value issues
            {"name": "Unquoted string values", "content": '{"key": value}', "expected": "unquoted values"},
            {"name": "Invalid numbers", "content": '{"key": 01.23}', "expected": "invalid numbers"},
            {"name": "Invalid boolean", "content": '{"key": True}', "expected": "boolean case"},
            {"name": "Invalid null", "content": '{"key": None}', "expected": "null case"},
            
            # Complex cases
            {"name": "Multiple errors", "content": '{key1: "value1", "key2": value2,}', "expected": "multiple issues"},
            {"name": "Deeply nested", "content": '{"a": {"b": {"c": "value",}}}', "expected": "nested trailing comma"},
            {"name": "Large object fragment", "content": '"a": 1, "b": 2, "c": 3, "d": 4, "e": 5', "expected": "large fragment"},
            
            # Edge cases
            {"name": "Empty object fragment", "content": '', "expected": "empty content"},
            {"name": "Only whitespace", "content": '   \n\t  ', "expected": "whitespace only"},
            {"name": "Comments (invalid JSON)", "content": '{"key": "value", // comment\n}', "expected": "comments"},
            {"name": "Multiline strings", "content": '{"key": "line1\nline2"}', "expected": "multiline"},
        ]
        
        self._test_cases("JSON", json_test_cases, "json")
    
    def analyze_yaml_coverage(self):
        """Analyze YAML format correction coverage."""
        print("\n🔍 ANALYZING YAML COVERAGE")
        print("=" * 50)
        
        yaml_test_cases = [
            # Indentation issues
            {"name": "Mixed tabs/spaces", "content": "key1: value1\n\tkey2: value2\n  key3: value3", "expected": "mixed indentation"},
            {"name": "Wrong indentation level", "content": "parent:\nchild: value", "expected": "indentation level"},
            {"name": "Inconsistent indentation", "content": "key1: value1\n   key2: value2\n key3: value3", "expected": "inconsistent indent"},
            
            # Syntax issues
            {"name": "Missing colon", "content": "key value", "expected": "missing colon"},
            {"name": "Missing space after colon", "content": "key:value", "expected": "colon spacing"},
            {"name": "Invalid list syntax", "content": "items:\n- item1\n item2", "expected": "list syntax"},
            
            # Quote issues
            {"name": "Unmatched quotes", "content": 'key: "value', "expected": "unmatched quotes"},
            {"name": "Wrong quote type", "content": "key: 'value with \"quotes\"'", "expected": "quote nesting"},
            
            # Structure issues
            {"name": "Invalid mapping", "content": "key1: value1: extra", "expected": "invalid mapping"},
            {"name": "Mixed flow/block", "content": "key: {nested: value}\nother: normal", "expected": "mixed styles"},
            
            # Complex cases
            {"name": "Multiline string issues", "content": "key: |\n  line1\n line2", "expected": "multiline string"},
            {"name": "Anchor/alias errors", "content": "key: &anchor value\nref: *wrong", "expected": "anchor/alias"},
        ]
        
        self._test_cases("YAML", yaml_test_cases, "yaml")
    
    def analyze_csv_coverage(self):
        """Analyze CSV format correction coverage."""
        print("\n🔍 ANALYZING CSV COVERAGE")
        print("=" * 50)
        
        csv_test_cases = [
            # Structure issues
            {"name": "Inconsistent columns", "content": "a,b,c\n1,2\n3,4,5,6", "expected": "column consistency"},
            {"name": "Missing headers", "content": "1,2,3\n4,5,6", "expected": "missing headers"},
            {"name": "Empty rows", "content": "a,b,c\n1,2,3\n\n4,5,6", "expected": "empty rows"},
            
            # Quote issues
            {"name": "Unescaped quotes", "content": 'name,desc\nJohn,"He said "hello""', "expected": "quote escaping"},
            {"name": "Mixed quote styles", "content": "name,desc\n'John',\"Jane\"", "expected": "quote consistency"},
            {"name": "Unquoted commas", "content": "name,desc\nJohn,Hello, world", "expected": "unquoted commas"},
            
            # Delimiter issues
            {"name": "Mixed delimiters", "content": "a,b,c\n1;2;3", "expected": "delimiter consistency"},
            {"name": "Tab delimited", "content": "a\tb\tc\n1\t2\t3", "expected": "tab delimiters"},
            
            # Data issues
            {"name": "Encoding issues", "content": "name,city\nJohn,Montréal", "expected": "encoding"},
            {"name": "Line ending issues", "content": "a,b,c\r\n1,2,3\r\n", "expected": "line endings"},
        ]
        
        self._test_cases("CSV", csv_test_cases, "csv")
    
    def analyze_markdown_coverage(self):
        """Analyze Markdown format correction coverage."""
        print("\n🔍 ANALYZING MARKDOWN COVERAGE")
        print("=" * 50)
        
        markdown_test_cases = [
            # Header issues
            {"name": "Missing space after #", "content": "#Header\n##Another", "expected": "header spacing"},
            {"name": "Too many #", "content": "#######Invalid Header", "expected": "header level"},
            {"name": "Inconsistent header style", "content": "# Header 1\nHeader 2\n========", "expected": "header style"},
            
            # Link issues
            {"name": "Spaced links", "content": "[text] (url)", "expected": "link spacing"},
            {"name": "Broken links", "content": "[text](", "expected": "broken links"},
            {"name": "Missing alt text", "content": "![](image.jpg)", "expected": "missing alt text"},
            
            # List issues
            {"name": "Inconsistent list markers", "content": "- item1\n* item2\n+ item3", "expected": "list markers"},
            {"name": "Missing space after marker", "content": "-item1\n-item2", "expected": "list spacing"},
            {"name": "Wrong indentation", "content": "- item1\n  - subitem\n- item2", "expected": "list indentation"},
            
            # Code issues
            {"name": "Unmatched code blocks", "content": "```\ncode\n", "expected": "unmatched code blocks"},
            {"name": "Unmatched inline code", "content": "This is `code", "expected": "unmatched inline code"},
            
            # Table issues
            {"name": "Malformed table", "content": "| a | b\n| 1 | 2 |", "expected": "table formatting"},
            {"name": "Missing table headers", "content": "| 1 | 2 |\n| 3 | 4 |", "expected": "table headers"},
        ]
        
        self._test_cases("Markdown", markdown_test_cases, "md")
    
    def _test_cases(self, format_name: str, test_cases: List[Dict], file_type: str):
        """Test a list of cases and identify gaps."""
        for case in test_cases:
            try:
                # Test validation
                validation_result = self.validator.validate_format(case["content"], file_type)
                
                # Test correction if correctable
                correction_success = False
                if validation_result.is_correctable:
                    correction_result = self.corrector.correct_format(case["content"], file_type)
                    correction_success = correction_result.success
                
                # Determine if this case is covered
                if validation_result.is_correctable and correction_success:
                    self.covered_cases.append({
                        'format': format_name,
                        'case': case["name"],
                        'status': 'COVERED'
                    })
                    print(f"  ✅ {case['name']}: COVERED")
                else:
                    self.gaps.append({
                        'format': format_name,
                        'case': case["name"],
                        'content': case["content"][:50] + "..." if len(case["content"]) > 50 else case["content"],
                        'expected': case["expected"],
                        'correctable': validation_result.is_correctable,
                        'correction_success': correction_success
                    })
                    status = "NOT CORRECTABLE" if not validation_result.is_correctable else "CORRECTION FAILED"
                    print(f"  ❌ {case['name']}: {status}")
                    
            except Exception as e:
                self.gaps.append({
                    'format': format_name,
                    'case': case["name"],
                    'content': case["content"][:50] + "..." if len(case["content"]) > 50 else case["content"],
                    'expected': case["expected"],
                    'error': str(e)
                })
                print(f"  💥 {case['name']}: ERROR - {str(e)}")
    
    def generate_report(self):
        """Generate a comprehensive coverage report."""
        print("\n" + "=" * 80)
        print("📊 COVERAGE ANALYSIS REPORT")
        print("=" * 80)
        
        total_cases = len(self.covered_cases) + len(self.gaps)
        covered_count = len(self.covered_cases)
        gap_count = len(self.gaps)
        
        print(f"Total Test Cases: {total_cases}")
        print(f"Covered Cases: {covered_count}")
        print(f"Gap Cases: {gap_count}")
        print(f"Coverage Rate: {(covered_count/total_cases)*100:.1f}%")
        
        if self.gaps:
            print(f"\n❌ IDENTIFIED GAPS ({gap_count} cases):")
            print("-" * 50)
            
            by_format = {}
            for gap in self.gaps:
                format_name = gap['format']
                if format_name not in by_format:
                    by_format[format_name] = []
                by_format[format_name].append(gap)
            
            for format_name, gaps in by_format.items():
                print(f"\n{format_name} ({len(gaps)} gaps):")
                for gap in gaps:
                    print(f"  • {gap['case']}")
                    print(f"    Expected: {gap['expected']}")
                    print(f"    Content: {repr(gap['content'])}")
                    if 'error' in gap:
                        print(f"    Error: {gap['error']}")
                    elif 'correctable' in gap:
                        print(f"    Correctable: {gap['correctable']}, Success: {gap.get('correction_success', False)}")
        
        print(f"\n✅ COVERED CASES ({covered_count} cases):")
        print("-" * 50)
        by_format = {}
        for case in self.covered_cases:
            format_name = case['format']
            if format_name not in by_format:
                by_format[format_name] = []
            by_format[format_name].append(case)
        
        for format_name, cases in by_format.items():
            print(f"{format_name}: {len(cases)} cases covered")
        
        return self.gaps

def main():
    """Run comprehensive coverage analysis."""
    print("🔍 FORMAT CORRECTION COVERAGE ANALYSIS")
    print("=" * 80)
    print("This analysis identifies gaps in format correction coverage")
    print("by testing real-world error patterns and edge cases.")
    print()
    
    analyzer = CoverageAnalyzer()
    
    # Analyze each format
    analyzer.analyze_json_coverage()
    analyzer.analyze_yaml_coverage()
    analyzer.analyze_csv_coverage()
    analyzer.analyze_markdown_coverage()
    
    # Generate comprehensive report
    gaps = analyzer.generate_report()
    
    print(f"\n🎯 NEXT STEPS:")
    print("-" * 30)
    if gaps:
        print("1. Review the identified gaps above")
        print("2. Prioritize gaps based on real-world frequency")
        print("3. Implement corrections for high-priority gaps")
        print("4. Add test cases for new corrections")
        print("5. Re-run this analysis to verify improvements")
    else:
        print("🎉 No gaps identified! Coverage appears comprehensive.")
    
    print(f"\n📋 RECOMMENDATIONS:")
    print("-" * 30)
    print("• Add user feedback mechanism to identify real-world cases")
    print("• Monitor error logs for uncaught format issues")
    print("• Implement fuzzing tests for edge case discovery")
    print("• Create user-submitted test case collection")
    
    return len(gaps) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
