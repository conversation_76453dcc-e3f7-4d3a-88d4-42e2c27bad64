<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-teal" Color="#006758" Opacity="1" />
      <SolidColorBrush x:Key="light-teal-10" Color="#006758" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M1.5,5V7H2.88l.7-.7L4.3,6H7.7l.72.3.7.7h2.79l.5.5h.09v.09l.5.5V13h1.5V5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,5H1.5L1,5.5V7H2V6H14v7H13v1h1.5l.5-.5v-8Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M2,3V5H14V3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,2H1.5L1,2.5v3l.5.5h13l.5-.5v-3ZM2,5V3H14V5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M11.5,15.5H.5v-7H3.744l.756-1h3l1,1h3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11.5,16H.5L0,15.5v-7L.5,8H3.293l.853-.854L4.5,7h3l.354.146L8.707,8H11.5l.5.5v7ZM1,15H11V9H8.5l-.354-.146L7.293,8H4.707l-.853.854L3.5,9H1Z" />
              <GeometryDrawing Brush="{DynamicResource light-teal}" Geometry="F1M3,11H2V10H3Z" />
              <GeometryDrawing Brush="{DynamicResource light-teal-10}" Geometry="F1M8,12a2,2,0,1,1-2-2A2,2,0,0,1,8,12Z" />
              <GeometryDrawing Brush="{DynamicResource light-teal}" Geometry="F1M6,14.5A2.5,2.5,0,1,1,8.5,12,2.5,2.5,0,0,1,6,14.5Zm0-4A1.5,1.5,0,1,0,7.5,12,1.5,1.5,0,0,0,6,10.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
