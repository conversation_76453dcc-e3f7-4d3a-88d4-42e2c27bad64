#!/usr/bin/env python3
"""
Debug the missing commas issue specifically.
"""

import os
import sys
import re
import json

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

def debug_missing_commas():
    """Debug the missing commas pattern."""
    
    test_content = '{"name": "John" "age": 30 "city": "Boston"}'
    print("🐛 DEBUGGING MISSING COMMAS")
    print("=" * 50)
    print(f"Content: {test_content}")
    print()
    
    # Test the pattern
    pattern = r'("(?:[^"\\]|\\.)*"|true|false|null|\d+(?:\.\d+)?)\s+("(?:[^"\\]|\\.)*"\s*:)'
    
    print("Testing pattern:")
    print(f"Pattern: {pattern}")
    
    matches = re.findall(pattern, test_content)
    print(f"Matches found: {matches}")
    
    if matches:
        print("\nApplying replacement:")
        result = re.sub(pattern, r'\1, \2', test_content)
        print(f"Result: {result}")
        
        print("\nTesting if result is valid JSON:")
        try:
            parsed = json.loads(result)
            print(f"✅ Valid JSON: {parsed}")
        except json.JSONDecodeError as e:
            print(f"❌ Still invalid: {e}")
            
            # Try a more aggressive approach
            print("\nTrying more aggressive comma insertion:")
            # Look for any quote followed by space and another quote with colon
            aggressive_pattern = r'("\w*")\s+("\w*":)'
            aggressive_result = re.sub(aggressive_pattern, r'\1, \2', test_content)
            print(f"Aggressive result: {aggressive_result}")
            
            try:
                parsed = json.loads(aggressive_result)
                print(f"✅ Aggressive approach works: {parsed}")
            except json.JSONDecodeError as e2:
                print(f"❌ Aggressive approach also fails: {e2}")
    else:
        print("❌ No matches found with current pattern")
        
        # Try simpler patterns
        print("\nTrying simpler patterns:")
        
        # Pattern 1: "value" "key":
        simple_pattern1 = r'("[\w\s]*")\s+("[\w]*":)'
        matches1 = re.findall(simple_pattern1, test_content)
        print(f"Simple pattern 1 matches: {matches1}")
        
        if matches1:
            result1 = re.sub(simple_pattern1, r'\1, \2', test_content)
            print(f"Simple result 1: {result1}")
            try:
                parsed = json.loads(result1)
                print(f"✅ Simple pattern 1 works: {parsed}")
            except json.JSONDecodeError as e:
                print(f"❌ Simple pattern 1 fails: {e}")

if __name__ == "__main__":
    debug_missing_commas()
