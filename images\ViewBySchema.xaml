<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12,8.85V11H11V9H4v2H3V8.5L3.5,8H7V6H8V8h2.88l.85.85Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8.23,5.35,8.88,6H5.5L5,5.5v-3L5.5,2h4l.5.5V3.58l-1,1V3H6V5H8.58ZM14,11.5v3l-.5.5h-4L9,14.5v-3l.5-.5h4ZM13,12H10v2h3ZM5.5,11l.5.5v3l-.5.5h-4L1,14.5v-3l.5-.5ZM5,12H2v2H5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,2.5v3H8.38l-.15-.15L8.58,5,9,4.58l.5-.5V2.5Zm4,9v3h4v-3Zm-8,0v3h4v-3Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M9.645,5.354l.707-.708L12,6.3V0h1V6.291l1.645-1.645.707.708-2.5,2.5h-.707Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
