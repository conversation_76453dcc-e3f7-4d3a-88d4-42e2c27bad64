<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M6.031,9.5H.5V.5h12V6.88A4,4,0,0,0,6.031,9.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M6.127,9A3.978,3.978,0,0,0,6,10H.5L0,9.5V.5L.5,0h12l.5.5V7.357a4.036,4.036,0,0,0-1-.82V1H1V9Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7,7.357V2H8V4.277A1.988,1.988,0,0,1,11,6c0,.042,0,.084,0,.125A4.039,4.039,0,0,0,10,6a1,1,0,1,0-1.882.471A4.028,4.028,0,0,0,7,7.357Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M6,8V4H5v.277A2,2,0,1,0,5,7.723V8ZM4,7A1,1,0,1,1,5,6,1,1,0,0,1,4,7Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M7.5,10A2.5,2.5,0,1,1,10,12.5,2.5,2.5,0,0,1,7.5,10Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M15.85,15.143l-3.442-3.372A2.964,2.964,0,0,0,13,10a3.007,3.007,0,1,0-1.3,2.472l3.455,3.385ZM8,10a2,2,0,1,1,2,2A2,2,0,0,1,8,10Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
