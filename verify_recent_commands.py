#!/usr/bin/env python3
"""
Simple verification script to check if recent AI commands are working.
"""

import os
import json

def check_recent_commands_config():
    """Check if recent commands are being saved to configuration."""
    print("🔍 Checking Recent AI Commands Configuration")
    print("=" * 50)
    
    # Look for config files
    config_locations = [
        "config.json",
        "jedit2_config.json", 
        os.path.expanduser("~/.jedit2/config.json"),
        os.path.expanduser("~/AppData/Local/JEdit2/config.json"),
        "jedit2/config.json"
    ]
    
    config_found = False
    recent_commands_found = False
    
    for config_path in config_locations:
        if os.path.exists(config_path):
            print(f"📁 Found config file: {config_path}")
            config_found = True
            
            try:
                with open(config_path, 'r') as f:
                    config_data = json.load(f)
                
                if "recent_ai_commands" in config_data:
                    recent_commands = config_data["recent_ai_commands"]
                    print(f"✅ Found recent_ai_commands in config!")
                    print(f"   Number of commands: {len(recent_commands)}")
                    
                    if recent_commands:
                        print("   Recent commands:")
                        for i, cmd in enumerate(recent_commands[:5]):  # Show first 5
                            print(f"     {i+1}. {cmd}")
                        if len(recent_commands) > 5:
                            print(f"     ... and {len(recent_commands) - 5} more")
                    else:
                        print("   (No commands stored yet)")
                    
                    recent_commands_found = True
                else:
                    print(f"   No recent_ai_commands found in this config")
                    
            except Exception as e:
                print(f"   Error reading config: {e}")
    
    if not config_found:
        print("❌ No config files found")
        print("   This might mean:")
        print("   1. The application hasn't been run yet")
        print("   2. Config is stored in a different location")
        print("   3. The feature isn't working")
    
    if not recent_commands_found:
        print("❌ No recent AI commands found in any config")
        print("   This might mean:")
        print("   1. No AI commands have been executed yet")
        print("   2. The feature isn't properly integrated")
        print("   3. Commands aren't being saved")
    
    return config_found, recent_commands_found


def check_code_integration():
    """Check if the code integration looks correct."""
    print("\n🔧 Checking Code Integration")
    print("=" * 50)
    
    # Check main window file
    main_window_path = "jedit2/main_window.py"
    if os.path.exists(main_window_path):
        print(f"📁 Checking {main_window_path}")
        
        with open(main_window_path, 'r') as f:
            content = f.read()
        
        checks = [
            ("ai_recent_combo", "Recent commands dropdown component"),
            ("_load_recent_ai_commands", "Load recent commands method"),
            ("_save_recent_ai_commands", "Save recent commands method"), 
            ("_add_to_recent_ai_commands", "Add to recent commands method"),
            ("_update_recent_ai_commands_dropdown", "Update dropdown method"),
            ("_on_ai_recent_selected", "Dropdown selection handler"),
            ("recent_ai_commands = []", "Recent commands list initialization"),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - NOT FOUND")
    else:
        print(f"❌ Main window file not found: {main_window_path}")
    
    # Check AI manager integration
    ai_manager_paths = [
        "jedit2/utils/ai_manager_production.py",
        "jedit2/utils/ai_system_integrator.py"
    ]
    
    for ai_path in ai_manager_paths:
        if os.path.exists(ai_path):
            print(f"📁 Checking {ai_path}")
            
            with open(ai_path, 'r') as f:
                content = f.read()
            
            if "_add_to_recent_ai_commands" in content:
                print(f"   ✅ Recent commands tracking integrated")
            else:
                print(f"   ❌ Recent commands tracking NOT integrated")


def create_test_config():
    """Create a test configuration with recent commands."""
    print("\n🧪 Creating Test Configuration")
    print("=" * 50)
    
    test_config = {
        "recent_ai_commands": [
            "open price.json",
            "sort column A ascending",
            "make column B bold", 
            "delete row 5",
            "add new row"
        ],
        "api_key": "test_key",
        "recent_files": []
    }
    
    test_config_path = "test_config.json"
    
    try:
        with open(test_config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        print(f"✅ Created test config: {test_config_path}")
        print("   This config contains sample recent AI commands")
        print("   You can use this to test if the dropdown loads correctly")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create test config: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Recent AI Commands Verification")
    print("=" * 60)
    
    # Run checks
    config_found, recent_commands_found = check_recent_commands_config()
    check_code_integration()
    test_config_created = create_test_config()
    
    print(f"\n📊 Summary:")
    print(f"   Config files found:        {'✅' if config_found else '❌'}")
    print(f"   Recent commands in config: {'✅' if recent_commands_found else '❌'}")
    print(f"   Test config created:       {'✅' if test_config_created else '❌'}")
    
    print(f"\n💡 Next Steps:")
    if not recent_commands_found:
        print("   1. Run the main application: python main_window.py")
        print("   2. Try executing an AI command (e.g., 'open price.json')")
        print("   3. Check if the dropdown gets populated")
        print("   4. Look for the config file to be created/updated")
    else:
        print("   ✅ Recent commands feature appears to be working!")
        print("   The dropdown should show the stored commands when you run the app.")
