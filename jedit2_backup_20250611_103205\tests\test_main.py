"""Tests for the main application module."""

import unittest
import wx
from jedit2.main import MainFrame


class TestMainFrame(unittest.TestCase):
    """Test cases for the MainFrame class."""

    def setUp(self) -> None:
        """Set up test environment."""
        self.app = wx.App()
        self.frame = MainFrame()

    def tearDown(self) -> None:
        """Clean up test environment."""
        self.frame.Destroy()
        self.app.Destroy()

    def test_frame_creation(self) -> None:
        """Test that the main frame is created successfully."""
        self.assertIsInstance(self.frame, MainFrame)
        self.assertEqual(self.frame.GetTitle(), "JEdit2")

    def test_new_tab(self) -> None:
        """Test adding a new tab."""
        initial_count = self.frame.notebook.GetPageCount()
        self.frame._add_new_tab(label="TestTab")
        self.assertEqual(self.frame.notebook.GetPageCount(), initial_count + 1)
        self.assertEqual(self.frame.notebook.GetPageText(self.frame.notebook.GetSelection()), "TestTab")

    def test_close_tab(self) -> None:
        """Test closing a tab (should not close last tab)."""
        self.frame._add_new_tab(label="Tab1")
        self.frame._add_new_tab(label="Tab2")
        count_before = self.frame.notebook.GetPageCount()
        self.frame._close_current_tab()
        count_after = self.frame.notebook.GetPageCount()
        self.assertEqual(count_after, count_before - 1)
        # Should not close the last tab
        self.frame._close_current_tab()
        self.frame._close_current_tab()
        self.assertEqual(self.frame.notebook.GetPageCount(), 1)

    def test_switch_tab(self) -> None:
        """Test switching between tabs."""
        self.frame._add_new_tab(label="TabA")
        self.frame._add_new_tab(label="TabB")
        count = self.frame.notebook.GetPageCount()
        idx = self.frame.notebook.GetSelection()
        self.frame._switch_tab(forward=True)
        new_idx = self.frame.notebook.GetSelection()
        self.assertNotEqual(idx, new_idx)
        self.frame._switch_tab(forward=False)
        self.assertEqual(self.frame.notebook.GetSelection(), idx)


if __name__ == "__main__":
    unittest.main() 