<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8.351,4,5.478,12H3.994L1.172,4h1.45L4.6,10.07a3.709,3.709,0,0,1,.145.675h.022a3.531,3.531,0,0,1,.162-.686L6.946,4Zm1.06,8V4H11.95a3,3,0,0,1,1.838.508,1.577,1.577,0,0,1,.678,1.322,1.887,1.887,0,0,1-.385,1.183,2.087,2.087,0,0,1-1.066.714v.022a2.082,2.082,0,0,1,1.319.611A1.857,1.857,0,0,1,14.828,9.7a2.014,2.014,0,0,1-.808,1.662A3.2,3.2,0,0,1,11.978,12Zm1.323-6.934V7.342h.859a1.655,1.655,0,0,0,1.085-.326,1.135,1.135,0,0,0,.393-.924q0-1.026-1.372-1.026Zm0,3.341V10.94h1.132a1.728,1.728,0,0,0,1.147-.343,1.176,1.176,0,0,0,.4-.946q0-1.244-1.724-1.244Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
