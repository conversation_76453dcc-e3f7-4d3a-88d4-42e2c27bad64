"""Lazy loading module for JEdit2.

This module provides lazy loading functionality for large files and data structures.
"""

import time
import threading
import weakref
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from .cache import CacheManager, CacheType
from .memory_manager import MemoryManager


class LoadStrategy(Enum):
    """Load strategy for lazy loading."""
    CHUNK = "chunk"  # Load data in chunks
    PAGE = "page"    # Load data in pages
    STREAM = "stream"  # Stream data continuously


@dataclass
class LoadStats:
    """Statistics for lazy loading."""
    total_chunks: int = 0
    loaded_chunks: int = 0
    total_size: int = 0
    loaded_size: int = 0
    load_time: float = 0.0
    last_load: float = 0.0
    hits: int = 0
    misses: int = 0


class LazyLoader:
    """Lazy loader for large files and data structures."""
    
    def __init__(
        self,
        cache_manager: CacheManager,
        memory_manager: MemoryManager,
        chunk_size: int = 1024 * 1024,  # 1MB chunks
        max_chunks: int = 100,
        strategy: LoadStrategy = LoadStrategy.CHUNK
    ) -> None:
        """Initialize the lazy loader.
        
        Args:
            cache_manager: Cache manager instance
            memory_manager: Memory manager instance
            chunk_size: Size of each chunk in bytes
            max_chunks: Maximum number of chunks to keep in memory
            strategy: Loading strategy
        """
        self.cache_manager = cache_manager
        self.memory_manager = memory_manager
        self.chunk_size = chunk_size
        self.max_chunks = max_chunks
        self.strategy = strategy
        
        # Try to get existing cache, or create a new one
        self._cache = cache_manager.get_cache("lazy_loader")
        if self._cache is None:
            self._cache = cache_manager.create_cache(
                name="lazy_loader",
                max_size=max_chunks,
                cache_type=CacheType.LRU
            )
        self._lock = threading.Lock()
        self._stats = LoadStats()
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_monitor = threading.Event()
    
    def start_monitoring(self) -> None:
        """Start monitoring memory usage."""
        if self._monitor_thread is None:
            self._stop_monitor.clear()
            self._monitor_thread = threading.Thread(
                target=self._monitor_memory,
                daemon=True
            )
            self._monitor_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop monitoring memory usage."""
        if self._monitor_thread is not None:
            self._stop_monitor.set()
            self._monitor_thread.join()
            self._monitor_thread = None
    
    def _monitor_memory(self) -> None:
        """Monitor memory usage and manage chunks."""
        while not self._stop_monitor.is_set():
            try:
                # Check memory usage
                if self.memory_manager.get_memory_usage() > 0.8:  # 80% threshold
                    with self._lock:
                        # Remove least recently used chunks
                        self._cache.evict(0.2)  # Remove 20% of chunks
                
                time.sleep(1)  # Check every second
            except Exception:
                pass
    
    def load_chunk(
        self,
        source: Any,
        chunk_id: int,
        force: bool = False
    ) -> Optional[bytes]:
        """Load a chunk of data.
        
        Args:
            source: Data source (file or other)
            chunk_id: Chunk identifier
            force: Force reload from source
            
        Returns:
            Chunk data or None if not found
        """
        start_time = time.time()
        
        # Try to get from cache first
        if not force:
            chunk = self._cache.get(chunk_id)
            if chunk is not None:
                self._stats.hits += 1
                self._stats.last_load = time.time()
                return chunk
        
        self._stats.misses += 1
        
        try:
            # Load chunk from source
            if hasattr(source, "read"):
                # File-like object
                source.seek(chunk_id * self.chunk_size)
                chunk = source.read(self.chunk_size)
            elif hasattr(source, "__getitem__"):
                # Sequence-like object
                start = chunk_id * self.chunk_size
                end = start + self.chunk_size
                chunk = source[start:end]
            else:
                raise ValueError("Unsupported source type")
            
            if chunk:
                # Cache the chunk
                self._cache.set(chunk_id, chunk)
                self._stats.loaded_chunks += 1
                self._stats.loaded_size += len(chunk)
                self._stats.last_load = time.time()
            
            return chunk
        except Exception:
            return None
        finally:
            self._stats.load_time += time.time() - start_time
    
    def load_range(
        self,
        source: Any,
        start: int,
        end: int,
        force: bool = False
    ) -> List[bytes]:
        """Load a range of chunks.
        
        Args:
            source: Data source
            start: Start chunk index
            end: End chunk index
            force: Force reload from source
            
        Returns:
            List of chunks
        """
        chunks = []
        for chunk_id in range(start, end):
            chunk = self.load_chunk(source, chunk_id, force)
            if chunk is not None:
                chunks.append(chunk)
        return chunks
    
    def prefetch(
        self,
        source: Any,
        chunk_id: int,
        look_ahead: int = 5
    ) -> None:
        """Prefetch chunks ahead of current position.
        
        Args:
            source: Data source
            chunk_id: Current chunk index
            look_ahead: Number of chunks to prefetch
        """
        for i in range(1, look_ahead + 1):
            self.load_chunk(source, chunk_id + i)
    
    def clear_cache(self) -> None:
        """Clear the chunk cache."""
        self._cache.clear()
        self._stats = LoadStats()
    
    def get_stats(self) -> LoadStats:
        """Get loading statistics.
        
        Returns:
            Loading statistics
        """
        return self._stats
    
    def set_strategy(self, strategy: LoadStrategy) -> None:
        """Set the loading strategy.
        
        Args:
            strategy: New loading strategy
        """
        self.strategy = strategy
    
    def set_chunk_size(self, size: int) -> None:
        """Set the chunk size.
        
        Args:
            size: New chunk size in bytes
        """
        self.chunk_size = size
        self.clear_cache()
    
    def set_max_chunks(self, max_chunks: int) -> None:
        """Set the maximum number of chunks.
        
        Args:
            max_chunks: New maximum number of chunks
        """
        self.max_chunks = max_chunks
        self._cache.set_max_size(max_chunks) 