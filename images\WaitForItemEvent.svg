<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}</style>
  </defs>
  <title>IconLightWaitForItemEvent</title>
  <g id="canvas" class="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <path class="light-defaultgrey-10" d="M14.5,6.5v6H4.5v-6Z" />
    <path class="light-defaultgrey" d="M2,5H1V1H2ZM4,5H3V1H4Z" />
    <path class="light-defaultgrey" d="M14.5,6H4.5L4,6.5v6l.5.5h10l.5-.5v-6ZM14,12H5V7h9Zm-2-2H7V9h5Z" />
  </g>
</svg>
