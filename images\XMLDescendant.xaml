<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M11.511,10.5H7.5v-2h4.011ZM9.5,13.5v2h4.011v-2Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12,10.5v-2L11.5,8h-4L7,8.5v2l.5.5h4ZM11,10H8V9h3Zm2.5,3h-4l-.5.5v2l.5.5h4l.5-.5v-2ZM13,15H10V14h3Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,5.5H5.468v-2H9.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9.5,6h-4L5,5.5v-2L5.5,3h4l.5.5v2ZM6,5H9V4H6ZM3.848,6.284l-1.787-1.8L3.814,2.729l-.707-.707L1,4.129v.706L3.138,6.989Zm7.3-3.556,1.787,1.8L11.186,6.282l.707.707L14,4.883V4.177L11.862,2.022Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
