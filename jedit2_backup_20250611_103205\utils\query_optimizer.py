#!/usr/bin/env python3
"""
Query Optimizer for Complex AI Queries
Handles the specific failure patterns identified in analysis.
"""

import re
from typing import List, Dict, Any, Optional, Tuple


class QueryComplexityAnalyzer:
    """Analyzes query complexity and suggests optimizations."""

    @staticmethod
    def analyze_complexity(query: str) -> Dict[str, Any]:
        """
        Analyze query complexity and identify potential issues.

        Returns:
            Dict with complexity metrics and recommendations
        """
        # Count operation indicators
        operation_keywords = [
            "open",
            "save",
            "close",
            "new",
            "create",
            "add",
            "insert",
            "copy",
            "paste",
            "move",
            "delete",
            "sort",
            "filter",
            "bold",
            "format",
            "transpose",
            "switch",
            "tab",
        ]

        file_operations = ["open", "save", "close", "new"]
        tab_operations = ["switch", "tab"]
        format_operations = ["bold", "format", "currency"]
        data_operations = ["transpose", "sort", "filter"]

        query_lower = query.lower()

        # Count different types of operations
        total_operations = sum(
            1 for keyword in operation_keywords if keyword in query_lower
        )
        file_ops = sum(1 for keyword in file_operations if keyword in query_lower)
        tab_ops = sum(1 for keyword in tab_operations if keyword in query_lower)
        format_ops = sum(1 for keyword in format_operations if keyword in query_lower)
        data_ops = sum(1 for keyword in data_operations if keyword in query_lower)

        # Calculate complexity score
        complexity_score = (
            total_operations * 1.0
            + file_ops * 1.5  # File operations add complexity
            + tab_ops * 2.0  # Tab operations are complex
            + format_ops * 1.2  # Formatting adds some complexity
            + data_ops * 1.8  # Data transformations are complex
        )

        # Determine complexity level
        if complexity_score <= 2.0:
            complexity_level = "Simple"
        elif complexity_score <= 4.0:
            complexity_level = "Moderate"
        elif complexity_score <= 6.0:
            complexity_level = "Complex"
        else:
            complexity_level = "Very Complex"

        # Identify specific patterns that caused failures
        failure_patterns = []
        if tab_ops > 0:
            failure_patterns.append("cross_tab_state_management")
        if total_operations >= 5:
            failure_patterns.append("multi_operation_sequence")
        if data_ops > 0 and file_ops > 0:
            failure_patterns.append("data_transformation_with_file_ops")
        if "transpose" in query_lower:
            failure_patterns.append("complex_data_transformation")

        return {
            "complexity_score": complexity_score,
            "complexity_level": complexity_level,
            "total_operations": total_operations,
            "operation_breakdown": {
                "file_operations": file_ops,
                "tab_operations": tab_ops,
                "format_operations": format_ops,
                "data_operations": data_ops,
            },
            "failure_risk_patterns": failure_patterns,
            "recommendations": QueryComplexityAnalyzer._get_recommendations(
                complexity_level, failure_patterns
            ),
        }

    @staticmethod
    def _get_recommendations(
        complexity_level: str, failure_patterns: List[str]
    ) -> List[str]:
        """Get specific recommendations based on complexity analysis."""
        recommendations = []

        if complexity_level in ["Complex", "Very Complex"]:
            recommendations.append("Consider breaking into smaller sequential queries")
            recommendations.append("Use enhanced JSON parsing (robust mode)")

        if "cross_tab_state_management" in failure_patterns:
            recommendations.append(
                "Avoid cross-tab operations - use single-tab workflows"
            )
            recommendations.append("Implement explicit tab context management")

        if "multi_operation_sequence" in failure_patterns:
            recommendations.append("Break into 2-3 operation chunks")
            recommendations.append("Use atomic high-level commands when possible")

        if "data_transformation_with_file_ops" in failure_patterns:
            recommendations.append("Separate file operations from data transformations")
            recommendations.append("Use intermediate save points")

        if "complex_data_transformation" in failure_patterns:
            recommendations.append("Implement TRANSPOSE_DATA as atomic command")
            recommendations.append("Provide data transformation fallbacks")

        return recommendations


class QueryDecomposer:
    """Decomposes complex queries into simpler, more reliable sub-queries."""

    @staticmethod
    def decompose_query(query: str) -> List[Dict[str, Any]]:
        """
        Decompose complex query into simpler sub-queries.

        Returns:
            List of sub-query dictionaries with execution order
        """
        analysis = QueryComplexityAnalyzer.analyze_complexity(query)

        # If query is simple or moderate, don't decompose
        if analysis["complexity_level"] in ["Simple", "Moderate"]:
            return [{"query": query, "order": 1, "type": "atomic"}]

        # Apply specific decomposition strategies
        if "cross_tab_state_management" in analysis["failure_risk_patterns"]:
            return QueryDecomposer._decompose_tab_operations(query)
        elif "multi_operation_sequence" in analysis["failure_risk_patterns"]:
            return QueryDecomposer._decompose_multi_operations(query)
        elif "data_transformation_with_file_ops" in analysis["failure_risk_patterns"]:
            return QueryDecomposer._decompose_file_data_operations(query)
        else:
            # Generic decomposition for very complex queries
            return QueryDecomposer._decompose_generic(query)

    @staticmethod
    def _decompose_tab_operations(query: str) -> List[Dict[str, Any]]:
        """Handle cross-tab operations by eliminating them."""
        # Strategy: Convert to single-tab workflow
        query_lower = query.lower()

        if "copy" in query_lower and "switch" in query_lower and "paste" in query_lower:
            # Failed pattern: "Copy current selection, switch tabs, paste selection"
            # Solution: Use copy-to-clipboard approach or avoid cross-tab operations
            return [
                {
                    "query": "Show message: Cross-tab operations not supported. Please use copy/paste within single tab.",
                    "order": 1,
                    "type": "error_message",
                }
            ]

        return [{"query": query, "order": 1, "type": "atomic"}]

    @staticmethod
    def _decompose_multi_operations(query: str) -> List[Dict[str, Any]]:
        """Break down multi-operation sequences."""
        # Example: "Open data.csv, add column after A, copy B to new column, make it bold, sort by new column"

        sub_queries = []
        query_lower = query.lower()

        # Extract file operations first
        if "open" in query_lower:
            file_match = re.search(r"open\s+([^,]+)", query_lower)
            if file_match:
                filename = file_match.group(1).strip()
                sub_queries.append(
                    {"query": f"Open {filename}", "order": 1, "type": "file_operation"}
                )

        # Extract column operations
        if "add column" in query_lower or "insert column" in query_lower:
            column_match = re.search(
                r"(?:add|insert)\s+column\s+(?:after\s+)?([A-Z])", query, re.IGNORECASE
            )
            if column_match:
                column = column_match.group(1)
                sub_queries.append(
                    {
                        "query": f"Insert column after {column}",
                        "order": 2,
                        "type": "structure_operation",
                    }
                )

        # Extract copy operations
        if "copy" in query_lower and "to" in query_lower:
            copy_match = re.search(
                r"copy\s+(?:column\s+)?([A-Z])\s+to\s+(?:new\s+column|column\s+([A-Z]))",
                query,
                re.IGNORECASE,
            )
            if copy_match:
                source = copy_match.group(1)
                target = copy_match.group(2) if copy_match.group(2) else "new column"
                sub_queries.append(
                    {
                        "query": f"Copy column {source} to {target}",
                        "order": 3,
                        "type": "data_operation",
                    }
                )

        # Extract formatting operations
        if "bold" in query_lower or "format" in query_lower:
            sub_queries.append(
                {
                    "query": "Make selected column bold",
                    "order": 4,
                    "type": "format_operation",
                }
            )

        # Extract sort operations
        if "sort" in query_lower:
            sort_match = re.search(r"sort\s+by\s+([^,]+)", query_lower)
            if sort_match:
                sort_column = sort_match.group(1).strip()
                sub_queries.append(
                    {
                        "query": f"Sort by {sort_column}",
                        "order": 5,
                        "type": "data_operation",
                    }
                )

        return (
            sub_queries
            if sub_queries
            else [{"query": query, "order": 1, "type": "atomic"}]
        )

    @staticmethod
    def _decompose_file_data_operations(query: str) -> List[Dict[str, Any]]:
        """Separate file operations from data transformations."""
        # Example: "Open prices.json, transpose data, save as new file, close original"

        sub_queries = []
        query_lower = query.lower()

        # File opening
        if "open" in query_lower:
            file_match = re.search(r"open\s+([^,]+)", query_lower)
            if file_match:
                filename = file_match.group(1).strip()
                sub_queries.append(
                    {"query": f"Open {filename}", "order": 1, "type": "file_operation"}
                )

        # Data transformation
        if "transpose" in query_lower:
            sub_queries.append(
                {
                    "query": "Transpose data (Note: Manual operation required - select all data, copy, paste special transpose)",
                    "order": 2,
                    "type": "data_transformation",
                }
            )

        # File saving
        if "save as" in query_lower:
            save_match = re.search(r"save\s+as\s+([^,]+)", query_lower)
            if save_match:
                new_filename = save_match.group(1).strip()
                sub_queries.append(
                    {
                        "query": f"Save as {new_filename}",
                        "order": 3,
                        "type": "file_operation",
                    }
                )

        # File closing
        if "close" in query_lower:
            sub_queries.append(
                {"query": "Close current file", "order": 4, "type": "file_operation"}
            )

        return (
            sub_queries
            if sub_queries
            else [{"query": query, "order": 1, "type": "atomic"}]
        )

    @staticmethod
    def _decompose_generic(query: str) -> List[Dict[str, Any]]:
        """Generic decomposition for very complex queries."""
        # Split by commas and conjunctions
        parts = re.split(r",\s*(?:then\s+|and\s+)?", query)

        sub_queries = []
        for i, part in enumerate(parts, 1):
            part = part.strip()
            if part:
                sub_queries.append({"query": part, "order": i, "type": "decomposed"})

        return (
            sub_queries
            if len(sub_queries) > 1
            else [{"query": query, "order": 1, "type": "atomic"}]
        )


def create_optimized_query_processor():
    """
    Create a query processor that optimizes complex queries for better success rates.

    Usage:
        processor = create_optimized_query_processor()
        result = processor(original_query)
    """

    def process_query(query: str) -> Dict[str, Any]:
        """
        Process a query with complexity analysis and optimization.

        Returns:
            Dict with analysis, decomposition, and recommendations
        """
        analysis = QueryComplexityAnalyzer.analyze_complexity(query)
        decomposition = QueryDecomposer.decompose_query(query)

        return {
            "original_query": query,
            "complexity_analysis": analysis,
            "decomposed_queries": decomposition,
            "should_decompose": len(decomposition) > 1,
            "estimated_success_improvement": _estimate_success_improvement(
                analysis, decomposition
            ),
        }

    return process_query


def _estimate_success_improvement(
    analysis: Dict[str, Any], decomposition: List[Dict[str, Any]]
) -> float:
    """Estimate how much the success rate would improve with decomposition."""
    if len(decomposition) == 1:
        return 0.0  # No decomposition, no improvement

    # Base improvement from reducing complexity
    complexity_reduction = (
        0.15 if analysis["complexity_level"] == "Very Complex" else 0.10
    )

    # Additional improvement from addressing specific failure patterns
    pattern_improvements = {
        "cross_tab_state_management": 0.25,
        "multi_operation_sequence": 0.20,
        "data_transformation_with_file_ops": 0.15,
        "complex_data_transformation": 0.10,
    }

    pattern_improvement = sum(
        pattern_improvements.get(pattern, 0.05)
        for pattern in analysis["failure_risk_patterns"]
    )

    total_improvement = min(
        complexity_reduction + pattern_improvement, 0.50
    )  # Cap at 50%
    return total_improvement


if __name__ == "__main__":
    # Test with the actual failed queries
    failed_queries = [
        "Copy current selection, switch tabs, paste selection",
        "Open data.csv, add column after A, copy B to new column, make it bold, sort by new column",
        "Open prices.json, transpose data, save as new file, close original",
    ]

    processor = create_optimized_query_processor()

    print("Query Optimization Analysis:")
    print("=" * 50)

    for i, query in enumerate(failed_queries, 1):
        print(f"\n{i}. Original Query: {query}")
        result = processor(query)

        analysis = result["complexity_analysis"]
        print(
            f"   Complexity: {analysis['complexity_level']} (Score: {analysis['complexity_score']:.1f})"
        )
        print(f"   Operations: {analysis['total_operations']}")
        print(f"   Risk Patterns: {', '.join(analysis['failure_risk_patterns'])}")

        if result["should_decompose"]:
            print(f"   Recommended Decomposition:")
            for sub_query in result["decomposed_queries"]:
                print(
                    f"     {sub_query['order']}. {sub_query['query']} ({sub_query['type']})"
                )
            improvement = result["estimated_success_improvement"]
            print(f"   Estimated Success Improvement: +{improvement:.1%}")
        else:
            print(f"   Recommendation: Use robust JSON parsing")

        print(f"   Specific Recommendations:")
        for rec in analysis["recommendations"]:
            print(f"     - {rec}")
