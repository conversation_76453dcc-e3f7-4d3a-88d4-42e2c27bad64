"""Standalone JEdit2 with LibreOffice-inspired interface.

This version provides professional data editing without requiring UNO bindings,
focusing on JEdit2's core strengths in data manipulation and format handling.
"""

import sys
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
import json
import yaml
import csv
from io import StringIO

# Import existing utilities
from jedit2.utils.error_handler import <PERSON>rror<PERSON>and<PERSON>, ErrorCategory, ErrorLevel
from jedit2.utils.memory_manager import MemoryManager
from jedit2.utils.cache import CacheManager
from jedit2.utils.config_manager import ConfigManager
from jedit2.adapters.formats import FormatAdapter

try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from jedit2.utils.ai_manager import AIManager
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    logging.warning("AI Manager not available")


class SpreadsheetGrid(ttk.Treeview):
    """Professional spreadsheet-like grid widget."""
    
    def __init__(self, parent, rows=100, cols=26):
        """Initialize the grid."""
        # Create column identifiers (A, B, C, ...)
        self.columns = [chr(65 + i) for i in range(cols)]
        
        super().__init__(parent, columns=self.columns, show='tree headings', height=25)
        
        # Configure columns
        self.column('#0', width=50, anchor='center')  # Row numbers
        for col in self.columns:
            self.column(col, width=100, anchor='w')
            self.heading(col, text=col)
        
        # Insert rows
        self.rows = rows
        for i in range(1, rows + 1):
            self.insert('', 'end', text=str(i), values=[''] * len(self.columns))
        
        # Data storage
        self.data = {}
        self.formatting = {}
        
        # Bind events
        self.bind('<Double-1>', self.on_cell_edit)
        self.bind('<Button-3>', self.on_right_click)
        
        # Selection tracking
        self.current_cell = None
        
    def on_cell_edit(self, event):
        """Handle cell editing."""
        selection = self.selection()
        if not selection:
            return
        
        item = selection[0]
        region = self.identify('region', event.x, event.y)
        
        if region == 'cell':
            column = self.identify('column', event.x, event.y)
            if column and column != '#0':
                self.edit_cell(item, column)
    
    def edit_cell(self, item, column):
        """Edit a specific cell."""
        current_value = self.set(item, column)
        
        # Create edit dialog
        dialog = tk.Toplevel(self)
        dialog.title("Edit Cell")
        dialog.geometry("400x150")
        dialog.transient(self)
        dialog.grab_set()
        
        tk.Label(dialog, text=f"Cell {column}{self.item(item, 'text')}:").pack(pady=5)
        
        text_var = tk.StringVar(value=current_value)
        entry = tk.Entry(dialog, textvariable=text_var, width=50)
        entry.pack(pady=5)
        entry.focus()
        entry.select_range(0, tk.END)
        
        def save_value():
            new_value = text_var.get()
            self.set(item, column, new_value)
            
            # Store in data structure
            row_num = int(self.item(item, 'text'))
            self.data[(row_num, column)] = new_value
            
            dialog.destroy()
        
        def cancel_edit():
            dialog.destroy()
        
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Save", command=save_value).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=cancel_edit).pack(side=tk.LEFT, padx=5)
        
        # Bind Enter and Escape
        dialog.bind('<Return>', lambda e: save_value())
        dialog.bind('<Escape>', lambda e: cancel_edit())
    
    def on_right_click(self, event):
        """Handle right click context menu."""
        selection = self.selection()
        if not selection:
            return
        
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="Format Bold", command=self.format_bold)
        menu.add_command(label="Format Italic", command=self.format_italic)
        menu.add_separator()
        menu.add_command(label="Format as Currency", command=self.format_currency)
        menu.add_command(label="Format as Percentage", command=self.format_percentage)
        menu.add_separator()
        menu.add_command(label="Clear Cell", command=self.clear_cell)
        
        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()
    
    def format_bold(self):
        """Apply bold formatting to selected cells."""
        selection = self.selection()
        for item in selection:
            # Store formatting info
            row_num = int(self.item(item, 'text'))
            self.formatting[(row_num, 'bold')] = True
            # Visual indication by changing tags
            self.item(item, tags=('bold',))
        
        # Configure bold tag
        self.tag_configure('bold', font=('TkDefaultFont', 10, 'bold'))
    
    def format_currency(self):
        """Format selected cells as currency."""
        selection = self.selection()
        for item in selection:
            for col in self.columns:
                value = self.set(item, col)
                if value and value.replace('.', '').replace('-', '').isdigit():
                    formatted_value = f"${float(value):.2f}"
                    self.set(item, col, formatted_value)
    
    def get_data_as_dict(self):
        """Get all data as dictionary."""
        result = {}
        for child in self.get_children():
            row_num = int(self.item(child, 'text'))
            row_data = {}
            for col in self.columns:
                value = self.set(child, col)
                if value:
                    row_data[col] = value
            if row_data:
                result[row_num] = row_data
        return result
    
    def load_data_from_dict(self, data):
        """Load data from dictionary."""
        # Clear existing data
        for child in self.get_children():
            self.delete(child)
        
        # Recreate rows and load data
        max_row = max([int(k) for k in data.keys()]) if data else self.rows
        for i in range(1, max(max_row + 1, self.rows + 1)):
            values = []
            for col in self.columns:
                values.append(data.get(i, {}).get(col, ''))
            self.insert('', 'end', text=str(i), values=values)
    
    # Placeholder methods for other formatting
    def format_italic(self): pass
    def format_percentage(self): pass
    def clear_cell(self): pass


class JEdit2StandaloneApp:
    """Standalone JEdit2 application with professional interface."""

    def __init__(self):
        """Initialize the application."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize managers
        self.error_handler = ErrorHandler()
        self.memory_manager = MemoryManager()
        self.config_manager = ConfigManager()
        self.cache_manager = CacheManager()
        self.format_adapter = FormatAdapter()
        
        # Initialize AI manager if available
        self.ai_manager = None
        if AI_AVAILABLE:
            try:
                self.ai_manager = AIManager()
            except Exception as e:
                self.logger.warning(f"AI Manager initialization failed: {e}")
        
        # Track open documents
        self.documents = {}
        self.current_document = None
        
        # Initialize UI
        self.setup_ui()
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('jedit2_standalone.log'),
                logging.StreamHandler()
            ]
        )
        
    def setup_ui(self):
        """Setup the user interface."""
        self.root = tk.Tk()
        self.root.title("JEdit2 - Professional Data Editor")
        self.root.geometry("1400x900")
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')  # More professional look
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area
        self.create_main_area()
        
        # Create status bar
        self.create_status_bar()
        
        # Set initial status
        self.status_var.set("Ready - Professional Data Editor")
        
    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Spreadsheet", command=self.new_spreadsheet, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="Open", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_command(label="Save", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As", command=self.save_file_as, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="Import CSV", command=self.import_csv)
        file_menu.add_command(label="Export CSV", command=self.export_csv)
        if EXCEL_AVAILABLE:
            file_menu.add_command(label="Import Excel", command=self.import_excel)
            file_menu.add_command(label="Export Excel", command=self.export_excel)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_exit)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="Redo", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="Find", command=self.find_data, accelerator="Ctrl+F")
        edit_menu.add_command(label="Replace", command=self.replace_data, accelerator="Ctrl+H")
        
        # Format menu
        format_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Format", menu=format_menu)
        format_menu.add_command(label="Bold", command=self.format_bold)
        format_menu.add_command(label="Italic", command=self.format_italic)
        format_menu.add_separator()
        format_menu.add_command(label="Currency", command=self.format_currency)
        format_menu.add_command(label="Percentage", command=self.format_percentage)
        
        # Data menu
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Data", menu=data_menu)
        data_menu.add_command(label="Sort Ascending", command=self.sort_ascending)
        data_menu.add_command(label="Sort Descending", command=self.sort_descending)
        data_menu.add_separator()
        data_menu.add_command(label="Filter", command=self.apply_filter)
        data_menu.add_command(label="Clear Filter", command=self.clear_filter)
        
        # AI menu (if available)
        if self.ai_manager:
            ai_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="AI", menu=ai_menu)
            ai_menu.add_command(label="Analyze Data", command=self.ai_analyze_data)
            ai_menu.add_command(label="Generate Formula", command=self.ai_generate_formula)
            ai_menu.add_command(label="Data Insights", command=self.ai_data_insights)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Memory Manager", command=self.show_memory_manager)
        tools_menu.add_command(label="Cache Manager", command=self.show_cache_manager)
        tools_menu.add_command(label="Configuration", command=self.show_configuration)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self.new_spreadsheet())
        self.root.bind('<Control-o>', lambda e: self.open_file())
        self.root.bind('<Control-s>', lambda e: self.save_file())
        self.root.bind('<Control-f>', lambda e: self.find_data())
        
    def create_toolbar(self):
        """Create the toolbar."""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
        
        # File operations
        ttk.Button(toolbar, text="New", command=self.new_spreadsheet).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Open", command=self.open_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Save", command=self.save_file).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # Formatting
        ttk.Button(toolbar, text="Bold", command=self.format_bold).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Currency", command=self.format_currency).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Percentage", command=self.format_percentage).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # Data operations
        ttk.Button(toolbar, text="Sort ↑", command=self.sort_ascending).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Sort ↓", command=self.sort_descending).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Filter", command=self.apply_filter).pack(side=tk.LEFT, padx=2)
        
        if self.ai_manager:
            ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
            ttk.Button(toolbar, text="AI Analyze", command=self.ai_analyze_data).pack(side=tk.LEFT, padx=2)
        
    def create_main_area(self):
        """Create the main content area."""
        # Create notebook for multiple documents
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create initial welcome tab
        welcome_frame = ttk.Frame(self.notebook)
        self.notebook.add(welcome_frame, text="Welcome")
        
        welcome_label = tk.Label(
            welcome_frame,
            text="JEdit2 - Professional Data Editor\n\n"
                 "Create a new spreadsheet or open an existing file\n"
                 "to get started with professional data editing.\n\n"
                 "Features:\n"
                 "• Professional spreadsheet interface\n"
                 "• Multiple file format support (CSV, Excel, JSON, YAML)\n"
                 "• Advanced formatting and data manipulation\n"
                 "• Memory management and caching\n"
                 f"• AI-powered data analysis{' (Available)' if self.ai_manager else ' (Not Available)'}\n\n"
                 "No LibreOffice installation required!",
            justify=tk.CENTER,
            font=("Arial", 11)
        )
        welcome_label.pack(expand=True)
        
    def create_status_bar(self):
        """Create the status bar."""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Memory indicator
        self.memory_var = tk.StringVar()
        memory_label = ttk.Label(status_frame, textvariable=self.memory_var, relief=tk.SUNKEN)
        memory_label.pack(side=tk.RIGHT)
        
        # Update memory display periodically
        self.update_memory_display()
        
    def update_memory_display(self):
        """Update memory display in status bar."""
        try:
            stats = self.memory_manager.get_memory_stats()
            used_gb = stats.used_memory / (1024**3)
            self.memory_var.set(f"Memory: {used_gb:.1f}GB")
        except Exception:
            self.memory_var.set("Memory: N/A")
        
        # Schedule next update
        self.root.after(5000, self.update_memory_display)
    
    def new_spreadsheet(self):
        """Create a new spreadsheet."""
        doc_id = f"spreadsheet_{len(self.documents) + 1}"
        
        # Create new tab
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text=f"Sheet {len(self.documents) + 1}")
        self.notebook.select(tab_frame)
        
        # Create spreadsheet grid
        grid_frame = ttk.Frame(tab_frame)
        grid_frame.pack(fill=tk.BOTH, expand=True)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(grid_frame, orient=tk.VERTICAL)
        h_scrollbar = ttk.Scrollbar(grid_frame, orient=tk.HORIZONTAL)
        
        grid = SpreadsheetGrid(grid_frame)
        grid.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        v_scrollbar.configure(command=grid.yview)
        h_scrollbar.configure(command=grid.xview)
        
        # Pack components
        grid.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Store document
        self.documents[doc_id] = {
            'type': 'spreadsheet',
            'frame': tab_frame,
            'grid': grid,
            'path': None,
            'modified': False
        }
        
        self.current_document = doc_id
        self.status_var.set(f"Created new spreadsheet: {doc_id}")
        self.logger.info(f"Created new spreadsheet: {doc_id}")
    
    def open_file(self):
        """Open an existing file."""
        file_path = filedialog.askopenfilename(
            title="Open File",
            filetypes=[
                ("All Supported", "*.csv;*.json;*.yaml;*.yml"),
                ("CSV Files", "*.csv"),
                ("JSON Files", "*.json"),
                ("YAML Files", "*.yaml;*.yml"),
            ] + ([("Excel Files", "*.xlsx;*.xls")] if EXCEL_AVAILABLE else []) + [
                ("All Files", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        try:
            # Detect format and load data
            file_format = self.format_adapter.detect_format(file_path)
            data = self.load_file_data(file_path, file_format)
            
            # Create new document
            doc_id = f"file_{len(self.documents) + 1}"
            self.create_document_from_data(doc_id, data, file_path)
            
            self.status_var.set(f"Opened: {Path(file_path).name}")
            self.logger.info(f"Opened file: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to open file: {e}")
            messagebox.showerror("Error", f"Failed to open file:\n{e}")
    
    def load_file_data(self, file_path, file_format):
        """Load data from file based on format."""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_format == 'csv':
                reader = csv.DictReader(f)
                return list(reader)
            elif file_format == 'json':
                return json.load(f)
            elif file_format in ('yaml', 'yml'):
                return yaml.safe_load(f)
        
        return {}
    
    def create_document_from_data(self, doc_id, data, file_path):
        """Create document from loaded data."""
        # Create new tab
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text=Path(file_path).name)
        self.notebook.select(tab_frame)
        
        # Create grid and load data
        grid_frame = ttk.Frame(tab_frame)
        grid_frame.pack(fill=tk.BOTH, expand=True)
        
        grid = SpreadsheetGrid(grid_frame)
        
        # Convert data to grid format
        if isinstance(data, list) and data:
            # List of dictionaries (CSV-like)
            grid_data = {}
            headers = list(data[0].keys())
            
            # Map headers to columns
            for col_idx, header in enumerate(headers[:len(grid.columns)]):
                grid.heading(grid.columns[col_idx], text=header)
            
            # Load row data
            for row_idx, row in enumerate(data[:grid.rows], 1):
                for col_idx, header in enumerate(headers[:len(grid.columns)]):
                    if header in row:
                        grid_data[(row_idx, grid.columns[col_idx])] = str(row[header])
            
            grid.data = grid_data
            
            # Update display
            for child in grid.get_children():
                row_num = int(grid.item(child, 'text'))
                values = []
                for col in grid.columns:
                    values.append(grid_data.get((row_num, col), ''))
                grid.item(child, values=values)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(grid_frame, orient=tk.VERTICAL)
        h_scrollbar = ttk.Scrollbar(grid_frame, orient=tk.HORIZONTAL)
        
        grid.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        v_scrollbar.configure(command=grid.yview)
        h_scrollbar.configure(command=grid.xview)
        
        # Pack components
        grid.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Store document
        self.documents[doc_id] = {
            'type': 'spreadsheet',
            'frame': tab_frame,
            'grid': grid,
            'path': file_path,
            'modified': False
        }
        
        self.current_document = doc_id
    
    def save_file(self):
        """Save current file."""
        if not self.current_document:
            messagebox.showwarning("Warning", "No document to save")
            return
        
        doc = self.documents[self.current_document]
        if doc['path']:
            self.save_document(doc, doc['path'])
        else:
            self.save_file_as()
    
    def save_file_as(self):
        """Save current file with new name."""
        if not self.current_document:
            messagebox.showwarning("Warning", "No document to save")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save As",
            filetypes=[
                ("CSV Files", "*.csv"),
                ("JSON Files", "*.json"),
                ("YAML Files", "*.yaml"),
            ] + ([("Excel Files", "*.xlsx")] if EXCEL_AVAILABLE else []),
            defaultextension=".csv"
        )
        
        if file_path:
            doc = self.documents[self.current_document]
            self.save_document(doc, file_path)
            doc['path'] = file_path
            
            # Update tab title
            tab_index = list(self.documents.keys()).index(self.current_document)
            self.notebook.tab(tab_index + 1, text=Path(file_path).name)  # +1 for welcome tab
    
    def save_document(self, doc, file_path):
        """Save document to file."""
        try:
            if doc['type'] == 'spreadsheet':
                data = doc['grid'].get_data_as_dict()
                file_format = self.format_adapter.detect_format(file_path)
                
                if file_format == 'csv':
                    self.save_as_csv(data, file_path)
                elif file_format == 'json':
                    self.save_as_json(data, file_path)
                elif file_format in ('yaml', 'yml'):
                    self.save_as_yaml(data, file_path)
                elif EXCEL_AVAILABLE and file_format in ('xlsx', 'xls'):
                    self.save_as_excel(data, file_path)
                
                doc['modified'] = False
                self.status_var.set(f"Saved: {Path(file_path).name}")
                
        except Exception as e:
            self.logger.error(f"Failed to save file: {e}")
            messagebox.showerror("Error", f"Failed to save file:\n{e}")
    
    def save_as_csv(self, data, file_path):
        """Save data as CSV."""
        if not data:
            return
        
        # Get all unique columns
        all_columns = set()
        for row_data in data.values():
            all_columns.update(row_data.keys())
        
        columns = sorted(all_columns)
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=columns)
            writer.writeheader()
            
            for row_num in sorted(data.keys()):
                row_data = data[row_num]
                writer.writerow(row_data)
    
    def save_as_json(self, data, file_path):
        """Save data as JSON."""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def save_as_yaml(self, data, file_path):
        """Save data as YAML."""
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
    
    # Placeholder methods for menu items
    def undo(self): pass
    def redo(self): pass
    def find_data(self): pass
    def replace_data(self): pass
    def format_bold(self):
        if self.current_document and 'grid' in self.documents[self.current_document]:
            self.documents[self.current_document]['grid'].format_bold()
    def format_italic(self): pass
    def format_currency(self):
        if self.current_document and 'grid' in self.documents[self.current_document]:
            self.documents[self.current_document]['grid'].format_currency()
    def format_percentage(self): pass
    def sort_ascending(self): pass
    def sort_descending(self): pass
    def apply_filter(self): pass
    def clear_filter(self): pass
    def import_csv(self): self.open_file()
    def export_csv(self): self.save_file_as()
    def import_excel(self): self.open_file()
    def export_excel(self): self.save_file_as()
    def ai_analyze_data(self): pass
    def ai_generate_formula(self): pass
    def ai_data_insights(self): pass
    
    def show_memory_manager(self):
        """Show memory manager dialog."""
        try:
            stats = self.memory_manager.get_memory_stats()
            messagebox.showinfo("Memory Statistics", f"Memory Stats:\n{stats}")
        except Exception as e:
            messagebox.showerror("Error", f"Memory manager error:\n{e}")
    
    def show_cache_manager(self):
        """Show cache manager dialog."""
        messagebox.showinfo("Cache Manager", "Cache management functionality")
    
    def show_configuration(self):
        """Show configuration dialog."""
        messagebox.showinfo("Configuration", "Configuration functionality")
    
    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo(
            "About JEdit2 Standalone",
            "JEdit2 - Professional Data Editor\n\n"
            "Standalone version with LibreOffice-inspired interface.\n"
            "No external dependencies required!\n\n"
            "Features:\n"
            "• Professional spreadsheet interface\n"
            "• Multiple file format support\n"
            "• Advanced data manipulation\n"
            f"• Memory management and caching\n"
            f"• AI integration: {'Available' if self.ai_manager else 'Not Available'}\n\n"
            "Version: 2.0.0-standalone"
        )
    
    def on_exit(self):
        """Handle application exit."""
        try:
            # Check for unsaved changes
            unsaved = [doc_id for doc_id, doc in self.documents.items() if doc.get('modified', False)]
            
            if unsaved:
                response = messagebox.askyesnocancel(
                    "Unsaved Changes",
                    f"You have {len(unsaved)} unsaved document(s). Save them before exit?"
                )
                if response is True:
                    for doc_id in unsaved:
                        # Auto-save or prompt for each
                        pass
                elif response is None:  # Cancel
                    return
            
            # Stop managers
            if self.memory_manager:
                self.memory_manager.stop_monitoring()
                stats = self.memory_manager.get_memory_stats()
                self.logger.info(f"Application exit. Memory stats: {stats}")
            
            self.root.quit()
            
        except Exception as e:
            self.logger.error(f"Error during exit: {e}")
            self.root.quit()
    
    def run(self):
        """Run the application."""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_exit)
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application runtime error: {e}")


def main():
    """Main entry point."""
    try:
        app = JEdit2StandaloneApp()
        app.run()
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 