#!/usr/bin/env python3
"""
Complete test for format validation functionality.
This tests the entire chain from menu click to dialog display.
"""

import sys
import os
import wx

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class FormatValidationTestApp(wx.App):
    """Test application for format validation."""
    
    def OnInit(self):
        """Initialize the test application."""
        try:
            from jedit2.main_window import MainWindow
            
            # Create main window
            self.frame = MainWindow()
            self.frame.Show()
            
            # Test format validation after a short delay
            wx.CallAfter(self.test_format_validation)
            
            return True
        except Exception as e:
            print(f"❌ Failed to initialize test app: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_format_validation(self):
        """Test the format validation functionality."""
        print("🧪 Testing Format Validation in Live Application")
        print("=" * 60)
        
        try:
            # Test 1: Check if format validation method exists
            if hasattr(self.frame, '_on_format_validation'):
                print("✅ _on_format_validation method exists")
            else:
                print("❌ _on_format_validation method missing")
                return
            
            # Test 2: Check if format validator is initialized
            if hasattr(self.frame, 'format_validator'):
                print("✅ format_validator is initialized")
            else:
                print("❌ format_validator not initialized")
                return
            
            # Test 3: Create a test file with JSON content
            print("\n📄 Creating test JSON content...")
            
            # Get the current page or create one
            if self.frame.notebook.GetPageCount() == 0:
                # Create a new tab
                from jedit2.view_container import ViewContainer
                new_tab = ViewContainer(self.frame.notebook)
                self.frame.notebook.AddPage(new_tab, "test.json")
            
            current_page = self.frame.notebook.GetCurrentPage()
            
            # Add some test JSON content
            test_json = '''{
    "name": "Test File",
    "version": 1.0,
    "features": ["validation", "testing"],
    "valid": true
}'''
            
            # Set content in text view
            if hasattr(current_page, 'text_view'):
                current_page.text_view.SetText(test_json)
                print("✅ Test JSON content added to text view")
            else:
                print("❌ No text view available")
                return
            
            # Test 4: Simulate format validation
            print("\n🔧 Testing format validation...")
            
            # Create a mock event
            event = wx.CommandEvent(wx.wxEVT_COMMAND_MENU_SELECTED, self.frame.ID_DATA_VALIDATION)
            
            # Call the format validation method directly
            try:
                self.frame._on_format_validation(event)
                print("✅ Format validation method executed successfully")
                print("   (Dialog should have appeared)")
            except Exception as e:
                print(f"❌ Format validation failed: {e}")
                import traceback
                traceback.print_exc()
                return
            
            # Test 5: Test with invalid JSON
            print("\n📄 Testing with invalid JSON...")
            
            invalid_json = '''{
    "name": "Test File",
    "version": 1.0,
    "features": ["validation", "testing"
    "valid": true
}'''  # Missing closing bracket
            
            current_page.text_view.SetText(invalid_json)
            
            try:
                self.frame._on_format_validation(event)
                print("✅ Invalid JSON validation executed successfully")
                print("   (Error dialog should have appeared)")
            except Exception as e:
                print(f"❌ Invalid JSON validation failed: {e}")
                import traceback
                traceback.print_exc()
            
            print(f"\n🎉 Format validation testing completed!")
            print(f"💡 Manual test steps:")
            print(f"   1. Open a JSON file (or create content in text view)")
            print(f"   2. Go to Tools > Format Validation")
            print(f"   3. Check if validation dialog appears")
            print(f"   4. Try with valid and invalid JSON/CSV/YAML files")
            
            # Close after a delay
            wx.CallLater(5000, self.frame.Close)
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()


def test_format_validation_components():
    """Test individual components before running the full app."""
    print("🔧 Testing Format Validation Components")
    print("=" * 50)
    
    try:
        # Test FormatValidator
        from jedit2.utils.format_validator import FormatValidator, ValidationResult
        validator = FormatValidator()
        
        # Test JSON validation
        result = validator.validate_json('{"test": "valid"}')
        print(f"✅ Valid JSON test: {'PASS' if result.is_valid else 'FAIL'}")
        
        result = validator.validate_json('{"test": "invalid"')  # Missing closing brace
        print(f"✅ Invalid JSON test: {'PASS' if not result.is_valid else 'FAIL'}")
        
        # Test FormatValidationDialog
        from jedit2.utils.format_validation_dialog import FormatValidationDialog
        print("✅ FormatValidationDialog imported successfully")
        
        # Test MainWindow import
        from jedit2.main_window import MainWindow
        print("✅ MainWindow imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Format Validation Complete Test Suite")
    print("=" * 60)
    
    # Test components first
    if not test_format_validation_components():
        print("❌ Component tests failed. Exiting.")
        sys.exit(1)
    
    print("\n🖼️ Starting GUI Test...")
    print("=" * 30)
    
    # Run the GUI test
    app = FormatValidationTestApp()
    app.MainLoop()
