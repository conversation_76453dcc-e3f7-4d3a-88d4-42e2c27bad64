"""Data indexing module for JEdit2.

This module provides data indexing functionality for efficient data access and search.
"""

import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Iterator
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict
import bisect
import logging
from datetime import datetime


class IndexType(Enum):
    """Type of index."""
    BTREE = "btree"
    HASH = "hash"
    SPATIAL = "spatial"
    FULLTEXT = "fulltext"


@dataclass
class IndexStats:
    """Index statistics."""
    name: str
    type: IndexType
    size: int
    memory_usage: int
    creation_time: float
    last_update_time: float
    hit_count: int
    miss_count: int
    avg_search_time: float
    avg_update_time: float


class DataIndex:
    """Data index for efficient data access and search."""
    
    def __init__(
        self,
        name: str,
        index_type: IndexType = IndexType.BTREE,
        max_size: Optional[int] = None,
        memory_limit: Optional[int] = None
    ) -> None:
        """Initialize the data index.
        
        Args:
            name: Index name
            index_type: Type of index
            max_size: Maximum number of entries
            memory_limit: Memory limit in bytes
        """
        self.name = name
        self.type = index_type
        self.max_size = max_size
        self.memory_limit = memory_limit
        
        # Initialize index data structure based on type
        if index_type == IndexType.BTREE:
            self._data: Dict[Any, List[Any]] = {}
        elif index_type == IndexType.HASH:
            self._data: Dict[Any, Set[Any]] = defaultdict(set)
        elif index_type == IndexType.SPATIAL:
            self._data: Dict[Tuple[float, float], List[Any]] = {}
        elif index_type == IndexType.FULLTEXT:
            self._data: Dict[str, Set[Any]] = defaultdict(set)
        
        # Initialize statistics
        self._stats = IndexStats(
            name=name,
            type=index_type,
            size=0,
            memory_usage=0,
            creation_time=time.time(),
            last_update_time=time.time(),
            hit_count=0,
            miss_count=0,
            avg_search_time=0.0,
            avg_update_time=0.0
        )
        
        # Initialize lock for thread safety
        self._lock = threading.Lock()
        
        # Initialize logger
        self._logger = logging.getLogger(f"DataIndex.{name}")
    
    def add(self, key: Any, value: Any) -> None:
        """Add a key-value pair to the index.
        
        Args:
            key: Index key
            value: Index value
        """
        with self._lock:
            start_time = time.time()
            
            # Check size limit
            if self.max_size is not None and self._stats.size >= self.max_size:
                self._logger.warning("Index size limit reached")
                return
            
            # Check memory limit
            if self.memory_limit is not None:
                current_memory = psutil.Process().memory_info().rss
                if current_memory > self.memory_limit:
                    self._optimize()
            
            # Add to index based on type
            if self.type == IndexType.BTREE:
                if key not in self._data:
                    self._data[key] = []
                self._data[key].append(value)
            elif self.type == IndexType.HASH:
                self._data[key].add(value)
            elif self.type == IndexType.SPATIAL:
                if not isinstance(key, tuple) or len(key) != 2:
                    raise ValueError("Spatial index requires (x, y) coordinates")
                if key not in self._data:
                    self._data[key] = []
                self._data[key].append(value)
            elif self.type == IndexType.FULLTEXT:
                if not isinstance(key, str):
                    raise ValueError("Fulltext index requires string keys")
                words = key.lower().split()
                for word in words:
                    self._data[word].add(value)
            
            # Update statistics
            self._stats.size += 1
            self._stats.last_update_time = time.time()
            update_time = time.time() - start_time
            self._stats.avg_update_time = (
                (self._stats.avg_update_time * (self._stats.size - 1) + update_time) /
                self._stats.size
            )
            
            # Update memory usage
            self._stats.memory_usage = psutil.Process().memory_info().rss
    
    def remove(self, key: Any, value: Optional[Any] = None) -> None:
        """Remove a key-value pair from the index.
        
        Args:
            key: Index key
            value: Index value to remove, or None to remove all values for key
        """
        with self._lock:
            start_time = time.time()
            
            # Remove from index based on type
            if self.type == IndexType.BTREE:
                if key in self._data:
                    if value is None:
                        del self._data[key]
                        self._stats.size -= len(self._data[key])
                    else:
                        self._data[key].remove(value)
                        self._stats.size -= 1
                        if not self._data[key]:
                            del self._data[key]
            elif self.type == IndexType.HASH:
                if key in self._data:
                    if value is None:
                        self._stats.size -= len(self._data[key])
                        del self._data[key]
                    else:
                        self._data[key].remove(value)
                        self._stats.size -= 1
                        if not self._data[key]:
                            del self._data[key]
            elif self.type == IndexType.SPATIAL:
                if key in self._data:
                    if value is None:
                        del self._data[key]
                        self._stats.size -= len(self._data[key])
                    else:
                        self._data[key].remove(value)
                        self._stats.size -= 1
                        if not self._data[key]:
                            del self._data[key]
            elif self.type == IndexType.FULLTEXT:
                if not isinstance(key, str):
                    raise ValueError("Fulltext index requires string keys")
                words = key.lower().split()
                for word in words:
                    if word in self._data:
                        if value is None:
                            self._stats.size -= len(self._data[word])
                            del self._data[word]
                        else:
                            self._data[word].remove(value)
                            self._stats.size -= 1
                            if not self._data[word]:
                                del self._data[word]
            
            # Update statistics
            self._stats.last_update_time = time.time()
            update_time = time.time() - start_time
            if self._stats.size > 0:
                self._stats.avg_update_time = (
                    (self._stats.avg_update_time * (self._stats.size + 1) - update_time) /
                    self._stats.size
                )
            
            # Update memory usage
            self._stats.memory_usage = psutil.Process().memory_info().rss
    
    def search(
        self,
        key: Any,
        exact: bool = True,
        limit: Optional[int] = None
    ) -> List[Any]:
        """Search for values in the index.
        
        Args:
            key: Search key
            exact: Whether to perform exact match
            limit: Maximum number of results to return
        
        Returns:
            List of matching values
        """
        with self._lock:
            start_time = time.time()
            
            # Search based on index type
            if self.type == IndexType.BTREE:
                if exact:
                    results = self._data.get(key, [])
                else:
                    results = []
                    for k, v in self._data.items():
                        if str(k).startswith(str(key)):
                            results.extend(v)
            elif self.type == IndexType.HASH:
                if exact:
                    results = list(self._data.get(key, set()))
                else:
                    results = []
                    for k, v in self._data.items():
                        if str(k).startswith(str(key)):
                            results.extend(v)
            elif self.type == IndexType.SPATIAL:
                if not isinstance(key, tuple) or len(key) != 2:
                    raise ValueError("Spatial search requires (x, y) coordinates")
                if exact:
                    results = self._data.get(key, [])
                else:
                    results = []
                    x, y = key
                    for (kx, ky), v in self._data.items():
                        if abs(kx - x) <= 1 and abs(ky - y) <= 1:
                            results.extend(v)
            elif self.type == IndexType.FULLTEXT:
                if not isinstance(key, str):
                    raise ValueError("Fulltext search requires string keys")
                words = key.lower().split()
                results = []
                for word in words:
                    if exact:
                        results.extend(self._data.get(word, set()))
                    else:
                        for k, v in self._data.items():
                            if k.startswith(word):
                                results.extend(v)
            
            # Update statistics
            search_time = time.time() - start_time
            if results:
                self._stats.hit_count += 1
            else:
                self._stats.miss_count += 1
            self._stats.avg_search_time = (
                (self._stats.avg_search_time * (self._stats.hit_count + self._stats.miss_count - 1) +
                 search_time) / (self._stats.hit_count + self._stats.miss_count)
            )
            
            # Apply limit
            if limit is not None:
                results = results[:limit]
            
            return results
    
    def clear(self) -> None:
        """Clear the index."""
        with self._lock:
            self._data.clear()
            self._stats.size = 0
            self._stats.memory_usage = psutil.Process().memory_info().rss
            self._stats.last_update_time = time.time()
    
    def get_stats(self) -> IndexStats:
        """Get index statistics.
        
        Returns:
            Index statistics
        """
        with self._lock:
            return self._stats
    
    def _optimize(self) -> None:
        """Optimize the index."""
        with self._lock:
            # Force garbage collection
            gc.collect()
            
            # Clear unused entries
            if self.type == IndexType.BTREE:
                for key in list(self._data.keys()):
                    if not self._data[key]:
                        del self._data[key]
            elif self.type == IndexType.HASH:
                for key in list(self._data.keys()):
                    if not self._data[key]:
                        del self._data[key]
            elif self.type == IndexType.SPATIAL:
                for key in list(self._data.keys()):
                    if not self._data[key]:
                        del self._data[key]
            elif self.type == IndexType.FULLTEXT:
                for key in list(self._data.keys()):
                    if not self._data[key]:
                        del self._data[key]
            
            # Update statistics
            self._stats.size = sum(len(v) for v in self._data.values())
            self._stats.memory_usage = psutil.Process().memory_info().rss
            self._stats.last_update_time = time.time()
    
    def __len__(self) -> int:
        """Get the number of entries in the index.
        
        Returns:
            Number of entries
        """
        with self._lock:
            return self._stats.size
    
    def __iter__(self) -> Iterator[Tuple[Any, Any]]:
        """Iterate over key-value pairs in the index.
        
        Returns:
            Iterator of (key, value) pairs
        """
        with self._lock:
            if self.type == IndexType.BTREE:
                for key, values in self._data.items():
                    for value in values:
                        yield key, value
            elif self.type == IndexType.HASH:
                for key, values in self._data.items():
                    for value in values:
                        yield key, value
            elif self.type == IndexType.SPATIAL:
                for key, values in self._data.items():
                    for value in values:
                        yield key, value
            elif self.type == IndexType.FULLTEXT:
                for key, values in self._data.items():
                    for value in values:
                        yield key, value 