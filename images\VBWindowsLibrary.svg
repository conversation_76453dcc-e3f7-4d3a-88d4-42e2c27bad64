<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-blue-10{fill: #005dba; opacity: 0.1;}.light-blue{fill: #005dba; opacity: 1;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}</style>
  </defs>
  <title>VBWindowsLibrary</title>
  <g id="canvas" class="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <path class="light-blue-10" d="M.5,9.5h8v6H.5Z" />
    <path class="light-blue" d="M8.5,9H.5L0,9.5v6l.5.5h8l.5-.5v-6ZM8,15H1V11H8Z" />
    <path class="light-defaultgrey-10" d="M15.39,13l-1.93.52L10.61,2.9l1.93-.52ZM2.5,8h2V2.5h-2Zm4,0h2V2.5h-2Z" />
    <path class="light-defaultgrey" d="M13.03,2.25l-.62-.35-1.93.51-.35.62,2.84,10.62.62.35,1.93-.51.35-.62Zm.78,10.66L11.22,3.25l.97-.26,2.59,9.66ZM4.5,2l.5.5V8H4V3H3V8H2V2.5L2.5,2Zm4,0,.5.5V8.09L8.91,8H8V3H7V8H6V2.5L6.5,2Z" />
  </g>
</svg>
