#!/usr/bin/env python3
"""
Test the newly implemented "quick win" format corrections.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_quick_wins():
    """Test the newly implemented quick win corrections."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    test_cases = [
        # JSON Quick Wins
        {
            'name': 'JSON: Single Quotes → Double Quotes',
            'content': "{'name': 'John', 'age': 30}",
            'file_type': 'json',
            'expected_change': 'single quotes'
        },
        {
            'name': 'JSON: Python Boolean → JSON Boolean',
            'content': '{"active": True, "deleted": False, "data": None}',
            'file_type': 'json',
            'expected_change': 'Python values'
        },
        {
            'name': 'JSON: Combined Python Style',
            'content': "{'name': '<PERSON>', 'active': True, 'data': None}",
            'file_type': 'json',
            'expected_change': 'quotes and values'
        },
        
        # YAML Quick Wins
        {
            'name': 'YAML: Missing Space After Colon',
            'content': 'name:John\nage:30\ncity:Boston',
            'file_type': 'yaml',
            'expected_change': 'colon spacing'
        },
        {
            'name': 'YAML: Mixed Colon Issues',
            'content': 'name: John\nage:30\ncity: Boston\nactive:true',
            'file_type': 'yaml',
            'expected_change': 'colon spacing'
        },
        
        # CSV Quick Wins
        {
            'name': 'CSV: Tab Delimited → Comma Delimited',
            'content': 'name\tage\tcity\nJohn\t30\tBoston\nJane\t25\tNYC',
            'file_type': 'csv',
            'expected_change': 'tab delimiters'
        },
        
        # Markdown Quick Wins
        {
            'name': 'Markdown: Standardize List Markers',
            'content': '- item1\n* item2\n+ item3\n- item4',
            'file_type': 'md',
            'expected_change': 'list markers'
        },
        {
            'name': 'Markdown: Mixed List Issues',
            'content': '* First item\n+ Second item\n-Third item\n* Fourth item',
            'file_type': 'md',
            'expected_change': 'list markers and spacing'
        }
    ]
    
    print("🚀 TESTING QUICK WIN IMPROVEMENTS")
    print("=" * 60)
    print("Testing newly implemented format corrections...")
    print()
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['name']}")
        print("-" * 50)
        print(f"   Original: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], case['file_type'])
        print(f"   Correctable: {validation_result.is_correctable}")
        
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], case['file_type'])
            success = correction_result.success
            
            if success:
                print(f"   ✅ SUCCESS")
                print(f"   Changes: {correction_result.changes_made}")
                print(f"   Result: {repr(correction_result.corrected_content[:100])}...")
                
                # Check if expected change was made
                expected_found = any(case['expected_change'].lower() in change.lower() 
                                   for change in correction_result.changes_made)
                if expected_found:
                    print(f"   ✅ Expected change detected!")
                else:
                    print(f"   ⚠️  Expected change '{case['expected_change']}' not found")
                    success = False
                    
            else:
                print(f"   ❌ FAILED")
                print(f"   Errors: {correction_result.errors}")
        else:
            print(f"   ❌ Not detected as correctable")
            success = False
        
        results.append({
            'name': case['name'],
            'success': success
        })
        print()
    
    # Summary
    print("=" * 60)
    print("📊 QUICK WINS TEST SUMMARY")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for r in results if r['success'])
    
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL QUICK WINS IMPLEMENTED SUCCESSFULLY!")
        print("The format correction system now handles these common cases:")
        for result in results:
            if result['success']:
                print(f"  ✅ {result['name']}")
    else:
        print(f"\n⚠️  {total - passed} quick win(s) need more work:")
        for result in results:
            if not result['success']:
                print(f"  ❌ {result['name']}")
    
    print(f"\n📈 COVERAGE IMPROVEMENT")
    print("-" * 30)
    print("Before: 33.9% coverage (19/56 cases)")
    estimated_new_coverage = 19 + passed  # Rough estimate
    estimated_total = 56  # From previous analysis
    print(f"After: ~{(estimated_new_coverage/estimated_total)*100:.1f}% coverage ({estimated_new_coverage}/{estimated_total} cases)")
    print(f"Improvement: +{passed} cases covered")
    
    return passed == total

if __name__ == "__main__":
    success = test_quick_wins()
    
    if success:
        print("\n🚀 Ready to implement more improvements!")
        print("Next: Run full coverage analysis to measure progress")
    else:
        print("\n🔧 Some quick wins need refinement")
        print("Review failed cases and adjust implementations")
