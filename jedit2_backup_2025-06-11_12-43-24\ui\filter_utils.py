"""Utility functions for advanced filtering functionality."""

import re
from datetime import datetime, date
from typing import List, Optional, Union, Any, Set
import logging

logger = logging.getLogger(__name__)


def detect_column_type(values: List[str]) -> str:
    """
    Detect the primary data type of a column based on its values.
    
    Args:
        values: List of string values from the column
        
    Returns:
        'text', 'number', 'date', or 'mixed'
    """
    if not values:
        return 'text'
    
    # Remove blanks for analysis
    non_blank_values = [v for v in values if v and v.strip()]
    
    if not non_blank_values:
        return 'text'
    
    # Count successful parsings
    numbers = 0
    dates = 0
    total = len(non_blank_values)
    
    for value in non_blank_values[:min(100, total)]:  # Sample first 100 for performance
        if parse_number(value) is not None:
            numbers += 1
        elif parse_date(value) is not None:
            dates += 1
    
    # Determine type based on success rates
    number_ratio = numbers / total
    date_ratio = dates / total
    
    if number_ratio >= 0.8:
        return 'number'
    elif date_ratio >= 0.8:
        return 'date'
    elif number_ratio >= 0.3 or date_ratio >= 0.3:
        return 'mixed'
    else:
        return 'text'


def parse_number(value: str) -> Optional[float]:
    """
    Parse a string value as a number.
    
    Args:
        value: String value to parse
        
    Returns:
        Float value or None if not a number
    """
    if not value or not value.strip():
        return None
    
    # Clean the value
    cleaned = value.strip()
    
    # Handle common number formats
    patterns = [
        # Basic number (1234.56, -1234.56)
        r'^-?\d+\.?\d*$',
        # Number with thousands separator (1,234.56)
        r'^-?\d{1,3}(,\d{3})*\.?\d*$',
        # Percentage (12.34%)
        r'^-?\d+\.?\d*%$',
        # Currency ($1,234.56, €1.234,56)
        r'^[$€£¥]?-?\d{1,3}(,\d{3})*\.?\d*$',
        # Scientific notation (1.23E+10, 1.23e-5)
        r'^-?\d+\.?\d*[eE][+-]?\d+$',
    ]
    
    for pattern in patterns:
        if re.match(pattern, cleaned):
            try:
                # Remove currency symbols and percentages
                clean_num = re.sub(r'[$€£¥%,]', '', cleaned)
                result = float(clean_num)
                
                # Handle percentage
                if cleaned.endswith('%'):
                    result = result / 100
                
                return result
            except ValueError:
                continue
    
    return None


def parse_date(value: str) -> Optional[datetime]:
    """
    Parse a string value as a date.
    
    Args:
        value: String value to parse
        
    Returns:
        datetime object or None if not a date
    """
    if not value or not value.strip():
        return None
    
    cleaned = value.strip()
    
    # Common date formats to try
    date_formats = [
        '%Y-%m-%d',         # 2023-12-25
        '%m/%d/%Y',         # 12/25/2023
        '%d/%m/%Y',         # 25/12/2023
        '%Y/%m/%d',         # 2023/12/25
        '%m-%d-%Y',         # 12-25-2023
        '%d-%m-%Y',         # 25-12-2023
        '%B %d, %Y',        # December 25, 2023
        '%b %d, %Y',        # Dec 25, 2023
        '%d %B %Y',         # 25 December 2023
        '%d %b %Y',         # 25 Dec 2023
        '%Y-%m-%d %H:%M:%S', # 2023-12-25 15:30:00
        '%m/%d/%Y %H:%M',   # 12/25/2023 15:30
        '%Y-%m-%dT%H:%M:%S', # ISO format
    ]
    
    for fmt in date_formats:
        try:
            return datetime.strptime(cleaned, fmt)
        except ValueError:
            continue
    
    # Try parsing with dateutil if available
    try:
        from dateutil import parser
        return parser.parse(cleaned)
    except (ImportError, ValueError, TypeError):
        pass
    
    return None


def get_unique_values(column_data: List[str]) -> List[str]:
    """
    Get sorted unique values from column data.
    
    Args:
        column_data: List of column values
        
    Returns:
        Sorted list of unique values
    """
    unique_set: Set[str] = set()
    has_blanks = False
    
    for value in column_data:
        if not value or value.isspace():
            has_blanks = True
        else:
            unique_set.add(str(value).strip())
    
    # Sort values based on detected type
    column_type = detect_column_type(column_data)
    
    if column_type == 'number':
        # Sort numerically
        def numeric_key(v: str) -> float:
            num = parse_number(v)
            return num if num is not None else float('inf')
        
        unique_list = sorted(unique_set, key=numeric_key)
    elif column_type == 'date':
        # Sort chronologically
        def date_key(v: str) -> datetime:
            dt = parse_date(v)
            return dt if dt is not None else datetime.max
        
        unique_list = sorted(unique_set, key=date_key)
    else:
        # Sort alphabetically (case-insensitive)
        unique_list = sorted(unique_set, key=str.lower)
    
    # Add blanks at the end if found
    if has_blanks:
        unique_list.append("(Blanks)")
    
    return unique_list


def is_blank(value: Any) -> bool:
    """
    Check if a value is considered blank.
    
    Args:
        value: Value to check
        
    Returns:
        True if the value is blank
    """
    if value is None:
        return True
    
    if isinstance(value, str):
        return not value or value.isspace()
    
    return False


def normalize_value(value: str, data_type: str) -> Union[str, float, datetime]:
    """
    Normalize a value based on its data type for comparison.
    
    Args:
        value: String value to normalize
        data_type: Type of data ('text', 'number', 'date', 'mixed')
        
    Returns:
        Normalized value for comparison
    """
    if is_blank(value):
        return ""
    
    if data_type == 'number':
        num = parse_number(value)
        return num if num is not None else value
    elif data_type == 'date':
        dt = parse_date(value)
        return dt if dt is not None else value
    else:
        return str(value).strip()


def format_value_for_display(value: Any) -> str:
    """
    Format a value for display in the UI.
    
    Args:
        value: Value to format
        
    Returns:
        Formatted string for display
    """
    if value is None or (isinstance(value, str) and not value.strip()):
        return "(Blank)"
    
    if isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S') if value.time() != value.time().replace(hour=0, minute=0, second=0, microsecond=0) else value.strftime('%Y-%m-%d')
    elif isinstance(value, date):
        return value.strftime('%Y-%m-%d')
    elif isinstance(value, float):
        # Format numbers nicely
        if value.is_integer():
            return str(int(value))
        else:
            return f"{value:.6g}"  # Use general format to avoid too many decimals
    else:
        return str(value)


def calculate_statistics(values: List[str], data_type: str) -> dict:
    """
    Calculate statistics for a column of values.
    
    Args:
        values: List of column values
        data_type: Type of data in the column
        
    Returns:
        Dictionary with statistics
    """
    stats = {
        'total_count': len(values),
        'unique_count': len(set(values)),
        'blank_count': sum(1 for v in values if is_blank(v))
    }
    
    non_blank_values = [v for v in values if not is_blank(v)]
    
    if data_type == 'number' and non_blank_values:
        numbers = [parse_number(v) for v in non_blank_values]
        valid_numbers = [n for n in numbers if n is not None]
        
        if valid_numbers:
            stats.update({
                'min_value': min(valid_numbers),
                'max_value': max(valid_numbers),
                'average': sum(valid_numbers) / len(valid_numbers),
                'number_count': len(valid_numbers)
            })
    
    elif data_type == 'date' and non_blank_values:
        dates = [parse_date(v) for v in non_blank_values]
        valid_dates = [d for d in dates if d is not None]
        
        if valid_dates:
            stats.update({
                'earliest_date': min(valid_dates),
                'latest_date': max(valid_dates),
                'date_count': len(valid_dates)
            })
    
    return stats 