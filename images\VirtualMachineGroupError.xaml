<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-red" Color="#c50b17" Opacity="1" />
      <SolidColorBrush x:Key="white" Color="#ffffff" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M11.5,1.5h-3a1,1,0,0,0-1,1V3a2.734,2.734,0,0,1,.5.05A2.527,2.527,0,0,1,9,3.5a2.37,2.37,0,0,1,.5.5A2.5,2.5,0,0,1,10,5.5V7.41a5.532,5.532,0,0,1,1-.31A4.712,4.712,0,0,1,12,7a4.07,4.07,0,0,1,.5.03V2.5A1,1,0,0,0,11.5,1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11.5,1h-3A1.5,1.5,0,0,0,7,2.5V3h.5a2.734,2.734,0,0,1,.5.05V2.5A.5.5,0,0,1,8.5,2h3a.5.5,0,0,1,.5.5V7a4.07,4.07,0,0,1,.5.03,4.293,4.293,0,0,1,.5.07V2.5A1.5,1.5,0,0,0,11.5,1Z" />
                <DrawingGroup Opacity="{DynamicResource cls-1}">
                  <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9,3v.5a2.37,2.37,0,0,1,.5.5H11V3Zm1,4v.41a5.532,5.532,0,0,1,1-.31V7Z" />
                </DrawingGroup>
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.41,14a5.532,5.532,0,0,1-.31-1H3.5L3,12.5v-1H2.5l-1,3H7.67C7.58,14.34,7.49,14.17,7.41,14Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.67,14.5c-.09-.16-.18-.33-.26-.5H2.19l.67-2H3V11H2.5l-.47.34-1,3L1.5,15H8A4.322,4.322,0,0,1,7.67,14.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.5,4.5h-3a1,1,0,0,0-1,1v7H7.03A4.07,4.07,0,0,1,7,12,4.941,4.941,0,0,1,8,9a5.469,5.469,0,0,1,.5-.58V5.5A1,1,0,0,0,7.5,4.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.5,4h-3A1.5,1.5,0,0,0,3,5.5v7l.5.5H7.1a4.293,4.293,0,0,1-.07-.5A4.07,4.07,0,0,1,7,12H4V5.5A.5.5,0,0,1,4.5,5h3a.5.5,0,0,1,.5.5V9a5.469,5.469,0,0,1,.5-.58A4.113,4.113,0,0,1,9,8V5.5A1.5,1.5,0,0,0,7.5,4Z" />
              <GeometryDrawing Brush="{DynamicResource light-red}" Geometry="F1M12,8a4,4,0,1,0,4,4A4,4,0,0,0,12,8Z" />
              <GeometryDrawing Brush="{DynamicResource white}" Geometry="F1M12.707,12l1.147,1.146-.708.708L12,12.707l-1.146,1.147-.708-.708L11.293,12l-1.147-1.146.708-.708L12,11.293l1.146-1.147.708.708Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5,10v1H7V10ZM5,6V7H7V6Z" />
              </DrawingGroup>
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
