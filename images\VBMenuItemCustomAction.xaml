<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M15.5,3.5V13H9V10H4V9h.5V.5h6v3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15.5,3H11V.5L10.5,0h-6L4,.5V10H5V4H15v9H9v1h6.5l.5-.5V3.5ZM10,3H5V1h5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,6H6V5h8Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5,7v3H15V7Zm9,2H6V8h8Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,13.5l-3,2v-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2,11.5v4l.777.416,3-2v-.832l-3-2Zm1,.935L4.6,13.5,3,14.565ZM0,11H1v5H0Zm7,0v5H8V11Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
