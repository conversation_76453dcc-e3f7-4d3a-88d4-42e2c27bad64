<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M.5,9.5h8v6H.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8.5,9H.5L0,9.5v6l.5.5h8l.5-.5v-6ZM8,15H1V11H8Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M15.39,13l-1.93.52L10.61,2.9l1.93-.52ZM2.5,8h2V2.5h-2Zm4,0h2V2.5h-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.03,2.25l-.62-.35-1.93.51-.35.62,2.84,10.62.62.35,1.93-.51.35-.62Zm.78,10.66L11.22,3.25l.97-.26,2.59,9.66ZM4.5,2l.5.5V8H4V3H3V8H2V2.5L2.5,2Zm4,0,.5.5V8.09L8.91,8H8V3H7V8H6V2.5L6.5,2Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
