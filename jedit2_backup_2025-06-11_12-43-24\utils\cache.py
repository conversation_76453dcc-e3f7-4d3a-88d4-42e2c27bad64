"""Caching system for JEdit2.

This module provides a caching system for JEdit2, supporting different cache types
and memory management.
"""

import time
import threading
import weakref
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from enum import Enum
from dataclasses import dataclass
from collections import OrderedDict


class CacheType(Enum):
    """Cache types."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    FIFO = "fifo"  # First In First Out
    TTL = "ttl"  # Time To Live


@dataclass
class CacheEntry:
    """Cache entry."""
    key: Any
    value: Any
    size: int
    created: float
    accessed: float
    hits: int = 0
    expires: Optional[float] = None


class Cache:
    """Cache for JEdit2."""
    
    def __init__(
        self,
        max_size: int = 1000,
        max_memory: int = 100 * 1024 * 1024,  # 100 MB
        cache_type: CacheType = CacheType.LRU,
        ttl: Optional[float] = None
    ) -> None:
        """Initialize the cache.
        
        Args:
            max_size: Maximum number of entries
            max_memory: Maximum memory usage in bytes
            cache_type: Cache type
            ttl: Time to live in seconds
        """
        self.max_size = max_size
        self.max_memory = max_memory
        self.cache_type = cache_type
        self.ttl = ttl
        
        self.entries: Dict[Any, CacheEntry] = {}
        self.lock = threading.RLock()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "memory_usage": 0
        }
    
    def get(self, key: Any) -> Optional[Any]:
        """Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value if found and not expired, None otherwise
        """
        with self.lock:
            entry = self.entries.get(key)
            
            if entry is None:
                self.stats["misses"] += 1
                return None
            
            # Check if expired
            if entry.expires and time.time() > entry.expires:
                self._remove_entry(key)
                self.stats["misses"] += 1
                return None
            
            # Update entry
            entry.accessed = time.time()
            entry.hits += 1
            self.stats["hits"] += 1
            
            return entry.value
    
    def set(
        self,
        key: Any,
        value: Any,
        size: Optional[int] = None,
        ttl: Optional[float] = None
    ) -> None:
        """Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            size: Size of value in bytes
            ttl: Time to live in seconds
        """
        with self.lock:
            # Calculate size if not provided
            if size is None:
                try:
                    size = len(str(value))
                except (TypeError, ValueError):
                    size = 1
            
            # Check if we need to evict entries
            while (
                len(self.entries) >= self.max_size or
                self.stats["memory_usage"] + size > self.max_memory
            ):
                self._evict_entry()
            
            # Create entry
            entry = CacheEntry(
                key=key,
                value=value,
                size=size,
                created=time.time(),
                accessed=time.time(),
                expires=time.time() + (ttl or self.ttl) if ttl or self.ttl else None
            )
            
            # Add entry
            self.entries[key] = entry
            self.stats["memory_usage"] += size
    
    def remove(self, key: Any) -> None:
        """Remove a value from the cache.
        
        Args:
            key: Cache key
        """
        with self.lock:
            self._remove_entry(key)
    
    def clear(self) -> None:
        """Clear the cache."""
        with self.lock:
            self.entries.clear()
            self.stats["memory_usage"] = 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get cache statistics.
        
        Returns:
            Cache statistics
        """
        with self.lock:
            return dict(self.stats)
    
    def _remove_entry(self, key: Any) -> None:
        """Remove an entry from the cache.
        
        Args:
            key: Cache key
        """
        entry = self.entries.pop(key, None)
        if entry:
            self.stats["memory_usage"] -= entry.size
    
    def _evict_entry(self) -> None:
        """Evict an entry from the cache."""
        if not self.entries:
            return
        
        # Find entry to evict
        if self.cache_type == CacheType.LRU:
            key = min(
                self.entries.keys(),
                key=lambda k: self.entries[k].accessed
            )
        elif self.cache_type == CacheType.LFU:
            key = min(
                self.entries.keys(),
                key=lambda k: self.entries[k].hits
            )
        elif self.cache_type == CacheType.FIFO:
            key = min(
                self.entries.keys(),
                key=lambda k: self.entries[k].created
            )
        elif self.cache_type == CacheType.TTL:
            key = min(
                self.entries.keys(),
                key=lambda k: self.entries[k].expires or float("inf")
            )
        else:
            key = next(iter(self.entries))
        
        # Remove entry
        self._remove_entry(key)
        self.stats["evictions"] += 1


class CacheManager:
    """Cache manager for JEdit2."""
    
    def __init__(self) -> None:
        """Initialize the cache manager."""
        self.caches: Dict[str, Cache] = {}
        self.lock = threading.RLock()
    
    def create_cache(
        self,
        name: str,
        max_size: int = 1000,
        max_memory: int = 100 * 1024 * 1024,  # 100 MB
        cache_type: CacheType = CacheType.LRU,
        ttl: Optional[float] = None
    ) -> Cache:
        """Create a new cache.
        
        Args:
            name: Cache name
            max_size: Maximum number of entries
            max_memory: Maximum memory usage in bytes
            cache_type: Cache type
            ttl: Time to live in seconds
            
        Returns:
            Created cache
        """
        with self.lock:
            if name in self.caches:
                raise ValueError(f"Cache {name} already exists")
            
            cache = Cache(
                max_size=max_size,
                max_memory=max_memory,
                cache_type=cache_type,
                ttl=ttl
            )
            self.caches[name] = cache
            return cache
    
    def get_cache(self, name: str) -> Optional[Cache]:
        """Get a cache.
        
        Args:
            name: Cache name
            
        Returns:
            Cache if found, None otherwise
        """
        with self.lock:
            return self.caches.get(name)
    
    def remove_cache(self, name: str) -> None:
        """Remove a cache.
        
        Args:
            name: Cache name
        """
        with self.lock:
            self.caches.pop(name, None)
    
    def clear_all(self) -> None:
        """Clear all caches."""
        with self.lock:
            for cache in self.caches.values():
                cache.clear()
    
    def get_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics for all caches.
        
        Returns:
            Cache statistics
        """
        with self.lock:
            return {
                name: cache.get_stats()
                for name, cache in self.caches.items()
            } 