#!/usr/bin/env python3
"""
Test script to verify format validation functionality.
"""

import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_format_validator():
    """Test the FormatValidator class directly."""
    print("🧪 Testing Format Validator")
    print("=" * 50)
    
    try:
        from jedit2.utils.format_validator import FormatValidator
        
        validator = FormatValidator()
        print("✅ FormatValidator imported successfully")
        
        # Test JSON validation
        print("\n📄 Testing JSON validation:")
        
        # Valid JSON
        valid_json = '{"name": "test", "value": 123}'
        result = validator.validate_json(valid_json)
        print(f"   Valid JSON: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        # Invalid JSON
        invalid_json = '{"name": "test", "value": 123'  # Missing closing brace
        result = validator.validate_json(invalid_json)
        print(f"   Invalid JSON: {'✅ PASS' if not result.is_valid else '❌ FAIL'}")
        print(f"   Error message: {result.errors[0] if result.errors else 'None'}")
        
        # Test CSV validation
        print("\n📊 Testing CSV validation:")
        
        valid_csv = "name,age,city\nJohn,25,NYC\nJane,30,LA"
        result = validator.validate_csv(valid_csv)
        print(f"   Valid CSV: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        # Test YAML validation
        print("\n📝 Testing YAML validation:")
        
        valid_yaml = "name: test\nvalue: 123\nlist:\n  - item1\n  - item2"
        result = validator.validate_yaml(valid_yaml)
        print(f"   Valid YAML: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        # Test the main validate_format method
        print("\n🔧 Testing validate_format method:")
        
        result = validator.validate_format(valid_json, "json")
        print(f"   JSON via validate_format: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        result = validator.validate_format(valid_csv, "csv")
        print(f"   CSV via validate_format: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        result = validator.validate_format(valid_yaml, "yaml")
        print(f"   YAML via validate_format: {'✅ PASS' if result.is_valid else '❌ FAIL'}")
        
        print("\n🎉 FormatValidator tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ FormatValidator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_format_validation_dialog():
    """Test the FormatValidationDialog class."""
    print("\n🖼️ Testing Format Validation Dialog")
    print("=" * 50)
    
    try:
        from jedit2.utils.format_validation_dialog import FormatValidationDialog
        from jedit2.utils.format_validator import ValidationResult
        
        print("✅ FormatValidationDialog imported successfully")
        
        # Create a test validation result
        test_result = ValidationResult(
            is_valid=False,
            errors=["JSON Syntax Error: Expecting ',' delimiter"],
            warnings=["Trailing commas detected"],
            line_number=5,
            column_number=12
        )
        
        print("✅ ValidationResult created successfully")
        print("   - Has errors and warnings")
        print("   - Has line and column numbers")
        
        print("\n🎉 FormatValidationDialog tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ FormatValidationDialog test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_window_integration():
    """Test the main window integration."""
    print("\n🏠 Testing Main Window Integration")
    print("=" * 50)
    
    try:
        # Check if the main window has the format validation method
        from jedit2.main_window import MainWindow
        
        # Check if the method exists
        if hasattr(MainWindow, '_on_format_validation'):
            print("✅ _on_format_validation method exists")
        else:
            print("❌ _on_format_validation method missing")
            return False
        
        # Check if the format_validator is initialized
        import inspect
        init_source = inspect.getsource(MainWindow.__init__)
        if "format_validator = FormatValidator()" in init_source:
            print("✅ format_validator initialization found")
        else:
            print("❌ format_validator initialization missing")
            return False
        
        # Check if the menu binding exists
        if "ID_DATA_VALIDATION" in init_source or "_on_format_validation" in init_source:
            print("✅ Menu binding appears to be present")
        else:
            print("❌ Menu binding may be missing")
        
        print("\n🎉 Main Window integration tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Main Window integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_test_files():
    """Create test files for manual testing."""
    print("\n📁 Creating Test Files")
    print("=" * 50)
    
    test_files = {
        "test_valid.json": '{\n  "name": "Test File",\n  "version": 1.0,\n  "features": ["validation", "testing"]\n}',
        "test_invalid.json": '{\n  "name": "Test File",\n  "version": 1.0,\n  "features": ["validation", "testing"]\n',  # Missing closing brace
        "test_valid.csv": "name,age,city\nJohn,25,New York\nJane,30,Los Angeles\nBob,35,Chicago",
        "test_invalid.csv": "name,age,city\nJohn,25\nJane,30,Los Angeles,Extra\nBob,35,Chicago",  # Inconsistent columns
        "test_valid.yaml": "name: Test File\nversion: 1.0\nfeatures:\n  - validation\n  - testing",
        "test_invalid.yaml": "name: Test File\nversion: 1.0\nfeatures:\n\t- validation\n\t- testing",  # Tabs instead of spaces
    }
    
    created_files = []
    
    for filename, content in test_files.items():
        try:
            with open(filename, 'w') as f:
                f.write(content)
            created_files.append(filename)
            print(f"✅ Created {filename}")
        except Exception as e:
            print(f"❌ Failed to create {filename}: {e}")
    
    if created_files:
        print(f"\n📋 Test files created: {len(created_files)}")
        print("   You can now:")
        print("   1. Open these files in JEdit2")
        print("   2. Go to Tools > Format Validation")
        print("   3. Test the validation functionality")
        
        print(f"\n🧹 To clean up later, run:")
        print(f"   rm {' '.join(created_files)}")
    
    return len(created_files) > 0


if __name__ == "__main__":
    print("🚀 Format Validation Test Suite")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_format_validator()
    test2_passed = test_format_validation_dialog()
    test3_passed = test_main_window_integration()
    test4_passed = create_test_files()
    
    print(f"\n📊 Test Results:")
    print(f"   Format Validator:     {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Validation Dialog:    {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Main Window Integration: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    print(f"   Test Files Created:   {'✅ PASS' if test4_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print(f"\n🎉 All core tests passed! Format validation should be working.")
        print(f"💡 If the menu item still doesn't work, try:")
        print(f"   1. Check if there are any error messages in the console")
        print(f"   2. Verify that a file is open when clicking Format Validation")
        print(f"   3. Try with the test files created above")
    else:
        print(f"\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)
