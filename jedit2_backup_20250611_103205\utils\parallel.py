"""Parallel processing module for JEdit2.

This module provides a thread pool and task management for concurrent file and data operations.
"""

import threading
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Callable, Any, Optional, Dict, List
import queue


class ParallelTask:
    """Represents a parallel task."""
    def __init__(self, func: Callable, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.future: Optional[Future] = None
        self.result: Any = None
        self.exception: Optional[Exception] = None
        self.status: str = "pending"

    def run(self):
        self.status = "running"
        try:
            self.result = self.func(*self.args, **self.kwargs)
            self.status = "completed"
        except Exception as e:
            self.exception = e
            self.status = "failed"


class ParallelManager:
    """Manages parallel tasks using a thread pool."""
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: List[ParallelTask] = []
        self.lock = threading.Lock()

    def submit(self, func: Callable, *args, **kwargs) -> ParallelTask:
        task = ParallelTask(func, *args, **kwargs)
        with self.lock:
            task.future = self.executor.submit(task.run)
            self.tasks.append(task)
        return task

    def shutdown(self, wait: bool = True):
        self.executor.shutdown(wait=wait)

    def get_status(self) -> List[Dict[str, Any]]:
        with self.lock:
            return [
                {
                    "func": t.func.__name__,
                    "status": t.status,
                    "result": t.result,
                    "exception": str(t.exception) if t.exception else None
                }
                for t in self.tasks
            ]

    def wait_all(self, timeout: Optional[float] = None):
        with self.lock:
            futures = [t.future for t in self.tasks if t.future]
        for f in as_completed(futures, timeout=timeout):
            pass

    def clear_completed(self):
        with self.lock:
            self.tasks = [t for t in self.tasks if t.status not in ("completed", "failed")] 