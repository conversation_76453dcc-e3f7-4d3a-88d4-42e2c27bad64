<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13,10.5H12v2l-.5.5h-.98l.33,1,.17.5H15Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.45,10.28,13,10H12v1h.69l1.5,3H10.85l.17.5.1.31-.1.19H15l.45-.72Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.5,1.5h-4a1,1,0,0,0-1,1V6.23L6,6.06l.94-.31,1.38.68L8.84,8l.34,1,.33,1,.34,1,.33,1,.17.5H11.5V2.5A1,1,0,0,0,10.5,1.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.5,1h-4A1.5,1.5,0,0,0,5,2.5V6.08l.23.24.27-.09L6,6.06V2.5A.5.5,0,0,1,6.5,2h4a.5.5,0,0,1,.5.5V12h-.82l.17.5.17.5h.98l.5-.5V2.5A1.5,1.5,0,0,0,10.5,1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.559,14.891l-1.9.635L5.124,7.939l1.9-.634ZM4.5,15.5H.5v-8h4Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9.51,10l.34,1H10V10ZM8.84,8l.34,1H10V8ZM7,4V5h3V4Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.5,7.146,6.862,6.83l-1.9.635h0L4.5,7H.5L0,7.5v8l.5.5h4l.5-.5V9.145l2.188,6.539L7.821,16l1.9-.635.315-.632ZM2,15H1V8H2Zm2,0H3V8H4Zm3.978-.107L5.757,8.255l.948-.318,2.221,6.639Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
