"""Professional Icon Management System for JEdit2.

This module provides high-quality professional icons to replace the basic
bitmap functions, solving the "visual disaster" problem in JEdit2's ribbon interface.
"""

import wx
import os
from pathlib import Path
from typing import Optional, Dict, Tuple


class ProfessionalIconManager:
    """Manages professional icons for JEdit2's interface."""
    
    def __init__(self):
        """Initialize the icon manager."""
        self.base_path = Path(__file__).parent.parent.parent / "images"
        self._icon_cache: Dict[str, wx.Bitmap] = {}
        
        # Define icon mappings for ribbon actions
        self.icon_map = {
            # Clipboard operations
            'paste': 'PasteTable.png',
            'copy': 'Copy.png', 
            'cut': 'Cut.png',
            
            # Text formatting
            'bold': 'Bold.png',
            'italic': 'Italic.png',
            'underline': 'Underline.png',
            
            # Alignment
            'align_left': 'AlignLeft.png',
            'align_center': 'AlignCenter.png', 
            'align_right': 'AlignRight.png',
            
            # Edit operations
            'undo': 'Undo.png',
            'redo': 'Redo.png',
            'find': 'Find.png',
            
            # Insert/Delete
            'insert': 'Add.png',
            'delete': 'Delete.png',
            'insert_row': 'AddRow.png',
            'insert_column': 'Column.png', 
            'delete_row': 'DeleteTableRow.png',
            'delete_column': 'DeleteTableColumn.png',
            
            # View
            'view_switch': 'SwitchView.png',
            'grid': 'Grid.png',
            'transpose': 'Transpose.png',
            
            # Number formatting
            'comma': 'NumberFormat.png',
            'percent': 'Percent.png',
            'increase_decimal': 'IncreaseDecimals.png', 
            'decrease_decimal': 'DecreaseDecimals.png',
        }
        
        # Alternative icon names if primary ones don't exist
        self.fallback_map = {
            'paste': ['Paste.svg', 'PasteTable.png'],
            'copy': ['Copy.svg', 'CopyItem.png'],
            'cut': ['Cut.svg'],
            'bold': ['Bold.svg'],
            'italic': ['Italic.svg'],
            'underline': ['Underline.svg'],
            'align_left': ['AlignLeft.svg', 'AlignToLeft.png'],
            'align_center': ['AlignCenter.svg', 'AlignToCenter.png'],
            'align_right': ['AlignRight.svg', 'AlignToRight.png'], 
            'undo': ['Undo.svg', 'UndoNoColor.png'],
            'redo': ['Redo.svg'],
            'find': ['Find.svg', 'Search.png'],
            'insert': ['Add.svg', 'AddButton.png', 'Insert.png'],
            'delete': ['Delete.svg', 'DeleteCell.png', 'Remove.png'],
            'view_switch': ['Switch.png', 'ToggleView.png'],
            'transpose': ['Matrix.png', 'Rotate.png'],
            'comma': ['Format.png'],
            'percent': ['Percentage.png'],
            'increase_decimal': ['Plus.png', 'Add.png'],
            'decrease_decimal': ['Minus.png', 'Remove.png'],
        }

    def get_icon_path(self, icon_name: str) -> Optional[Path]:
        """Find the best available icon file for the given name."""
        # Try primary icon name
        primary_path = self.base_path / self.icon_map.get(icon_name, f"{icon_name}.png")
        if primary_path.exists():
            return primary_path
            
        # Try fallback options
        fallbacks = self.fallback_map.get(icon_name, [])
        for fallback in fallbacks:
            fallback_path = self.base_path / fallback
            if fallback_path.exists():
                return fallback_path
                
        # Try common variations
        variations = [
            f"{icon_name.title()}.png",
            f"{icon_name.title()}.svg", 
            f"{icon_name.upper()}.png",
            f"{icon_name.lower()}.png",
        ]
        
        for variation in variations:
            var_path = self.base_path / variation
            if var_path.exists():
                return var_path
                
        return None

    def load_icon(self, icon_name: str, size: Tuple[int, int] = (16, 16)) -> Optional[wx.Bitmap]:
        """Load a professional icon with the specified size."""
        cache_key = f"{icon_name}_{size[0]}x{size[1]}"
        
        # Check cache first
        if cache_key in self._icon_cache:
            return self._icon_cache[cache_key]
            
        # Find the icon file
        icon_path = self.get_icon_path(icon_name)
        if not icon_path:
            # Silently return None - fallback will handle this
            return None
            
        try:
            # Load the image
            if icon_path.suffix.lower() == '.svg':
                # For SVG files, we need special handling
                bitmap = self._load_svg_icon(icon_path, size)
            else:
                # For PNG/other formats
                image = wx.Image(str(icon_path))
                if image.IsOk():
                    # Scale to desired size
                    image = image.Scale(size[0], size[1], wx.IMAGE_QUALITY_HIGH)
                    bitmap = wx.Bitmap(image)
                else:
                    print(f"Failed to load image: {icon_path}")
                    return None
                    
            # Cache the result
            self._icon_cache[cache_key] = bitmap
            return bitmap
            
        except Exception as e:
            print(f"Error loading icon {icon_name} from {icon_path}: {e}")
            return None

    def _load_svg_icon(self, svg_path: Path, size: Tuple[int, int]) -> Optional[wx.Bitmap]:
        """Load SVG icon (fallback to PNG if SVG fails)."""
        try:
            # Try to use wxPython's SVG support if available
            with open(svg_path, 'r', encoding='utf-8') as f:
                svg_data = f.read()
            
            # This might fail on older wxPython versions
            bundle = wx.BitmapBundle.FromSVG(svg_data, wx.Size(*size))
            return bundle.GetBitmap(wx.Size(*size))
            
        except Exception:
            # Fallback: look for a PNG version
            png_path = svg_path.with_suffix('.png')
            if png_path.exists():
                image = wx.Image(str(png_path))
                if image.IsOk():
                    image = image.Scale(size[0], size[1], wx.IMAGE_QUALITY_HIGH)
                    return wx.Bitmap(image)
            return None

    def get_professional_bitmap(self, icon_name: str, size: Tuple[int, int] = (16, 16)) -> wx.Bitmap:
        """Get a professional bitmap, with fallback to basic bitmap if needed."""
        bitmap = self.load_icon(icon_name, size)
        if bitmap and bitmap.IsOk():
            return bitmap
            
        # Fallback to basic bitmap functions (existing code)
        # Silently use fallback - this is expected behavior
        return self._create_fallback_bitmap(icon_name, size)

    def _create_fallback_bitmap(self, icon_name: str, size: Tuple[int, int]) -> wx.Bitmap:
        """Create a simple fallback bitmap if professional icon fails."""
        width, height = size
        bitmap = wx.Bitmap(width, height)
        dc = wx.MemoryDC(bitmap)
        
        # Use system button face color for seamless integration
        system_color = wx.SystemSettings.GetColour(wx.SYS_COLOUR_BTNFACE)
        dc.SetBackground(wx.Brush(system_color))
        dc.Clear()
        
        # Draw a simple generic icon instead of text
        dc.SetPen(wx.Pen(wx.Colour(100, 100, 100), 1))
        dc.SetBrush(wx.Brush(wx.Colour(150, 150, 150)))
        
        # Draw a simple rectangle as a generic icon
        margin = 2
        dc.DrawRectangle(margin, margin, width - 2*margin, height - 2*margin)
        
        dc.SelectObject(wx.NullBitmap)
        return bitmap


# Global icon manager instance
_icon_manager = None

def get_icon_manager() -> ProfessionalIconManager:
    """Get the global icon manager instance."""
    global _icon_manager
    if _icon_manager is None:
        _icon_manager = ProfessionalIconManager()
    return _icon_manager

def get_professional_icon(icon_name: str, size: Tuple[int, int] = (16, 16)) -> wx.Bitmap:
    """Convenience function to get a professional icon."""
    return get_icon_manager().get_professional_bitmap(icon_name, size)

# Convenience functions for common sizes
def get_small_icon(icon_name: str) -> wx.Bitmap:
    """Get 16x16 icon."""
    return get_professional_icon(icon_name, (16, 16))

def get_medium_icon(icon_name: str) -> wx.Bitmap:
    """Get 24x24 icon."""  
    return get_professional_icon(icon_name, (24, 24))

def get_large_icon(icon_name: str) -> wx.Bitmap:
    """Get 32x32 icon."""
    return get_professional_icon(icon_name, (32, 32)) 