"""White Ribbon Art Provider for JEdit2.

This module provides a custom ribbon art provider that creates a clean
white background for the ribbon interface, making it look more modern
and professional.
"""

import wx
import wx.lib.agw.ribbon as RB


class WhiteRibbonArtProvider(RB.RibbonAUIArtProvider):
    """Custom ribbon art provider with white background."""
    
    def __init__(self):
        """Initialize the white ribbon art provider."""
        super().__init__()
        
        # Set white colors for ribbon backgrounds
        self.SetColour(RB.RIBBON_ART_PANEL_BACKGROUND_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PANEL_BACKGROUND_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PANEL_BACKGROUND_TOP_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PANEL_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        
        # Set white for ribbon bar background
        self.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_TOP_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        
        # Set white for page backgrounds
        self.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_TOP_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        
        # Keep subtle borders but make them light grey
        self.SetColour(RB.RIBBON_ART_PANEL_BORDER_COLOUR, wx.Colour(220, 220, 220))
        self.SetColour(RB.RIBBON_ART_BAR_BORDER_COLOUR, wx.Colour(220, 220, 220))
        
        # Keep text readable
        self.SetColour(RB.RIBBON_ART_PANEL_LABEL_COLOUR, wx.Colour(80, 80, 80))
        self.SetColour(RB.RIBBON_ART_PAGE_LABEL_COLOUR, wx.Colour(80, 80, 80))
        
        # Make button backgrounds white/very light grey
        self.SetColour(RB.RIBBON_ART_BUTTON_BAR_LABEL_COLOUR, wx.Colour(80, 80, 80))
        
        # Tab colors - white with subtle hover effects
        self.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_COLOUR, wx.Colour(245, 245, 245))
        self.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_TOP_COLOUR, wx.Colour(250, 250, 250))
        self.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        
        # Active tab should be pure white
        self.SetColour(RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_COLOUR, wx.Colour(255, 255, 255))
        self.SetColour(RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(255, 255, 255))
        
        # Hover effects - very light blue
        self.SetColour(RB.RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR, wx.Colour(240, 248, 255))
        self.SetColour(RB.RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR, wx.Colour(248, 252, 255))
        self.SetColour(RB.RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_COLOUR, wx.Colour(245, 250, 255))
        self.SetColour(RB.RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR, wx.Colour(250, 253, 255))


class ModernWhiteRibbonArtProvider(RB.RibbonMSWArtProvider):
    """Alternative modern white ribbon art provider using MSW style as base."""
    
    def __init__(self):
        """Initialize the modern white ribbon art provider."""
        super().__init__()
        
        # Override colors to create clean white theme
        self._ribbon_colours = {
            RB.RIBBON_ART_PANEL_BACKGROUND_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_PANEL_BACKGROUND_GRADIENT_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_BAR_BACKGROUND_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_BAR_BACKGROUND_GRADIENT_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_PAGE_BACKGROUND_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_PANEL_BORDER_COLOUR: wx.Colour(225, 225, 225),
            RB.RIBBON_ART_PANEL_LABEL_COLOUR: wx.Colour(68, 68, 68),
            RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR: wx.Colour(255, 255, 255),
            RB.RIBBON_ART_TAB_BACKGROUND_COLOUR: wx.Colour(240, 240, 240),
            RB.RIBBON_ART_TAB_BACKGROUND_GRADIENT_COLOUR: wx.Colour(250, 250, 250),
            RB.RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR: wx.Colour(235, 245, 255),
            RB.RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR: wx.Colour(245, 250, 255),
        }
        
        # Apply the colors
        for color_id, color_value in self._ribbon_colours.items():
            self.SetColour(color_id, color_value)


def get_white_ribbon_art_provider(style: str = "aui"):
    """Get a white ribbon art provider.
    
    Args:
        style: "aui" for AUI-based or "msw" for MSW-based
        
    Returns:
        A ribbon art provider with white theme
    """
    if style.lower() == "msw":
        return ModernWhiteRibbonArtProvider()
    else:
        return WhiteRibbonArtProvider() 