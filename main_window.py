﻿"""Main window for JEdit2.

This module provides the main window class for JEdit2.
"""

import os
import re
import time
import json
import math
from typing import (
    Optional,
    List,
    Any,
    Dict,
    Tuple,
    Union,
    Callable,
)

import wx
import wx.grid
import wx.lib.agw.ribbon as RB
import wx.stc as stc

from jedit2.utils.error_handler import <PERSON>rror<PERSON>and<PERSON>
from jedit2.utils.error_report import ErrorReportDialog
from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_validation_dialog import FormatValidationDialog
from jedit2.utils.clipboard_manager import ClipboardManager
from jedit2.utils.data_index import DataIndex
from jedit2.utils.index_dialog import IndexDialog
from jedit2.utils.memory_manager import MemoryManager
from jedit2.utils.memory_dialog import MemoryDialog
from jedit2.utils.cache import CacheManager
from jedit2.utils.cache_dialog import CacheDialog
from jedit2.utils.lazy_loader import <PERSON><PERSON><PERSON><PERSON><PERSON>
from jedit2.utils.lazy_dialog import LazyDialog
from jedit2.utils.parallel import ParallelManager
from jedit2.formats import csv_handler, json_handler, yaml_handler
from jedit2.utils.config_manager import ConfigManager
from jedit2.utils.ai_manager import AIManager
from jedit2.ui.ai_settings_dialog import AISettingsDialog
from jedit2.ui.advanced_filter_dialog import AdvancedFilterDialog
from jedit2.ui.filter_utils import detect_column_type
from jedit2.ui.filter_criteria import (
    FilterCriterion,
    ValueListCriterion,
    TextFilterCriterion,
    NumberFilterCriterion,
)


# Custom menu IDs will be generated inside the MainWindow's __init__ method.


def get_ribbon_background_color() -> wx.Colour:
    """Get the appropriate background color for ribbon icons."""
    # Return white to match our custom white ribbon theme
    return wx.Colour(255, 255, 255)


def create_text_bitmap(text: str, size: int = 16, min_width: int = 24) -> wx.Bitmap:
    """Creates a bitmap from a text string, suitable for a ribbon icon."""
    # Calculate appropriate width based on text length
    temp_dc = wx.MemoryDC()
    try:
        font = wx.SystemSettings.GetFont(wx.SYS_DEFAULT_GUI_FONT)
    except AttributeError:
        font = wx.Font(
            7, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL
        )

    font.SetPointSize(7)
    temp_dc.SetFont(font)
    text_width, text_height = temp_dc.GetTextExtent(text)

    # Use larger of minimum width or text width + padding
    bitmap_width = max(min_width, text_width + 6)
    bitmap_height = size

    bitmap = wx.Bitmap(bitmap_width, bitmap_height)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Set font and text color
    dc.SetFont(font)
    dc.SetTextForeground(wx.SystemSettings.GetColour(wx.SYS_COLOUR_BTNTEXT))

    # Center text
    text_x = (bitmap_width - text_width) // 2
    text_y = (bitmap_height - text_height) // 2
    dc.DrawText(text, text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_bold_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a bold 'B' icon inspired by Excel."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create bold font for the 'B'
    font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 0, 0))  # Black

    # Draw 'B' centered
    text_width, text_height = dc.GetTextExtent("B")
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    dc.DrawText("B", text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_italic_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates an italic 'I' icon inspired by Excel."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create italic font for the 'I'
    font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_ITALIC, wx.FONTWEIGHT_NORMAL)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 0, 0))  # Black

    # Draw 'I' centered
    text_width, text_height = dc.GetTextExtent("I")
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    dc.DrawText("I", text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_underline_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates an underlined 'U' icon inspired by Excel."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create font for the 'U'
    font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 0, 0))  # Black

    # Draw 'U' centered
    text_width, text_height = dc.GetTextExtent("U")
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2 - 1
    dc.DrawText("U", text_x, text_y)

    # Draw underline
    dc.SetPen(wx.Pen(wx.Colour(0, 0, 0), 1))
    underline_y = text_y + text_height - 1
    dc.DrawLine(text_x, underline_y, text_x + text_width, underline_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_alignment_bitmap(alignment: str, size: int = 16) -> wx.Bitmap:
    """Creates alignment icons inspired by Excel."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Set drawing color
    dc.SetPen(wx.Pen(wx.Colour(0, 0, 0), 1))
    dc.SetBrush(wx.Brush(wx.Colour(0, 0, 0)))

    # Draw lines representing text alignment
    margin = 2
    line_height = 2
    line_spacing = 3

    if alignment == "left":
        # Left aligned lines (different lengths)
        lines = [10, 8, 12, 6]
    elif alignment == "center":
        # Center aligned lines
        lines = [8, 10, 6, 8]
        margin = 4
    else:  # right
        # Right aligned lines
        lines = [8, 10, 6, 12]

    for i, length in enumerate(lines):
        y = margin + i * line_spacing
        if alignment == "left":
            x = margin
        elif alignment == "center":
            x = (size - length) // 2
        else:  # right
            x = size - margin - length
        dc.DrawRectangle(x, y, length, line_height)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_table_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a table/grid icon inspired by Excel."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Set drawing color
    dc.SetPen(wx.Pen(wx.Colour(0, 0, 0), 1))

    # Draw grid
    margin = 2
    grid_size = size - 2 * margin
    cell_size = grid_size // 3

    # Draw vertical lines
    for i in range(4):
        x = margin + i * cell_size
        dc.DrawLine(x, margin, x, margin + grid_size)

    # Draw horizontal lines
    for i in range(4):
        y = margin + i * cell_size
        dc.DrawLine(margin, y, margin + grid_size, y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_comma_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a comma formatting icon (thousands separator)."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create font
    font = wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 0, 0))  # Black

    # Draw comma-separated number
    text = "1,000"
    text_width, text_height = dc.GetTextExtent(text)
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    dc.DrawText(text, text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_percent_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a percentage formatting icon."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create font
    font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 0, 0))  # Black

    # Draw percentage symbol
    text = "%"
    text_width, text_height = dc.GetTextExtent(text)
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    dc.DrawText(text, text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_dollar_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a dollar/currency formatting icon."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Create font
    font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
    dc.SetFont(font)
    dc.SetTextForeground(wx.Colour(0, 128, 0))  # Green

    # Draw dollar symbol
    text = "$"
    text_width, text_height = dc.GetTextExtent(text)
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    dc.DrawText(text, text_x, text_y)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_manage_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a cleaner, anti-aliased management/settings icon."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking and clearing
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Use GraphicsContext for anti-aliased drawing
    gc = wx.GraphicsContext.Create(dc)
    if gc:
        gc.SetAntialiasMode(wx.ANTIALIAS_DEFAULT)
        center = size / 2.0
        outer_radius = size / 2.0 - 1.0
        inner_radius = outer_radius * 0.6
        hole_radius = outer_radius * 0.3
        num_teeth = 8

        # Define gear path
        path = gc.CreatePath()

        # Create the teeth and body
        for i in range(num_teeth):
            angle = i * 2 * math.pi / num_teeth

            # Start of valley
            path.AddArc(
                center, center, inner_radius, angle, angle + math.pi / num_teeth, True
            )

            # Tooth
            p_start_angle = angle + math.pi / num_teeth
            p_end_angle = angle + 2 * math.pi / num_teeth - math.pi / num_teeth

            path.AddArc(center, center, outer_radius, p_start_angle, p_end_angle, True)

        path.CloseSubpath()

        # Draw gear
        gc.SetPen(wx.Pen(wx.Colour(40, 40, 40), 1))
        gc.SetBrush(wx.Brush(wx.Colour(110, 110, 110)))
        gc.FillPath(path)
        gc.StrokePath(path)

        # Draw the central hole (using magenta to punch it out with the mask)
        gc.SetPen(wx.TRANSPARENT_PEN)
        gc.SetBrush(wx.Brush(wx.Colour(255, 0, 255)))
        gc.DrawEllipse(
            center - hole_radius, center - hole_radius, hole_radius * 2, hole_radius * 2
        )

    dc.SelectObject(wx.NullBitmap)

    # Create mask
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_view_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a view/switch icon with two overlapping rectangles."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    # Use a magenta background for masking
    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    # Set drawing colors
    dc.SetPen(wx.Pen(wx.Colour(0, 0, 0), 1))
    dc.SetBrush(wx.Brush(wx.Colour(220, 220, 220)))

    # Draw first rectangle (background)
    rect1_size = size // 2
    dc.DrawRectangle(2, 2, rect1_size, rect1_size)

    # Draw second rectangle (foreground, slightly offset)
    dc.SetBrush(wx.Brush(wx.Colour(100, 150, 200)))
    offset = size // 4
    dc.DrawRectangle(2 + offset, 2 + offset, rect1_size, rect1_size)

    dc.SelectObject(wx.NullBitmap)

    # Create mask from the magenta background
    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_transpose_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a cleaner transpose icon with a curved arrow."""
    bitmap = wx.Bitmap(size, size)
    dc = wx.MemoryDC(bitmap)

    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    gc = wx.GraphicsContext.Create(dc)
    if gc:
        gc.SetAntialiasMode(wx.ANTIALIAS_DEFAULT)

        # Document 1 (Horizontal lines)
        gc.SetPen(wx.Pen(wx.Colour(60, 60, 60), 1))
        gc.SetBrush(wx.Brush(wx.Colour(240, 240, 240)))
        gc.DrawRoundedRectangle(1, 1, 8, 10, 1)
        gc.SetPen(wx.Pen(wx.Colour(120, 120, 120), 1))
        for i in range(3):
            path = gc.CreatePath()
            path.MoveToPoint(3, 4 + i * 2)
            path.AddLineToPoint(7, 4 + i * 2)
            gc.StrokePath(path)

        # Document 2 (Vertical lines)
        gc.SetPen(wx.Pen(wx.Colour(60, 60, 60), 1))
        gc.SetBrush(wx.Brush(wx.Colour(240, 240, 240)))
        gc.DrawRoundedRectangle(7, 5, 8, 10, 1)
        gc.SetPen(wx.Pen(wx.Colour(120, 120, 120), 1))
        for i in range(3):
            path = gc.CreatePath()
            path.MoveToPoint(9 + i * 2, 7)
            path.AddLineToPoint(9 + i * 2, 13)
            gc.StrokePath(path)

        # Curved Arrow
        gc.SetPen(wx.Pen(wx.Colour(0, 100, 200), 2))
        path = gc.CreatePath()
        path.MoveToPoint(6, 12)
        path.AddCurveToPoint(10, 14, 10, 2, 6, 4)

        # Arrowhead
        arrowhead = gc.CreatePath()
        arrowhead.MoveToPoint(6, 4)
        arrowhead.AddLineToPoint(4.5, 6)
        arrowhead.AddLineToPoint(7.5, 5.5)
        arrowhead.CloseSubpath()
        gc.SetBrush(wx.Brush(wx.Colour(0, 100, 200)))
        gc.FillPath(arrowhead)

    dc.SelectObject(wx.NullBitmap)

    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_increase_decimal_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a cleaner bitmap for increasing decimal places."""
    width = 48  # A bit wider for clarity
    bitmap = wx.Bitmap(width, size)
    dc = wx.MemoryDC(bitmap)

    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    gc = wx.GraphicsContext.Create(dc)
    if gc:
        gc.SetAntialiasMode(wx.ANTIALIAS_DEFAULT)
        font = wx.SystemSettings.GetFont(wx.SYS_DEFAULT_GUI_FONT)
        font.SetPointSize(7)
        gc.SetFont(font, wx.BLACK)

        # Draw text ".0" -> ".00"
        gc.DrawText(".0", 2, 3)
        gc.DrawText(".00", 28, 3)

        # Arrow
        gc.SetPen(wx.Pen(wx.Colour(0, 128, 0), 2))
        path = gc.CreatePath()
        path.MoveToPoint(16, 8)
        path.AddLineToPoint(24, 8)
        gc.StrokePath(path)

        # Arrowhead
        arrowhead = gc.CreatePath()
        arrowhead.MoveToPoint(24, 8)
        arrowhead.AddLineToPoint(21, 5)
        arrowhead.AddLineToPoint(21, 11)
        arrowhead.CloseSubpath()
        gc.SetBrush(wx.Brush(wx.Colour(0, 128, 0)))
        gc.FillPath(arrowhead)

    dc.SelectObject(wx.NullBitmap)

    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


def create_decrease_decimal_bitmap(size: int = 16) -> wx.Bitmap:
    """Creates a cleaner bitmap for decreasing decimal places."""
    width = 48  # A bit wider for clarity
    bitmap = wx.Bitmap(width, size)
    dc = wx.MemoryDC(bitmap)

    dc.SetBackground(wx.Brush(wx.Colour(255, 0, 255)))
    dc.Clear()

    gc = wx.GraphicsContext.Create(dc)
    if gc:
        gc.SetAntialiasMode(wx.ANTIALIAS_DEFAULT)
        font = wx.SystemSettings.GetFont(wx.SYS_DEFAULT_GUI_FONT)
        font.SetPointSize(7)
        gc.SetFont(font, wx.BLACK)

        # Draw text ".00" -> ".0"
        gc.DrawText(".00", 2, 3)
        gc.DrawText(".0", 32, 3)

        # Arrow
        gc.SetPen(wx.Pen(wx.Colour(200, 0, 0), 2))
        path = gc.CreatePath()
        path.MoveToPoint(20, 8)
        path.AddLineToPoint(28, 8)
        gc.StrokePath(path)

        # Arrowhead
        arrowhead = gc.CreatePath()
        arrowhead.MoveToPoint(28, 8)
        arrowhead.AddLineToPoint(25, 5)
        arrowhead.AddLineToPoint(25, 11)
        arrowhead.CloseSubpath()
        gc.SetBrush(wx.Brush(wx.Colour(200, 0, 0)))
        gc.FillPath(arrowhead)

    dc.SelectObject(wx.NullBitmap)

    image = bitmap.ConvertToImage()
    image.SetMaskColour(255, 0, 255)
    return wx.Bitmap(image)


class ViewContainer(wx.Panel):
    """A container panel that holds and switches between a text and a grid view."""

    def __init__(self, parent: wx.Notebook):
        super().__init__(parent)
        self.notebook = parent

        self.sizer = wx.BoxSizer(wx.VERTICAL)

        self.text_view = stc.StyledTextCtrl(self)
        self.grid_view = SpreadsheetView(self)

        self.sizer.Add(self.text_view, 1, wx.EXPAND)
        self.sizer.Add(self.grid_view, 1, wx.EXPAND)
        self.SetSizer(self.sizer)

        # Start with text view visible, grid hidden
        self.grid_view.Hide()
        self.current_view = "text"

    def switch_view(self):
        """Toggles visibility between the text and grid views."""
        if self.current_view == "text":
            self.text_view.Hide()
            self.grid_view.Show()
            self.current_view = "grid"
        else:
            self.grid_view.Hide()
            self.text_view.Show()
            self.current_view = "text"
        self.Layout()


class SpreadsheetView(wx.grid.Grid):
    """A view for editing spreadsheets."""

    def __init__(self, parent: wx.Window, data_format: str = "csv") -> None:
        super().__init__(parent)
        self.data_format = data_format  # Store the original format
        self.undo_stack = []  # Stack for undo operations
        self.redo_stack = []  # Stack for redo operations
        self.max_undo_levels = 50  # Maximum undo levels

        self.active_filters: Dict[int, str] = {}
        self.advanced_filters: Dict[int, FilterCriterion] = {}
        self.original_data: Optional[List[List[str]]] = None
        self.sort_state: Optional[Tuple[int, bool]] = None  # (col_index, ascending)

        self.Bind(wx.grid.EVT_GRID_LABEL_RIGHT_CLICK, self._on_label_right_click)
        self.Bind(wx.grid.EVT_GRID_CELL_RIGHT_CLICK, self._on_cell_right_click)
        self.Bind(wx.grid.EVT_GRID_COL_AUTO_SIZE, self._on_col_auto_size)

    def set_data(self, data: List[List[str]]) -> None:
        """Sets the grid's master data and updates the view."""
        self.original_data = [row[:] for row in data]
        # Reset filters and sorting when new data is loaded
        self.active_filters.clear()
        self.advanced_filters.clear()
        self.sort_state = None
        self._update_view()

    def _update_view(self) -> None:
        """Applies current sorting and filtering to original_data and updates the
        grid display."""
        if self.original_data is None:
            self._update_grid_display([])
            return

        data_to_display = [row[:] for row in self.original_data]

        if self.sort_state:
            col, asc = self.sort_state

            def sort_key(row):
                if col >= len(row):
                    return (2, None)

                val = row[col]

                if not val or val.isspace():
                    return (2, None)

                try:
                    return (0, float(val.replace(",", "")))
                except ValueError:
                    return (1, val.lower())

            data_to_display.sort(key=sort_key, reverse=not asc)

        # Apply advanced filters first (they're more comprehensive)
        if self.advanced_filters:
            filtered_data = []
            for row_data in data_to_display:
                match = True
                for col_idx, criterion in self.advanced_filters.items():
                    if col_idx < len(row_data):
                        cell_value = row_data[col_idx]
                        # Detect column type for this filter
                        column_data = [
                            row[col_idx]
                            for row in self.original_data
                            if col_idx < len(row)
                        ]
                        data_type = detect_column_type(column_data)

                        if not criterion.matches(cell_value, data_type):
                            match = False
                            break
                    else:
                        # Column doesn't exist, treat as blank
                        if not criterion.matches("", "text"):
                            match = False
                            break
                if match:
                    filtered_data.append(row_data)
            data_to_display = filtered_data
        elif self.active_filters:
            # Fallback to simple filters for backward compatibility
            filtered_data = []
            for row_data in data_to_display:
                match = True
                for col_idx, filter_text in self.active_filters.items():
                    if not (
                        0 <= col_idx < len(row_data)
                        and filter_text in row_data[col_idx].lower()
                    ):
                        match = False
                        break
                if match:
                    filtered_data.append(row_data)
            data_to_display = filtered_data

        self._update_grid_display(data_to_display)

    def _update_grid_display(self, data: List[List[str]]):
        """Internal method to push data to the wx.Grid UI."""
        is_created = self.GetTable() is not None

        self.BeginBatch()
        try:
            new_rows = len(data)
            # For columns, use the original data structure to maintain column count
            # when no rows match filter
            if new_rows > 0:
                new_cols = max(len(row) for row in data)
            elif self.original_data and len(self.original_data) > 0:
                # No filtered rows, but use original data column count to maintain
                # grid structure
                new_cols = max(len(row) for row in self.original_data)
            else:
                new_cols = 0

            if not is_created:
                self.CreateGrid(new_rows, new_cols)
            else:
                # Resize rows
                current_rows = self.GetNumberRows()
                if current_rows > new_rows:
                    self.DeleteRows(new_rows, current_rows - new_rows)
                if current_rows < new_rows:
                    self.AppendRows(new_rows - current_rows)

                # Resize columns
                current_cols = self.GetNumberCols()
                if current_cols > new_cols:
                    self.DeleteCols(new_cols, current_cols - new_cols)
                if current_cols < new_cols:
                    self.AppendCols(new_cols - current_cols)

                self.ClearGrid()

            # Fill the resized grid with new data
            for r_idx, row_data in enumerate(data):
                for c_idx, cell_data in enumerate(row_data):
                    if r_idx < self.GetNumberRows() and c_idx < self.GetNumberCols():
                        self.SetCellValue(r_idx, c_idx, str(cell_data))

            # Update visual filter indicators
            self._update_filter_indicators()
        finally:
            self.EndBatch()
            self.ForceRefresh()

    def _on_col_auto_size(self, event: wx.grid.GridSizeEvent) -> None:
        """Autosize column on double-click of the divider."""
        col = event.GetRowOrCol()
        if col >= 0:
            # Prevent the default auto-sizing behavior
            event.Veto()
            self._auto_size_column_to_content(col)
        else:
            event.Skip()

    def _auto_size_column_to_content(self, col: int) -> None:
        """Auto-size column based on content width."""
        if not self.original_data:
            return

        # Get all data for this column (from original data, not filtered view)
        column_values = []

        # Add the column header
        if col < self.GetNumberCols():
            header_label = self.GetColLabelValue(col)
            if header_label:
                column_values.append(header_label)

        # Add all values from the column (including filtered out ones)
        for row_data in self.original_data:
            if col < len(row_data):
                column_values.append(str(row_data[col]))

        if not column_values:
            return

        # Calculate the maximum width needed
        dc = wx.MemoryDC()
        dc.SelectObject(wx.Bitmap(1, 1))

        # Get the default font
        font = self.GetDefaultCellFont()
        dc.SetFont(font)

        max_width = 0
        for value in column_values:
            if value:  # Skip empty values
                text_width, _ = dc.GetTextExtent(str(value))
                max_width = max(max_width, text_width)

        # Add some padding (similar to what Excel does)
        padding = 20  # pixels
        optimal_width = max_width + padding

        # Set a reasonable minimum and maximum width
        min_width = 30
        max_width_limit = 400

        final_width = max(min_width, min(optimal_width, max_width_limit))

        # Set the column width
        self.SetColSize(col, final_width)
        self.ForceRefresh()

    def _on_label_right_click(self, event: wx.grid.GridEvent) -> None:
        """Handle right-clicks on grid labels (both row and column)."""
        col = event.GetCol()
        row = event.GetRow()

        if col >= 0 and row == -1:
            # Column label clicked
            self._on_col_label_click(col)
        elif row >= 0 and col == -1:
            # Row label clicked
            self._on_row_label_click(row)
        # If both are -1, it's the corner (do nothing)

    def _on_col_label_click(self, col: int) -> None:
        """Handle clicks on column labels to show a sort/filter/edit menu."""

        menu = wx.Menu()
        ID_COPY_COL = wx.NewId()
        ID_PASTE_COL = wx.NewId()
        ID_INSERT_LEFT = wx.NewId()
        ID_INSERT_RIGHT = wx.NewId()
        ID_DELETE_COL = wx.NewId()
        ID_SORT_ASC = wx.NewId()
        ID_SORT_DESC = wx.NewId()
        ID_FILTER = wx.NewId()
        ID_CLEAR_FILTER = wx.NewId()

        # Copy/Paste options
        menu.Append(ID_COPY_COL, "Copy Column")
        menu.Append(ID_PASTE_COL, "Paste Column")
        menu.AppendSeparator()

        # Insert/Delete options
        menu.Append(ID_INSERT_LEFT, "Insert Column Left")
        menu.Append(ID_INSERT_RIGHT, "Insert Column Right")
        menu.Append(ID_DELETE_COL, "Delete Column")
        menu.AppendSeparator()

        # Sort options
        menu.Append(ID_SORT_ASC, "Sort Ascending")
        menu.Append(ID_SORT_DESC, "Sort Descending")
        menu.AppendSeparator()

        # Filter options
        menu.Append(ID_FILTER, "Filter...")

        # Only show "Clear Filter" if a filter is active on this column
        if col in self.active_filters or col in self.advanced_filters:
            menu.Append(ID_CLEAR_FILTER, "Clear Filter")

        # Bind events
        self.Bind(wx.EVT_MENU, lambda e: self._copy_column(col), id=ID_COPY_COL)
        self.Bind(wx.EVT_MENU, lambda e: self._paste_column(col), id=ID_PASTE_COL)
        self.Bind(wx.EVT_MENU, lambda e: self._insert_column_at(col), id=ID_INSERT_LEFT)
        self.Bind(
            wx.EVT_MENU, lambda e: self._insert_column_at(col + 1), id=ID_INSERT_RIGHT
        )
        self.Bind(wx.EVT_MENU, lambda e: self._delete_column_at(col), id=ID_DELETE_COL)
        self.Bind(
            wx.EVT_MENU, lambda e: self.sort_column(col, ascending=True), id=ID_SORT_ASC
        )
        self.Bind(
            wx.EVT_MENU,
            lambda e: self.sort_column(col, ascending=False),
            id=ID_SORT_DESC,
        )
        self.Bind(wx.EVT_MENU, lambda e: self._show_filter_dialog(col), id=ID_FILTER)
        self.Bind(wx.EVT_MENU, lambda e: self.clear_filter(col), id=ID_CLEAR_FILTER)

        self.PopupMenu(menu)
        menu.Destroy()

    def _on_row_label_click(self, row: int) -> None:
        """Handle clicks on row labels to show insert/delete menu."""
        menu = wx.Menu()
        ID_COPY_ROW = wx.NewId()
        ID_PASTE_ROW = wx.NewId()
        ID_INSERT_ABOVE = wx.NewId()
        ID_INSERT_BELOW = wx.NewId()
        ID_DELETE_ROW = wx.NewId()

        # Copy/Paste options
        menu.Append(ID_COPY_ROW, "Copy Row")
        menu.Append(ID_PASTE_ROW, "Paste Row")
        menu.AppendSeparator()

        # Insert/Delete options
        menu.Append(ID_INSERT_ABOVE, "Insert Row Above")
        menu.Append(ID_INSERT_BELOW, "Insert Row Below")
        menu.Append(ID_DELETE_ROW, "Delete Row")

        # Bind events
        self.Bind(wx.EVT_MENU, lambda e: self._copy_row(row), id=ID_COPY_ROW)
        self.Bind(wx.EVT_MENU, lambda e: self._paste_row(row), id=ID_PASTE_ROW)
        self.Bind(wx.EVT_MENU, lambda e: self._insert_row_at(row), id=ID_INSERT_ABOVE)
        self.Bind(
            wx.EVT_MENU, lambda e: self._insert_row_at(row + 1), id=ID_INSERT_BELOW
        )
        self.Bind(wx.EVT_MENU, lambda e: self._delete_row_at(row), id=ID_DELETE_ROW)

        self.PopupMenu(menu)
        menu.Destroy()

    def _insert_column_at(self, col: int) -> None:
        """Insert a column at the specified position."""
        if self.original_data is None:
            return

        self.save_state_for_undo("insert_col", col_pos=col)

        # Insert empty column in the original data
        for row_data in self.original_data:
            row_data.insert(col, "")

        self._update_view()

    def _delete_column_at(self, col: int) -> None:
        """Delete the column at the specified position."""
        if self.original_data is None or col >= len(self.original_data[0]):
            return

        # Save column data for undo
        col_data = [
            row_data[col] if col < len(row_data) else ""
            for row_data in self.original_data
        ]
        self.save_state_for_undo("delete_col", col_pos=col, col_data=col_data)

        # Remove column from the original data
        for row_data in self.original_data:
            if col < len(row_data):
                row_data.pop(col)

        self._update_view()

    def _insert_row_at(self, row: int) -> None:
        """Insert a row at the specified position."""
        if self.original_data is None:
            return

        self.save_state_for_undo("insert_row", row_pos=row)

        # Insert empty row in the original data
        num_cols = (
            max(len(row_data) for row_data in self.original_data)
            if self.original_data
            else 0
        )
        empty_row = [""] * num_cols
        self.original_data.insert(row, empty_row)

        self._update_view()

    def _delete_row_at(self, row: int) -> None:
        """Delete the row at the specified position."""
        if self.original_data is None or row >= len(self.original_data):
            return

        # Save row data for undo
        row_data = self.original_data[row][:]
        self.save_state_for_undo("delete_row", row_pos=row, row_data=row_data)

        # Remove row from the original data
        self.original_data.pop(row)

        self._update_view()

    def _copy_column(self, col: int) -> None:
        """Copy a column to the clipboard."""
        if self.original_data is None or col >= len(self.original_data[0]):
            return

        # Extract column data
        col_data = []
        for row_data in self.original_data:
            if col < len(row_data):
                col_data.append(row_data[col])
            else:
                col_data.append("")

        # Store in clipboard as tab-separated text
        clipboard_text = "\n".join(col_data)

        if wx.TheClipboard.Open():
            wx.TheClipboard.SetData(wx.TextDataObject(clipboard_text))
            wx.TheClipboard.Close()

    def _paste_column(self, col: int) -> None:
        """Paste column data from clipboard."""
        if self.original_data is None:
            return

        if wx.TheClipboard.Open():
            data = wx.TextDataObject()
            if wx.TheClipboard.GetData(data):
                text = data.GetText()
                lines = text.split("\n")

                self.save_state_for_undo("paste_col", col_pos=col)

                # Paste data into column
                for i, line in enumerate(lines):
                    if i < len(self.original_data):
                        # Ensure row has enough columns
                        while len(self.original_data[i]) <= col:
                            self.original_data[i].append("")
                        self.original_data[i][col] = line.strip()

                self._update_view()

            wx.TheClipboard.Close()

    def _copy_row(self, row: int) -> None:
        """Copy a row to the clipboard."""
        if self.original_data is None or row >= len(self.original_data):
            return

        # Get row data
        row_data = self.original_data[row][:]

        # Store in clipboard as tab-separated text
        clipboard_text = "\t".join(row_data)

        if wx.TheClipboard.Open():
            wx.TheClipboard.SetData(wx.TextDataObject(clipboard_text))
            wx.TheClipboard.Close()

    def _paste_row(self, row: int) -> None:
        """Paste row data from clipboard."""
        if self.original_data is None:
            return

        if wx.TheClipboard.Open():
            data = wx.TextDataObject()
            if wx.TheClipboard.GetData(data):
                text = data.GetText()
                cells = text.split("\t")

                self.save_state_for_undo("paste_row", row_pos=row)

                # Ensure row exists
                if row >= len(self.original_data):
                    # Add empty rows if needed
                    while len(self.original_data) <= row:
                        num_cols = (
                            max(len(row_data) for row_data in self.original_data)
                            if self.original_data
                            else len(cells)
                        )
                        self.original_data.append([""] * num_cols)

                # Ensure row has enough columns
                while len(self.original_data[row]) < len(cells):
                    self.original_data[row].append("")

                # Paste data into row
                for i, cell in enumerate(cells):
                    if i < len(self.original_data[row]):
                        self.original_data[row][i] = cell.strip()

                self._update_view()

            wx.TheClipboard.Close()

    def _on_cell_right_click(self, event: wx.grid.GridEvent) -> None:
        """Handle right-clicks on grid cells to show copy/paste context menu."""
        menu = wx.Menu()

        # Context menu IDs
        ID_COPY_CELL = wx.NewId()
        ID_PASTE_CELL = wx.NewId()
        ID_COPY_CELLS = wx.NewId()
        ID_PASTE_CELLS = wx.NewId()
        ID_COPY_ROWS = wx.NewId()
        ID_PASTE_ROWS = wx.NewId()
        ID_COPY_COLUMNS = wx.NewId()
        ID_PASTE_COLUMNS = wx.NewId()
        ID_CLEAR_CELLS = wx.NewId()

        # Determine what's selected
        selected_cells = self.GetSelectedCells()
        selected_rows = self.GetSelectedRows()
        selected_cols = self.GetSelectedCols()
        selection_blocks = self.GetSelectionBlockTopLeft()

        # Single cell operations (always available)
        menu.Append(ID_COPY_CELL, "Copy Cell")
        menu.Append(ID_PASTE_CELL, "Paste Cell")
        menu.AppendSeparator()

        # Multi-cell operations (if multiple cells selected)
        if (
            len(selected_cells) > 1
            or len(selection_blocks) > 0
            or (
                selected_cells
                and len(selected_cells) == 1
                and (
                    self.IsInSelection(event.GetRow(), event.GetCol())
                    or len(self.GetSelectionBlockTopLeft()) > 0
                )
            )
        ):
            menu.Append(ID_COPY_CELLS, "Copy Selection")
            menu.Append(ID_PASTE_CELLS, "Paste to Selection")
            menu.AppendSeparator()

        # Row operations (if rows selected)
        if selected_rows or (selected_cells and self._is_full_row_selected()):
            menu.Append(ID_COPY_ROWS, "Copy Rows")
            menu.Append(ID_PASTE_ROWS, "Paste Rows")
            menu.AppendSeparator()

        # Column operations (if columns selected)
        if selected_cols or (selected_cells and self._is_full_column_selected()):
            menu.Append(ID_COPY_COLUMNS, "Copy Columns")
            menu.Append(ID_PASTE_COLUMNS, "Paste Columns")
            menu.AppendSeparator()

        # Clear operation
        menu.Append(ID_CLEAR_CELLS, "Clear Selection")

        # Bind event handlers
        self.Bind(
            wx.EVT_MENU,
            lambda e: self._copy_single_cell(event.GetRow(), event.GetCol()),
            id=ID_COPY_CELL,
        )
        self.Bind(
            wx.EVT_MENU,
            lambda e: self._paste_single_cell(event.GetRow(), event.GetCol()),
            id=ID_PASTE_CELL,
        )
        self.Bind(wx.EVT_MENU, lambda e: self._copy_selection(), id=ID_COPY_CELLS)
        self.Bind(wx.EVT_MENU, lambda e: self._paste_to_selection(), id=ID_PASTE_CELLS)
        self.Bind(wx.EVT_MENU, lambda e: self._copy_selected_rows(), id=ID_COPY_ROWS)
        self.Bind(
            wx.EVT_MENU, lambda e: self._paste_to_selected_rows(), id=ID_PASTE_ROWS
        )
        self.Bind(
            wx.EVT_MENU, lambda e: self._copy_selected_columns(), id=ID_COPY_COLUMNS
        )
        self.Bind(
            wx.EVT_MENU,
            lambda e: self._paste_to_selected_columns(),
            id=ID_PASTE_COLUMNS,
        )
        self.Bind(wx.EVT_MENU, lambda e: self._clear_selection(), id=ID_CLEAR_CELLS)

        # Show the menu
        self.PopupMenu(menu)
        menu.Destroy()

    def _is_full_row_selected(self) -> bool:
        """Check if the current selection spans full rows."""
        blocks = self.GetSelectionBlockTopLeft()
        if not blocks:
            return False

        bottom_right = self.GetSelectionBlockBottomRight()
        for i, (top, left) in enumerate(blocks):
            bottom, right = bottom_right[i]
            if left != 0 or right != self.GetNumberCols() - 1:
                return False
        return True

    def _is_full_column_selected(self) -> bool:
        """Check if the current selection spans full columns."""
        blocks = self.GetSelectionBlockTopLeft()
        if not blocks:
            return False

        bottom_right = self.GetSelectionBlockBottomRight()
        for i, (top, left) in enumerate(blocks):
            bottom, right = bottom_right[i]
            if top != 0 or bottom != self.GetNumberRows() - 1:
                return False
        return True

    def _copy_single_cell(self, row: int, col: int) -> None:
        """Copy a single cell value to clipboard."""
        if 0 <= row < self.GetNumberRows() and 0 <= col < self.GetNumberCols():
            value = self.GetCellValue(row, col)
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(value))
                    wx.GetTopLevelParent(self).SetStatusText(
                        f"Copied cell {self._get_cell_reference(row, col)}"
                    )
                finally:
                    clipboard.Close()

    def _paste_single_cell(self, row: int, col: int) -> None:
        """Paste clipboard content to a single cell."""
        if 0 <= row < self.GetNumberRows() and 0 <= col < self.GetNumberCols():
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    if clipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                        text_obj = wx.TextDataObject()
                        clipboard.GetData(text_obj)
                        text = text_obj.GetText()
                        self.SetCellValue(row, col, text)
                        wx.GetTopLevelParent(self).SetStatusText(
                            f"Pasted to cell {self._get_cell_reference(row, col)}"
                        )
                        self.save_state_for_undo("paste_cell", row=row, col=col)
                finally:
                    clipboard.Close()

    def _copy_selection(self) -> None:
        """Copy the current selection to clipboard."""
        try:
            # Get the selection blocks
            blocks = self.GetSelectionBlockTopLeft()
            if not blocks:
                selected_cells = self.GetSelectedCells()
                if selected_cells:
                    # Copy just the selected cells
                    text_lines = []
                    for row, col in selected_cells:
                        value = self.GetCellValue(row, col)
                        text_lines.append(value)
                    text = "\n".join(text_lines)
                else:
                    return
            else:
                # Copy selection blocks
                bottom_right = self.GetSelectionBlockBottomRight()
                text_lines = []

                for i, (top, left) in enumerate(blocks):
                    bottom, right = bottom_right[i]
                    for row in range(top, bottom + 1):
                        row_values = []
                        for col in range(left, right + 1):
                            value = self.GetCellValue(row, col)
                            row_values.append(value)
                        text_lines.append("\t".join(row_values))

                text = "\n".join(text_lines)

            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(text))
                    wx.GetTopLevelParent(self).SetStatusText(
                        "Copied selection to clipboard"
                    )
                finally:
                    clipboard.Close()
        except Exception as e:
            wx.GetTopLevelParent(self).SetStatusText(
                f"Error copying selection: {str(e)}"
            )

    def _paste_to_selection(self) -> None:
        """Paste clipboard content to the current selection."""
        clipboard = wx.TheClipboard
        if not clipboard.Open():
            return

        try:
            if clipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                text_obj = wx.TextDataObject()
                clipboard.GetData(text_obj)
                text = text_obj.GetText()

                if not text:
                    return

                lines = text.split("\n")
                blocks = self.GetSelectionBlockTopLeft()

                if blocks:
                    bottom_right = self.GetSelectionBlockBottomRight()
                    for i, (top, left) in enumerate(blocks):
                        bottom, right = bottom_right[i]
                        self._paste_text_to_block(lines, top, left, bottom, right)
                else:
                    # Paste to selected cells
                    selected_cells = self.GetSelectedCells()
                    if selected_cells and len(lines) == len(selected_cells):
                        for i, (row, col) in enumerate(selected_cells):
                            if i < len(lines):
                                self.SetCellValue(row, col, lines[i])

                wx.GetTopLevelParent(self).SetStatusText("Pasted to selection")
                self.save_state_for_undo("paste_selection")
        finally:
            clipboard.Close()

    def _paste_text_to_block(
        self, lines: list, top: int, left: int, bottom: int, right: int
    ) -> None:
        """Paste text lines to a specific block area."""
        for row_offset, line in enumerate(lines):
            target_row = top + row_offset
            if target_row > bottom:
                break

            if "\t" in line:
                values = line.split("\t")
                for col_offset, value in enumerate(values):
                    target_col = left + col_offset
                    if target_col > right:
                        break
                    self.SetCellValue(target_row, target_col, value)
            else:
                # Single value for the entire row
                self.SetCellValue(target_row, left, line)

    def _copy_selected_rows(self) -> None:
        """Copy selected rows to clipboard."""
        selected_rows = self.GetSelectedRows()
        if not selected_rows:
            # Check if selection blocks represent full rows
            if self._is_full_row_selected():
                blocks = self.GetSelectionBlockTopLeft()
                bottom_right = self.GetSelectionBlockBottomRight()
                selected_rows = []
                for i, (top, left) in enumerate(blocks):
                    bottom, right = bottom_right[i]
                    selected_rows.extend(range(top, bottom + 1))

        if selected_rows:
            text_lines = []
            for row in sorted(selected_rows):
                row_values = []
                for col in range(self.GetNumberCols()):
                    value = self.GetCellValue(row, col)
                    row_values.append(value)
                text_lines.append("\t".join(row_values))

            text = "\n".join(text_lines)
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(text))
                    wx.GetTopLevelParent(self).SetStatusText(
                        f"Copied {len(selected_rows)} rows to clipboard"
                    )
                finally:
                    clipboard.Close()

    def _paste_to_selected_rows(self) -> None:
        """Paste clipboard content to selected rows."""
        selected_rows = self.GetSelectedRows()
        if not selected_rows:
            return

        clipboard = wx.TheClipboard
        if not clipboard.Open():
            return

        try:
            if clipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                text_obj = wx.TextDataObject()
                clipboard.GetData(text_obj)
                text = text_obj.GetText()

                if text:
                    lines = text.split("\n")
                    for i, row in enumerate(sorted(selected_rows)):
                        if i < len(lines):
                            values = lines[i].split("\t")
                            for col, value in enumerate(values):
                                if col < self.GetNumberCols():
                                    self.SetCellValue(row, col, value)

                    wx.GetTopLevelParent(self).SetStatusText(
                        f"Pasted to {len(selected_rows)} rows"
                    )
                    self.save_state_for_undo("paste_to_rows")
        finally:
            clipboard.Close()

    def _copy_selected_columns(self) -> None:
        """Copy selected columns to clipboard."""
        selected_cols = self.GetSelectedCols()
        if not selected_cols:
            # Check if selection blocks represent full columns
            if self._is_full_column_selected():
                blocks = self.GetSelectionBlockTopLeft()
                bottom_right = self.GetSelectionBlockBottomRight()
                selected_cols = []
                for i, (top, left) in enumerate(blocks):
                    bottom, right = bottom_right[i]
                    selected_cols.extend(range(left, right + 1))

        if selected_cols:
            text_lines = []
            for row in range(self.GetNumberRows()):
                row_values = []
                for col in sorted(selected_cols):
                    value = self.GetCellValue(row, col)
                    row_values.append(value)
                text_lines.append("\t".join(row_values))

            text = "\n".join(text_lines)
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(text))
                    wx.GetTopLevelParent(self).SetStatusText(
                        f"Copied {len(selected_cols)} columns to clipboard"
                    )
                finally:
                    clipboard.Close()

    def _paste_to_selected_columns(self) -> None:
        """Paste clipboard content to selected columns."""
        selected_cols = self.GetSelectedCols()
        if not selected_cols:
            return

        clipboard = wx.TheClipboard
        if not clipboard.Open():
            return

        try:
            if clipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                text_obj = wx.TextDataObject()
                clipboard.GetData(text_obj)
                text = text_obj.GetText()

                if text:
                    lines = text.split("\n")
                    for row, line in enumerate(lines):
                        if row < self.GetNumberRows():
                            values = line.split("\t")
                            for i, col in enumerate(sorted(selected_cols)):
                                if i < len(values):
                                    self.SetCellValue(row, col, values[i])

                    wx.GetTopLevelParent(self).SetStatusText(
                        f"Pasted to {len(selected_cols)} columns"
                    )
                    self.save_state_for_undo("paste_to_columns")
        finally:
            clipboard.Close()

    def _clear_selection(self) -> None:
        """Clear the current selection."""
        blocks = self.GetSelectionBlockTopLeft()
        if blocks:
            bottom_right = self.GetSelectionBlockBottomRight()
            for i, (top, left) in enumerate(blocks):
                bottom, right = bottom_right[i]
                for row in range(top, bottom + 1):
                    for col in range(left, right + 1):
                        self.SetCellValue(row, col, "")
        else:
            selected_cells = self.GetSelectedCells()
            for row, col in selected_cells:
                self.SetCellValue(row, col, "")

        wx.GetTopLevelParent(self).SetStatusText("Cleared selection")
        self.save_state_for_undo("clear_selection")

    def _get_cell_reference(self, row: int, col: int) -> str:
        """Get Excel-style cell reference (e.g., A1, B2)."""
        col_letter = (
            chr(ord("A") + col)
            if col < 26
            else f"{chr(ord('A') + col // 26 - 1)}{chr(ord('A') + col % 26)}"
        )
        return f"{col_letter}{row + 1}"

    def sort_column(self, col_index: int, ascending: bool = True) -> None:
        """Sets the sort state and updates the view."""
        self._save_view_state_for_undo()
        self.sort_state = (col_index, ascending)
        self._update_view()

    def _show_filter_dialog(self, col_index: int) -> None:
        """Shows the advanced filter dialog to get filter criteria from the user."""
        try:
            # Get column data
            column_data = []
            if self.original_data:
                for row in self.original_data:
                    if col_index < len(row):
                        column_data.append(row[col_index])
                    else:
                        column_data.append("")
            else:
                for row in range(self.GetNumberRows()):
                    column_data.append(self.GetCellValue(row, col_index))

            # Get column name
            column_name = chr(ord("A") + col_index)

            # Current filter (if any)
            current_filter = self.active_filters.get(col_index, "")

            # Show advanced filter dialog
            with AdvancedFilterDialog(
                self, column_name, column_data, current_filter
            ) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    filter_result = dlg.get_filter_result()
                    if filter_result:
                        self.apply_advanced_filter(col_index, filter_result)
        except Exception as e:
            import logging

            logging.getLogger(__name__).error(f"Error showing filter dialog: {e}")
            # Fallback to simple dialog
            with wx.TextEntryDialog(
                self, f"Show rows where column {column_name} contains:", "Filter Column"
            ) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    filter_text = dlg.GetValue()
                    self.apply_filter(col_index, filter_text)

    def apply_advanced_filter(self, col_index: int, filter_result: dict) -> None:
        """Applies an advanced filter to a column and updates the view."""
        try:
            self._save_view_state_for_undo()

            if filter_result["action"] == "clear":
                self.clear_filter(col_index)
            elif filter_result["action"] == "none":
                # All values selected - clear filter
                self.clear_filter(col_index)
            elif filter_result["action"] == "values":
                # Create value list criterion
                values = filter_result.get("values", [])
                if not isinstance(values, list):
                    raise ValueError("Filter values must be a list")

                criterion = ValueListCriterion(
                    selected_values=set(values),
                    include_blanks=filter_result.get("include_blanks", False),
                )
                self.advanced_filters[col_index] = criterion
                # Also update simple filter for backward compatibility
                if values:
                    self.active_filters[col_index] = "|".join(
                        str(v) for v in values
                    ).lower()
                elif col_index in self.active_filters:
                    del self.active_filters[col_index]
            elif filter_result["action"] == "criterion":
                # Use provided criterion directly
                criterion = filter_result.get("criterion")
                if criterion is None:
                    raise ValueError("Filter criterion is required")

                self.advanced_filters[col_index] = criterion
                # Clear simple filter when using advanced criterion
                if col_index in self.active_filters:
                    del self.active_filters[col_index]
            else:
                raise ValueError(
                    f"Unknown filter action: {filter_result.get('action', 'None')}"
                )

            self._update_view()
            self._update_filter_indicators()

        except Exception as e:
            import logging

            logging.getLogger(__name__).error(f"Error applying advanced filter: {e}")

            # Try to recover by clearing the problematic filter
            try:
                if col_index in self.advanced_filters:
                    del self.advanced_filters[col_index]
                if col_index in self.active_filters:
                    del self.active_filters[col_index]
                self._update_view()
                self._update_filter_indicators()

                # Show user-friendly error message
                wx.MessageBox(
                    f"Error applying filter to column {col_index + 1}.\n"
                    f"The filter has been cleared to prevent data corruption.\n\n"
                    f"Error details: {str(e)}",
                    "Filter Error - Recovered",
                    wx.OK | wx.ICON_WARNING,
                )
            except Exception as recovery_error:
                logging.getLogger(__name__).error(
                    f"Error during filter recovery: {recovery_error}"
                )
                wx.MessageBox(
                    "Critical error applying filter. Please save your work and "
                    "restart the application.",
                    "Critical Filter Error",
                    wx.OK | wx.ICON_ERROR,
                )

    def apply_filter(self, col_index: int, filter_text: str) -> None:
        """Applies a simple filter to a column and updates the view."""
        if not filter_text:
            self.clear_filter(col_index)
            return

        self._save_view_state_for_undo()
        self.active_filters[col_index] = filter_text.lower()

        # Create text criterion for consistency
        criterion = TextFilterCriterion(
            operator=TextFilterCriterion.CONTAINS,
            value=filter_text,
            case_sensitive=False,
        )
        self.advanced_filters[col_index] = criterion

        self._update_view()
        self._update_filter_indicators()

    def clear_filter(self, col_index: int) -> None:
        """Clears the filter for a specific column."""
        changes_made = False

        if col_index in self.active_filters:
            del self.active_filters[col_index]
            changes_made = True

        if col_index in self.advanced_filters:
            del self.advanced_filters[col_index]
            changes_made = True

        if changes_made:
            self._save_view_state_for_undo()
            self._update_view()
            self._update_filter_indicators()

    def save_state_for_undo(self, action_type: str, **kwargs):
        """Save the current state for undo functionality."""
        state = {
            "action_type": action_type,
            "rows": self.GetNumberRows(),
            "cols": self.GetNumberCols(),
            "timestamp": time.time(),
        }
        state.update(kwargs)

        # Save specific data based on action type
        if action_type == "insert_row":
            state["row_pos"] = kwargs.get("row_pos", 0)
        elif action_type == "delete_row":
            state["row_pos"] = kwargs.get("row_pos", 0)
            # Save the row data that was deleted
            state["row_data"] = []
            for col in range(self.GetNumberCols()):
                state["row_data"].append(
                    self.GetCellValue(kwargs.get("row_pos", 0), col)
                )
        elif action_type == "insert_col":
            state["col_pos"] = kwargs.get("col_pos", 0)
        elif action_type == "delete_col":
            state["col_pos"] = kwargs.get("col_pos", 0)
            # Save the column data that was deleted
            state["col_data"] = []
            for row in range(self.GetNumberRows()):
                state["col_data"].append(
                    self.GetCellValue(row, kwargs.get("col_pos", 0))
                )
        elif action_type in ["format_comma", "format_percent", "format_currency"]:
            # Save cell data for formatting operations
            state["cell_data"] = kwargs.get("cell_data", [])

        self.undo_stack.append(state)
        self.redo_stack.clear()  # Clear redo stack when new action is performed

        # Limit undo stack size
        if len(self.undo_stack) > self.max_undo_levels:
            self.undo_stack.pop(0)

    def can_undo(self) -> bool:
        """Check if undo is possible."""
        return len(self.undo_stack) > 0

    def can_redo(self) -> bool:
        """Check if redo is possible."""
        return len(self.redo_stack) > 0

    def perform_undo(self) -> bool:
        """Perform undo operation."""
        if not self.can_undo():
            return False

        state = self.undo_stack.pop()
        action_type = state["action_type"]

        if action_type == "view_change":
            try:
                # Save current view state for redo
                current_view_state = {
                    "action_type": "view_change",
                    "previous_state": {
                        "sort_state": self.sort_state,
                        "active_filters": self.active_filters.copy(),
                    },
                }
                self.redo_stack.append(current_view_state)

                # Restore previous view state
                previous_state = state["previous_state"]
                self.sort_state = previous_state.get("sort_state")
                self.active_filters = previous_state.get("active_filters", {})
                self._update_view()
                return True
            except Exception as e:
                print(f"Undo failed for view_change: {e}")
                self.undo_stack.append(state)
                return False

        # --- Existing Undo Logic for data changes ---
        redo_state = {
            "action_type": action_type,
            "rows": self.GetNumberRows(),
            "cols": self.GetNumberCols(),
        }

        try:
            if action_type == "insert_row":
                # Undo row insertion by deleting the row
                row_pos = state["row_pos"]
                if row_pos < self.GetNumberRows():
                    self.DeleteRows(row_pos, 1)
                    redo_state["row_pos"] = row_pos

            elif action_type == "delete_row":
                # Undo row deletion by inserting the row back
                row_pos = state["row_pos"]
                self.InsertRows(row_pos, 1)
                # Restore the row data
                for col, cell_value in enumerate(state.get("row_data", [])):
                    if col < self.GetNumberCols():
                        self.SetCellValue(row_pos, col, cell_value)
                redo_state["row_pos"] = row_pos
                redo_state["row_data"] = state.get("row_data", [])

            elif action_type == "insert_col":
                # Undo column insertion by deleting the column
                col_pos = state["col_pos"]
                if col_pos < self.GetNumberCols():
                    self.DeleteCols(col_pos, 1)
                    redo_state["col_pos"] = col_pos

            elif action_type == "delete_col":
                # Undo column deletion by inserting the column back
                col_pos = state["col_pos"]
                self.InsertCols(col_pos, 1)
                # Restore the column data
                for row, cell_value in enumerate(state.get("col_data", [])):
                    if row < self.GetNumberRows():
                        self.SetCellValue(row, col_pos, cell_value)
                redo_state["col_pos"] = col_pos
                redo_state["col_data"] = state.get("col_data", [])

            elif action_type in ["format_comma", "format_percent", "format_currency"]:
                # Undo formatting by restoring original cell values
                cell_data = state.get("cell_data", [])
                # Save current values for redo
                redo_cell_data = []
                for row, col, original_value in cell_data:
                    if row < self.GetNumberRows() and col < self.GetNumberCols():
                        redo_cell_data.append((row, col, self.GetCellValue(row, col)))
                        self.SetCellValue(row, col, original_value)
                redo_state["cell_data"] = redo_cell_data

            self.redo_stack.append(redo_state)
            self.ForceRefresh()
            return True

        except Exception as e:
            print(f"Undo failed: {e}")
            return False

    def perform_redo(self) -> bool:
        """Perform redo operation."""
        if not self.can_redo():
            return False

        state = self.redo_stack.pop()
        action_type = state["action_type"]

        if action_type == "view_change":
            try:
                # Save current view state for undo
                undo_state = {
                    "action_type": "view_change",
                    "previous_state": {
                        "sort_state": self.sort_state,
                        "active_filters": self.active_filters.copy(),
                    },
                }
                self.undo_stack.append(undo_state)

                # Restore the "redone" view state
                redone_state = state["previous_state"]
                self.sort_state = redone_state.get("sort_state")
                self.active_filters = redone_state.get("active_filters", {})
                self._update_view()
                return True
            except Exception as e:
                print(f"Redo failed for view_change: {e}")
                self.redo_stack.append(state)
                return False

        # --- Existing Redo Logic for data changes ---
        try:
            if action_type == "insert_row":
                # Redo row insertion
                row_pos = state["row_pos"]
                self.InsertRows(row_pos, 1)
                # Save state for undo again
                self.save_state_for_undo("insert_row", row_pos=row_pos)

            elif action_type == "delete_row":
                # Redo row deletion
                row_pos = state["row_pos"]
                self.save_state_for_undo("delete_row", row_pos=row_pos)
                self.DeleteRows(row_pos, 1)

            elif action_type == "insert_col":
                # Redo column insertion
                col_pos = state["col_pos"]
                self.InsertCols(col_pos, 1)
                self.save_state_for_undo("insert_col", col_pos=col_pos)

            elif action_type == "delete_col":
                # Redo column deletion
                col_pos = state["col_pos"]
                self.save_state_for_undo("delete_col", col_pos=col_pos)
                self.DeleteCols(col_pos, 1)

            elif action_type in ["format_comma", "format_percent", "format_currency"]:
                # Redo formatting by applying the formatting again
                cell_data = state.get("cell_data", [])
                # Save current values for undo
                undo_cell_data = []
                for row, col, formatted_value in cell_data:
                    if row < self.GetNumberRows() and col < self.GetNumberCols():
                        undo_cell_data.append((row, col, self.GetCellValue(row, col)))
                        self.SetCellValue(row, col, formatted_value)
                self.save_state_for_undo(action_type, cell_data=undo_cell_data)

            self.ForceRefresh()
            return True

        except Exception as e:
            print(f"Redo failed: {e}")
            return False

    def _update_filter_indicators(self) -> None:
        """Update visual indicators for filtered columns."""
        if not hasattr(self, "GetNumberCols"):  # Safety check
            return

        # Get combined active filters
        all_filters = {}
        all_filters.update(self.active_filters)
        all_filters.update(self.advanced_filters)

        # Update column headers with filter indicators
        for col in range(self.GetNumberCols()):
            header_text = self.GetColLabelValue(col)
            base_header = header_text.rstrip(" 🔽⚫")  # Remove existing indicators

            if col in all_filters:
                # Add filter indicator (down arrow with filter symbol)
                new_header = f"{base_header} 🔽"
                self.SetColLabelValue(col, new_header)
                # Set tooltip with filter info
                filter_info = self._get_filter_description(col)
                if hasattr(self, "SetColLabelToolTip"):  # Not all wx versions have this
                    self.SetColLabelToolTip(col, f"Filtered: {filter_info}")
            else:
                # Reset to normal header
                self.SetColLabelValue(col, base_header)

        # Update status bar in parent window if available
        self._update_status_bar()

    def _get_filter_description(self, col: int) -> str:
        """Get human-readable description of active filter on column."""
        descriptions = []

        # Check simple filters
        if col in self.active_filters:
            filter_text = self.active_filters[col]
            descriptions.append(f"Contains '{filter_text}'")

        # Check advanced filters
        if col in self.advanced_filters:
            criterion = self.advanced_filters[col]
            if hasattr(criterion, "get_description"):
                descriptions.append(criterion.get_description())
            else:
                descriptions.append("Custom filter")

        return "; ".join(descriptions) if descriptions else "Filter active"

    def _update_status_bar(self) -> None:
        """Update the parent window's status bar with filter information."""
        try:
            # Get parent window (traverse up hierarchy)
            parent = self.GetParent()
            while parent and not hasattr(parent, "status_bar"):
                parent = parent.GetParent()

            if parent and hasattr(parent, "status_bar"):
                if not self.original_data:
                    return

                total_rows = len(self.original_data)
                visible_rows = self.GetNumberRows()

                # Count active filters
                total_filters = len(self.active_filters) + len(self.advanced_filters)

                if total_filters > 0:
                    status_text = f"{visible_rows} of {total_rows} rows visible"
                    if total_filters == 1:
                        status_text += " (1 filter active)"
                    else:
                        status_text += f" ({total_filters} filters active)"
                    parent.status_bar.SetStatusText(status_text)
                else:
                    parent.status_bar.SetStatusText(f"{total_rows} rows")
        except Exception:
            # Silently ignore status bar update errors
            pass

    def _save_view_state_for_undo(self):
        """Saves the current sort and filter state for an undo operation."""
        previous_state = {
            "sort_state": self.sort_state,
            "active_filters": self.active_filters.copy(),
        }
        self.save_state_for_undo("view_change", previous_state=previous_state)


class MainWindow(wx.Frame):
    """Main window for JEdit2."""

    def __init__(
        self,
        parent: Optional[wx.Window] = None,
        title: str = "JEdit",
        size: Tuple[int, int] = (800, 600),
    ) -> None:
        """Initialize the main window.

        Args:
            parent: Parent window
            title: Window title
            size: Window size (width, height)
        """
        super().__init__(parent, title=title, size=size)

        # Initialize rate limiting
        self._requests_per_minute = 0
        self._requests_per_hour = 0
        self._requests_per_day = 0
        self._last_request_time = 0
        self._min_interval = 1.0  # Minimum seconds between requests

        # Rate limits for Gemini API
        self._max_requests_per_minute = 60  # Gemini Pro limit
        self._max_requests_per_hour = 1000  # Gemini Pro limit
        self._max_requests_per_day = 10000  # Gemini Pro limit

        # Reset timestamps
        self._minute_reset_time = time.time()
        self._hour_reset_time = time.time()
        self._day_reset_time = time.time()

        # Initialize the UI
        self._init_ui()

        # Use shared managers, or create them if they are not provided
        self.error_handler = ErrorHandler.get_instance()
        self.memory_manager = MemoryManager()

        # Generate custom IDs now that the wx.App object exists

        self.ID_INDEX_MANAGER = wx.NewId()
        self.ID_MEMORY_MANAGER = wx.NewId()
        self.ID_CACHE_MANAGER = wx.NewId()
        self.ID_LAZY_LOADING = wx.NewId()
        self.ID_ERROR_REPORT = wx.NewId()
        self.ID_NEW_SPREADSHEET = wx.NewId()
        self.ID_SWITCH_VIEW = wx.NewId()
        self.ID_AI_SETTINGS = wx.NewId()
        self.ID_AI_SUBMIT = wx.NewId()

        # Add IDs for more compact ribbon controls
        self.ID_INSERT_DROPDOWN = wx.NewId()
        self.ID_DELETE_DROPDOWN = wx.NewId()

        # Add the new formatting and management ID constants
        self.ID_FORMAT_COMMA = wx.NewId()
        self.ID_FORMAT_PERCENT = wx.NewId()
        self.ID_FORMAT_CURRENCY = wx.NewId()
        self.ID_MANAGE_DROPDOWN = wx.NewId()
        self.ID_TRANSPOSE = wx.NewId()
        self.ID_DATA_VALIDATION = wx.NewId()

        # Tab context menu IDs
        self.ID_TAB_CLOSE = wx.NewId()
        self.ID_TAB_CLOSE_OTHERS = wx.NewId()
        self.ID_TAB_CLOSE_ALL = wx.NewId()
        self.ID_TAB_RENAME = wx.NewId()

        # Add the missing ID constants that were accidentally removed
        self.ID_FORMAT_BOLD = wx.NewId()
        self.ID_FORMAT_ITALIC = wx.NewId()
        self.ID_FORMAT_UNDERLINE = wx.NewId()
        self.ID_ALIGN_LEFT = wx.NewId()
        self.ID_ALIGN_CENTER = wx.NewId()
        self.ID_ALIGN_RIGHT = wx.NewId()
        self.ID_INSERT_ROW = wx.NewId()
        self.ID_DELETE_ROW = wx.NewId()
        self.ID_INSERT_COLUMN = wx.NewId()
        self.ID_DELETE_COLUMN = wx.NewId()
        self.ID_INC_DECIMAL = wx.NewId()
        self.ID_DEC_DECIMAL = wx.NewId()
        self.ID_ZOOM_IN = wx.NewId()
        self.ID_ZOOM_OUT = wx.NewId()
        self.ID_VIEW_SWITCH = wx.NewId()
        self.ID_FILTER_HISTORY = wx.NewId()
        self.ID_ADVANCED_FILTER = wx.NewId()
        self.ID_SHOW_CALCULATOR = wx.NewId()
        self.ID_QUICK_NUMBER_FILTER = wx.NewId()
        self.ID_FREEZE_PANES = wx.NewId()
        self.ID_UNFREEZE_PANES = wx.NewId()

        # These are still specific to the window
        self.format_validator = FormatValidator()
        self.clipboard_manager = ClipboardManager()
        self.indexes: Dict[str, DataIndex] = {}
        self.cache_manager = CacheManager()
        self.lazy_loader = LazyLoader(self.cache_manager, self.memory_manager)
        self.parallel_manager = ParallelManager()
        self.config_manager = ConfigManager()
        self.ai_manager = AIManager(api_key=self.config_manager.get_setting("api_key"))

        # Setup logging for AI integration
        import logging

        self.logger = logging.getLogger(__name__)

        # Integrate complete AI system with comprehensive testing and monitoring
        try:
            from jedit2.utils.ai_system_integrator import AISystemIntegrator

            # Create AI system integrator with production configuration
            ai_config = {
                "integration_mode": "production",
                "enable_background_testing": True,
                "enable_real_time_monitoring": True,
                "enable_automatic_reports": True,
                "shadow_mode": False,  # Full integration
                "testing_config": {
                    "run_on_startup": True,
                    "hourly_testing": True,
                    "max_parallel_tests": 2,
                    "auto_fix_enabled": False,  # Conservative for production
                },
                "monitoring_config": {
                    "alert_thresholds": {
                        "success_rate_warning": 0.98,
                        "response_time_warning": 3.0,
                        "error_rate_critical": 0.05,
                    },
                    "enable_adaptive_learning": True,
                    "export_metrics": True,
                },
            }

            self.ai_system_integrator = AISystemIntegrator(self, ai_config)

            # Integrate the complete system
            if self.ai_system_integrator.integrate_complete_system():
                self.logger.info("✅ Complete AI system integrated successfully")
            else:
                self.logger.warning("AI system integration failed, using fallback")
                # Fallback to production AI manager
                from jedit2.utils.ai_manager_production import (
                    integrate_production_ai_manager,
                )
                integrate_production_ai_manager(self)

        except ImportError as e:
            self.logger.warning(
                f"Complete AI system not available ({e}), using production AI manager"
            )
            try:
                from jedit2.utils.ai_manager_production import (
                    integrate_production_ai_manager,
                )
                integrate_production_ai_manager(self)
            except ImportError:
                self.logger.warning(
                    "Production AI Manager not available, using standard AI manager"
                )

        # Recently used files
        self.recent_files = []
        self.max_recent_files = 10
        self._load_recent_files()

        # Add AI system status methods
        self._setup_ai_system_methods()

        self._create_ribbon()
        self._create_menu_bar()
        self._init_ui()
        self._create_status_bar()
        self._bind_events()
        self._setup_accelerators()

        # Set minimum size and center the window
        self.SetMinSize((800, 600))
        self.CenterOnScreen()

    def _create_menu_bar(self) -> None:
        """Create the menu bar."""
        menu_bar = wx.MenuBar()

        file_menu = wx.Menu()
        file_menu.Append(wx.ID_NEW, "New\tCtrl+N")
        file_menu.Append(self.ID_NEW_SPREADSHEET, "New Spreadsheet\tCtrl+Shift+N")
        file_menu.Append(wx.ID_OPEN, "Open\tCtrl+O")

        # Recent files submenu
        self.recent_files_menu = wx.Menu()
        self._update_recent_files_menu()
        file_menu.AppendSubMenu(self.recent_files_menu, "Recent Files")

        file_menu.AppendSeparator()
        file_menu.Append(wx.ID_SAVE, "Save\tCtrl+S")
        file_menu.Append(wx.ID_SAVEAS, "Save As...\tCtrl+Shift+S")
        file_menu.AppendSeparator()
        file_menu.Append(wx.ID_EXIT, "Exit\tCtrl+Q")

        edit_menu = wx.Menu()
        edit_menu.Append(wx.ID_UNDO, "Undo\tCtrl+Z")
        edit_menu.Append(wx.ID_REDO, "Redo\tCtrl+Y")
        edit_menu.AppendSeparator()
        edit_menu.Append(wx.ID_CUT, "Cut\tCtrl+X")
        edit_menu.Append(wx.ID_COPY, "Copy\tCtrl+C")
        edit_menu.Append(wx.ID_PASTE, "Paste\tCtrl+V")

        view_menu = wx.Menu()
        view_menu.Append(self.ID_ZOOM_IN, "Zoom In\tCtrl++")
        view_menu.Append(self.ID_ZOOM_OUT, "Zoom Out\tCtrl+-")
        view_menu.AppendSeparator()
        view_menu.Append(self.ID_FREEZE_PANES, "Freeze Panes\tCtrl+Shift+F")
        view_menu.Append(self.ID_UNFREEZE_PANES, "Unfreeze Panes\tCtrl+Shift+U")
        view_menu.AppendSeparator()
        view_menu.Append(self.ID_VIEW_SWITCH, "Switch View\tCtrl+Tab")

        format_menu = wx.Menu()
        format_menu.Append(self.ID_FORMAT_BOLD, "Bold\tCtrl+B")
        format_menu.Append(self.ID_FORMAT_ITALIC, "Italic\tCtrl+I")
        format_menu.Append(self.ID_FORMAT_UNDERLINE, "Underline\tCtrl+U")

        tools_menu = wx.Menu()
        tools_menu.Append(wx.ID_FIND, "Find\tCtrl+F")
        tools_menu.Append(self.ID_DATA_VALIDATION, "Format Validation")
        tools_menu.Append(self.ID_INDEX_MANAGER, "Index Manager")
        tools_menu.Append(self.ID_MEMORY_MANAGER, "Memory Manager")
        tools_menu.Append(self.ID_CACHE_MANAGER, "Cache Manager")
        tools_menu.Append(self.ID_LAZY_LOADING, "Lazy Loading Manager")
        tools_menu.Append(self.ID_ERROR_REPORT, "Error-reporting Manager")
        tools_menu.AppendSeparator()
        tools_menu.Append(self.ID_AI_SETTINGS, "AI Settings")

        help_menu = wx.Menu()
        help_menu.Append(wx.ID_HELP, "Help")
        help_menu.Append(wx.ID_ABOUT, "About")

        menu_bar.Append(file_menu, "File")
        menu_bar.Append(edit_menu, "Edit")
        menu_bar.Append(view_menu, "View")
        menu_bar.Append(format_menu, "Format")
        menu_bar.Append(tools_menu, "Tools")
        menu_bar.Append(help_menu, "Help")

        self.SetMenuBar(menu_bar)

    def _create_custom_bitmap(
        self, icon_name: str, draw_func, size=(16, 16)
    ) -> wx.Bitmap:
        """Creates a custom bitmap using system-appropriate background colors to
        avoid transparency issues."""
        width, height = size

        # Use system colors that work well with ribbon controls
        # This avoids the transparency issues entirely
        bitmap = wx.Bitmap(width, height)
        dc = wx.MemoryDC(bitmap)

        # Use a light gray background that matches ribbon control expectations
        # This is much more reliable than trying to make backgrounds transparent
        bg_color = wx.SystemSettings.GetColour(wx.SYS_COLOUR_BTNFACE)
        if bg_color.IsOk():
            dc.SetBackground(wx.Brush(bg_color))
        else:
            # Fallback to a light gray that works well
            dc.SetBackground(wx.Brush(wx.Colour(240, 240, 240)))

        dc.Clear()

        # Draw the icon
        draw_func(dc, size)

        dc.SelectObject(wx.NullBitmap)
        return bitmap

    def _create_ribbon(self) -> None:
        """Create the ribbon bar with a more compact, Excel-like layout."""
        self._ribbon = RB.RibbonBar(self, wx.ID_ANY)

        # Create art provider and set to white theme
        art_provider = RB.RibbonAUIArtProvider()

        # Set white colors for clean modern look
        white = wx.Colour(255, 255, 255)
        light_grey = wx.Colour(245, 245, 245)
        border_grey = wx.Colour(220, 220, 220)
        text_grey = wx.Colour(80, 80, 80)

        # Try to set white colors (some constants may not exist in all versions)
        try:
            # Panel backgrounds
            art_provider.SetColour(RB.RIBBON_ART_PANEL_BACKGROUND_COLOUR, white)
            art_provider.SetColour(
                RB.RIBBON_ART_PANEL_BACKGROUND_GRADIENT_COLOUR, white
            )
        except AttributeError:
            pass

        try:
            # Bar backgrounds
            art_provider.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_COLOUR, white)
            art_provider.SetColour(RB.RIBBON_ART_BAR_BACKGROUND_GRADIENT_COLOUR, white)
        except AttributeError:
            pass

        try:
            # Page backgrounds
            art_provider.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_COLOUR, white)
            art_provider.SetColour(RB.RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR, white)
        except AttributeError:
            pass

        try:
            # Tab backgrounds
            art_provider.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_COLOUR, light_grey)
            art_provider.SetColour(RB.RIBBON_ART_TAB_BACKGROUND_GRADIENT_COLOUR, white)
            art_provider.SetColour(RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR, white)
            art_provider.SetColour(
                RB.RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR, white
            )
        except AttributeError:
            pass

        try:
            # Borders
            art_provider.SetColour(RB.RIBBON_ART_PANEL_BORDER_COLOUR, border_grey)
            art_provider.SetColour(RB.RIBBON_ART_BAR_BORDER_COLOUR, border_grey)
        except AttributeError:
            pass

        try:
            # Text colors
            art_provider.SetColour(RB.RIBBON_ART_PANEL_LABEL_COLOUR, text_grey)
            art_provider.SetColour(RB.RIBBON_ART_PAGE_LABEL_COLOUR, text_grey)
        except AttributeError:
            pass

        self._ribbon.SetArtProvider(art_provider)

        # --- Icon Drawing Functions (Using MemoryDC) ---
        def draw_paste(dc, size):
            # Clipboard part - dark outline
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            dc.SetBrush(wx.Brush(wx.Colour(180, 180, 180)))
            dc.DrawRoundedRectangle(3, 1, min(26, size[0] - 3), min(28, size[1] - 1), 4)
            # Paper part - white
            dc.SetBrush(wx.Brush(wx.WHITE))
            dc.DrawRectangle(8, 5, min(16, size[0] - 8), min(22, size[1] - 5))
            # Lines on paper - black
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            for i in range(min(5, (size[1] - 8) // 4)):
                dc.DrawLine(10, 8 + i * 4, min(22, size[0] - 2), 8 + i * 4)

        def draw_cut(dc, size):
            dc.SetPen(wx.Pen(wx.BLACK, 2))
            # Scissor blades
            dc.DrawLine(3, 13, 8, 8)
            dc.DrawLine(8, 8, 3, 3)
            dc.DrawLine(13, 13, 8, 8)
            dc.DrawLine(8, 8, 13, 3)
            # Handles
            dc.SetBrush(wx.Brush(wx.BLACK))
            dc.DrawEllipse(1, 1, 4, 4)
            dc.DrawEllipse(11, 1, 4, 4)

        def draw_copy(dc, size):
            # Back paper
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            dc.SetBrush(wx.Brush(wx.WHITE))
            dc.DrawRectangle(2, 4, 9, 10)
            # Front paper
            dc.DrawRectangle(4, 2, 9, 10)
            # Lines on papers
            for i in range(3):
                dc.DrawLine(6, 5 + i * 2, 11, 5 + i * 2)

        def draw_bold(dc, size):
            font = wx.Font(wx.FontInfo(10).Bold())
            dc.SetFont(font)
            dc.SetTextForeground(wx.BLACK)
            dc.DrawText("B", 4, 1)

        def draw_italic(dc, size):
            font = wx.Font(wx.FontInfo(10).Italic())
            dc.SetFont(font)
            dc.SetTextForeground(wx.BLACK)
            dc.DrawText("I", 6, 1)

        def draw_underline(dc, size):
            font = wx.Font(wx.FontInfo(9))
            dc.SetFont(font)
            dc.SetTextForeground(wx.BLACK)
            dc.DrawText("U", 4, 1)
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            dc.DrawLine(4, 13, 12, 13)

        def draw_align(dc, size, align_type):
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            dc.SetBrush(wx.Brush(wx.BLACK))
            lines = [12, 8, 12, 10, 8]
            for i, length in enumerate(lines):
                y = 2 + i * 2.5
                if align_type == "left":
                    x = 2
                elif align_type == "center":
                    x = (size[0] - length) / 2
                else:  # right
                    x = size[0] - length - 2
                dc.DrawRectangle(int(x), int(y), int(length), 2)

        def draw_insert(dc, size):
            dc.SetPen(wx.Pen(wx.Colour("#00008B"), 2))  # Dark Blue
            dc.DrawLine(8, 2, 8, 14)  # Vertical line
            dc.DrawLine(2, 8, 14, 8)  # Horizontal line

        def draw_delete(dc, size):
            dc.SetPen(wx.Pen(wx.Colour("#8B0000"), 2))  # Dark Red
            dc.DrawLine(2, 8, 14, 8)  # Horizontal line

        def draw_view_switch(dc, size):
            dc.SetPen(wx.Pen(wx.BLACK, 1))
            dc.SetBrush(wx.Brush(wx.Colour("#E6E6FA")))
            dc.DrawRectangle(2, 2, 9, 9)
            dc.SetBrush(wx.Brush(wx.Colour("#ADD8E6")))
            dc.DrawRectangle(5, 5, 9, 9)

        def draw_find(dc, size):
            dc.SetPen(wx.Pen(wx.Colour("#4169E1"), 2))  # Royal Blue
            dc.SetBrush(wx.TRANSPARENT_BRUSH)
            dc.DrawCircle(7, 7, 5)  # Magnifying glass circle
            dc.DrawLine(11, 11, 15, 15)  # Handle

        def draw_undo(dc, size):
            dc.SetPen(wx.Pen(wx.Colour("#FFD700"), 2))  # Gold
            dc.SetBrush(wx.TRANSPARENT_BRUSH)
            # Simple arrow pointing left
            dc.DrawLine(10, 4, 6, 8)
            dc.DrawLine(6, 8, 10, 12)
            dc.DrawLine(6, 8, 14, 8)

        def draw_redo(dc, size):
            dc.SetPen(wx.Pen(wx.Colour("#2E8B57"), 2))  # Sea Green
            dc.SetBrush(wx.TRANSPARENT_BRUSH)
            # Simple arrow pointing right
            dc.DrawLine(6, 4, 10, 8)
            dc.DrawLine(10, 8, 6, 12)
            dc.DrawLine(2, 8, 10, 8)

        def draw_comma_style(dc, size):
            font = wx.Font(wx.FontInfo(8).Bold())
            dc.SetFont(font)
            dc.SetTextForeground(wx.BLACK)
            dc.DrawText(",00", 1, 1)

        def draw_percent_style(dc, size):
            font = wx.Font(wx.FontInfo(10).Bold())
            dc.SetFont(font)
            dc.SetTextForeground(wx.BLACK)
            dc.DrawText("%", 4, 1)

        def draw_transpose(dc, size):
            # Draw transpose matrix icon
            dc.SetPen(wx.Pen(wx.Colour(0, 0, 0), 1))
            # Draw original matrix
            dc.DrawRectangle(2, 2, 6, 4)
            dc.DrawLine(4, 2, 4, 6)
            dc.DrawLine(2, 4, 8, 4)
            # Draw arrow
            dc.DrawLine(9, 3, 11, 3)
            dc.DrawLine(10, 2, 11, 3)
            dc.DrawLine(10, 4, 11, 3)
            # Draw transposed matrix
            dc.DrawRectangle(12, 2, 4, 6)
            dc.DrawLine(14, 2, 14, 8)
            dc.DrawLine(12, 4, 16, 4)

        # Using professional icons instead of basic bitmaps
        from jedit2.ui.professional_icons import get_professional_icon

        # --- Create Professional Bitmaps ---
        paste_bmp = get_professional_icon("paste", (20, 20))
        cut_bmp = get_professional_icon("cut", (16, 16))
        copy_bmp = get_professional_icon("copy", (16, 16))
        bold_bmp = get_professional_icon("bold", (16, 16))
        italic_bmp = get_professional_icon("italic", (16, 16))
        underline_bmp = get_professional_icon("underline", (16, 16))
        align_left_bmp = get_professional_icon("align_left", (16, 16))
        align_center_bmp = get_professional_icon("align_center", (16, 16))
        align_right_bmp = get_professional_icon("align_right", (16, 16))
        insert_bmp = get_professional_icon("insert", (16, 16))
        delete_bmp = get_professional_icon("delete", (16, 16))
        view_switch_bmp = get_professional_icon("view_switch", (16, 16))
        find_bmp = get_professional_icon("find", (16, 16))
        undo_bmp = get_professional_icon("undo", (16, 16))
        redo_bmp = get_professional_icon("redo", (16, 16))
        comma_bmp = get_professional_icon("comma", (16, 16))
        percent_bmp = get_professional_icon("percent", (16, 16))

        home_page = RB.RibbonPage(self._ribbon, wx.ID_ANY, "Home")

        # --- Panel 1: Clipboard ---
        clipboard_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "Clipboard", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        panel_sizer = wx.BoxSizer(wx.HORIZONTAL)
        paste_button = wx.BitmapButton(
            clipboard_panel, wx.ID_PASTE, paste_bmp, size=(36, 36)
        )
        paste_button.SetToolTip("Paste (Ctrl+V)")
        panel_sizer.Add(paste_button, 0, wx.ALL, 2)
        cut_copy_sizer = wx.BoxSizer(wx.VERTICAL)
        cut_btn = wx.BitmapButton(clipboard_panel, wx.ID_CUT, cut_bmp, size=(36, 17))
        cut_btn.SetToolTip("Cut (Ctrl+X)")
        copy_btn = wx.BitmapButton(clipboard_panel, wx.ID_COPY, copy_bmp, size=(36, 17))
        copy_btn.SetToolTip("Copy (Ctrl+C)")
        cut_copy_sizer.Add(cut_btn, 1, wx.EXPAND | wx.BOTTOM, 1)
        cut_copy_sizer.Add(copy_btn, 1, wx.EXPAND | wx.TOP, 1)
        panel_sizer.Add(cut_copy_sizer, 0, wx.ALL | wx.EXPAND, 2)
        clipboard_panel.SetSizer(panel_sizer)

        # --- Panel 2: Font & Alignment ---
        font_align_panel = RB.RibbonPanel(
            home_page,
            wx.ID_ANY,
            "Font & Alignment",
            style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE,
        )
        font_align_sizer = wx.BoxSizer(wx.VERTICAL)
        font_row_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.bold_toggle = wx.ToggleButton(
            font_align_panel, self.ID_FORMAT_BOLD, "", size=(24, 24)
        )
        self.bold_toggle.SetBitmap(bold_bmp)
        self.bold_toggle.SetToolTip("Bold (Ctrl+B)")
        self.italic_toggle = wx.ToggleButton(
            font_align_panel, self.ID_FORMAT_ITALIC, "", size=(24, 24)
        )
        self.italic_toggle.SetBitmap(italic_bmp)
        self.italic_toggle.SetToolTip("Italic (Ctrl+I)")
        self.underline_toggle = wx.ToggleButton(
            font_align_panel, self.ID_FORMAT_UNDERLINE, "", size=(24, 24)
        )
        self.underline_toggle.SetBitmap(underline_bmp)
        self.underline_toggle.SetToolTip("Underline (Ctrl+U)")
        font_row_sizer.Add(self.bold_toggle, 0, wx.ALL, 1)
        font_row_sizer.Add(self.italic_toggle, 0, wx.ALL, 1)
        font_row_sizer.Add(self.underline_toggle, 0, wx.ALL, 1)
        align_row_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.left_align_btn = wx.Button(
            font_align_panel, self.ID_ALIGN_LEFT, "", size=(24, 24)
        )
        self.left_align_btn.SetBitmap(align_left_bmp)
        self.left_align_btn.SetToolTip("Align Left")
        self.center_align_btn = wx.Button(
            font_align_panel, self.ID_ALIGN_CENTER, "", size=(24, 24)
        )
        self.center_align_btn.SetBitmap(align_center_bmp)
        self.center_align_btn.SetToolTip("Align Center")
        self.right_align_btn = wx.Button(
            font_align_panel, self.ID_ALIGN_RIGHT, "", size=(24, 24)
        )
        self.right_align_btn.SetBitmap(align_right_bmp)
        self.right_align_btn.SetToolTip("Align Right")
        align_row_sizer.Add(self.left_align_btn, 0, wx.ALL, 1)
        align_row_sizer.Add(self.center_align_btn, 0, wx.ALL, 1)
        align_row_sizer.Add(self.right_align_btn, 0, wx.ALL, 1)
        font_align_sizer.Add(font_row_sizer)
        font_align_sizer.Add(align_row_sizer)
        font_align_panel.SetSizer(font_align_sizer)

        # --- Panel 3: Number ---
        number_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "Number", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        number_sizer = wx.GridSizer(2, 3, 2, 2)  # 2 rows, 3 cols, 2px gaps

        # Get professional icons
        currency_bmp = get_professional_icon("currency", (16, 16))
        percent_bmp = get_professional_icon("percent", (16, 16))
        comma_bmp = get_professional_icon("comma", (16, 16))
        inc_decimal_bmp = get_professional_icon("increase_decimal", (16, 16))
        dec_decimal_bmp = get_professional_icon("decrease_decimal", (16, 16))

        # Create bitmap buttons without backgrounds
        currency_btn = wx.BitmapButton(
            number_panel,
            self.ID_FORMAT_CURRENCY,
            currency_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        currency_btn.SetToolTip("Currency Format")

        percent_btn = wx.BitmapButton(
            number_panel,
            self.ID_FORMAT_PERCENT,
            percent_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        percent_btn.SetToolTip("Percent Format")

        comma_btn = wx.BitmapButton(
            number_panel,
            self.ID_FORMAT_COMMA,
            comma_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        comma_btn.SetToolTip("Comma Separator")

        inc_decimal_btn = wx.BitmapButton(
            number_panel,
            self.ID_INC_DECIMAL,
            inc_decimal_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        inc_decimal_btn.SetToolTip("Increase Decimal Places")

        dec_decimal_btn = wx.BitmapButton(
            number_panel,
            self.ID_DEC_DECIMAL,
            dec_decimal_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        dec_decimal_btn.SetToolTip("Decrease Decimal Places")

        # Add buttons to sizer
        number_sizer.Add(currency_btn, 0, wx.EXPAND)
        number_sizer.Add(percent_btn, 0, wx.EXPAND)
        number_sizer.Add(comma_btn, 0, wx.EXPAND)
        number_sizer.Add(inc_decimal_btn, 0, wx.EXPAND)
        number_sizer.Add(dec_decimal_btn, 0, wx.EXPAND)

        number_panel.SetSizer(number_sizer)

        # --- Panel 4: Cells & View ---
        cells_view_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "Cells & View", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        cells_view_sizer = wx.GridSizer(2, 3, 2, 2)  # 2 rows, 3 cols

        # Get professional icons
        insert_bmp = get_professional_icon("insert", (16, 16))
        delete_bmp = get_professional_icon("delete", (16, 16))
        transpose_bmp = get_professional_icon("transpose", (16, 16))
        view_switch_bmp = get_professional_icon("view_switch", (16, 16))

        # Create bitmap buttons without backgrounds
        insert_btn = wx.BitmapButton(
            cells_view_panel,
            self.ID_INSERT_ROW,
            insert_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        insert_btn.SetToolTip("Insert Row")

        delete_btn = wx.BitmapButton(
            cells_view_panel,
            self.ID_DELETE_ROW,
            delete_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        delete_btn.SetToolTip("Delete Row")

        transpose_btn = wx.BitmapButton(
            cells_view_panel,
            self.ID_TRANSPOSE,
            transpose_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        transpose_btn.SetToolTip("Transpose Data")

        view_switch_btn = wx.BitmapButton(
            cells_view_panel,
            self.ID_VIEW_SWITCH,
            view_switch_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        view_switch_btn.SetToolTip("Switch View")

        # Add buttons to sizer
        cells_view_sizer.Add(insert_btn, 0, wx.EXPAND)
        cells_view_sizer.Add(delete_btn, 0, wx.EXPAND)
        cells_view_sizer.Add(transpose_btn, 0, wx.EXPAND)
        cells_view_sizer.Add(view_switch_btn, 0, wx.EXPAND)

        cells_view_panel.SetSizer(cells_view_sizer)

        # --- Panel 5: Editing ---
        editing_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "Editing", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        editing_sizer = wx.GridSizer(2, 3, 2, 2)  # 2 rows, 3 cols, 2px gaps

        # Get professional icons
        find_bmp = get_professional_icon("find", (16, 16))
        undo_bmp = get_professional_icon("undo", (16, 16))
        redo_bmp = get_professional_icon("redo", (16, 16))
        filter_bmp = get_professional_icon("filter", (16, 16))
        calc_bmp = get_professional_icon("calculator", (16, 16))

        # Create and add buttons with proper styling to remove backgrounds
        find_btn = wx.BitmapButton(
            editing_panel, wx.ID_FIND, find_bmp, size=(24, 24), style=wx.BU_AUTODRAW
        )
        find_btn.SetToolTip("Find & Replace")

        undo_btn = wx.BitmapButton(
            editing_panel, wx.ID_UNDO, undo_bmp, size=(24, 24), style=wx.BU_AUTODRAW
        )
        undo_btn.SetToolTip("Undo")

        redo_btn = wx.BitmapButton(
            editing_panel, wx.ID_REDO, redo_bmp, size=(24, 24), style=wx.BU_AUTODRAW
        )
        redo_btn.SetToolTip("Redo")

        filter_btn = wx.BitmapButton(
            editing_panel,
            self.ID_ADVANCED_FILTER,
            filter_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        filter_btn.SetToolTip("Advanced Filter")

        calc_btn = wx.BitmapButton(
            editing_panel,
            self.ID_SHOW_CALCULATOR,
            calc_bmp,
            size=(24, 24),
            style=wx.BU_AUTODRAW,
        )
        calc_btn.SetToolTip("Calculator")

        editing_sizer.Add(find_btn, 0, wx.EXPAND)
        editing_sizer.Add(undo_btn, 0, wx.EXPAND)
        editing_sizer.Add(redo_btn, 0, wx.EXPAND)
        editing_sizer.Add(filter_btn, 0, wx.EXPAND)
        editing_sizer.Add(calc_btn, 0, wx.EXPAND)

        editing_panel.SetSizer(editing_sizer)

        # --- Panel 6: AI Assistant ---
        ai_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "AI Assistant", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        ai_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.ai_input = wx.TextCtrl(
            ai_panel, self.ID_AI_SUBMIT, style=wx.TE_PROCESS_ENTER, size=(200, -1)
        )
        self.ai_input.SetToolTip("Enter AI command and press Enter")
        ai_sizer.Add(self.ai_input, 1, wx.EXPAND | wx.ALL, 2)
        ai_panel.SetSizer(ai_sizer)

        # --- Panel 7: AI Debug ---
        debug_panel = RB.RibbonPanel(
            home_page, wx.ID_ANY, "AI Debug", style=RB.RIBBON_PANEL_NO_AUTO_MINIMISE
        )
        debug_sizer = wx.BoxSizer(wx.VERTICAL)

        # AI Debug display
        debug_label = wx.StaticText(debug_panel, wx.ID_ANY, "AI Response:")
        self.ai_debug_display = wx.TextCtrl(
            debug_panel,
            wx.ID_ANY,
            value="No AI query yet...",
            style=wx.TE_MULTILINE | wx.TE_READONLY,
            size=(300, 60),
        )
        self.ai_debug_display.SetFont(
            wx.Font(
                8, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL
            )
        )

        debug_sizer.Add(debug_label, 0, wx.ALL, 2)
        debug_sizer.Add(self.ai_debug_display, 1, wx.EXPAND | wx.ALL, 2)
        debug_panel.SetSizer(debug_sizer)

        self._ribbon.Realize()

    def _create_status_bar(self) -> None:
        """Create the status bar."""
        self.status_bar = self.CreateStatusBar()
        self.status_bar.SetStatusText("Ready")

    def _create_layout(self) -> None:
        """Create the layout."""
        # Main sizer for the frame
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        main_sizer.Add(self._ribbon, 0, wx.EXPAND)
        self.panel = wx.Panel(self)
        panel_sizer = wx.BoxSizer(wx.VERTICAL)
        self.notebook = wx.Notebook(self.panel)
        panel_sizer.Add(self.notebook, 1, wx.EXPAND | wx.ALL, border=5)
        self.panel.SetSizer(panel_sizer)
        main_sizer.Add(self.panel, 1, wx.EXPAND)
        self.SetSizer(main_sizer)
        self.SetMinSize((800, 600))
        self.CenterOnScreen()

    def _bind_events(self) -> None:
        """Bind all UI events to their handlers."""
        # Menu Events
        self.Bind(wx.EVT_MENU, self._on_new, id=wx.ID_NEW)
        self.Bind(wx.EVT_MENU, self._on_new_spreadsheet, id=self.ID_NEW_SPREADSHEET)
        self.Bind(wx.EVT_MENU, self._on_open, id=wx.ID_OPEN)
        self.Bind(wx.EVT_MENU, self._on_save, id=wx.ID_SAVE)
        self.Bind(wx.EVT_MENU, self._on_save_as, id=wx.ID_SAVEAS)
        self.Bind(wx.EVT_MENU, self._on_exit, id=wx.ID_EXIT)
        self.Bind(wx.EVT_MENU, self._on_undo, id=wx.ID_UNDO)
        self.Bind(wx.EVT_MENU, self._on_redo, id=wx.ID_REDO)
        self.Bind(wx.EVT_MENU, self._on_cut, id=wx.ID_CUT)
        self.Bind(wx.EVT_MENU, self._on_copy, id=wx.ID_COPY)
        self.Bind(wx.EVT_MENU, self._on_paste, id=wx.ID_PASTE)
        self.Bind(wx.EVT_MENU, self._on_ai_settings, id=self.ID_AI_SETTINGS)
        self.Bind(wx.EVT_MENU, self._on_find, id=wx.ID_FIND)
        self.Bind(wx.EVT_MENU, self._on_zoom_in, id=self.ID_ZOOM_IN)
        self.Bind(wx.EVT_MENU, self._on_zoom_out, id=self.ID_ZOOM_OUT)
        self.Bind(wx.EVT_MENU, self._on_freeze_panes, id=self.ID_FREEZE_PANES)
        self.Bind(wx.EVT_MENU, self._on_unfreeze_panes, id=self.ID_UNFREEZE_PANES)
        self.Bind(wx.EVT_MENU, self._on_switch_view, id=self.ID_VIEW_SWITCH)
        self.Bind(wx.EVT_MENU, self._on_bold, id=self.ID_FORMAT_BOLD)
        self.Bind(wx.EVT_MENU, self._on_italic, id=self.ID_FORMAT_ITALIC)
        self.Bind(wx.EVT_MENU, self._on_underline, id=self.ID_FORMAT_UNDERLINE)

        # Ribbon ButtonBar Events
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_switch_view, id=self.ID_VIEW_SWITCH
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_transpose, id=self.ID_TRANSPOSE
        )
        self.Bind(RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_find, id=wx.ID_FIND)
        self.Bind(RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_undo, id=wx.ID_UNDO)
        self.Bind(RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_redo, id=wx.ID_REDO)
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED,
            self._on_advanced_filter,
            id=self.ID_ADVANCED_FILTER,
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED,
            self._on_show_calculator,
            id=self.ID_SHOW_CALCULATOR,
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_insert_row, id=self.ID_INSERT_ROW
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED,
            self._on_insert_col,
            id=self.ID_INSERT_COLUMN,
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED, self._on_delete_row, id=self.ID_DELETE_ROW
        )
        self.Bind(
            RB.EVT_RIBBONBUTTONBAR_CLICKED,
            self._on_delete_col,
            id=self.ID_DELETE_COLUMN,
        )

        # Custom Button Events (for controls not in a button bar)
        self.Bind(wx.EVT_BUTTON, self._on_paste, id=wx.ID_PASTE)
        self.Bind(wx.EVT_BUTTON, self._on_cut, id=wx.ID_CUT)
        self.Bind(wx.EVT_BUTTON, self._on_copy, id=wx.ID_COPY)
        self.Bind(wx.EVT_TOGGLEBUTTON, self._on_bold, id=self.ID_FORMAT_BOLD)
        self.Bind(wx.EVT_TOGGLEBUTTON, self._on_italic, id=self.ID_FORMAT_ITALIC)
        self.Bind(wx.EVT_TOGGLEBUTTON, self._on_underline, id=self.ID_FORMAT_UNDERLINE)
        self.Bind(wx.EVT_BUTTON, self._on_align_left, id=self.ID_ALIGN_LEFT)
        self.Bind(wx.EVT_BUTTON, self._on_align_center, id=self.ID_ALIGN_CENTER)
        self.Bind(wx.EVT_BUTTON, self._on_align_right, id=self.ID_ALIGN_RIGHT)
        self.Bind(wx.EVT_BUTTON, self._on_format_percent, id=self.ID_FORMAT_PERCENT)
        self.Bind(wx.EVT_BUTTON, self._on_format_comma, id=self.ID_FORMAT_COMMA)
        self.Bind(wx.EVT_BUTTON, self._on_format_currency, id=self.ID_FORMAT_CURRENCY)
        self.Bind(wx.EVT_BUTTON, self._on_inc_decimal, id=self.ID_INC_DECIMAL)
        self.Bind(wx.EVT_BUTTON, self._on_dec_decimal, id=self.ID_DEC_DECIMAL)
        self.Bind(wx.EVT_BUTTON, self._on_insert_row, id=self.ID_INSERT_ROW)
        self.Bind(wx.EVT_BUTTON, self._on_delete_row, id=self.ID_DELETE_ROW)
        self.Bind(wx.EVT_BUTTON, self._on_find, id=wx.ID_FIND)
        self.Bind(wx.EVT_BUTTON, self._on_undo, id=wx.ID_UNDO)
        self.Bind(wx.EVT_BUTTON, self._on_redo, id=wx.ID_REDO)
        self.Bind(wx.EVT_BUTTON, self._on_switch_view, id=self.ID_VIEW_SWITCH)
        self.Bind(wx.EVT_BUTTON, self._on_transpose, id=self.ID_TRANSPOSE)
        self.Bind(wx.EVT_BUTTON, self._on_advanced_filter, id=self.ID_ADVANCED_FILTER)
        self.Bind(wx.EVT_BUTTON, self._on_show_calculator, id=self.ID_SHOW_CALCULATOR)

        # AI Panel Events
        self.Bind(wx.EVT_BUTTON, self._on_ai_submit, id=self.ID_AI_SUBMIT)
        self.ai_input.Bind(wx.EVT_TEXT_ENTER, self._on_ai_submit)

        # Ribbon Dropdown Events (removed - using individual buttons now)

        # Tab Context Menu Events
        self.notebook.Bind(wx.EVT_RIGHT_UP, self._on_tab_right_click)

    def _get_file_type(self, file_path: str) -> str:
        """Get file type based on extension.

        Args:
            file_path: Path to the file

        Returns:
            File type string
        """
        _, ext = os.path.splitext(file_path.lower())

        file_types = {
            ".txt": "text",
            ".py": "python",
            ".js": "javascript",
            ".html": "html",
            ".htm": "html",
            ".css": "css",
            ".json": "json",
            ".xml": "xml",
            ".md": "markdown",
            ".yml": "yaml",
            ".yaml": "yaml",
            ".csv": "csv",
        }

        return file_types.get(ext, "text")

    def _init_ui(self) -> None:
        """Initialize the core UI layout."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # The ribbon must be added to the frame's sizer first
        main_sizer.Add(self._ribbon, 0, wx.EXPAND)

        # The main panel contains the notebook
        self.panel = wx.Panel(self)
        panel_sizer = wx.BoxSizer(wx.VERTICAL)

        self.notebook = wx.Notebook(self.panel)
        first_tab = ViewContainer(self.notebook)
        self.notebook.AddPage(first_tab, "Editor")

        panel_sizer.Add(self.notebook, 1, wx.EXPAND)
        self.panel.SetSizer(panel_sizer)

        # Add the panel to the main sizer
        main_sizer.Add(self.panel, 1, wx.EXPAND)

        self.SetSizer(main_sizer)

    def _setup_accelerators(self) -> None:
        """Setup keyboard accelerators."""
        # Create accelerator table for keyboard shortcuts
        accelerator_entries = [
            (wx.ACCEL_CTRL, ord("N"), wx.ID_NEW),
            (wx.ACCEL_CTRL, ord("O"), wx.ID_OPEN),
            (wx.ACCEL_CTRL, ord("S"), wx.ID_SAVE),
            (wx.ACCEL_CTRL | wx.ACCEL_SHIFT, ord("S"), wx.ID_SAVEAS),
            (wx.ACCEL_CTRL, ord("Z"), wx.ID_UNDO),
            (wx.ACCEL_CTRL, ord("Y"), wx.ID_REDO),
            (wx.ACCEL_CTRL, ord("X"), wx.ID_CUT),
            (wx.ACCEL_CTRL, ord("C"), wx.ID_COPY),
            (wx.ACCEL_CTRL, ord("V"), wx.ID_PASTE),
        ]

        accelerator_table = wx.AcceleratorTable(accelerator_entries)
        self.SetAcceleratorTable(accelerator_table)

    def _on_new(self, event: wx.CommandEvent) -> None:
        """Handle new text file event."""
        new_tab = ViewContainer(self.notebook)
        self.notebook.AddPage(new_tab, "Untitled")
        self.notebook.SetSelection(self.notebook.GetPageCount() - 1)
        self.status_bar.SetStatusText("New text file created")

    def _on_new_spreadsheet(self, event: wx.CommandEvent) -> None:
        """Handle new spreadsheet event."""
        # Create a new container tab first
        new_tab = ViewContainer(self.notebook)

        # Get the grid view from the container. It's already created, but we need
        # to ensure it's properly set up.
        grid = new_tab.grid_view

        # This is where we ensure the grid is properly initialized for a new sheet
        # The parent is the notebook, and it's a new sheet, so format is 'csv'
        grid.data_format = "csv"

        grid.CreateGrid(100, 26)  # Create a default grid
        # Set column labels A, B, C...
        for i in range(26):
            grid.SetColLabelValue(i, chr(ord("A") + i))

        # Switch the container to show the grid view.
        new_tab.grid_view.Show()
        new_tab.text_view.Hide()
        new_tab.current_view = "grid"
        new_tab.Layout()

        self.notebook.AddPage(new_tab, "Untitled Sheet")
        self.notebook.SetSelection(self.notebook.GetPageCount() - 1)
        self.status_bar.SetStatusText("New spreadsheet created")

    def _on_open(self, event: wx.CommandEvent) -> None:
        """Handle open event for both text and spreadsheet files."""
        current_page = self.notebook.GetCurrentPage()
        is_modified = False

        if isinstance(current_page, ViewContainer):
            # Check modification status of the active view inside the container
            if current_page.current_view == "text":
                if current_page.text_view.IsModified():
                    is_modified = True
            # Grid modification check is still manual

        if is_modified:
            if (
                wx.MessageBox(
                    "The current file has unsaved changes. Save now?",
                    "Confirm",
                    wx.YES_NO | wx.ICON_QUESTION,
                )
                == wx.YES
            ):
                self._on_save(event)

        wildcard = (
            "All Supported Files|*.txt;*.csv;*.json;*.py;*.yaml;*.md;"
            "*.html;*.css;*.js;*.xml|"
            "Text files (*.txt)|*.txt|"
            "CSV files (*.csv)|*.csv|"
            "JSON files (*.json)|*.json|"
            "Python files (*.py)|*.py|"
            "YAML files (*.yaml)|*.yaml;*.yml|"
            "All files (*.*)|*.*"
        )
        dialog = wx.FileDialog(
            self,
            "Open file",
            wildcard=wildcard,
            style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST,
        )
        dialog.SetSize((1000, 700))
        dialog.CenterOnParent()

        if dialog.ShowModal() == wx.ID_OK:
            path = dialog.GetPath()
            self._open_file(path)

        dialog.Destroy()

    def _open_grid_based_file(self, path: str, file_type: str) -> None:
        """Opens a grid-based file (CSV, JSON, YAML) in a new tab."""
        try:
            # Load data using appropriate handler
            if file_type == "csv":
                data = csv_handler.parse_csv(path)
            elif file_type == "json":
                data = json_handler.parse_json(path)
            elif file_type == "yaml":
                data = yaml_handler.parse_yaml(path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")

            if not data:
                # Create an empty sheet if file is empty or cannot be parsed
                # into a table
                self._on_new_spreadsheet(wx.CommandEvent())
                return

            # rows = len(data)  # Not used currently

            # Create a new container tab
            new_tab = ViewContainer(self.notebook)

            # Populate the grid view
            grid = new_tab.grid_view
            grid.data_format = file_type

            grid.set_data(data)

            # Populate the text view with the raw file content
            with open(path, "r", encoding="utf-8", errors="replace") as f:
                raw_content = f.read()
            new_tab.text_view.SetText(raw_content)

            # Show the appropriate view
            new_tab.text_view.Hide()
            new_tab.grid_view.Show()
            new_tab.current_view = "grid"
            new_tab.Layout()

            self.notebook.AddPage(new_tab, os.path.basename(path))
            self.notebook.SetSelection(self.notebook.GetPageCount() - 1)
            self.status_bar.SetStatusText(
                f"Opened {path} as {file_type.upper()} spreadsheet"
            )
        except Exception as e:
            error_msg = f"Error opening {file_type.upper()} file: {str(e)}"
            wx.MessageBox(
                error_msg, f"Error Opening {file_type.upper()}", wx.OK | wx.ICON_ERROR
            )

    def _open_text_based_file(self, path: str, file_type: str) -> None:
        """Opens a text-based file in a new styled editor tab, with formatting."""
        try:
            with open(path, "r", encoding="utf-8", errors="replace") as f:
                content = f.read()

            # Create a new container tab
            new_tab = ViewContainer(self.notebook)
            new_editor = new_tab.text_view

            # Apply formatting and syntax highlighting based on file type
            if file_type == "json":
                try:
                    # Pretty-print JSON
                    obj = json.loads(content)
                    content = json.dumps(obj, indent=4)
                except json.JSONDecodeError:
                    # File is not valid JSON, load as plain text
                    pass
                new_editor.SetLexer(stc.STC_LEX_JSON)
            elif file_type == "yaml":
                new_editor.SetLexer(stc.STC_LEX_YAML)
            elif file_type == "python":
                new_editor.SetLexer(stc.STC_LEX_PYTHON)
            elif file_type == "html":
                new_editor.SetLexer(stc.STC_LEX_HTML)
            elif file_type == "xml":
                new_editor.SetLexer(stc.STC_LEX_XML)
            elif file_type == "css":
                new_editor.SetLexer(stc.STC_LEX_CSS)
            elif file_type == "javascript":
                new_editor.SetLexer(stc.STC_LEX_ESCRIPT)
            else:
                new_editor.SetLexer(stc.STC_LEX_NULL)

            new_editor.SetText(content)

            # Also populate the grid view in case the user wants to switch
            try:
                if file_type == "json":
                    data = json_handler.parse_json(path)
                    if data:
                        new_tab.grid_view.set_data(data)
            except Exception:
                pass  # Fail silently if it can't be a grid

            self.notebook.AddPage(new_tab, os.path.basename(path))
            self.notebook.SetSelection(self.notebook.GetPageCount() - 1)
            self.status_bar.SetStatusText(f"Opened {path}")
        except Exception as e:
            wx.MessageBox(str(e), "Error Opening File", wx.OK | wx.ICON_ERROR)

    def _on_save(self, event: wx.CommandEvent) -> None:
        """Handle save event for the active tab."""
        current_page = self.notebook.GetCurrentPage()
        if not isinstance(current_page, ViewContainer):
            return

        if current_page.current_view == "text":
            self._save_text(current_page.text_view)
        else:  # 'grid'
            self._save_grid_file(current_page.grid_view)

    def _save_text(
        self, editor: stc.StyledTextCtrl, force_dialog: bool = False
    ) -> None:
        """Saves the content of a text editor tab to a file."""
        filepath = getattr(editor, "filepath", None)
        if force_dialog or not filepath:
            with wx.FileDialog(
                self,
                "Save As",
                wildcard="Text files (*.txt)|*.txt|All files (*.*)|*.*",
                style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT,
            ) as dialog:
                if dialog.ShowModal() == wx.ID_CANCEL:
                    return
                filepath = dialog.GetPath()

        try:
            with open(filepath, "w", encoding="utf-8") as file:
                file.write(editor.GetText())
            page_index = self.notebook.FindPage(editor)
            if page_index != wx.NOT_FOUND:
                self.notebook.SetPageText(page_index, os.path.basename(filepath))
            self.status_bar.SetStatusText(f"Saved to {filepath}")
        except Exception as e:
            wx.MessageBox(str(e), "Error", wx.OK | wx.ICON_ERROR)

        editor.SetSavePoint()
        self.status_bar.SetStatusText(f"Saved to {filepath}")

    def _save_grid_file(
        self, grid: SpreadsheetView, force_dialog: bool = False
    ) -> None:
        """Saves the grid data to a file, prompting for format if needed."""
        filepath = getattr(grid, "filepath", None)
        data_format = getattr(grid, "data_format", "csv")  # Default to csv

        if force_dialog or not filepath:
            wildcard = (
                "CSV files (*.csv)|*.csv|"
                "JSON files (*.json)|*.json|"
                "YAML files (*.yaml)|*.yaml|"
                "All files (*.*)|*.*"
            )
            with wx.FileDialog(
                self,
                "Save Grid As",
                wildcard=wildcard,
                style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT,
            ) as dialog:
                if dialog.ShowModal() == wx.ID_CANCEL:
                    return
                filepath = dialog.GetPath()

                # Update format based on chosen extension
                ext = os.path.splitext(filepath)[1].lower()
                if ext == ".csv":
                    data_format = "csv"
                elif ext == ".json":
                    data_format = "json"
                elif ext == ".yaml":
                    data_format = "yaml"

        if not filepath:
            return

        try:
            # Prepare data from grid
            data = []
            for r in range(grid.GetNumberRows()):
                row = [grid.GetCellValue(r, c) for c in range(grid.GetNumberCols())]
                data.append(row)

            # Save using the appropriate handler
            if data_format == "csv":
                csv_handler.save_csv(filepath, data)
            elif data_format == "json":
                json_handler.save_json(filepath, data)
            elif data_format == "yaml":
                yaml_handler.save_yaml(filepath, data)

            grid.filepath = filepath
            grid.data_format = data_format
            # Update tab name
            page_idx = self.notebook.FindPage(grid)
            if page_idx != wx.NOT_FOUND:
                self.notebook.SetPageText(page_idx, os.path.basename(filepath))

            self.status_bar.SetStatusText(f"Grid saved to {filepath}")
        except Exception as e:
            wx.MessageBox(
                str(e), f"Error Saving {data_format.upper()}", wx.OK | wx.ICON_ERROR
            )

    def _on_save_as(self, event: wx.CommandEvent) -> None:
        """Handle 'Save As' for the active tab."""
        current_page = self.notebook.GetCurrentPage()
        if not isinstance(current_page, ViewContainer):
            return

        if current_page.current_view == "text":
            self._save_text(current_page.text_view, force_dialog=True)
        else:  # 'grid'
            self._save_grid_file(current_page.grid_view, force_dialog=True)

    def _on_exit(self, event: wx.CommandEvent) -> None:
        """Handle exit event."""
        # Check if any tabs have unsaved changes
        for i in range(self.notebook.GetPageCount()):
            page = self.notebook.GetPage(i)
            if isinstance(page, ViewContainer):
                if page.current_view == "text" and page.text_view.IsModified():
                    result = wx.MessageBox(
                        "You have unsaved changes. Save before exiting?",
                        "Unsaved Changes",
                        wx.YES_NO | wx.CANCEL | wx.ICON_QUESTION,
                    )
                    if result == wx.YES:
                        self._save_text(page.text_view)
                    elif result == wx.CANCEL:
                        return
                    break
        self.Close()

    def _on_undo(self, event: wx.CommandEvent) -> None:
        """Handle undo event."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer):
            if page.current_view == "text":
                if page.text_view.CanUndo():
                    page.text_view.Undo()
                    self.status_bar.SetStatusText("Undo")
                else:
                    self.status_bar.SetStatusText("Nothing to undo")
            elif page.current_view == "grid":
                grid = page.grid_view
                if grid.can_undo():
                    if grid.perform_undo():
                        self.status_bar.SetStatusText("Undo grid operation")
                    else:
                        self.status_bar.SetStatusText("Undo failed")
                else:
                    self.status_bar.SetStatusText("Nothing to undo")

    def _on_redo(self, event: wx.CommandEvent) -> None:
        """Handle redo event."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer):
            if page.current_view == "text":
                if page.text_view.CanRedo():
                    page.text_view.Redo()
                    self.status_bar.SetStatusText("Redo")
                else:
                    self.status_bar.SetStatusText("Nothing to redo")
            elif page.current_view == "grid":
                grid = page.grid_view
                if grid.can_redo():
                    if grid.perform_redo():
                        self.status_bar.SetStatusText("Redo grid operation")
                    else:
                        self.status_bar.SetStatusText("Redo failed")
                else:
                    self.status_bar.SetStatusText("Nothing to redo")

    def _on_cut(self, event: wx.CommandEvent) -> None:
        """Handle cut event."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer):
            if page.current_view == "text":
                if page.text_view.CanCut():
                    page.text_view.Cut()
                    self.status_bar.SetStatusText("Cut text")
            elif page.current_view == "grid":
                # For grid: copy then clear the selection
                grid = page.grid_view
                tab_name = self.notebook.GetPageText(self.notebook.GetSelection())
                self._copy_grid_selection(grid, tab_name)
                self._clear_grid_selection(grid)
                self.status_bar.SetStatusText("Cut cells")

    def _on_copy(self, event: wx.CommandEvent) -> None:
        """Handle copy event."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer):
            tab_name = self.notebook.GetPageText(self.notebook.GetSelection())

            if page.current_view == "text":
                active_view_focus = self.FindFocus()
                if isinstance(active_view_focus, stc.StyledTextCtrl):
                    selected_text = active_view_focus.GetSelectedText()
                    if selected_text:
                        self.clipboard_manager.copy_text(selected_text, tab_name)
                        self.status_bar.SetStatusText(
                            f"Copied text ({len(selected_text)} characters)"
                        )
                    else:
                        active_view_focus.Copy()  # Fallback to system copy

            elif page.current_view == "grid":
                grid = page.grid_view
                self._copy_grid_selection(grid, tab_name)

    def _on_paste(self, event: wx.CommandEvent) -> None:
        """Handle paste event."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer):
            if page.current_view == "text":
                active_view_focus = self.FindFocus()
                if isinstance(active_view_focus, stc.StyledTextCtrl):
                    active_view_focus.Paste()

            elif page.current_view == "grid":
                grid = page.grid_view
                self._paste_to_grid(grid)

    def _on_index_manager(self, event: wx.CommandEvent) -> None:
        """Handle index manager event."""
        dialog = IndexDialog(self)
        dialog.ShowModal()
        dialog.Destroy()

    def _on_memory_manager(self, event: wx.CommandEvent) -> None:
        """Handle memory manager event."""
        dialog = MemoryDialog(self)
        dialog.ShowModal()
        dialog.Destroy()

    def _on_cache_manager(self, event: wx.CommandEvent) -> None:
        """Handle cache manager event."""
        dialog = CacheDialog(self, self.cache_manager)
        dialog.ShowModal()
        dialog.Destroy()

    def _on_lazy_loading(self, event: wx.CommandEvent) -> None:
        """Handle lazy loading event."""
        dialog = LazyDialog(self, self.lazy_loader)
        dialog.ShowModal()
        dialog.Destroy()

    def _on_error_report(self, event: wx.CommandEvent) -> None:
        """Handle error report event."""
        dialog = ErrorReportDialog(self)
        dialog.ShowModal()
        dialog.Destroy()

    def _on_help(self, event: wx.CommandEvent) -> None:
        """Handle help event."""
        wx.MessageBox(
            "JEdit2 Help\n\n"
            "1. File operations: New, Open, Save\n"
            "2. Edit operations: Cut, Copy, Paste\n"
            "3. View operations: Zoom In/Out\n"
            "4. Tools: Validate, Index Manager, Memory Manager, Cache Manager, "
            "Lazy Loading, Error Report",
            "Help",
            wx.OK | wx.ICON_INFORMATION,
        )

    def _on_about(self, event: wx.CommandEvent) -> None:
        """Handle about event."""
        wx.MessageBox(
            "JEdit2\n\n" "A modern text editor with advanced features.",
            "About",
            wx.OK | wx.ICON_INFORMATION,
        )

    def _on_switch_view(self, event: wx.CommandEvent) -> None:
        """Handle switching between text and grid view."""
        current_page = self.notebook.GetCurrentPage()
        if isinstance(current_page, ViewContainer):
            current_page.switch_view()

    def _get_current_grid(self) -> Optional[SpreadsheetView]:
        """Gets the currently active grid if it's visible."""
        page = self.notebook.GetCurrentPage()
        if isinstance(page, ViewContainer) and page.current_view == "grid":
            return page.grid_view
        return None

    # --- Start of Format Event Handlers ---

    def _apply_to_selected_cells(self, func: Callable[[int, int], None]) -> None:
        """Helper to apply a function to all selected cells in the grid."""
        grid = self._get_current_grid()
        if not grid:
            return

        # This covers individual cell selections
        cells = grid.GetSelectedCells()
        if not cells:  # If no individual cells, check for blocks
            top_left = grid.GetSelectionBlockTopLeft()
            bottom_right = grid.GetSelectionBlockBottomRight()
            if top_left and bottom_right:
                for row in range(top_left[0].GetRow(), bottom_right[0].GetRow() + 1):
                    for col in range(
                        top_left[0].GetCol(), bottom_right[0].GetCol() + 1
                    ):
                        func(row, col)
            else:  # If no blocks, check for row/col selections
                rows = grid.GetSelectedRows()
                cols = grid.GetSelectedCols()
                if rows:
                    for row in rows:
                        for col in range(grid.GetNumberCols()):
                            func(row, col)
                if cols:
                    for col in cols:
                        for row in range(grid.GetNumberRows()):
                            func(row, col)
        else:  # Process individually selected cells
            for cell in cells:
                func(cell.GetRow(), cell.GetCol())
        grid.ForceRefresh()

    def _on_align_left(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        self._apply_to_selected_cells(
            lambda r, c: grid.SetCellAlignment(
                r, c, wx.ALIGN_LEFT, wx.ALIGN_CENTRE_VERTICAL
            )
        )

    def _on_align_center(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        self._apply_to_selected_cells(
            lambda r, c: grid.SetCellAlignment(
                r, c, wx.ALIGN_CENTRE, wx.ALIGN_CENTRE_VERTICAL
            )
        )

    def _on_align_right(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        self._apply_to_selected_cells(
            lambda r, c: grid.SetCellAlignment(
                r, c, wx.ALIGN_RIGHT, wx.ALIGN_CENTRE_VERTICAL
            )
        )

    def _on_bold(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return

        def toggle_bold(r, c):
            font = grid.GetCellFont(r, c)
            if not font.IsOk():
                font = grid.GetDefaultCellFont()

            is_bold = font.GetWeight() == wx.FONTWEIGHT_BOLD
            font.SetWeight(wx.FONTWEIGHT_NORMAL if is_bold else wx.FONTWEIGHT_BOLD)
            grid.SetCellFont(r, c, font)

        self._apply_to_selected_cells(toggle_bold)

    def _on_italic(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return

        def toggle_italic(r, c):
            font = grid.GetCellFont(r, c)
            if not font.IsOk():
                font = grid.GetDefaultCellFont()

            is_italic = font.GetStyle() == wx.FONTSTYLE_ITALIC
            font.SetStyle(wx.FONTSTYLE_NORMAL if is_italic else wx.FONTSTYLE_ITALIC)
            grid.SetCellFont(r, c, font)

        self._apply_to_selected_cells(toggle_italic)

    def _on_insert_row(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        pos = grid.GetGridCursorRow()
        grid.save_state_for_undo("insert_row", row_pos=pos)
        grid.InsertRows(pos=pos, numRows=1)

    def _on_delete_row(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        pos = grid.GetGridCursorRow()
        if grid.GetNumberRows() > 1:  # Don't delete the last row
            grid.save_state_for_undo("delete_row", row_pos=pos)
            grid.DeleteRows(pos=pos, numRows=1)

    def _on_insert_col(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        pos = grid.GetGridCursorCol()
        grid.save_state_for_undo("insert_col", col_pos=pos)
        grid.InsertCols(pos=pos, numCols=1)

    def _on_delete_col(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return
        pos = grid.GetGridCursorCol()
        if grid.GetNumberCols() > 1:  # Don't delete the last col
            grid.save_state_for_undo("delete_col", col_pos=pos)
            grid.DeleteCols(pos=pos, numCols=1)

    def _on_inc_decimal(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return

        def inc_decimal(r, c):
            val = grid.GetCellValue(r, c)
            try:
                num = float(val)
                decimals = len(val.split(".")[-1]) if "." in val else 0
                grid.SetCellValue(r, c, f"{num:.{decimals + 1}f}")
            except (ValueError, IndexError):
                pass  # Ignore non-float cells

        self._apply_to_selected_cells(inc_decimal)

    def _on_dec_decimal(self, event: wx.CommandEvent) -> None:
        grid = self._get_current_grid()
        if not grid:
            return

        def dec_decimal(r, c):
            val = grid.GetCellValue(r, c)
            try:
                num = float(val)
                decimals = len(val.split(".")[-1]) if "." in val else 0
                if decimals > 0:
                    grid.SetCellValue(r, c, f"{num:.{decimals - 1}f}")
            except (ValueError, IndexError):
                pass  # Ignore non-float cells

        self._apply_to_selected_cells(dec_decimal)

    def _on_underline(self, event: wx.CommandEvent) -> None:
        """Handle underline formatting."""
        grid = self._get_current_grid()
        if not grid:
            return

        def toggle_underline(r, c):
            font = grid.GetCellFont(r, c)
            if not font.IsOk():
                font = grid.GetDefaultCellFont()

            is_underlined = font.GetUnderlined()
            font.SetUnderlined(not is_underlined)
            grid.SetCellFont(r, c, font)

        self._apply_to_selected_cells(toggle_underline)

    def _on_zoom_in(self, event: wx.CommandEvent) -> None:
        """Handle zoom in."""
        current_page = self.notebook.GetCurrentPage()
        if isinstance(current_page, ViewContainer):
            if current_page.current_view == "text":
                editor = current_page.text_view
                zoom = editor.GetZoom()
                editor.SetZoom(min(zoom + 1, 20))  # Max zoom 20
            elif current_page.current_view == "grid":
                grid = current_page.grid_view
                font = grid.GetDefaultCellFont()
                size = font.GetPointSize()
                font.SetPointSize(min(size + 1, 72))  # Max size 72
                grid.SetDefaultCellFont(font)
                grid.ForceRefresh()

    def _on_zoom_out(self, event: wx.CommandEvent) -> None:
        """Handle zoom out."""
        current_page = self.notebook.GetCurrentPage()
        if isinstance(current_page, ViewContainer):
            if current_page.current_view == "text":
                editor = current_page.text_view
                zoom = editor.GetZoom()
                editor.SetZoom(max(zoom - 1, -10))  # Min zoom -10
            elif current_page.current_view == "grid":
                grid = current_page.grid_view
                font = grid.GetDefaultCellFont()
                size = font.GetPointSize()
                font.SetPointSize(max(size - 1, 6))  # Min size 6
                grid.SetDefaultCellFont(font)
                grid.ForceRefresh()

    def _on_view_switch(self, event: wx.CommandEvent) -> None:
        """Handle view switching."""
        current_page = self.notebook.GetCurrentPage()
        if isinstance(current_page, ViewContainer):
            current_page.switch_view()

    def _on_format_comma(self, event: wx.CommandEvent) -> None:
        """Handle comma formatting (thousands separator)."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Save current state for undo
        self._save_formatting_state_for_undo(grid, "format_comma")

        def format_comma(r, c):
            value = grid.GetCellValue(r, c)
            try:
                # Try to parse as number and format with commas
                num = float(value.replace(",", ""))
                formatted = f"{num:,.0f}" if num.is_integer() else f"{num:,.2f}"
                grid.SetCellValue(r, c, formatted)
            except ValueError:
                pass  # Not a number, skip formatting

        self._apply_to_selected_cells(format_comma)

    def _on_format_percent(self, event: wx.CommandEvent) -> None:
        """Handle percentage formatting."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Save current state for undo
        self._save_formatting_state_for_undo(grid, "format_percent")

        def format_percent(r, c):
            value = grid.GetCellValue(r, c)
            try:
                # Try to parse as number and format as percentage
                num = float(value.replace("%", "").replace(",", ""))
                # If the number is between 0 and 1, assume it's already a decimal
                if 0 <= num <= 1:
                    formatted = f"{num * 100:.1f}%"
                else:
                    formatted = f"{num:.1f}%"
                grid.SetCellValue(r, c, formatted)
            except ValueError:
                pass  # Not a number, skip formatting

        self._apply_to_selected_cells(format_percent)

    def _on_format_currency(self, event: wx.CommandEvent) -> None:
        """Handle currency formatting."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Save current state for undo
        self._save_formatting_state_for_undo(grid, "format_currency")

        def format_currency(r, c):
            value = grid.GetCellValue(r, c)
            try:
                # Try to parse as number and format as currency
                num = float(value.replace("$", "").replace(",", ""))
                formatted = f"${num:,.2f}"
                grid.SetCellValue(r, c, formatted)
            except ValueError:
                pass  # Not a number, skip formatting

        self._apply_to_selected_cells(format_currency)

    def _save_formatting_state_for_undo(
        self, grid: SpreadsheetView, action_type: str
    ) -> None:
        """Save the current cell values for formatting undo."""
        # Get selected cells
        selected_cells = []
        if grid.GetSelectedCells():
            selected_cells = grid.GetSelectedCells()
        elif grid.GetSelectionBlockTopLeft() and grid.GetSelectionBlockBottomRight():
            # Block selection
            top_left = grid.GetSelectionBlockTopLeft()[0]
            bottom_right = grid.GetSelectionBlockBottomRight()[0]
            for row in range(top_left[0], bottom_right[0] + 1):
                for col in range(top_left[1], bottom_right[1] + 1):
                    selected_cells.append((row, col))
        else:
            # Single cell
            selected_cells = [(grid.GetGridCursorRow(), grid.GetGridCursorCol())]

        # Save original values
        cell_data = []
        for row, col in selected_cells:
            cell_data.append((row, col, grid.GetCellValue(row, col)))

        grid.save_state_for_undo(action_type, cell_data=cell_data)

    def _on_manage_dropdown(self, event: RB.RibbonButtonBarEvent) -> None:
        """Handle management dropdown menu."""
        # Create popup menu for management tools
        menu = wx.Menu()

        # Add menu items for each management tool
        menu.Append(self.ID_INDEX_MANAGER, "Index Manager", "Manage data indexing")
        menu.Append(self.ID_MEMORY_MANAGER, "Memory Manager", "Monitor memory usage")
        menu.Append(self.ID_CACHE_MANAGER, "Cache Manager", "Manage data caching")
        menu.Append(self.ID_LAZY_LOADING, "Lazy Loading", "Configure lazy loading")
        menu.Append(self.ID_ERROR_REPORT, "Error Report", "View error reports")
        menu.AppendSeparator()
        menu.Append(
            self.ID_DATA_VALIDATION,
            "Format Validation",
            "Validate file format and syntax",
        )

        # Bind menu events
        self.Bind(wx.EVT_MENU, self._on_index_manager, id=self.ID_INDEX_MANAGER)
        self.Bind(wx.EVT_MENU, self._on_memory_manager, id=self.ID_MEMORY_MANAGER)
        self.Bind(wx.EVT_MENU, self._on_cache_manager, id=self.ID_CACHE_MANAGER)
        self.Bind(wx.EVT_MENU, self._on_lazy_loading, id=self.ID_LAZY_LOADING)
        self.Bind(wx.EVT_MENU, self._on_error_report, id=self.ID_ERROR_REPORT)
        self.Bind(wx.EVT_MENU, self._on_format_validation, id=self.ID_DATA_VALIDATION)

        # Show the popup menu
        self.PopupMenu(menu)
        menu.Destroy()

    def _on_transpose(self, event: wx.CommandEvent) -> None:
        """Handle transpose operation - swap rows and columns."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Get current grid dimensions
        rows = grid.GetNumberRows()
        cols = grid.GetNumberCols()

        if rows == 0 or cols == 0:
            return

        # Create a matrix to hold the transposed data
        transposed_data = []
        for col in range(cols):
            row_data = []
            for row in range(rows):
                value = grid.GetCellValue(row, col)
                row_data.append(value)
            transposed_data.append(row_data)

        # Clear the existing grid
        if grid.GetNumberRows() > 0:
            grid.DeleteRows(0, grid.GetNumberRows())
        if grid.GetNumberCols() > 0:
            grid.DeleteCols(0, grid.GetNumberCols())

        # Create new grid with transposed dimensions
        grid.AppendRows(cols)  # Original cols become new rows
        grid.AppendCols(rows)  # Original rows become new cols

        # Fill with transposed data
        for row in range(cols):
            for col in range(rows):
                grid.SetCellValue(row, col, transposed_data[row][col])

        # Update column labels
        for i in range(rows):
            if i < 26:
                grid.SetColLabelValue(i, chr(ord("A") + i))
            else:
                # For columns beyond Z, use AA, AB, etc.
                first = chr(ord("A") + (i // 26) - 1)
                second = chr(ord("A") + (i % 26))
                grid.SetColLabelValue(i, first + second)

        grid.ForceRefresh()
        self.status_bar.SetStatusText("Data transposed successfully")

    def _on_format_validation(self, event: wx.CommandEvent) -> None:
        """Handle format validation for the current file."""
        # Get the current page and its content
        current_page = self.notebook.GetCurrentPage()
        if not isinstance(current_page, ViewContainer):
            wx.MessageBox(
                "No file is currently open for validation.",
                "No File",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        # Get content and file type
        content = ""
        file_type = "txt"

        if current_page.current_view == "text":
            # Text view - get content from text editor
            editor = current_page.text_view
            content = editor.GetText()

            # Try to determine file type from tab name or extension
            tab_index = self.notebook.GetSelection()
            tab_name = self.notebook.GetPageText(tab_index)

            if "." in tab_name:
                file_type = tab_name.split(".")[-1]
            elif hasattr(current_page, "file_path") and current_page.file_path:
                file_type = self._get_file_type(current_page.file_path)

        elif current_page.current_view == "grid":
            # Grid view - validate based on the data format
            grid = current_page.grid_view
            file_type = getattr(grid, "data_format", "csv")

            # For CSV, reconstruct the CSV content
            if file_type == "csv":
                rows = []
                for row in range(grid.GetNumberRows()):
                    row_data = []
                    for col in range(grid.GetNumberCols()):
                        cell_value = grid.GetCellValue(row, col)
                        # Escape quotes and commas if needed
                        if "," in cell_value or '"' in cell_value:
                            cell_value = f'"{cell_value.replace('"', '""')}"'
                        row_data.append(cell_value)
                    rows.append(",".join(row_data))
                content = "\n".join(rows)
            else:
                wx.MessageBox(
                    f"Format validation not supported for {file_type} in grid view.",
                    "Unsupported Format",
                    wx.OK | wx.ICON_INFORMATION,
                )
                return

        if not content.strip():
            wx.MessageBox(
                "No content to validate.", "Empty File", wx.OK | wx.ICON_INFORMATION
            )
            return

        # Perform validation
        result = self.format_validator.validate_format(content, file_type)

        # Show results dialog
        dialog = FormatValidationDialog(self, result, file_type)
        dialog.ShowModal()
        dialog.Destroy()

    # --- End of Format Event Handlers ---

    # --- Tab Management Methods (Phase 3) ---

    def _on_tab_right_click(self, event: wx.MouseEvent) -> None:
        """Handle right-click on tab for context menu."""
        # Get the tab index at the click position
        tab_index = self._get_tab_index_at_position(event.GetPosition())

        if tab_index != -1:
            # Store the tab index for context menu actions
            self._context_tab_index = tab_index

            # Create context menu
            menu = wx.Menu()
            menu.Append(self.ID_TAB_CLOSE, "Close Tab", "Close this tab")
            menu.Append(
                self.ID_TAB_CLOSE_OTHERS, "Close Other Tabs", "Close all other tabs"
            )
            menu.Append(self.ID_TAB_CLOSE_ALL, "Close All Tabs", "Close all tabs")
            menu.AppendSeparator()
            menu.Append(self.ID_TAB_RENAME, "Rename Tab", "Rename this tab")

            # Bind menu events
            self.Bind(wx.EVT_MENU, self._on_tab_close, id=self.ID_TAB_CLOSE)
            self.Bind(
                wx.EVT_MENU, self._on_tab_close_others, id=self.ID_TAB_CLOSE_OTHERS
            )
            self.Bind(wx.EVT_MENU, self._on_tab_close_all, id=self.ID_TAB_CLOSE_ALL)
            self.Bind(wx.EVT_MENU, self._on_tab_rename, id=self.ID_TAB_RENAME)

            # Show menu
            self.PopupMenu(menu)
            menu.Destroy()

    def _get_tab_index_at_position(self, pos: wx.Point) -> int:
        """Get the tab index at the given position.

        Args:
            pos: Mouse position

        Returns:
            Tab index or -1 if no tab at position
        """
        # Hit test for tabs
        for i in range(self.notebook.GetPageCount()):
            tab_rect = (
                self.notebook.GetTabRect(i)
                if hasattr(self.notebook, "GetTabRect")
                else None
            )
            if tab_rect and tab_rect.Contains(pos):
                return i

        # Fallback: use current selection if hit test not available
        if self.notebook.GetPageCount() > 0:
            return self.notebook.GetSelection()

        return -1

    def _get_all_tabs(self) -> List[Tuple[int, wx.Window, str]]:
        """Get all open tabs.

        Returns:
            List of (index, page, title) tuples
        """
        tabs = []
        for i in range(self.notebook.GetPageCount()):
            page = self.notebook.GetPage(i)
            title = self.notebook.GetPageText(i)
            tabs.append((i, page, title))
        return tabs

    def _get_active_tab(self) -> Optional[Tuple[int, wx.Window, str]]:
        """Get the currently active tab.

        Returns:
            (index, page, title) tuple or None if no tabs
        """
        if self.notebook.GetPageCount() == 0:
            return None

        index = self.notebook.GetSelection()
        page = self.notebook.GetPage(index)
        title = self.notebook.GetPageText(index)
        return (index, page, title)

    def _on_tab_close(self, event: wx.CommandEvent) -> None:
        """Handle close tab context menu item."""
        if hasattr(self, "_context_tab_index"):
            self._close_tab(self._context_tab_index)

    def _on_tab_close_others(self, event: wx.CommandEvent) -> None:
        """Handle close other tabs context menu item."""
        if hasattr(self, "_context_tab_index"):
            current_index = self._context_tab_index

            # Close tabs after the current one (in reverse order)
            for i in range(self.notebook.GetPageCount() - 1, current_index, -1):
                self._close_tab(i)

            # Close tabs before the current one (in reverse order)
            for i in range(current_index - 1, -1, -1):
                self._close_tab(i)

    def _on_tab_close_all(self, event: wx.CommandEvent) -> None:
        """Handle close all tabs context menu item."""
        # Close all tabs in reverse order
        for i in range(self.notebook.GetPageCount() - 1, -1, -1):
            self._close_tab(i)

        # Add a default tab if all were closed
        if self.notebook.GetPageCount() == 0:
            new_tab = ViewContainer(self.notebook)
            self.notebook.AddPage(new_tab, "Editor")

    def _on_tab_rename(self, event: wx.CommandEvent) -> None:
        """Handle rename tab context menu item."""
        if hasattr(self, "_context_tab_index"):
            current_title = self.notebook.GetPageText(self._context_tab_index)

            # Show rename dialog
            dialog = wx.TextEntryDialog(
                self, "Enter new tab name:", "Rename Tab", current_title
            )

            if dialog.ShowModal() == wx.ID_OK:
                new_title = dialog.GetValue().strip()
                if new_title and new_title != current_title:
                    self.notebook.SetPageText(self._context_tab_index, new_title)
                    self.status_bar.SetStatusText(f"Tab renamed to '{new_title}'")

            dialog.Destroy()

    def _close_tab(self, index: int) -> bool:
        """Close a tab at the given index.

        Args:
            index: Tab index to close

        Returns:
            True if tab was closed, False if cancelled
        """
        if index < 0 or index >= self.notebook.GetPageCount():
            return False

        page = self.notebook.GetPage(index)
        title = self.notebook.GetPageText(index)

        # Check if the tab has unsaved changes
        if isinstance(page, ViewContainer):
            is_modified = False

            if page.current_view == "text":
                if page.text_view.IsModified():
                    is_modified = True
            # Grid modification check would go here

            if is_modified:
                result = wx.MessageBox(
                    f"The tab '{title}' has unsaved changes. Save before closing?",
                    "Unsaved Changes",
                    wx.YES_NO | wx.CANCEL | wx.ICON_QUESTION,
                )

                if result == wx.CANCEL:
                    return False
                elif result == wx.YES:
                    # Save the file
                    self.notebook.SetSelection(index)
                    self._on_save(wx.CommandEvent())

        # Remove the tab
        self.notebook.DeletePage(index)
        self.status_bar.SetStatusText(f"Closed tab '{title}'")
        return True

    def _copy_grid_selection(self, grid, tab_name: str) -> None:
        """Copy selected cells from grid to enhanced clipboard.

        Args:
            grid: Grid to copy from
            tab_name: Name of the tab containing the grid
        """
        selected_cells = grid.GetSelectedCells()
        selected_cols = grid.GetSelectedCols()
        selected_rows = grid.GetSelectedRows()
        blocks = grid.GetSelectionBlockTopLeft()

        if selected_cols:
            # Full column selection
            min_col = min(selected_cols)
            max_col = max(selected_cols)
            print(f"DEBUG: Copying columns {min_col} to {max_col}")
            cell_data = self.clipboard_manager.create_cell_data_from_grid(
                grid,
                0,
                min_col,
                grid.GetNumberRows() - 1,
                max_col
            )

        elif selected_rows:
            # Full row selection
            min_row = min(selected_rows)
            max_row = max(selected_rows)
            print(f"DEBUG: Copying rows {min_row} to {max_row}")
            cell_data = self.clipboard_manager.create_cell_data_from_grid(
                grid,
                min_row,
                0,
                max_row,
                grid.GetNumberCols() - 1
            )

        elif blocks:
            # Block selection
            bottom_right = grid.GetSelectionBlockBottomRight()
            if bottom_right:
                start_row = blocks[0].GetRow()
                start_col = blocks[0].GetCol()
                end_row = bottom_right[0].GetRow()
                end_col = bottom_right[0].GetCol()
                print(
                    f"DEBUG: Copying block ({start_row},{start_col}) to "
                    f"({end_row},{end_col})"
                )

                cell_data = self.clipboard_manager.create_cell_data_from_grid(
                    grid,
                    start_row,
                    start_col,
                    end_row,
                    end_col
                )
            else:
                return

        elif selected_cells:
            # Individual cell selection
            min_row = min(cell.GetRow() for cell in selected_cells)
            max_row = max(cell.GetRow() for cell in selected_cells)
            min_col = min(cell.GetCol() for cell in selected_cells)
            max_col = max(cell.GetCol() for cell in selected_cells)
            print(
                f"DEBUG: Copying individual cells ({min_row},{min_col}) to "
                f"({max_row},{max_col})"
            )

            cell_data = self.clipboard_manager.create_cell_data_from_grid(
                grid,
                min_row,
                min_col,
                max_row,
                max_col
            )

        else:
            # Current cell
            row = grid.GetGridCursorRow()
            col = grid.GetGridCursorCol()
            print(f"DEBUG: Copying current cell ({row},{col})")
            cell_data = self.clipboard_manager.create_cell_data_from_grid(
                grid,
                row,
                col,
                row,
                col
            )

        # Copy to clipboard manager
        file_type = getattr(grid, "data_format", "csv")
        self.clipboard_manager.copy_cells(cell_data, tab_name, file_type)

        rows = len(cell_data)
        cols = len(cell_data[0]) if cell_data else 0
        self.status_bar.SetStatusText(f"Copied {rows}x{cols} cells from grid")

    def _paste_to_grid(self, grid, preserve_formatting: bool = True) -> None:
        """Paste data to grid from enhanced clipboard.

        Args:
            grid: Grid to paste to
            preserve_formatting: Whether to preserve formatting
        """
        if not self.clipboard_manager.has_cells_data():
            # Try system clipboard for text
            text = self.clipboard_manager.get_text_from_system()
            if text:
                self._paste_text_to_grid(grid, text)
            return

        # Get current cursor position
        target_row = grid.GetGridCursorRow()
        target_col = grid.GetGridCursorCol()

        # Get clipboard data
        paste_data = self.clipboard_manager.get_clipboard_data()
        if not paste_data or not paste_data.cells_data:
            return

        cells = paste_data.cells_data
        rows_needed = len(cells)
        cols_needed = len(cells[0]) if cells else 0

        # Ensure grid has enough space
        current_rows = grid.GetNumberRows()
        current_cols = grid.GetNumberCols()

        if target_row + rows_needed > current_rows:
            grid.AppendRows(target_row + rows_needed - current_rows)

        if target_col + cols_needed > current_cols:
            grid.AppendCols(target_col + cols_needed - current_cols)

        # Paste data
        pasted_cells = 0
        for row_idx, row_data in enumerate(cells):
            for col_idx, cell_data in enumerate(row_data):
                actual_row = target_row + row_idx
                actual_col = target_col + col_idx

                # Set cell value
                grid.SetCellValue(actual_row, actual_col, cell_data.value)

                # Apply formatting if requested and available
                if preserve_formatting and cell_data.formatting:
                    self._apply_cell_formatting(
                        grid, actual_row, actual_col, cell_data.formatting
                    )

                pasted_cells += 1

        grid.ForceRefresh()
        self.status_bar.SetStatusText(f"Pasted {pasted_cells} cells to grid")

    def _paste_text_to_grid(self, grid, text: str) -> None:
        """Paste plain text to grid, interpreting tabs and newlines.

        Args:
            grid: Grid to paste to
            text: Text to paste
        """
        lines = text.split("\n")
        target_row = grid.GetGridCursorRow()
        target_col = grid.GetGridCursorCol()

        rows_needed = len(lines)
        max_cols_needed = 0

        # Parse text into rows and columns
        parsed_data = []
        for line in lines:
            if "\t" in line:
                columns = line.split("\t")
            elif "," in line and line.count(",") > line.count(" "):
                # Likely CSV data
                columns = [col.strip() for col in line.split(",")]
            else:
                columns = [line]

            parsed_data.append(columns)
            max_cols_needed = max(max_cols_needed, len(columns))

        # Ensure grid has enough space
        current_rows = grid.GetNumberRows()
        current_cols = grid.GetNumberCols()

        if target_row + rows_needed > current_rows:
            grid.AppendRows(target_row + rows_needed - current_rows)

        if target_col + max_cols_needed > current_cols:
            grid.AppendCols(target_col + max_cols_needed - current_cols)

        # Paste data
        pasted_cells = 0
        for row_idx, columns in enumerate(parsed_data):
            for col_idx, value in enumerate(columns):
                actual_row = target_row + row_idx
                actual_col = target_col + col_idx
                grid.SetCellValue(actual_row, actual_col, value)
                pasted_cells += 1

        grid.ForceRefresh()
        self.status_bar.SetStatusText(f"Pasted text as {pasted_cells} cells")

    def _apply_cell_formatting(
        self, grid, row: int, col: int, formatting: Dict[str, Any]
    ) -> None:
        """Apply formatting to a grid cell.

        Args:
            grid: Grid containing the cell
            row: Row index
            col: Column index
            formatting: Formatting dictionary
        """
        try:
            # Apply font formatting
            if any(
                key in formatting
                for key in ["font_family", "font_size", "bold", "italic", "underlined"]
            ):
                font = grid.GetCellFont(row, col)
                if not font.IsOk():
                    font = grid.GetDefaultCellFont()

                if "font_family" in formatting:
                    font.SetFaceName(formatting["font_family"])
                if "font_size" in formatting:
                    font.SetPointSize(formatting["font_size"])
                if "bold" in formatting:
                    font.SetWeight(
                        wx.FONTWEIGHT_BOLD
                        if formatting["bold"]
                        else wx.FONTWEIGHT_NORMAL
                    )
                if "italic" in formatting:
                    font.SetStyle(
                        wx.FONTSTYLE_ITALIC
                        if formatting["italic"]
                        else wx.FONTSTYLE_NORMAL
                    )
                if "underlined" in formatting:
                    font.SetUnderlined(formatting["underlined"])

                grid.SetCellFont(row, col, font)

            # Apply colors
            if "background_color" in formatting:
                color = wx.Colour(formatting["background_color"])
                grid.SetCellBackgroundColour(row, col, color)

            if "text_color" in formatting:
                color = wx.Colour(formatting["text_color"])
                grid.SetCellTextColour(row, col, color)

            # Apply alignment
            if (
                "horizontal_alignment" in formatting
                and "vertical_alignment" in formatting
            ):
                grid.SetCellAlignment(
                    row,
                    col,
                    formatting["horizontal_alignment"],
                    formatting["vertical_alignment"],
                )

        except Exception:
            # If formatting fails, continue without it
            pass

    def _clear_grid_selection(self, grid) -> None:
        """Clear the selected cells/rows/columns in the grid.

        Args:
            grid: Grid to clear selection from
        """
        selected_cells = grid.GetSelectedCells()
        selected_rows = grid.GetSelectedRows()
        selected_cols = grid.GetSelectedCols()
        blocks = grid.GetSelectionBlockTopLeft()

        if selected_cols:
            # Clear full columns
            for col in selected_cols:
                for row in range(grid.GetNumberRows()):
                    grid.SetCellValue(row, col, "")

        elif selected_rows:
            # Clear full rows
            for row in selected_rows:
                for col in range(grid.GetNumberCols()):
                    grid.SetCellValue(row, col, "")

        elif blocks:
            # Clear block selection
            bottom_right = grid.GetSelectionBlockBottomRight()
            if bottom_right:
                start_row = blocks[0].GetRow()
                start_col = blocks[0].GetCol()
                end_row = bottom_right[0].GetRow()
                end_col = bottom_right[0].GetCol()

                for row in range(start_row, end_row + 1):
                    for col in range(start_col, end_col + 1):
                        grid.SetCellValue(row, col, "")

        elif selected_cells:
            # Clear individual cells
            for cell in selected_cells:
                grid.SetCellValue(cell.GetRow(), cell.GetCol(), "")

        else:
            # Clear current cell
            row = grid.GetGridCursorRow()
            col = grid.GetGridCursorCol()
            grid.SetCellValue(row, col, "")

        grid.ForceRefresh()

    def _on_ai_settings(self, event: wx.CommandEvent) -> None:
        """Opens the AI Settings dialog."""
        dialog = AISettingsDialog(self, self.config_manager)
        if dialog.ShowModal() == wx.ID_OK:
            # Re-initialize the AI Manager with the new key
            api_key = self.config_manager.get_setting("api_key")
            if api_key:
                self.ai_manager = AIManager(api_key=api_key)
        dialog.Destroy()

    def _on_ai_submit(self, event: wx.CommandEvent) -> None:
        """Handle AI query submission and command dispatch.

        Args:
            event: The wx event that triggered this handler.
        """
        # Check and reset rate limiting counters
        current_time = time.time()

        # Reset minute counter if needed
        if current_time - self._minute_reset_time >= 60:
            self._requests_per_minute = 0
            self._minute_reset_time = current_time

        # Reset hour counter if needed
        if current_time - self._hour_reset_time >= 3600:
            self._requests_per_hour = 0
            self._hour_reset_time = current_time

        # Reset day counter if needed
        if current_time - self._day_reset_time >= 86400:
            self._requests_per_day = 0
            self._day_reset_time = current_time

        # Check rate limits
        if self._requests_per_minute >= self._max_requests_per_minute:
            wx.MessageBox(
                "Rate limit exceeded: Too many requests per minute",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        if self._requests_per_hour >= self._max_requests_per_hour:
            wx.MessageBox(
                "Rate limit exceeded: Too many requests per hour",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        if self._requests_per_day >= self._max_requests_per_day:
            wx.MessageBox(
                "Rate limit exceeded: Too many requests per day",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        # Check minimum interval between requests
        if current_time - self._last_request_time < self._min_interval:
            wx.MessageBox(
                "Please wait before making another request",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        # Update request counters
        self._requests_per_minute += 1
        self._requests_per_hour += 1
        self._requests_per_day += 1
        self._last_request_time = current_time

        # Validate API key first
        api_key = self.config_manager.get_setting("api_key")
        if not api_key:
            wx.MessageBox(
                "API key not set. Please set your API key in AI Settings.",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            self._on_ai_settings(event)
            return

        # Get and validate query
        query = self.ai_input.GetValue().strip()
        if not query:
            wx.MessageBox(
                "Please enter a command for the AI.",
                "Info",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        # Validate query length
        if len(query) > 1000:  # Reasonable limit for AI queries
            wx.MessageBox(
                "Query is too long. Please keep it under 1000 characters.",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        # Basic sanitization - remove any potentially harmful characters
        query = re.sub(r"[^\w\s.,?!-]", "", query)
        if not query:
            wx.MessageBox(
                "Invalid query. Please enter a valid command.",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        # Get AI response - this returns (commands_list, raw_response)
        commands, raw_response = self.ai_manager.get_ai_response(query)

        # Debug: Log the AI response to help troubleshoot column mapping issues
        if commands:
            import logging

            logging.getLogger(__name__).info(f"AI Response: {commands}")
            print(f"DEBUG - AI Query: '{query}'")
            print(f"DEBUG - AI Response: {commands}")

            # Update debug panel
            debug_text = (
                f"Query: {query}\n\nAI Response:\n{commands}\n\n"
                f"Commands: {len(commands) if commands else 0}"
            )
            self.ai_debug_display.SetValue(debug_text)
        else:
            self.ai_debug_display.SetValue(
                f"Query: {query}\n\nError: No valid AI response received\n"
                f"Raw: {raw_response[:200] if raw_response else 'No response'}"
            )

        if commands and isinstance(commands, list):
            # Process each command in the list
            for cmd_dict in commands:
                capability = cmd_dict.get("command")
                params = cmd_dict.get("params", {})

                # Dispatch to the correct handler or high-level command handler
                if capability == "COPY_COLUMN_TO_NEW_COLUMN_AFTER":
                    # Use the high-level command handler
                    self.ai_handler.execute_command(capability, **params)
                elif capability == "DELETE_ROW":
                    self._ai_delete_row(params.get("row_index"))
                elif capability == "APPLY_BOLD_FORMATTING":
                    self._ai_apply_bold_formatting()
                elif capability == "SORT_COLUMN_ASCENDING":
                    self._ai_sort_column(params.get("column_index"))
                elif capability == "ADD_COLUMN":
                    self._ai_add_column(
                        params.get("column_index"), params.get("column_name")
                    )
                elif capability == "ADD_ROW":
                    self._ai_add_row(params.get("row_index"))
                elif capability == "INSERT_ROW_ABOVE":
                    self._ai_insert_row_above(params.get("row_index"))
                elif capability == "INSERT_ROW_BELOW":
                    self._ai_insert_row_below(params.get("row_index"))
                elif capability == "INSERT_COLUMN_LEFT":
                    self._ai_insert_column_left(params.get("column_index"))
                elif capability == "INSERT_COLUMN_RIGHT":
                    self._ai_insert_column_right(params.get("column_index"))
                elif capability == "APPLY_ITALIC_FORMATTING":
                    self._ai_apply_italic_formatting()
                elif capability == "APPLY_UNDERLINE_FORMATTING":
                    self._ai_apply_underline_formatting()
                elif capability == "ALIGN_LEFT":
                    self._ai_align_left()
                elif capability == "ALIGN_CENTER":
                    self._ai_align_center()
                elif capability == "ALIGN_RIGHT":
                    self._ai_align_right()
                elif capability == "FORMAT_AS_CURRENCY":
                    self._ai_format_as_currency()
                elif capability == "FORMAT_AS_PERCENT":
                    self._ai_format_as_percent()
                elif capability == "DELETE_COLUMN":
                    self._ai_delete_column(params.get("column_index"))
                elif capability == "SORT_COLUMN_DESCENDING":
                    self._ai_sort_column(params.get("column_index"), ascending=False)
                elif capability == "UNDO_LAST_ACTION":
                    self._ai_undo()
                elif capability == "REDO_LAST_ACTION":
                    self._ai_redo()
                elif capability == "COPY_SELECTION":
                    self._ai_copy()
                elif capability == "PASTE_SELECTION":
                    self._ai_paste()
                elif capability == "CLEAR_SELECTION":
                    self._ai_clear_selection()
                elif capability == "NEW_FILE":
                    self._ai_new_file()
                elif capability == "NEW_SPREADSHEET":
                    self._ai_new_spreadsheet()
                elif capability == "SAVE_FILE":
                    self._ai_save_file()
                elif capability == "CLOSE_TAB":
                    self._ai_close_tab(params.get("tab_name"))
                elif capability == "SWITCH_TAB":
                    self._ai_switch_tab(params.get("tab_name"))
                elif capability == "OPEN_FILE":
                    self._ai_open_file(params.get("file_path"))
                # Filter capabilities
                elif capability == "FILTER_COLUMN":
                    self._ai_filter_column(params)
                elif capability == "FILTER_NUMBER_RANGE":
                    self._ai_filter_number_range(
                        params.get("column_index"),
                        params.get("min_value"),
                        params.get("max_value"),
                    )
                elif capability == "FILTER_NUMBER_GREATER_THAN":
                    self._ai_filter_number_greater_than(
                        params.get("column_index"),
                        params.get("value"),
                        params.get("include_equal", False),
                    )
                elif capability == "FILTER_NUMBER_LESS_THAN":
                    self._ai_filter_number_less_than(
                        params.get("column_index"),
                        params.get("value"),
                        params.get("include_equal", False),
                    )
                elif capability == "FILTER_TEXT_CONTAINS":
                    self._ai_filter_text_contains(
                        params.get("column_index"),
                        params.get("text"),
                        params.get("case_sensitive", False),
                    )
                elif capability == "FILTER_TEXT_EQUALS":
                    self._ai_filter_text_equals(
                        params.get("column_index"),
                        params.get("text"),
                        params.get("case_sensitive", False),
                    )
                elif capability == "FILTER_TEXT_STARTS_WITH":
                    self._ai_filter_text_starts_with(
                        params.get("column_index"),
                        params.get("prefix"),
                        params.get("case_sensitive", False),
                    )
                elif capability == "FILTER_TEXT_ENDS_WITH":
                    self._ai_filter_text_ends_with(
                        params.get("column_index"),
                        params.get("suffix"),
                        params.get("case_sensitive", False),
                    )
                elif capability == "FILTER_BLANKS":
                    self._ai_filter_blanks(params.get("column_index"))
                elif capability == "FILTER_NON_BLANKS":
                    self._ai_filter_non_blanks(params.get("column_index"))
                elif capability == "FILTER_TOP_N":
                    self._ai_filter_top_n(params.get("column_index"), params.get("n"))
                elif capability == "FILTER_BOTTOM_N":
                    self._ai_filter_bottom_n(
                        params.get("column_index"), params.get("n")
                    )
                elif capability == "FILTER_ABOVE_AVERAGE":
                    self._ai_filter_above_average(params.get("column_index"))
                elif capability == "FILTER_BELOW_AVERAGE":
                    self._ai_filter_below_average(params.get("column_index"))
                elif capability == "CLEAR_FILTER":
                    self._ai_clear_filter(params.get("column_index"))
                elif capability == "CLEAR_ALL_FILTERS":
                    self._ai_clear_all_filters()
                elif capability == "SHOW_FILTER_HISTORY":
                    self._ai_show_filter_history()
                elif capability == "FORMAT_AS_COMMA":
                    self._ai_format_as_comma()
                elif capability == "FREEZE_PANES":
                    self._ai_freeze_panes()
                elif capability == "UNFREEZE_PANES":
                    self._ai_unfreeze_panes()
                else:
                    wx.MessageBox(
                        f"Unknown AI capability: {capability}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
        else:
            wx.MessageBox(
                "Failed to process AI command: All parsing attempts failed\n"
                "Please try rephrasing your request.",
                "AI Processing Error",
                wx.OK | wx.ICON_ERROR,
            )

        self.ai_input.Clear()

    def _ai_add_row(self, row_index: any) -> None:
        """Adds a row based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            return

        pos = -1  # Default to end
        if isinstance(row_index, int):
            pos = row_index
        elif row_index == "current":
            pos = grid.GetGridCursorRow()

        try:
            # InsertRows inserts *before* the given position.
            # If pos is -1 or greater than row count, it appends.
            if pos == -1 or pos >= grid.GetNumberRows():
                grid.AppendRows(numRows=1)
                self.status_bar.SetStatusText("Added a new row at the end.")
            else:
                grid.InsertRows(pos=pos, numRows=1)
                self.status_bar.SetStatusText(
                    "Added a new row at position {}.".format(pos + 1)
                )

            grid.save_state_for_undo("insert_row", row_pos=pos)
        except Exception as error:
            wx.MessageBox(
                "Failed to add row: {}".format(error), "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_insert_row_above(self, row_index: any) -> None:
        """Inserts a row above the specified row based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        if row_index == "current":
            current_row = grid.GetGridCursorRow()
            if current_row != -1:
                grid._insert_row_at(current_row)
            else:
                wx.MessageBox(
                    "No row currently selected.", "Error", wx.OK | wx.ICON_ERROR
                )
        else:
            try:
                row_idx = int(row_index) - 1  # Convert from 1-based to 0-based
                if 0 <= row_idx <= grid.GetNumberRows():
                    grid._insert_row_at(row_idx)
                else:
                    wx.MessageBox(
                        f"Invalid row index: {row_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
            except (ValueError, TypeError):
                wx.MessageBox(
                    f"Invalid row index: {row_index}", "Error", wx.OK | wx.ICON_ERROR
                )

    def _ai_insert_row_below(self, row_index: any) -> None:
        """Inserts a row below the specified row based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        if row_index == "current":
            current_row = grid.GetGridCursorRow()
            if current_row != -1:
                grid._insert_row_at(current_row + 1)
            else:
                wx.MessageBox(
                    "No row currently selected.", "Error", wx.OK | wx.ICON_ERROR
                )
        else:
            try:
                row_idx = int(row_index)  # 1-based index, insert after
                if 1 <= row_idx <= grid.GetNumberRows():
                    grid._insert_row_at(
                        row_idx
                    )  # Convert to 0-based by inserting at row_idx
                else:
                    wx.MessageBox(
                        f"Invalid row index: {row_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
            except (ValueError, TypeError):
                wx.MessageBox(
                    f"Invalid row index: {row_index}", "Error", wx.OK | wx.ICON_ERROR
                )

    def _ai_insert_column_left(self, column_index: any) -> None:
        """Inserts a column to the left of the specified column based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        if column_index == "current":
            current_col = grid.GetGridCursorCol()
            if current_col != -1:
                grid._insert_column_at(current_col)
            else:
                wx.MessageBox(
                    "No column currently selected.", "Error", wx.OK | wx.ICON_ERROR
                )
        else:
            try:
                if isinstance(column_index, str) and column_index.isalpha():
                    col_idx = ord(column_index.upper()) - ord("A")
                else:
                    col_idx = int(column_index) - 1  # Convert from 1-based to 0-based

                if 0 <= col_idx <= grid.GetNumberCols():
                    grid._insert_column_at(col_idx)
                else:
                    wx.MessageBox(
                        f"Invalid column index: {column_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
            except (ValueError, TypeError):
                wx.MessageBox(
                    f"Invalid column index: {column_index}",
                    "Error",
                    wx.OK | wx.ICON_ERROR,
                )

    def _ai_insert_column_right(self, column_index: any) -> None:
        """Inserts a column to the right of the specified column based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        if column_index == "current":
            current_col = grid.GetGridCursorCol()
            if current_col != -1:
                grid._insert_column_at(current_col + 1)
            else:
                wx.MessageBox(
                    "No column currently selected.", "Error", wx.OK | wx.ICON_ERROR
                )
        else:
            try:
                if isinstance(column_index, str) and column_index.isalpha():
                    col_idx = ord(column_index.upper()) - ord("A") + 1
                else:
                    col_idx = int(column_index)  # 1-based index, insert after

                if 1 <= col_idx <= grid.GetNumberCols() + 1:
                    grid._insert_column_at(col_idx - 1)  # Convert to 0-based
                else:
                    wx.MessageBox(
                        f"Invalid column index: {column_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
            except (ValueError, TypeError):
                wx.MessageBox(
                    f"Invalid column index: {column_index}",
                    "Error",
                    wx.OK | wx.ICON_ERROR,
                )

    def _ai_add_column(
        self, column_index: Optional[Union[int, str]], column_name: Optional[str]
    ) -> None:
        """Adds a column based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            return

        num_cols = grid.GetNumberCols()

        # Determine insert position
        if column_index is None:
            # Add to the end if no index is specified
            insert_pos = num_cols
        else:
            if isinstance(column_index, str):
                try:
                    # Assumes single letter column, e.g., 'A'
                    insert_pos = ord(column_index.upper()) - ord("A")
                except (TypeError, ValueError):
                    wx.MessageBox(
                        f"Invalid column reference: {column_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
                    return
            else:
                insert_pos = int(column_index)

            # The capability is defined as inserting *after* the given index
            insert_pos += 1

        if not (0 <= insert_pos <= num_cols):
            wx.MessageBox(
                f"Cannot insert column at position {insert_pos}. "
                f"Grid has {num_cols} columns.",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        grid.InsertCols(pos=insert_pos, numCols=1)

        if column_name:
            grid.SetColLabelValue(insert_pos, column_name)

    def _ai_delete_row(self, row_index: any) -> None:
        """Deletes a row based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            return

        if row_index == "current":
            current_row = grid.GetGridCursorRow()
            if current_row != -1:
                grid.DeleteRows(pos=current_row, numRows=1)
        else:
            try:
                row_to_delete = int(row_index)
                if 0 <= row_to_delete < grid.GetNumberRows():
                    grid.DeleteRows(pos=row_to_delete, numRows=1)
                else:
                    wx.MessageBox(
                        f"Invalid row index: {row_to_delete}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
            except (ValueError, TypeError):
                wx.MessageBox(
                    f"Invalid row index specified by AI: {row_index}",
                    "Error",
                    wx.OK | wx.ICON_ERROR,
                )

    def _ai_apply_bold_formatting(self) -> None:
        """Applies bold formatting to the selection."""
        self._on_bold(wx.CommandEvent())

    def _ai_apply_italic_formatting(self) -> None:
        """Applies italic formatting to the selection."""
        self._on_italic(wx.CommandEvent())

    def _ai_apply_underline_formatting(self) -> None:
        """Applies underline formatting to the selection."""
        self._on_underline(wx.CommandEvent())

    def _ai_align_left(self) -> None:
        """Aligns selected content to the left."""
        self._on_align_left(wx.CommandEvent())

    def _ai_align_center(self) -> None:
        """Aligns selected content to the center."""
        self._on_align_center(wx.CommandEvent())

    def _ai_align_right(self) -> None:
        """Aligns selected content to the right."""
        self._on_align_right(wx.CommandEvent())

    def _ai_format_as_currency(self) -> None:
        """Formats selection as currency."""
        self._on_format_currency(wx.CommandEvent())

    def _ai_format_as_percent(self) -> None:
        """Formats selection as percentage."""
        self._on_format_percent(wx.CommandEvent())

    def _ai_delete_column(self, column_index: any) -> None:
        """Deletes a column based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        pos = -1
        if isinstance(column_index, int):
            pos = column_index
        elif isinstance(column_index, str) and column_index.isalpha():
            # Convert 'A' to 0, 'B' to 1, etc.
            pos = ord(column_index.upper()) - ord("A")
        elif column_index == "current":
            pos = grid.GetGridCursorCol()

        if pos != -1 and 0 <= pos < grid.GetNumberCols():
            if grid.GetNumberCols() > 1:
                try:
                    grid.DeleteCols(pos=pos, numCols=1)
                    self.status_bar.SetStatusText(
                        f"Deleted column at position {pos + 1}."
                    )
                    grid.save_state_for_undo("delete_col", col_pos=pos)
                except Exception as e:
                    wx.MessageBox(
                        f"Failed to delete column: {e}", "Error", wx.OK | wx.ICON_ERROR
                    )
        else:
            wx.MessageBox(
                f"Invalid column specified: {column_index}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_sort_column(self, column_index: any, ascending: bool = True) -> None:
        """Sorts a column based on AI command."""
        grid = self._get_current_grid()
        if not grid:
            return

        col = -1
        if isinstance(column_index, int):
            col = column_index
        elif isinstance(column_index, str) and column_index.isalpha():
            col = ord(column_index.upper()) - ord("A")

        if col == -1 or col >= grid.GetNumberCols():
            wx.MessageBox(
                f"Invalid column specified for sorting: {column_index}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )
            return

        grid.sort_column(col, ascending)

        order = "ascending" if ascending else "descending"
        self.status_bar.SetStatusText(f"Sorted column {col + 1} in {order} order.")

    def _ai_undo(self) -> None:
        """Undoes the last action."""
        self._on_undo(wx.CommandEvent())

    def _ai_redo(self) -> None:
        """Redoes the last action."""
        self._on_redo(wx.CommandEvent())

    def _ai_copy(self) -> None:
        """Copies the current selection."""
        self._on_copy(wx.CommandEvent())

    def _ai_paste(self) -> None:
        """Pastes from the clipboard."""
        self._on_paste(wx.CommandEvent())

    def _ai_copy_row(self, row_index: any) -> None:
        """Copies a row to the clipboard."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        row = -1
        if isinstance(row_index, int):
            row = row_index - 1  # Convert 1-based to 0-based
        elif row_index == "current":
            row = grid.GetGridCursorRow()

        if row >= 0 and row < grid.GetNumberRows():
            grid._copy_row(row)
            self.status_bar.SetStatusText(f"Copied row {row + 1} to clipboard.")
        else:
            wx.MessageBox(
                f"Invalid row specified: {row_index}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_paste_row(self, row_index: any) -> None:
        """Pastes from clipboard to a row."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        row = -1
        if isinstance(row_index, int):
            row = row_index - 1  # Convert 1-based to 0-based
        elif row_index == "current":
            row = grid.GetGridCursorRow()

        if row >= 0:
            grid._paste_row(row)
            self.status_bar.SetStatusText(f"Pasted to row {row + 1}.")
        else:
            wx.MessageBox(
                f"Invalid row specified: {row_index}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_copy_column(self, column_index: any) -> None:
        """Copies a column to the clipboard."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col = -1
        if isinstance(column_index, int):
            col = column_index - 1  # Convert 1-based to 0-based
        elif isinstance(column_index, str) and column_index.isalpha():
            col = ord(column_index.upper()) - ord("A")
        elif column_index == "current":
            col = grid.GetGridCursorCol()

        if col >= 0 and col < grid.GetNumberCols():
            grid._copy_column(col)
            col_name = chr(ord("A") + col)
            self.status_bar.SetStatusText(f"Copied column {col_name} to clipboard.")
        else:
            wx.MessageBox(
                f"Invalid column specified: {column_index}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_paste_column(self, column_index: any) -> None:
        """Pastes from clipboard to a column."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col = -1
        if isinstance(column_index, int):
            col = column_index - 1  # Convert 1-based to 0-based
        elif isinstance(column_index, str) and column_index.isalpha():
            col = ord(column_index.upper()) - ord("A")
        elif column_index == "current":
            col = grid.GetGridCursorCol()

        if col >= 0:
            grid._paste_column(col)
            col_name = chr(ord("A") + col)
            self.status_bar.SetStatusText(f"Pasted to column {col_name}.")
        else:
            wx.MessageBox(
                f"Invalid column specified: {column_index}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_copy_cell(
        self, row_index: any = "current", column_index: any = "current"
    ) -> None:
        """AI function to copy a single cell."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Convert row identifier
        if isinstance(row_index, str):
            if row_index.lower() == "current":
                row = grid.GetGridCursorRow()
            else:
                try:
                    row = int(row_index) - 1
                except ValueError:
                    wx.MessageBox(
                        f"Invalid row identifier: {row_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
                    return
        else:
            row = int(row_index) - 1

        # Convert column identifier
        if isinstance(column_index, str):
            if column_index.lower() == "current":
                col = grid.GetGridCursorCol()
            elif column_index.isalpha():
                col = ord(column_index.upper()) - ord("A")
            else:
                try:
                    col = int(column_index) - 1
                except ValueError:
                    wx.MessageBox(
                        f"Invalid column identifier: {column_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
                    return
        else:
            col = int(column_index) - 1

        if 0 <= row < grid.GetNumberRows() and 0 <= col < grid.GetNumberCols():
            grid._copy_single_cell(row, col)
        else:
            wx.MessageBox(
                f"Cell {row_index},{column_index} is out of range",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_paste_cell(
        self, row_index: any = "current", column_index: any = "current"
    ) -> None:
        """AI function to paste to a single cell."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Convert row identifier
        if isinstance(row_index, str):
            if row_index.lower() == "current":
                row = grid.GetGridCursorRow()
            else:
                try:
                    row = int(row_index) - 1
                except ValueError:
                    wx.MessageBox(
                        f"Invalid row identifier: {row_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
                    return
        else:
            row = int(row_index) - 1

        # Convert column identifier
        if isinstance(column_index, str):
            if column_index.lower() == "current":
                col = grid.GetGridCursorCol()
            elif column_index.isalpha():
                col = ord(column_index.upper()) - ord("A")
            else:
                try:
                    col = int(column_index) - 1
                except ValueError:
                    wx.MessageBox(
                        f"Invalid column identifier: {column_index}",
                        "Error",
                        wx.OK | wx.ICON_ERROR,
                    )
                    return
        else:
            col = int(column_index) - 1

        if 0 <= row < grid.GetNumberRows() and 0 <= col < grid.GetNumberCols():
            grid._paste_single_cell(row, col)
        else:
            wx.MessageBox(
                f"Cell {row_index},{column_index} is out of range",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_copy_range(
        self, start_row: any, start_col: any, end_row: any, end_col: any
    ) -> None:
        """AI function to copy a range of cells."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Convert identifiers
        try:
            if isinstance(start_row, str):
                start_row = (
                    int(start_row) - 1
                    if start_row.isdigit()
                    else grid.GetGridCursorRow()
                )
            else:
                start_row = int(start_row) - 1

            if isinstance(end_row, str):
                end_row = (
                    int(end_row) - 1 if end_row.isdigit() else grid.GetGridCursorRow()
                )
            else:
                end_row = int(end_row) - 1

            if isinstance(start_col, str):
                if start_col.isalpha():
                    start_col = ord(start_col.upper()) - ord("A")
                else:
                    start_col = (
                        int(start_col) - 1
                        if start_col.isdigit()
                        else grid.GetGridCursorCol()
                    )
            else:
                start_col = int(start_col) - 1

            if isinstance(end_col, str):
                if end_col.isalpha():
                    end_col = ord(end_col.upper()) - ord("A")
                else:
                    end_col = (
                        int(end_col) - 1
                        if end_col.isdigit()
                        else grid.GetGridCursorCol()
                    )
            else:
                end_col = int(end_col) - 1
        except ValueError as e:
            wx.MessageBox(
                f"Invalid range parameters: {e}", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        # Select the range and copy
        grid.SelectBlock(start_row, start_col, end_row, end_col)
        grid._copy_selection()

    def _ai_paste_range(self, start_row: any, start_col: any) -> None:
        """AI function to paste to a range starting at the specified cell."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Convert identifiers
        try:
            if isinstance(start_row, str):
                start_row = (
                    int(start_row) - 1
                    if start_row.isdigit()
                    else grid.GetGridCursorRow()
                )
            else:
                start_row = int(start_row) - 1

            if isinstance(start_col, str):
                if start_col.isalpha():
                    start_col = ord(start_col.upper()) - ord("A")
                else:
                    start_col = (
                        int(start_col) - 1
                        if start_col.isdigit()
                        else grid.GetGridCursorCol()
                    )
            else:
                start_col = int(start_col) - 1
        except ValueError as e:
            wx.MessageBox(f"Invalid parameters: {e}", "Error", wx.OK | wx.ICON_ERROR)
            return

        # Set cursor position and paste
        grid.SetGridCursor(start_row, start_col)

        # Get clipboard content to determine paste area
        clipboard = wx.TheClipboard
        if clipboard.Open():
            try:
                if clipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                    text_obj = wx.TextDataObject()
                    clipboard.GetData(text_obj)
                    text = text_obj.GetText()

                    if text:
                        lines = text.split("\n")
                        max_cols = (
                            max(len(line.split("\t")) for line in lines) if lines else 1
                        )
                        end_row = start_row + len(lines) - 1
                        end_col = start_col + max_cols - 1

                        # Select the area and paste
                        grid.SelectBlock(
                            start_row,
                            start_col,
                            min(end_row, grid.GetNumberRows() - 1),
                            min(end_col, grid.GetNumberCols() - 1),
                        )
                        grid._paste_to_selection()
            finally:
                clipboard.Close()

    def _ai_copy_multiple_rows(self, row_indices: list) -> None:
        """AI function to copy multiple specific rows."""
        grid = self._get_current_grid()
        if not grid:
            return

        try:
            # Convert row identifiers
            rows = []
            for idx in row_indices:
                if isinstance(idx, str):
                    if idx.lower() == "current":
                        rows.append(grid.GetGridCursorRow())
                    else:
                        rows.append(int(idx) - 1)
                else:
                    rows.append(int(idx) - 1)

            # Validate rows
            valid_rows = [r for r in rows if 0 <= r < grid.GetNumberRows()]
            if not valid_rows:
                wx.MessageBox("No valid rows specified", "Error", wx.OK | wx.ICON_ERROR)
                return

            # Copy rows
            text_lines = []
            for row in sorted(valid_rows):
                row_values = []
                for col in range(grid.GetNumberCols()):
                    value = grid.GetCellValue(row, col)
                    row_values.append(value)
                text_lines.append("\t".join(row_values))

            text = "\n".join(text_lines)
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(text))
                    self.SetStatusText(f"Copied {len(valid_rows)} rows to clipboard")
                finally:
                    clipboard.Close()

        except ValueError as e:
            wx.MessageBox(f"Invalid row indices: {e}", "Error", wx.OK | wx.ICON_ERROR)

    def _ai_copy_multiple_columns(self, column_indices: list) -> None:
        """AI function to copy multiple specific columns."""
        grid = self._get_current_grid()
        if not grid:
            return

        try:
            # Convert column identifiers
            cols = []
            for idx in column_indices:
                if isinstance(idx, str):
                    if idx.lower() == "current":
                        cols.append(grid.GetGridCursorCol())
                    elif idx.isalpha():
                        cols.append(ord(idx.upper()) - ord("A"))
                    else:
                        cols.append(int(idx) - 1)
                else:
                    cols.append(int(idx) - 1)

            # Validate columns
            valid_cols = [c for c in cols if 0 <= c < grid.GetNumberCols()]
            if not valid_cols:
                wx.MessageBox(
                    "No valid columns specified", "Error", wx.OK | wx.ICON_ERROR
                )
                return

            # Copy columns
            text_lines = []
            for row in range(grid.GetNumberRows()):
                row_values = []
                for col in sorted(valid_cols):
                    value = grid.GetCellValue(row, col)
                    row_values.append(value)
                text_lines.append("\t".join(row_values))

            text = "\n".join(text_lines)
            clipboard = wx.TheClipboard
            if clipboard.Open():
                try:
                    clipboard.SetData(wx.TextDataObject(text))
                    self.SetStatusText(f"Copied {len(valid_cols)} columns to clipboard")
                finally:
                    clipboard.Close()

        except ValueError as e:
            wx.MessageBox(
                f"Invalid column indices: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_clear_selection(self) -> None:
        """Clears the content of the current selection."""
        grid = self._get_current_grid()
        if not grid:
            return
        self._clear_grid_selection(grid)
        self.status_bar.SetStatusText("Selection cleared.")

    def _ai_new_file(self) -> None:
        """Creates a new text file."""
        self._on_new(wx.CommandEvent())

    def _ai_new_spreadsheet(self) -> None:
        """Creates a new spreadsheet."""
        self._on_new_spreadsheet(wx.CommandEvent())

    def _ai_save_file(self) -> None:
        """Saves the current file."""
        self._on_save(wx.CommandEvent())

    def _ai_close_tab(self, tab_name: any) -> None:
        """Closes a specified tab."""
        if tab_name == "current" or tab_name is None:
            index = self.notebook.GetSelection()
        elif isinstance(tab_name, int):
            index = tab_name - 1  # User provides 1-based, convert to 0-based
        else:  # String name
            index = -1
            for i in range(self.notebook.GetPageCount()):
                if self.notebook.GetPageText(i) == tab_name:
                    index = i
                    break

        if 0 <= index < self.notebook.GetPageCount():
            self._close_tab(index)
        else:
            wx.MessageBox(
                f"Could not find tab: {tab_name}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_switch_tab(self, tab_name: any) -> None:
        """Switches to a specified tab."""
        if isinstance(tab_name, int):
            index = tab_name - 1  # User provides 1-based
        else:  # String name
            index = -1
            for i in range(self.notebook.GetPageCount()):
                if self.notebook.GetPageText(i) == tab_name:
                    index = i
                    break

        if 0 <= index < self.notebook.GetPageCount():
            self.notebook.SetSelection(index)
            self.status_bar.SetStatusText(
                f"Switched to tab: {self.notebook.GetPageText(index)}"
            )
        else:
            wx.MessageBox(
                f"Could not find tab: {tab_name}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_open_file(self, file_path: Optional[str]) -> None:
        """Opens a file from the workspace."""
        if not file_path:
            wx.MessageBox(
                "No file path provided by the AI.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        # Assume the file is in the current working directory if no path is specified
        full_path = os.path.abspath(file_path)

        if os.path.exists(full_path):
            file_type = self._get_file_type(full_path)
            if file_type in ["csv", "json", "yaml"]:
                self._open_grid_based_file(full_path, file_type)
            else:
                self._open_text_based_file(full_path, file_type)
        else:
            wx.MessageBox(
                f"File not found: {full_path}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _save_view_state_for_undo(self):
        """Saves the current sort and filter state for an undo operation."""
        previous_state = {
            "sort_state": self.sort_state,
            "active_filters": self.active_filters.copy(),
        }
        self.save_state_for_undo("view_change", previous_state=previous_state)

    def _on_insert_dropdown(self, event: RB.RibbonButtonBarEvent) -> None:
        """Show a menu for inserting rows or columns."""
        menu = wx.Menu()
        menu.Append(self.ID_INSERT_ROW, "Insert Row")
        menu.Append(self.ID_INSERT_COLUMN, "Insert Column")

        # We don't need to re-bind these here if they are already bound in _bind_events
        # self.Bind(wx.EVT_MENU, self._on_insert_row, id=self.ID_INSERT_ROW)
        # self.Bind(wx.EVT_MENU, self._on_insert_col, id=self.ID_INSERT_COLUMN)

        event.PopupMenu(menu)
        # menu.Destroy() # Let wxPython handle destruction after PopupMenu

    def _on_delete_dropdown(self, event: RB.RibbonButtonBarEvent) -> None:
        """Show a menu for deleting rows or columns."""
        menu = wx.Menu()
        menu.Append(self.ID_DELETE_ROW, "Delete Row")
        menu.Append(self.ID_DELETE_COLUMN, "Delete Column")

        # We don't need to re-bind these here
        # self.Bind(wx.EVT_MENU, self._on_delete_row, id=self.ID_DELETE_ROW)
        # self.Bind(wx.EVT_MENU, self._on_delete_col, id=self.ID_DELETE_COLUMN)

        event.PopupMenu(menu)
        # menu.Destroy()

    def _on_quick_number_filter(self, event: RB.RibbonButtonBarEvent) -> None:
        """Handle Quick Number Filter button click."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "Quick Number Filter is only available in spreadsheet view.",
                "Info",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        # Get current column
        col_index = grid.GetGridCursorCol()
        if col_index == -1:
            wx.MessageBox(
                "Please select a column to filter.",
                "Info",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        # Get column data for type checking
        column_data = []
        for row in range(grid.GetNumberRows()):
            value = grid.GetCellValue(row, col_index)
            column_data.append(value)

        # Check if column contains numbers
        data_type = "number"
        for value in column_data:
            if not value.strip():
                continue
            try:
                float(value)
            except ValueError:
                data_type = "text"
                break

        if data_type != "number":
            result = wx.MessageBox(
                "The selected column appears to contain {} data, not numbers.\n"
                "Open number filter anyway?".format(data_type),
                "Column Type Check",
                wx.YES_NO | wx.ICON_QUESTION,
            )
            if result != wx.YES:
                return

        # Show number filter dialog directly
        column_name = (
            grid.GetColLabelValue(col_index) or "Column {}".format(col_index + 1)
        )

        try:
            from jedit2.ui.number_filter_dialog import NumberFilterDialog

            current_criterion = grid.advanced_filters.get(col_index)
            if not isinstance(current_criterion, NumberFilterCriterion):
                current_criterion = None

            with NumberFilterDialog(
                self, column_name, column_data, current_criterion
            ) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    criterion = dlg.get_filter_result()
                    if criterion:
                        grid.apply_advanced_filter(
                            col_index, {"action": "criterion", "criterion": criterion}
                        )

        except Exception as error:
            import logging

            logging.getLogger(__name__).error(
                "Error showing number filter dialog: {}".format(error)
            )
            wx.MessageBox(
                "Error showing number filter dialog.", "Error", wx.OK | wx.ICON_ERROR
            )

    def _on_filter_history(self, event: RB.RibbonButtonBarEvent) -> None:
        """Handle Filter History button click."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "Filter History is only available in spreadsheet view.",
                "Info",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        # Combine both filter types for the dialog
        all_filters = {}
        all_filters.update(grid.active_filters)
        all_filters.update(grid.advanced_filters)

        if not all_filters:
            wx.MessageBox(
                "No active filters to manage.", "Info", wx.OK | wx.ICON_INFORMATION
            )
            return

        # Get column names
        column_names = []
        if grid.original_data:
            # If we have headers, use them
            if len(grid.original_data) > 0:
                column_names = [
                    f"Column {chr(ord('A') + i)}"
                    for i in range(len(grid.original_data[0]))
                ]

        # Show filter history dialog
        try:
            from jedit2.ui.filter_history_dialog import FilterHistoryDialog

            with FilterHistoryDialog(self, all_filters, column_names) as dlg:
                if dlg.ShowModal() == wx.ID_OK and dlg.was_modified():
                    # Remove selected filters
                    filters_to_remove = dlg.get_filters_to_remove()
                    for col_index in filters_to_remove:
                        grid.clear_filter(col_index)

                    # Show confirmation message
                    if filters_to_remove:
                        count = len(filters_to_remove)
                        if count == 1:
                            self.status_bar.SetStatusText("1 filter removed")
                        else:
                            self.status_bar.SetStatusText(f"{count} filters removed")
        except Exception as e:
            import logging

            logging.getLogger(__name__).error(
                f"Error showing filter history dialog: {e}"
            )
            wx.MessageBox(
                "Error showing filter history dialog.", "Error", wx.OK | wx.ICON_ERROR
            )

    def _on_find(self, event: wx.CommandEvent) -> None:
        """Handle find event."""
        # TODO: Implement a find dialog
        wx.MessageBox(
            "Find functionality is not yet implemented.",
            "Not Implemented",
            wx.OK | wx.ICON_INFORMATION,
        )

    # AI Filter Methods

    def _get_column_index(self, column_index: any) -> int:
        """Convert various column index formats to 0-based integer."""
        print(
            f"DEBUG - _get_column_index input: {column_index} "
            f"(type: {type(column_index)})"
        )

        if column_index is None:
            return -1
        elif isinstance(column_index, str) and column_index.isalpha():
            result = ord(column_index.upper()) - ord("A")
            print(f"DEBUG - _get_column_index letter '{column_index}' -> {result}")
            return result
        elif isinstance(column_index, int):
            # AI already returns 0-based indices, don't subtract 1!
            print(
                f"DEBUG - _get_column_index int {column_index} -> {column_index} "
                f"(keeping 0-based)"
            )
            return column_index
        else:
            print("DEBUG - _get_column_index unknown format, returning -1")
            return -1

    def _ai_filter_column(self, params: dict) -> None:
        """General filter column handler."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(params.get("column_index"))
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        filter_type = params.get("filter_type", "text")
        operator = params.get("operator", "equals")
        value = params.get("value")
        value2 = params.get("value2")
        case_sensitive = params.get("case_sensitive", False)

        try:
            if filter_type == "number":
                from jedit2.ui.filter_criteria import NumberFilterCriterion

                if operator == "between":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.BETWEEN, value, value2
                    )
                elif operator == "greater_than":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.GREATER_THAN, value
                    )
                elif operator == "greater_equal":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.GREATER_EQUAL, value
                    )
                elif operator == "less_than":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.LESS_THAN, value
                    )
                elif operator == "less_equal":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.LESS_EQUAL, value
                    )
                elif operator == "not_equals":
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.NOT_EQUALS, value
                    )
                else:  # equals
                    criterion = NumberFilterCriterion(
                        NumberFilterCriterion.EQUALS, value
                    )

            elif filter_type == "text":
                from jedit2.ui.filter_criteria import TextFilterCriterion

                if operator == "contains":
                    criterion = TextFilterCriterion(
                        TextFilterCriterion.CONTAINS, value, case_sensitive
                    )
                elif operator == "starts_with":
                    criterion = TextFilterCriterion(
                        TextFilterCriterion.BEGINS_WITH, value, case_sensitive
                    )
                elif operator == "ends_with":
                    criterion = TextFilterCriterion(
                        TextFilterCriterion.ENDS_WITH, value, case_sensitive
                    )
                elif operator == "not_equals":
                    criterion = TextFilterCriterion(
                        TextFilterCriterion.NOT_EQUALS, value, case_sensitive
                    )
                else:  # equals
                    criterion = TextFilterCriterion(
                        TextFilterCriterion.EQUALS, value, case_sensitive
                    )

            else:
                wx.MessageBox(
                    f"Unsupported filter type: {filter_type}",
                    "Error",
                    wx.OK | wx.ICON_ERROR,
                )
                return

            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Applied {filter_type} filter to {column_name}"
            )

        except Exception as e:
            wx.MessageBox(f"Error applying filter: {e}", "Error", wx.OK | wx.ICON_ERROR)

    def _ai_filter_number_range(
        self, column_index: any, min_value: float, max_value: float
    ) -> None:
        """Filter numbers within a range."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            criterion = NumberFilterCriterion(
                NumberFilterCriterion.BETWEEN,
                min(min_value, max_value),
                max(min_value, max_value),
            )
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for values between {min_value} and {max_value}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying number range filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_number_greater_than(
        self, column_index: any, value: float, include_equal: bool = False
    ) -> None:
        """Filter numbers greater than a value."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            operator = (
                NumberFilterCriterion.GREATER_EQUAL
                if include_equal
                else NumberFilterCriterion.GREATER_THAN
            )
            criterion = NumberFilterCriterion(operator, value)

            # Store current row count before filtering
            original_rows = grid.GetNumberRows() if grid.original_data else 0

            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            op_text = ">=" if include_equal else ">"

            # Check how many rows remain after filtering
            filtered_rows = grid.GetNumberRows()
            if filtered_rows == 0 and original_rows > 0:
                self.status_bar.SetStatusText(
                    f"No rows match filter: {column_name} {op_text} {value} "
                    f"(0 of {original_rows} rows shown)"
                )
            else:
                self.status_bar.SetStatusText(
                    f"Filtered {column_name} for values {op_text} {value} "
                    f"({filtered_rows} of {original_rows} rows shown)"
                )

        except Exception as e:
            import logging

            logging.getLogger(__name__).error(f"AI Filter Error: {e}")
            wx.MessageBox(
                f"Error applying greater than filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_number_less_than(
        self, column_index: any, value: float, include_equal: bool = False
    ) -> None:
        """Filter numbers less than a value."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            operator = (
                NumberFilterCriterion.LESS_EQUAL
                if include_equal
                else NumberFilterCriterion.LESS_THAN
            )
            criterion = NumberFilterCriterion(operator, value)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            op_text = "<=" if include_equal else "<"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for values {op_text} {value}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying less than filter: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_filter_text_contains(
        self, column_index: any, text: str, case_sensitive: bool = False
    ) -> None:
        """Filter text that contains a substring."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import TextFilterCriterion

            criterion = TextFilterCriterion(
                TextFilterCriterion.CONTAINS, text, case_sensitive
            )
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            case_text = " (case sensitive)" if case_sensitive else ""
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for text containing '{text}'{case_text}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying text contains filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_text_equals(
        self, column_index: any, text: str, case_sensitive: bool = False
    ) -> None:
        """Filter text that exactly matches."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import TextFilterCriterion

            criterion = TextFilterCriterion(
                TextFilterCriterion.EQUALS, text, case_sensitive
            )
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            case_text = " (case sensitive)" if case_sensitive else ""
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for text equals '{text}'{case_text}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying text equals filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_text_starts_with(
        self, column_index: any, prefix: str, case_sensitive: bool = False
    ) -> None:
        """Filter text that starts with a prefix."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import TextFilterCriterion

            criterion = TextFilterCriterion(
                TextFilterCriterion.BEGINS_WITH, prefix, case_sensitive
            )
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            case_text = " (case sensitive)" if case_sensitive else ""
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for text starting with '{prefix}'{case_text}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying text starts with filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_text_ends_with(
        self, column_index: any, suffix: str, case_sensitive: bool = False
    ) -> None:
        """Filter text that ends with a suffix."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import TextFilterCriterion

            criterion = TextFilterCriterion(
                TextFilterCriterion.ENDS_WITH, suffix, case_sensitive
            )
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            case_text = " (case sensitive)" if case_sensitive else ""
            self.status_bar.SetStatusText(
                f"Filtered {column_name} for text ending with '{suffix}'{case_text}"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying text ends with filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_blanks(self, column_index: any) -> None:
        """Filter to show only blank cells."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import BlanksCriterion

            criterion = BlanksCriterion(show_blanks=True)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show only blank cells"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying blanks filter: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_filter_non_blanks(self, column_index: any) -> None:
        """Filter to show only non-blank cells."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import BlanksCriterion

            criterion = BlanksCriterion(show_blanks=False)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show only non-blank cells"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying non-blanks filter: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_filter_top_n(self, column_index: any, n: int) -> None:
        """Filter to show top N values."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            criterion = NumberFilterCriterion(NumberFilterCriterion.TOP_N, n)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show top {n} values"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying top N filter: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_filter_bottom_n(self, column_index: any, n: int) -> None:
        """Filter to show bottom N values."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            criterion = NumberFilterCriterion(NumberFilterCriterion.BOTTOM_N, n)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show bottom {n} values"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying bottom N filter: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_filter_above_average(self, column_index: any) -> None:
        """Filter to show above average values."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            criterion = NumberFilterCriterion(NumberFilterCriterion.ABOVE_AVERAGE)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show above average values"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying above average filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_filter_below_average(self, column_index: any) -> None:
        """Filter to show below average values."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            from jedit2.ui.filter_criteria import NumberFilterCriterion

            criterion = NumberFilterCriterion(NumberFilterCriterion.BELOW_AVERAGE)
            grid.apply_advanced_filter(
                col_index, {"action": "criterion", "criterion": criterion}
            )
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(
                f"Filtered {column_name} to show below average values"
            )
        except Exception as e:
            wx.MessageBox(
                f"Error applying below average filter: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_clear_filter(self, column_index: any) -> None:
        """Clear filter from a column."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        col_index = self._get_column_index(column_index)
        if col_index < 0 or col_index >= grid.GetNumberCols():
            wx.MessageBox("Invalid column index.", "Error", wx.OK | wx.ICON_ERROR)
            return

        try:
            grid.clear_filter(col_index)
            column_name = grid.GetColLabelValue(col_index) or f"Column {col_index + 1}"
            self.status_bar.SetStatusText(f"Cleared filter from {column_name}")
        except Exception as e:
            wx.MessageBox(f"Error clearing filter: {e}", "Error", wx.OK | wx.ICON_ERROR)

    def _ai_clear_all_filters(self) -> None:
        """Clear all filters."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "No spreadsheet is currently active.", "Error", wx.OK | wx.ICON_ERROR
            )
            return

        try:
            # Clear all filters
            grid.active_filters.clear()
            grid.advanced_filters.clear()
            grid._update_view()
            self.status_bar.SetStatusText("Cleared all filters")
        except Exception as e:
            wx.MessageBox(
                f"Error clearing all filters: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_show_filter_history(self) -> None:
        """Show filter history dialog."""
        try:
            self._on_filter_history(None)
        except Exception as e:
            wx.MessageBox(
                f"Error showing filter history: {e}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_format_as_comma(self) -> None:
        """AI interface for comma formatting."""
        try:
            self._on_format_comma(wx.CommandEvent())
        except Exception as e:
            wx.MessageBox(
                f"Error formatting with comma separator: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _ai_freeze_panes(self) -> None:
        """AI interface for freeze panes."""
        try:
            self._on_freeze_panes(wx.CommandEvent())
        except (AttributeError, RuntimeError) as error:
            wx.MessageBox(
                f"Error freezing panes: {error}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _ai_unfreeze_panes(self) -> None:
        """AI interface for unfreeze panes."""
        try:
            self._on_unfreeze_panes(wx.CommandEvent())
        except (AttributeError, RuntimeError) as error:
            wx.MessageBox(
                f"Error unfreezing panes: {error}", "Error", wx.OK | wx.ICON_ERROR
            )

    def _on_advanced_filter(self, event: wx.CommandEvent) -> None:
        """Show the advanced filter for the current column."""
        grid = self._get_current_grid()
        if not grid:
            wx.MessageBox(
                "This feature is only available in a spreadsheet.",
                "Info",
                wx.OK | wx.ICON_INFORMATION,
            )
            return

        current_col = grid.GetGridCursorCol()
        if current_col == -1:
            current_col = 0  # Default to first column if none is selected

        grid._show_filter_dialog(current_col)

    def _on_show_calculator(self, event: wx.CommandEvent) -> None:
        """Show a simple calculator dialog."""
        wx.MessageBox(
            "Calculator functionality is not yet implemented.",
            "Coming Soon",
            wx.OK | wx.ICON_INFORMATION,
        )

    def _load_recent_files(self) -> None:
        """Load recent files list from configuration."""
        try:
            recent_files_str = self.config_manager.get_setting("recent_files", "")
            if recent_files_str:
                import json
                self.recent_files = json.loads(recent_files_str)
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            print(f"Error loading recent files: {e}")
            self.recent_files = []

    def _save_recent_files(self) -> None:
        """Save recent files list to configuration."""
        try:
            import json
            self.config_manager.set_setting(
                "recent_files", json.dumps(self.recent_files)
            )
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            print(f"Error saving recent files: {e}")

    def _add_to_recent_files(self, file_path: str) -> None:
        """Add a file to the recent files list."""
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        self.recent_files.insert(0, file_path)

        # Limit the list size
        if len(self.recent_files) > self.max_recent_files:
            self.recent_files = self.recent_files[: self.max_recent_files]

        self._save_recent_files()
        self._update_recent_files_menu()

    def _update_recent_files_menu(self) -> None:
        """Update the recent files menu."""
        # Clear existing items
        for item in self.recent_files_menu.GetMenuItems():
            self.recent_files_menu.Delete(item)

        if not self.recent_files:
            self.recent_files_menu.Append(wx.ID_ANY, "(No recent files)", "").Enable(
                False
            )
        else:
            for i, file_path in enumerate(self.recent_files):
                file_name = os.path.basename(file_path)
                menu_id = wx.NewId()
                self.recent_files_menu.Append(menu_id, f"&{i+1} {file_name}", file_path)
                self.Bind(
                    wx.EVT_MENU,
                    lambda evt, path=file_path: self._on_recent_file(path),
                    id=menu_id,
                )

    def _on_recent_file(self, file_path: str) -> None:
        """Open a recent file."""
        import os

        if os.path.exists(file_path):
            self._open_file(file_path)
        else:
            wx.MessageBox(
                f"File not found: {file_path}", "File Not Found", wx.OK | wx.ICON_ERROR
            )
            # Remove from recent files
            if file_path in self.recent_files:
                self.recent_files.remove(file_path)
                self._save_recent_files()
                self._update_recent_files_menu()

    def _open_file(self, file_path: str) -> None:
        """Open a file and add it to recent files."""
        file_type = self._get_file_type(file_path)
        if file_type in ["csv", "xlsx", "json", "yaml"]:
            self._open_grid_based_file(file_path, file_type)
        else:
            self._open_text_based_file(file_path, file_type)

        self._add_to_recent_files(file_path)

    def _on_freeze_panes(self, event: wx.CommandEvent) -> None:
        """Handle freeze panes functionality."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Get current cursor position
        current_row = grid.GetGridCursorRow()
        current_col = grid.GetGridCursorCol()

        # Implement freeze panes logic
        if hasattr(grid, "freeze_rows"):
            grid.freeze_rows = current_row
            grid.freeze_cols = current_col
        else:
            # For basic implementation, store freeze information
            grid.freeze_rows = current_row
            grid.freeze_cols = current_col

        # Update grid display to show frozen panes
        self._apply_freeze_panes(grid, current_row, current_col)
        self.status_bar.SetStatusText(
            f"Frozen panes at row {current_row + 1}, "
            f"column {chr(ord('A') + current_col)}"
        )

    def _apply_freeze_panes(
        self, grid: SpreadsheetView, freeze_row: int, freeze_col: int
    ) -> None:
        """Apply freeze panes to the grid."""
        # For basic implementation using wxPython Grid capabilities
        # Note: wxPython doesn't have built-in freeze panes like Excel
        # This is a simplified implementation
        try:
            # Set the frozen corner
            if freeze_row > 0:
                grid.SetRowLabelSize(40)  # Make sure row labels are visible
            if freeze_col > 0:
                grid.SetColLabelSize(25)  # Make sure column labels are visible

            # Store freeze information for scrolling behavior
            grid.freeze_row = freeze_row
            grid.freeze_col = freeze_col

            # Refresh the grid
            grid.ForceRefresh()

        except (AttributeError, RuntimeError) as error:
            wx.MessageBox(
                f"Error applying freeze panes: {str(error)}",
                "Error",
                wx.OK | wx.ICON_ERROR,
            )

    def _on_unfreeze_panes(self, event: wx.CommandEvent) -> None:
        """Handle unfreeze panes functionality."""
        grid = self._get_current_grid()
        if not grid:
            return

        # Check if panes are currently frozen
        if not hasattr(grid, "freeze_rows") or (
            getattr(grid, "freeze_rows", 0) == 0
            and getattr(grid, "freeze_cols", 0) == 0
        ):
            return

        # Clear freeze information
        grid.freeze_rows = 0
        grid.freeze_cols = 0
        if hasattr(grid, "freeze_row"):
            grid.freeze_row = 0
        if hasattr(grid, "freeze_col"):
            grid.freeze_col = 0

        # Refresh the grid to remove freeze display
        grid.ForceRefresh()
        self.status_bar.SetStatusText("Unfrozen all panes")

    def _setup_ai_system_methods(self) -> None:
        """Setup convenient methods for accessing AI system functionality."""
        pass  # Methods will be added by the AI system integrator if available

    def get_ai_system_status(self) -> dict:
        """Get the current AI system status."""
        if hasattr(self, 'ai_system_integrator') and self.ai_system_integrator:
            try:
                if hasattr(self.ai_system_integrator, 'ai_system') and self.ai_system_integrator.ai_system:
                    return self.ai_system_integrator.ai_system.get_system_status()
            except Exception as e:
                self.logger.warning(f"Failed to get AI system status: {e}")

        return {
            "status": "unavailable",
            "message": "AI system not integrated or not available"
        }

    def get_ai_system_health(self) -> dict:
        """Get the current AI system health check results."""
        if hasattr(self, 'ai_system_integrator') and self.ai_system_integrator:
            try:
                if hasattr(self.ai_system_integrator, 'ai_system') and self.ai_system_integrator.ai_system:
                    return self.ai_system_integrator.ai_system.run_health_check()
            except Exception as e:
                self.logger.warning(f"Failed to get AI system health: {e}")

        return {
            "overall_status": "unavailable",
            "message": "AI system not integrated or not available"
        }

    def generate_ai_system_report(self) -> dict:
        """Generate a comprehensive AI system report."""
        if hasattr(self, 'ai_system_integrator') and self.ai_system_integrator:
            try:
                if hasattr(self.ai_system_integrator, 'ai_system') and self.ai_system_integrator.ai_system:
                    return self.ai_system_integrator.ai_system.generate_system_report()
            except Exception as e:
                self.logger.warning(f"Failed to generate AI system report: {e}")

        return {
            "status": "unavailable",
            "message": "AI system not integrated or not available"
        }
