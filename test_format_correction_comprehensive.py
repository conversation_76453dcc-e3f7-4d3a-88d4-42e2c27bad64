#!/usr/bin/env python3
"""
Comprehensive automated tests for format correction features.
Tests all supported file formats: JSON, YAML, CSV, MD, XLSX
"""

import os
import sys
import tempfile
import json
import yaml
import csv
import pandas as pd
from pathlib import Path

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

class FormatCorrectionTestSuite:
    """Comprehensive test suite for format correction features."""
    
    def __init__(self):
        self.validator = FormatValidator()
        self.corrector = FormatCorrector()
        self.test_results = []
        self.temp_dir = tempfile.mkdtemp()
        
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status}: {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
        
    def test_json_correction(self):
        """Test JSON format correction."""
        print("\n=== Testing JSON Format Correction ===")
        
        # Test 1: Trailing comma
        test_cases = [
            {
                'name': 'JSON Trailing Comma',
                'content': '{"name": "test", "value": 123,}',
                'expected_fixes': ['trailing comma']
            },
            {
                'name': 'JSON Missing Quotes',
                'content': '{name: "test", value: 123}',
                'expected_fixes': ['missing quotes']
            },
            {
                'name': 'JSON Unescaped Quotes',
                'content': '{"message": "He said "hello" to me"}',
                'expected_fixes': ['unescaped quotes']
            },
            {
                'name': 'JSON Multiple Issues',
                'content': '{name: "test", "message": "He said "hello"",}',
                'expected_fixes': ['multiple issues']
            }
        ]
        
        for case in test_cases:
            try:
                # Validate first
                validation_result = self.validator.validate_format(case['content'], 'json')
                
                if validation_result.is_correctable:
                    # Attempt correction
                    correction_result = self.corrector.correct_format(case['content'], 'json')
                    
                    if correction_result.success:
                        # Verify corrected content is valid JSON
                        try:
                            json.loads(correction_result.corrected_content)
                            self.log_result(case['name'], True, f"Fixed: {', '.join(correction_result.changes_made)}")
                        except json.JSONDecodeError as e:
                            self.log_result(case['name'], False, f"Corrected content still invalid: {e}")
                    else:
                        self.log_result(case['name'], False, f"Correction failed: {', '.join(correction_result.errors)}")
                else:
                    self.log_result(case['name'], False, "Not detected as correctable")
                    
            except Exception as e:
                self.log_result(case['name'], False, f"Exception: {e}")
                
    def test_yaml_correction(self):
        """Test YAML format correction."""
        print("\n=== Testing YAML Format Correction ===")
        
        test_cases = [
            {
                'name': 'YAML Indentation',
                'content': 'name: test\n  value: 123\nother: data',
                'expected_fixes': ['indentation']
            },
            {
                'name': 'YAML Missing Colon',
                'content': 'name test\nvalue: 123',
                'expected_fixes': ['missing colon']
            },
            {
                'name': 'YAML Tab Characters',
                'content': 'name: test\n\tvalue: 123',
                'expected_fixes': ['tab characters']
            }
        ]
        
        for case in test_cases:
            try:
                validation_result = self.validator.validate_format(case['content'], 'yaml')
                
                if validation_result.is_correctable:
                    correction_result = self.corrector.correct_format(case['content'], 'yaml')
                    
                    if correction_result.success:
                        try:
                            yaml.safe_load(correction_result.corrected_content)
                            self.log_result(case['name'], True, f"Fixed: {', '.join(correction_result.changes_made)}")
                        except yaml.YAMLError as e:
                            self.log_result(case['name'], False, f"Corrected content still invalid: {e}")
                    else:
                        self.log_result(case['name'], False, f"Correction failed: {', '.join(correction_result.errors)}")
                else:
                    self.log_result(case['name'], False, "Not detected as correctable")
                    
            except Exception as e:
                self.log_result(case['name'], False, f"Exception: {e}")
                
    def test_csv_correction(self):
        """Test CSV format correction."""
        print("\n=== Testing CSV Format Correction ===")
        
        test_cases = [
            {
                'name': 'CSV Inconsistent Columns',
                'content': 'name,age,city\nJohn,25\nJane,30,Boston,Extra',
                'expected_fixes': ['column count']
            },
            {
                'name': 'CSV Unquoted Commas',
                'content': 'name,description\nJohn,A person, who likes coding\nJane,Simple person',
                'expected_fixes': ['unquoted commas']
            },
            {
                'name': 'CSV Missing Headers',
                'content': 'John,25,Boston\nJane,30,NYC',
                'expected_fixes': ['missing headers']
            }
        ]
        
        for case in test_cases:
            try:
                validation_result = self.validator.validate_format(case['content'], 'csv')
                
                if validation_result.is_correctable:
                    correction_result = self.corrector.correct_format(case['content'], 'csv')
                    
                    if correction_result.success:
                        # Try to parse corrected CSV
                        try:
                            from io import StringIO
                            csv.reader(StringIO(correction_result.corrected_content))
                            self.log_result(case['name'], True, f"Fixed: {', '.join(correction_result.changes_made)}")
                        except Exception as e:
                            self.log_result(case['name'], False, f"Corrected content still invalid: {e}")
                    else:
                        self.log_result(case['name'], False, f"Correction failed: {', '.join(correction_result.errors)}")
                else:
                    self.log_result(case['name'], False, "Not detected as correctable")
                    
            except Exception as e:
                self.log_result(case['name'], False, f"Exception: {e}")
                
    def test_markdown_correction(self):
        """Test Markdown format correction."""
        print("\n=== Testing Markdown Format Correction ===")
        
        test_cases = [
            {
                'name': 'MD Header Spacing',
                'content': '#Header\n##Another Header\nContent here',
                'expected_fixes': ['header spacing']
            },
            {
                'name': 'MD List Formatting',
                'content': '- Item 1\n-Item 2\n - Item 3',
                'expected_fixes': ['list formatting']
            },
            {
                'name': 'MD Link Formatting',
                'content': 'Check out [this link] (http://example.com) for more info.',
                'expected_fixes': ['link formatting']
            }
        ]
        
        for case in test_cases:
            try:
                validation_result = self.validator.validate_format(case['content'], 'md')
                
                if validation_result.is_correctable:
                    correction_result = self.corrector.correct_format(case['content'], 'md')
                    
                    if correction_result.success:
                        self.log_result(case['name'], True, f"Fixed: {', '.join(correction_result.changes_made)}")
                    else:
                        self.log_result(case['name'], False, f"Correction failed: {', '.join(correction_result.errors)}")
                else:
                    self.log_result(case['name'], False, "Not detected as correctable")
                    
            except Exception as e:
                self.log_result(case['name'], False, f"Exception: {e}")
                
    def test_xlsx_validation(self):
        """Test XLSX format validation (correction not applicable for binary format)."""
        print("\n=== Testing XLSX Format Validation ===")
        
        try:
            # Create a test XLSX file
            test_file = os.path.join(self.temp_dir, 'test.xlsx')
            df = pd.DataFrame({
                'Name': ['John', 'Jane'],
                'Age': [25, 30],
                'City': ['Boston', 'NYC']
            })
            df.to_excel(test_file, index=False)
            
            # Read the file content as bytes (simulating file reading)
            with open(test_file, 'rb') as f:
                content = f.read()
                
            # For XLSX, we mainly test that validation doesn't crash
            # since correction is not applicable to binary formats
            validation_result = self.validator.validate_format(content, 'xlsx')
            self.log_result('XLSX Validation', True, "Validation completed without errors")
            
        except Exception as e:
            self.log_result('XLSX Validation', False, f"Exception: {e}")
            
    def test_error_handling(self):
        """Test error handling and edge cases."""
        print("\n=== Testing Error Handling ===")
        
        test_cases = [
            {
                'name': 'Empty Content',
                'content': '',
                'file_type': 'json'
            },
            {
                'name': 'Invalid File Type',
                'content': '{"test": "data"}',
                'file_type': 'invalid'
            },
            {
                'name': 'Severely Corrupted JSON',
                'content': '{{{{{invalid json content}}}}}',
                'file_type': 'json'
            },
            {
                'name': 'Binary Content as Text',
                'content': b'\x00\x01\x02\x03'.decode('latin-1'),
                'file_type': 'json'
            }
        ]
        
        for case in test_cases:
            try:
                validation_result = self.validator.validate_format(case['content'], case['file_type'])
                correction_result = self.corrector.correct_format(case['content'], case['file_type'])
                
                # For error cases, we expect graceful handling (no exceptions)
                self.log_result(case['name'], True, "Handled gracefully")
                
            except Exception as e:
                self.log_result(case['name'], False, f"Unhandled exception: {e}")
                
    def run_all_tests(self):
        """Run all format correction tests."""
        print("🧪 Starting Comprehensive Format Correction Tests")
        print("=" * 60)
        
        # Run all test categories
        self.test_json_correction()
        self.test_yaml_correction()
        self.test_csv_correction()
        self.test_markdown_correction()
        self.test_xlsx_validation()
        self.test_error_handling()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
                    
        return failed_tests == 0

if __name__ == "__main__":
    test_suite = FormatCorrectionTestSuite()
    success = test_suite.run_all_tests()
    sys.exit(0 if success else 1)
