"""JSON file handler for JEdit2."""
import json
from typing import List, Any, Dict

def _flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """Flatten a nested dictionary into a single level dictionary.
    
    Args:
        d: Dictionary to flatten
        parent_key: Parent key for nested items
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items: List[tuple] = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(_flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def _unflatten_dict(d: Dict[str, Any], sep: str = '.') -> Dict[str, Any]:
    """Convert a flattened dictionary back to a nested structure.
    
    Args:
        d: Flattened dictionary
        sep: Separator used in keys
        
    Returns:
        Nested dictionary
    """
    result: Dict[str, Any] = {}
    for k, v in d.items():
        parts = k.split(sep)
        current = result
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        current[parts[-1]] = v
    return result

def parse_json(path: str) -> List[List[Any]]:
    """Parse a JSON file and return its contents as a list of rows for tabular display.

    Args:
        path: Path to the JSON file.
    Returns:
        List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be parsed.
    """
    with open(path, encoding='utf-8') as f:
        data = json.load(f)
    
    # Flatten nested structure
    if isinstance(data, dict):
        flat_data = _flatten_dict(data)
        return [["Key", "Value"]] + [[k, str(v)] for k, v in flat_data.items()]
    
    # Handle list of dicts
    if isinstance(data, list) and all(isinstance(row, dict) for row in data):
        if not data:
            return [["No data"]]
        headers = list(data[0].keys())
        rows = [headers]
        for row in data:
            rows.append([str(row.get(h, "")) for h in headers])
        return rows
    
    # Handle list of lists
    if isinstance(data, list) and all(isinstance(row, list) for row in data):
        return data
    
    raise Exception("Unsupported JSON structure for tabular display.")

def save_json(path: str, data: List[List[Any]]) -> None:
    """Save data to a JSON file.

    Args:
        path: Path where to save the JSON file.
        data: List of rows, each row is a list of cell values.
    Raises:
        Exception: If the file cannot be saved.
    """
    if not data:
        raise Exception("No data to save")
    
    # If the data is in key-value format (from flattened dict)
    if len(data[0]) == 2 and data[0] == ["Key", "Value"]:
        flat_dict = {row[0]: row[1] for row in data[1:]}
        nested_dict = _unflatten_dict(flat_dict)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(nested_dict, f, indent=4)
        return
    
    # If the data is in table format (headers + rows)
    headers = data[0]
    rows = data[1:]
    if all(len(row) == len(headers) for row in rows):
        result = []
        for row in rows:
            result.append(dict(zip(headers, row)))
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=4)
        return
    
    # If the data is a simple list of lists
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4) 