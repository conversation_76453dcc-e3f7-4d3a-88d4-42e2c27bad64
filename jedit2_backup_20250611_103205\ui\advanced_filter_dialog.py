"""Advanced Filter Dialog for Excel-like filtering functionality."""

import wx
from typing import List, Dict, Set, Optional, Any, Callable
import logging
from .filter_utils import detect_column_type

logger = logging.getLogger(__name__)


class AdvancedFilterDialog(wx.Dialog):
    """Excel-like filter dialog with value selection and advanced options."""
    
    def __init__(self, parent: wx.Window, column_name: str, column_data: List[str], 
                 current_filter: Optional[str] = None):
        """
        Initialize the advanced filter dialog.
        
        Args:
            parent: Parent window
            column_name: Name of the column being filtered
            column_data: List of all values in the column
            current_filter: Currently applied filter (if any)
        """
        super().__init__(parent, title=f"Filter - {column_name}", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.column_name = column_name
        self.column_data = column_data
        self.current_filter = current_filter
        self.unique_values = self._get_unique_values()
        self.filtered_values = list(self.unique_values)
        self.selected_values: Set[str] = set()
        self.filter_result: Optional[Dict[str, Any]] = None
        
        self._init_ui()
        self._bind_events()
        self._populate_values()
        
        # Set initial size and center
        self.SetSize((350, 500))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Header panel with search and select all
        header_panel = self._create_header_panel()
        main_sizer.Add(header_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Values panel with scrollable checkbox list
        values_panel = self._create_values_panel()
        main_sizer.Add(values_panel, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 5)
        
        # Options panel for advanced filters
        options_panel = self._create_options_panel()
        main_sizer.Add(options_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 5)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Status panel
        status_panel = self._create_status_panel()
        main_sizer.Add(status_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 5)
        
        self.SetSizer(main_sizer)
    
    def _create_header_panel(self) -> wx.Panel:
        """Create the header panel with search and select all controls."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Search box
        search_label = wx.StaticText(panel, label="Search:")
        sizer.Add(search_label, 0, wx.BOTTOM, 2)
        
        self.search_ctrl = wx.TextCtrl(panel, style=wx.TE_PROCESS_ENTER)
        self.search_ctrl.SetHint("Type to filter or condition: >40000, <50000, >=30000, <=60000, !=25, 25..70000, 25:60000")
        sizer.Add(self.search_ctrl, 0, wx.EXPAND | wx.BOTTOM, 5)
        
        # Select All checkbox
        self.select_all_cb = wx.CheckBox(panel, label="Select All")
        self.select_all_cb.SetValue(True)
        sizer.Add(self.select_all_cb, 0, wx.BOTTOM, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_values_panel(self) -> wx.Panel:
        """Create the scrollable panel with value checkboxes."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Scrolled window for values
        self.values_scroll = wx.ScrolledWindow(panel)
        self.values_scroll.SetScrollRate(5, 5)
        self.values_sizer = wx.BoxSizer(wx.VERTICAL)
        self.values_scroll.SetSizer(self.values_sizer)
        
        sizer.Add(self.values_scroll, 1, wx.EXPAND)
        panel.SetSizer(sizer)
        return panel
    
    def _create_options_panel(self) -> wx.Panel:
        """Create the options panel for advanced filtering."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Custom Filter button
        self.custom_btn = wx.Button(panel, label="Custom Filter...")
        sizer.Add(self.custom_btn, 0, wx.RIGHT, 5)
        
        # Text/Number/Date filters button (will be populated based on data type)
        data_type = detect_column_type(self.column_data)
        if data_type == 'number':
            self.advanced_btn = wx.Button(panel, label="Number Filters...")
        elif data_type == 'date':
            self.advanced_btn = wx.Button(panel, label="Date Filters...")
        else:
            self.advanced_btn = wx.Button(panel, label="Text Filters...")
        sizer.Add(self.advanced_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel with OK, Cancel, Clear."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.clear_btn = wx.Button(panel, label="Clear Filter")
        sizer.Add(self.clear_btn, 0, wx.RIGHT, 5)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 5)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_status_panel(self) -> wx.Panel:
        """Create the status panel showing item counts."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.status_label = wx.StaticText(panel, label="")
        sizer.Add(self.status_label, 1, wx.ALIGN_CENTER_VERTICAL)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.search_ctrl.Bind(wx.EVT_TEXT, self._on_search)
        self.search_ctrl.Bind(wx.EVT_TEXT_ENTER, self._on_search)
        self.select_all_cb.Bind(wx.EVT_CHECKBOX, self._on_select_all)
        self.custom_btn.Bind(wx.EVT_BUTTON, self._on_custom_filter)
        self.advanced_btn.Bind(wx.EVT_BUTTON, self._on_advanced_filter)
        self.clear_btn.Bind(wx.EVT_BUTTON, self._on_clear_filter)
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _get_unique_values(self) -> List[str]:
        """Get sorted unique values from column data."""
        unique_set = set()
        blanks_found = False
        
        for value in self.column_data:
            if not value or value.isspace():
                blanks_found = True
            else:
                unique_set.add(str(value).strip())
        
        # Sort values naturally
        unique_list = sorted(unique_set, key=str.lower)
        
        # Add blanks at the end if found
        if blanks_found:
            unique_list.append("(Blanks)")
        
        return unique_list
    
    def _populate_values(self) -> None:
        """Populate the values list with checkboxes."""
        # Clear existing controls
        self.values_sizer.Clear(True)
        self.value_checkboxes: Dict[str, wx.CheckBox] = {}
        
        # Add checkboxes for filtered values
        for value in self.filtered_values:
            cb = wx.CheckBox(self.values_scroll, label=value)
            cb.SetValue(True)  # Initially all selected
            self.value_checkboxes[value] = cb
            self.values_sizer.Add(cb, 0, wx.EXPAND | wx.ALL, 2)
            
            # Bind checkbox event
            cb.Bind(wx.EVT_CHECKBOX, self._on_value_checkbox)
        
        # Update selected values
        self.selected_values = set(self.filtered_values)
        self._update_status()
        
        # Refresh layout
        self.values_scroll.SetVirtualSize(self.values_sizer.GetMinSize())
        self.values_scroll.Layout()
    
    def _on_search(self, event: wx.CommandEvent) -> None:
        """Handle search text changes."""
        search_text = self.search_ctrl.GetValue().strip()
        
        if not search_text:
            self.filtered_values = list(self.unique_values)
            self.filter_result = None  # Clear any stored condition
        else:
            # Check if this is a direct condition expression
            condition_result = self._parse_condition_expression(search_text)
            if condition_result:
                # Store the condition but don't close dialog yet - let user see preview
                self.filter_result = condition_result
                # Show all values for now since condition doesn't filter the list
                self.filtered_values = list(self.unique_values)
                self._populate_values()
                
                # If this is an Enter key event, apply the filter immediately
                if event.GetEventType() == wx.EVT_TEXT_ENTER.typeId:
                    self.EndModal(wx.ID_OK)
                return
            
            # Otherwise, continue with normal search (wildcards and text matching)
            self.filter_result = None  # Clear any stored condition
            search_lower = search_text.lower()
            
            # Support wildcards (* and ?)
            search_pattern = search_lower.replace('*', '.*').replace('?', '.')
            try:
                import re
                pattern = re.compile(search_pattern, re.IGNORECASE)
                self.filtered_values = [v for v in self.unique_values 
                                      if pattern.search(v.lower())]
            except re.error:
                # Fallback to simple contains search
                self.filtered_values = [v for v in self.unique_values 
                                      if search_lower in v.lower()]
        
        self._populate_values()

    def _parse_condition_expression(self, expression: str) -> Optional[Dict[str, Any]]:
        """
        Parse condition expressions like '>40000', '<50000', '>=30000', '=25', '25..70000'
        
        Returns:
            Filter result dict if valid expression, None otherwise
        """
        expression = expression.strip()
        if not expression:
            return None
        
        # Detect column data type
        from .filter_utils import detect_column_type
        data_type = detect_column_type(self.column_data)
        
        # Try to parse numeric expressions first (most common)
        if data_type in ['number', 'mixed'] or any(c.isdigit() or c in '.-+' for c in expression):
            result = self._parse_numeric_expression(expression)
            if result:
                return result
        
        # Try to parse date expressions  
        if data_type in ['date', 'mixed']:
            result = self._parse_date_expression(expression)
            if result:
                return result
        
        # Try to parse text expressions (equals, not equals)
        result = self._parse_text_expression(expression)
        if result:
            return result
            
        return None
    
    def _parse_numeric_expression(self, expression: str) -> Optional[Dict[str, Any]]:
        """Parse numeric condition expressions."""
        import re
        from .filter_criteria import NumberFilterCriterion
        from .filter_utils import parse_number
        
        # Remove spaces for easier parsing
        expr = expression.replace(' ', '')
        
        # Range patterns: 25..70000, 25-70000, 25:70000
        range_patterns = [
            r'^([+-]?\d*\.?\d+)\.\.([+-]?\d*\.?\d+)$',  # 25..70000
            r'^([+-]?\d*\.?\d+):([+-]?\d*\.?\d+)$',    # 25:70000  
            r'^([+-]?\d*\.?\d+)\s*-\s*([+-]?\d*\.?\d+)$' # 25-70000 (but not negative numbers)
        ]
        
        for pattern in range_patterns:
            range_match = re.match(pattern, expr)
            if range_match:
                try:
                    value1 = float(range_match.group(1))
                    value2 = float(range_match.group(2))
                    
                    # Don't treat negative numbers as ranges
                    if expr.startswith('-') and not re.match(r'^-?\d+\s*-\s*\d+', expr):
                        continue
                        
                    criterion = NumberFilterCriterion(
                        operator=NumberFilterCriterion.BETWEEN,
                        value1=min(value1, value2),  # Ensure proper order
                        value2=max(value1, value2)
                    )

                    return {"action": "criterion", "criterion": criterion}
                except ValueError:
                    continue
        
        # Comparison operators with enhanced support
        patterns = [
            # Two character operators first (order matters!)
            (r'^>=([+-]?\d*\.?\d+)$', NumberFilterCriterion.GREATER_EQUAL),
            (r'^<=([+-]?\d*\.?\d+)$', NumberFilterCriterion.LESS_EQUAL), 
            (r'^!=([+-]?\d*\.?\d+)$', NumberFilterCriterion.NOT_EQUALS),
            (r'^<>([+-]?\d*\.?\d+)$', NumberFilterCriterion.NOT_EQUALS),  # Excel-style not equals
            
            # Single character operators
            (r'^>([+-]?\d*\.?\d+)$', NumberFilterCriterion.GREATER_THAN),
            (r'^<([+-]?\d*\.?\d+)$', NumberFilterCriterion.LESS_THAN),
            (r'^=([+-]?\d*\.?\d+)$', NumberFilterCriterion.EQUALS),
            
            # Text-style operators (case insensitive)
            (r'^eq\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.EQUALS),        # eq 50000
            (r'^gt\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.GREATER_THAN),  # gt 50000
            (r'^lt\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.LESS_THAN),     # lt 50000
            (r'^gte?\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.GREATER_EQUAL), # gte 50000 or ge 50000
            (r'^lte?\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.LESS_EQUAL),  # lte 50000 or le 50000
            (r'^ne\s*([+-]?\d*\.?\d+)$', NumberFilterCriterion.NOT_EQUALS),    # ne 50000
            
            # Plain number (equals)
            (r'^([+-]?\d*\.?\d+)$', NumberFilterCriterion.EQUALS),
        ]
        
        for pattern, operator in patterns:
            match = re.match(pattern, expr)
            if match:
                try:
                    value = float(match.group(1))
                    criterion = NumberFilterCriterion(
                        operator=operator,
                        value1=value
                    )

                    return {"action": "criterion", "criterion": criterion}
                except ValueError:
                    continue
        
        return None
    
    def _parse_date_expression(self, expression: str) -> Optional[Dict[str, Any]]:
        """Parse date condition expressions."""
        # TODO: Implement date expression parsing like '>2023-01-01', '<today', etc.
        # For now, return None to fall back to text search
        return None
    
    def _parse_text_expression(self, expression: str) -> Optional[Dict[str, Any]]:
        """Parse text condition expressions."""
        from .filter_criteria import TextFilterCriterion
        
        # Exact match: ="text" or "text" 
        if expression.startswith('="') and expression.endswith('"'):
            value = expression[2:-1]  # Remove =" and "
            criterion = TextFilterCriterion(
                operator=TextFilterCriterion.EQUALS,
                value=value,
                case_sensitive=False
            )
            return {"action": "criterion", "criterion": criterion}
        
        # Not equals: !="text"
        if expression.startswith('!="') and expression.endswith('"'):
            value = expression[3:-1]  # Remove !=" and "
            criterion = TextFilterCriterion(
                operator=TextFilterCriterion.NOT_EQUALS,
                value=value,
                case_sensitive=False
            )
            return {"action": "criterion", "criterion": criterion}
        
        return None

    def _on_select_all(self, event: wx.CommandEvent) -> None:
        """Handle Select All checkbox."""
        select_all = self.select_all_cb.GetValue()
        
        for cb in self.value_checkboxes.values():
            cb.SetValue(select_all)
        
        if select_all:
            self.selected_values = set(self.filtered_values)
        else:
            self.selected_values.clear()
        
        self._update_status()
    
    def _on_value_checkbox(self, event: wx.CommandEvent) -> None:
        """Handle individual value checkbox changes."""
        cb = event.GetEventObject()
        value = cb.GetLabel()
        
        if cb.GetValue():
            self.selected_values.add(value)
        else:
            self.selected_values.discard(value)
        
        # Update Select All checkbox state
        if len(self.selected_values) == len(self.filtered_values):
            self.select_all_cb.SetValue(True)
        elif len(self.selected_values) == 0:
            self.select_all_cb.SetValue(False)
        else:
            self.select_all_cb.Set3StateValue(wx.CHK_UNDETERMINED)
        
        self._update_status()
    
    def _update_status(self) -> None:
        """Update the status label with item counts."""
        total_items = len(self.unique_values)
        visible_items = len(self.filtered_values)
        selected_items = len(self.selected_values)
        
        if self.search_ctrl.GetValue():
            status = f"{selected_items} of {visible_items} items selected (filtered from {total_items})"
        else:
            status = f"{selected_items} of {total_items} items selected"
        
        self.status_label.SetLabel(status)
    
    def _on_custom_filter(self, event: wx.CommandEvent) -> None:
        """Handle custom filter button click."""
        try:
            from .custom_filter_dialog import CustomFilterDialog
            
            with CustomFilterDialog(self, self.column_name, self.column_data) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    criterion = dlg.get_filter_result()
                    if criterion:
                        # Apply the custom filter criterion
                        self.filter_result = {
                            "action": "criterion",
                            "criterion": criterion
                        }
                        self.EndModal(wx.ID_OK)
        except Exception as e:
            logger.error(f"Error showing custom filter dialog: {e}")
            wx.MessageBox("Error showing custom filter dialog.", "Error", wx.OK | wx.ICON_ERROR)
    
    def _on_advanced_filter(self, event: wx.CommandEvent) -> None:
        """Handle advanced filter button click."""
        data_type = detect_column_type(self.column_data)
        
        if data_type in ['text', 'mixed']:
            self._show_text_filter_dialog()
        elif data_type == 'number':
            self._show_number_filter_dialog()
        elif data_type == 'date':
            self._show_date_filter_dialog()
        else:
            self._show_text_filter_dialog()  # Default to text
    
    def _show_text_filter_dialog(self) -> None:
        """Show text filter dialog."""
        try:
            from .text_filter_dialog import TextFilterDialog
            
            with TextFilterDialog(self, self.column_name) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    criterion = dlg.get_filter_result()
                    if criterion:
                        # Apply the text filter criterion
                        self.filter_result = {
                            "action": "criterion",
                            "criterion": criterion
                        }
                        self.EndModal(wx.ID_OK)
        except Exception as e:
            logger.error(f"Error showing text filter dialog: {e}")
            wx.MessageBox("Error showing text filter dialog.", "Error", wx.OK | wx.ICON_ERROR)
    
    def _show_number_filter_dialog(self) -> None:
        """Show number filter dialog."""
        try:
            from .number_filter_dialog import NumberFilterDialog
            
            with NumberFilterDialog(self, self.column_name, self.column_data) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    criterion = dlg.get_filter_result()
                    if criterion:
                        # Apply the number filter criterion
                        self.filter_result = {
                            "action": "criterion",
                            "criterion": criterion
                        }
                        self.EndModal(wx.ID_OK)
        except Exception as e:
            logger.error(f"Error showing number filter dialog: {e}")
            wx.MessageBox("Error showing number filter dialog.", "Error", wx.OK | wx.ICON_ERROR)
    
    def _show_date_filter_dialog(self) -> None:
        """Show date filter dialog."""
        try:
            from .date_filter_dialog import DateFilterDialog
            
            with DateFilterDialog(self, self.column_name, self.column_data) as dlg:
                if dlg.ShowModal() == wx.ID_OK:
                    criterion = dlg.get_filter_result()
                    if criterion:
                        # Apply the date filter criterion
                        self.filter_result = {
                            "action": "criterion",
                            "criterion": criterion
                        }
                        self.EndModal(wx.ID_OK)
        except Exception as e:
            logger.error(f"Error showing date filter dialog: {e}")
            wx.MessageBox("Error showing date filter dialog.", "Error", wx.OK | wx.ICON_ERROR)
    
    def _on_clear_filter(self, event: wx.CommandEvent) -> None:
        """Handle clear filter button click."""
        self.filter_result = {"action": "clear"}
        self.EndModal(wx.ID_OK)
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        # If we have a stored condition (from search), use that
        if self.filter_result is not None and self.filter_result.get("action") == "criterion":
            # Keep the stored condition
            pass
        else:
            # Prepare filter result based on selected values
            if len(self.selected_values) == len(self.unique_values):
                # All values selected - no filter needed
                self.filter_result = {"action": "none"}
            else:
                # Return selected values
                selected_list = list(self.selected_values)
                self.filter_result = {
                    "action": "values",
                    "values": selected_list,
                    "include_blanks": "(Blanks)" in selected_list
                }
        
        self.EndModal(wx.ID_OK)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filter_result = None
        self.EndModal(wx.ID_CANCEL)
    
    def get_filter_result(self) -> Optional[Dict[str, Any]]:
        """Get the filter result after dialog is closed."""
        return self.filter_result 