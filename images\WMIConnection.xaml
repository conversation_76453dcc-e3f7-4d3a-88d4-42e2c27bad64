<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,14.5h-3v-2h3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9.5,15h-3L6,14.5v-2l.5-.5h3l.5.5v2ZM7,14H9V13H7Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2,13H6v1H2Zm8,0v1h4V13ZM9,11H7v1H9Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,2V4H13l-.5-.5L12,4h-.5V5l.5.5v5l-.5.5h-1l-.5-.5v-5l.5-.5V4h-1a.5.5,0,0,0-.5.5.5.5,0,0,1-1,0A2.5,2.5,0,0,1,10.5,2H12l.5.5L13,2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5.5,6.3v4.2L5,11H4l-.5-.5V6.3a2.317,2.317,0,0,1,.217-4.295v2.2H5.279V2l0,0A2.322,2.322,0,0,1,5.5,6.3Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
