#!/usr/bin/env python3
"""Demo of professional format correction tools."""

import json
import subprocess
import shutil

def demo_json_correction():
    """Demonstrate JSON correction using python -m json.tool."""
    print("=== JSON Correction Demo ===")
    
    # Bad JSON with missing comma
    bad_json = '{"name": "<PERSON>" "age": 30, "city": "New York"}'
    
    print(f"Original JSON: {bad_json}")
    
    # Try to parse
    try:
        json.loads(bad_json)
        print("✓ JSON is already valid")
    except json.JSONDecodeError as e:
        print(f"✗ JSON error: {e}")
        
        # Try using python -m json.tool
        if shutil.which('python'):
            print("Attempting correction with python -m json.tool...")
            result = subprocess.run(
                ['python', '-m', 'json.tool'],
                input=bad_json,
                text=True,
                capture_output=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("✓ Corrected successfully!")
                print(f"Corrected JSON: {result.stdout.strip()}")
            else:
                print(f"✗ Tool failed: {result.stderr}")
                
                # Manual fix for demonstration
                print("Applying manual fix...")
                fixed_json = bad_json.replace('"John" "age"', '"John", "age"')
                try:
                    parsed = json.loads(fixed_json)
                    formatted = json.dumps(parsed, indent=2)
                    print(f"✓ Manually corrected: {formatted}")
                except json.JSONDecodeError:
                    print("✗ Manual fix failed")
    print()

def demo_yaml_correction():
    """Demonstrate YAML correction."""
    print("=== YAML Correction Demo ===")
    
    import yaml
    
    # Bad YAML with tabs
    bad_yaml = "name: John\n\tage: 30\n\taddress:\n\t\tstreet: 123 Main St"
    
    print(f"Original YAML:\n{repr(bad_yaml)}")
    
    try:
        yaml.safe_load(bad_yaml)
        print("✓ YAML is already valid")
    except yaml.YAMLError as e:
        print(f"✗ YAML error: {e}")
        
        # Fix tabs to spaces
        fixed_yaml = bad_yaml.replace('\t', '  ')
        print(f"Fixed YAML:\n{repr(fixed_yaml)}")
        
        try:
            data = yaml.safe_load(fixed_yaml)
            formatted = yaml.dump(data, default_flow_style=False)
            print(f"✓ Corrected YAML:\n{formatted}")
        except yaml.YAMLError:
            print("✗ Fix failed")
    print()

def demo_csv_correction():
    """Demonstrate CSV correction."""
    print("=== CSV Correction Demo ===")
    
    import csv
    import io
    
    # Bad CSV with inconsistent columns
    bad_csv = "name,age,city\nJohn,30\nJane,25,Boston,Extra\nBob,35,Chicago"
    
    print(f"Original CSV:\n{bad_csv}")
    
    try:
        reader = csv.reader(io.StringIO(bad_csv))
        rows = list(reader)
        
        # Find max columns
        max_cols = max(len(row) for row in rows)
        print(f"Max columns: {max_cols}")
        
        # Standardize
        corrected_rows = []
        for row in rows:
            if len(row) < max_cols:
                row.extend([''] * (max_cols - len(row)))
            elif len(row) > max_cols:
                row = row[:max_cols]  # Truncate extra columns
            corrected_rows.append(row)
        
        # Write back
        output = io.StringIO()
        writer = csv.writer(output)
        for row in corrected_rows:
            writer.writerow(row)
        
        corrected_csv = output.getvalue().strip()
        print(f"✓ Corrected CSV:\n{corrected_csv}")
        
    except Exception as e:
        print(f"✗ CSV correction failed: {e}")
    print()

def check_tools():
    """Check which professional tools are available."""
    print("=== Available Professional Tools ===")
    
    tools = {
        'python': 'Built-in Python',
        'jq': 'JSON processor',
        'black': 'Python formatter',
        'autopep8': 'Python PEP8 formatter',
        'yamllint': 'YAML linter',
        'csvclean': 'CSV cleaner (csvkit)',
        'mdformat': 'Markdown formatter'
    }
    
    for tool, description in tools.items():
        available = shutil.which(tool) is not None
        status = "✓" if available else "✗"
        print(f"{status} {tool:12} - {description}")
    
    print()
    print("To install missing tools:")
    print("pip install black autopep8 yamllint csvkit mdformat")
    print("# For jq: download from https://stedolan.github.io/jq/")
    print()

if __name__ == "__main__":
    check_tools()
    demo_json_correction()
    demo_yaml_correction()
    demo_csv_correction()
    
    print("=== Summary ===")
    print("Professional tools provide:")
    print("• Better error handling")
    print("• Industry-standard formatting")
    print("• Comprehensive validation")
    print("• No need to reinvent the wheel!")
    print()
    print("This avoids C++ assertion failures and other low-level errors.")
