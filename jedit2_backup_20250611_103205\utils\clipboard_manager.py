"""Clipboard manager for JEdit2.

This module provides enhanced clipboard functionality for cross-tab operations,
supporting cell data, formatting, and metadata preservation.
"""

import json
import pickle
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum


class ClipboardDataType(Enum):
    """Types of clipboard data."""
    TEXT = "text"
    CELLS = "cells"
    ROWS = "rows"
    COLUMNS = "columns"
    GRID_SELECTION = "grid_selection"


@dataclass
class CellData:
    """Data for a single cell."""
    value: str
    formatted_value: Optional[str] = None
    data_type: Optional[str] = None  # 'text', 'number', 'date', etc.
    formatting: Optional[Dict[str, Any]] = None  # Font, color, alignment, etc.
    validation_rule: Optional[str] = None


@dataclass
class ClipboardData:
    """Container for clipboard data with metadata."""
    data_type: ClipboardDataType
    source_tab: Optional[str] = None
    source_file_type: Optional[str] = None
    timestamp: Optional[str] = None
    
    # Different data formats
    text_data: Optional[str] = None
    cells_data: Optional[List[List[CellData]]] = None
    rows_data: Optional[List[int]] = None  # Row indices
    columns_data: Optional[List[int]] = None  # Column indices
    
    # Metadata
    dimensions: Optional[Tuple[int, int]] = None  # (rows, cols)
    selection_info: Optional[Dict[str, Any]] = None


class ClipboardManager:
    """Enhanced clipboard manager for cross-tab operations."""
    
    def __init__(self):
        """Initialize the clipboard manager."""
        self._internal_clipboard: Optional[ClipboardData] = None
        self._system_clipboard_backup: Optional[str] = None
    
    def copy_text(self, text: str, source_tab: str = None) -> None:
        """Copy plain text to clipboard.
        
        Args:
            text: Text to copy
            source_tab: Name of source tab
        """
        import wx
        
        # Store in internal clipboard
        self._internal_clipboard = ClipboardData(
            data_type=ClipboardDataType.TEXT,
            source_tab=source_tab,
            text_data=text
        )
        
        # Also copy to system clipboard
        if wx.TheClipboard.Open():
            wx.TheClipboard.SetData(wx.TextDataObject(text))
            wx.TheClipboard.Close()
    
    def copy_cells(self, cells: List[List[CellData]], source_tab: str = None, 
                   source_file_type: str = None) -> None:
        """Copy cell data to clipboard.
        
        Args:
            cells: 2D array of cell data
            source_tab: Name of source tab
            source_file_type: Type of source file (csv, json, etc.)
        """
        import wx
        from datetime import datetime
        
        if not cells or not cells[0]:
            return
        
        rows = len(cells)
        cols = len(cells[0])
        
        # Store in internal clipboard
        self._internal_clipboard = ClipboardData(
            data_type=ClipboardDataType.CELLS,
            source_tab=source_tab,
            source_file_type=source_file_type,
            timestamp=datetime.now().isoformat(),
            cells_data=cells,
            dimensions=(rows, cols)
        )
        
        # Create text representation for system clipboard
        text_lines = []
        for row in cells:
            row_values = [cell.value for cell in row]
            text_lines.append('\t'.join(row_values))
        
        text_data = '\n'.join(text_lines)
        
        if wx.TheClipboard.Open():
            wx.TheClipboard.SetData(wx.TextDataObject(text_data))
            wx.TheClipboard.Close()
    
    def copy_rows(self, row_indices: List[int], grid_data: List[List[CellData]], 
                  source_tab: str = None) -> None:
        """Copy entire rows to clipboard.
        
        Args:
            row_indices: List of row indices to copy
            grid_data: Full grid data
            source_tab: Name of source tab
        """
        if not row_indices or not grid_data:
            return
        
        # Extract row data
        selected_rows = []
        for row_idx in sorted(row_indices):
            if 0 <= row_idx < len(grid_data):
                selected_rows.append(grid_data[row_idx])
        
        if selected_rows:
            self._internal_clipboard = ClipboardData(
                data_type=ClipboardDataType.ROWS,
                source_tab=source_tab,
                cells_data=selected_rows,
                rows_data=row_indices,
                dimensions=(len(selected_rows), len(selected_rows[0]) if selected_rows else 0)
            )
            
            # Copy as text to system clipboard
            self._copy_cells_as_text(selected_rows)
    
    def copy_columns(self, col_indices: List[int], grid_data: List[List[CellData]], 
                     source_tab: str = None) -> None:
        """Copy entire columns to clipboard.
        
        Args:
            col_indices: List of column indices to copy
            grid_data: Full grid data
            source_tab: Name of source tab
        """
        if not col_indices or not grid_data:
            return
        
        # Extract column data
        selected_columns = []
        for row in grid_data:
            column_row = []
            for col_idx in sorted(col_indices):
                if 0 <= col_idx < len(row):
                    column_row.append(row[col_idx])
            if column_row:
                selected_columns.append(column_row)
        
        if selected_columns:
            self._internal_clipboard = ClipboardData(
                data_type=ClipboardDataType.COLUMNS,
                source_tab=source_tab,
                cells_data=selected_columns,
                columns_data=col_indices,
                dimensions=(len(selected_columns), len(col_indices))
            )
            
            # Copy as text to system clipboard
            self._copy_cells_as_text(selected_columns)
    
    def get_clipboard_data(self) -> Optional[ClipboardData]:
        """Get the current internal clipboard data.
        
        Returns:
            ClipboardData or None if no data
        """
        return self._internal_clipboard
    
    def has_cells_data(self) -> bool:
        """Check if clipboard contains cell data.
        
        Returns:
            True if clipboard has cell data
        """
        return (self._internal_clipboard is not None and 
                self._internal_clipboard.data_type in [
                    ClipboardDataType.CELLS,
                    ClipboardDataType.ROWS, 
                    ClipboardDataType.COLUMNS
                ])
    
    def get_text_from_system(self) -> Optional[str]:
        """Get text from system clipboard.
        
        Returns:
            Text from system clipboard or None
        """
        import wx
        
        text = None
        if wx.TheClipboard.Open():
            if wx.TheClipboard.IsSupported(wx.DataFormat(wx.DF_TEXT)):
                data = wx.TextDataObject()
                if wx.TheClipboard.GetData(data):
                    text = data.GetText()
            wx.TheClipboard.Close()
        
        return text
    
    def paste_as_cells(self, target_row: int, target_col: int, 
                       preserve_formatting: bool = True) -> Optional[Tuple[List[List[CellData]], int, int]]:
        """Prepare cell data for pasting.
        
        Args:
            target_row: Target row for pasting
            target_col: Target column for pasting
            preserve_formatting: Whether to preserve formatting
            
        Returns:
            (cell_data, rows_needed, cols_needed) or None
        """
        if not self.has_cells_data():
            return None
        
        clipboard_data = self._internal_clipboard
        cells = clipboard_data.cells_data
        
        if not cells:
            return None
        
        # If not preserving formatting, strip formatting data
        if not preserve_formatting:
            stripped_cells = []
            for row in cells:
                stripped_row = []
                for cell in row:
                    stripped_cell = CellData(
                        value=cell.value,
                        data_type=cell.data_type
                    )
                    stripped_row.append(stripped_cell)
                stripped_cells.append(stripped_row)
            cells = stripped_cells
        
        rows_needed = len(cells)
        cols_needed = len(cells[0]) if cells else 0
        
        return (cells, rows_needed, cols_needed)
    
    def clear(self) -> None:
        """Clear the internal clipboard."""
        self._internal_clipboard = None
    
    def get_clipboard_info(self) -> Dict[str, Any]:
        """Get information about current clipboard contents.
        
        Returns:
            Dictionary with clipboard information
        """
        if not self._internal_clipboard:
            return {"has_data": False}
        
        clipboard_data = self._internal_clipboard
        info = {
            "has_data": True,
            "data_type": clipboard_data.data_type.value,
            "source_tab": clipboard_data.source_tab,
            "source_file_type": clipboard_data.source_file_type,
            "timestamp": clipboard_data.timestamp
        }
        
        if clipboard_data.dimensions:
            info["dimensions"] = clipboard_data.dimensions
        
        if clipboard_data.data_type == ClipboardDataType.TEXT:
            info["text_length"] = len(clipboard_data.text_data) if clipboard_data.text_data else 0
        elif clipboard_data.data_type in [ClipboardDataType.CELLS, ClipboardDataType.ROWS, ClipboardDataType.COLUMNS]:
            if clipboard_data.cells_data:
                info["cell_count"] = sum(len(row) for row in clipboard_data.cells_data)
        
        return info
    
    def _copy_cells_as_text(self, cells: List[List[CellData]]) -> None:
        """Copy cells as tab-separated text to system clipboard.
        
        Args:
            cells: Cell data to copy
        """
        import wx
        
        text_lines = []
        for row in cells:
            row_values = [cell.value for cell in row]
            text_lines.append('\t'.join(row_values))
        
        text_data = '\n'.join(text_lines)
        
        if wx.TheClipboard.Open():
            wx.TheClipboard.SetData(wx.TextDataObject(text_data))
            wx.TheClipboard.Close()
    
    def create_cell_data_from_grid(self, grid, start_row: int, start_col: int, 
                                   end_row: int, end_col: int) -> List[List[CellData]]:
        """Create CellData from a wxPython grid selection.
        
        Args:
            grid: wxPython grid object
            start_row: Starting row
            start_col: Starting column  
            end_row: Ending row
            end_col: Ending column
            
        Returns:
            2D array of CellData
        """
        import wx
        
        cells = []
        
        for row in range(start_row, end_row + 1):
            cell_row = []
            for col in range(start_col, end_col + 1):
                value = grid.GetCellValue(row, col)
                
                # Extract formatting if available
                formatting = {}
                try:
                    font = grid.GetCellFont(row, col)
                    if font.IsOk():
                        formatting['font_family'] = font.GetFaceName()
                        formatting['font_size'] = font.GetPointSize()
                        formatting['bold'] = font.GetWeight() == wx.FONTWEIGHT_BOLD
                        formatting['italic'] = font.GetStyle() == wx.FONTSTYLE_ITALIC
                        formatting['underlined'] = font.GetUnderlined()
                    
                    # Get cell colors
                    bg_color = grid.GetCellBackgroundColour(row, col)
                    text_color = grid.GetCellTextColour(row, col)
                    if bg_color.IsOk():
                        formatting['background_color'] = bg_color.GetAsString()
                    if text_color.IsOk():
                        formatting['text_color'] = text_color.GetAsString()
                    
                    # Get alignment
                    h_align, v_align = grid.GetCellAlignment(row, col)
                    formatting['horizontal_alignment'] = h_align
                    formatting['vertical_alignment'] = v_align
                    
                except:
                    # If any formatting extraction fails, continue with empty formatting
                    pass
                
                # Determine data type
                data_type = "text"
                if value.strip():
                    try:
                        float(value.replace(',', ''))
                        data_type = "number"
                    except ValueError:
                        pass
                
                cell = CellData(
                    value=value,
                    data_type=data_type,
                    formatting=formatting if formatting else None
                )
                cell_row.append(cell)
            
            cells.append(cell_row)
        
        return cells 