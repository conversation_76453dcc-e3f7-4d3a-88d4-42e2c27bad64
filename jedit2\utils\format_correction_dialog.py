"""Format correction dialog for JEdit2.

This module provides a dialog for displaying format validation results and correction options.
"""

import wx
from typing import Op<PERSON>
from .format_validator import ValidationResult
from .format_corrector import CorrectionResult


class FormatCorrectionDialog(wx.Dialog):
    """Dialog for displaying format validation results and correction options."""
    
    def __init__(self, parent: wx.Window, validation_result: ValidationResult, 
                 file_type: str, current_content: str) -> None:
        """Initialize the format correction dialog.
        
        Args:
            parent: Parent window
            validation_result: Validation result to display
            file_type: Type of file that was validated
            current_content: Current file content
        """
        title = f"{file_type.upper()} Format Validation & Correction"
        super().__init__(
            parent,
            title=title,
            size=(700, 500),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.validation_result = validation_result
        self.file_type = file_type
        self.current_content = current_content
        self.correction_result = None
        self.user_choice = None
        
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Status header
        status_panel = self._create_status_panel()
        main_sizer.Add(status_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Results area
        if self.validation_result.errors or self.validation_result.warnings:
            results_text = self._create_results_text()
            main_sizer.Add(results_text, 1, wx.EXPAND | wx.ALL, 5)
        
        # Correction options (if correctable)
        if self.validation_result.is_correctable:
            correction_panel = self._create_correction_panel()
            main_sizer.Add(correction_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
    
    def _create_status_panel(self) -> wx.Panel:
        """Create the status panel showing overall validation result."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Status icon
        if self.validation_result.is_valid:
            if self.validation_result.warnings:
                icon = wx.ArtProvider.GetBitmap(wx.ART_WARNING, wx.ART_MESSAGE_BOX, (32, 32))
                status_text = f"{self.file_type.upper()} format is valid with warnings"
            else:
                icon = wx.ArtProvider.GetBitmap(wx.ART_TICK_MARK, wx.ART_MESSAGE_BOX, (32, 32))
                status_text = f"{self.file_type.upper()} format is valid"
            status_color = wx.Colour(0, 128, 0)  # Green
        else:
            icon = wx.ArtProvider.GetBitmap(wx.ART_ERROR, wx.ART_MESSAGE_BOX, (32, 32))
            status_text = f"{self.file_type.upper()} format has errors"
            status_color = wx.Colour(200, 0, 0)  # Red
        
        # Icon
        icon_bitmap = wx.StaticBitmap(panel, bitmap=icon)
        sizer.Add(icon_bitmap, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        # Status text
        status_label = wx.StaticText(panel, label=status_text)
        font = status_label.GetFont()
        font.SetWeight(wx.FONTWEIGHT_BOLD)
        font.SetPointSize(font.GetPointSize() + 2)
        status_label.SetFont(font)
        status_label.SetForegroundColour(status_color)
        sizer.Add(status_label, 1, wx.ALIGN_CENTER_VERTICAL)
        
        # Summary info
        summary_parts = []
        if self.validation_result.errors:
            summary_parts.append(f"{len(self.validation_result.errors)} error(s)")
        if self.validation_result.warnings:
            summary_parts.append(f"{len(self.validation_result.warnings)} warning(s)")
        
        if summary_parts:
            summary_text = " • ".join(summary_parts)
            summary_label = wx.StaticText(panel, label=summary_text)
            summary_label.SetForegroundColour(wx.Colour(100, 100, 100))
            sizer.Add(summary_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.LEFT, 10)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_results_text(self) -> wx.TextCtrl:
        """Create the text control showing detailed results."""
        text_ctrl = wx.TextCtrl(
            self,
            style=wx.TE_MULTILINE | wx.TE_READONLY | wx.TE_RICH2
        )
        
        # Set font to monospace for better formatting
        font = wx.Font(10, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
        text_ctrl.SetFont(font)
        
        # Build content
        content = []
        
        if self.validation_result.errors:
            content.append("ERRORS:")
            content.append("=" * 50)
            for i, error in enumerate(self.validation_result.errors, 1):
                if self.validation_result.line_number:
                    content.append(f"{i}. Line {self.validation_result.line_number}: {error}")
                else:
                    content.append(f"{i}. {error}")
            content.append("")  # Empty line
        
        if self.validation_result.warnings:
            content.append("WARNINGS:")
            content.append("=" * 50)
            for i, warning in enumerate(self.validation_result.warnings, 1):
                content.append(f"{i}. {warning}")
            content.append("")  # Empty line
        
        if self.validation_result.is_correctable and self.validation_result.correction_suggestions:
            content.append("POSSIBLE CORRECTIONS:")
            content.append("=" * 50)
            for i, suggestion in enumerate(self.validation_result.correction_suggestions, 1):
                content.append(f"{i}. {suggestion}")
        
        text_ctrl.SetValue("\n".join(content))
        
        # Color the sections
        text = text_ctrl.GetValue()
        
        if self.validation_result.errors:
            error_start = text.find("ERRORS:")
            if error_start != -1:
                error_end = text.find("WARNINGS:")
                if error_end == -1:
                    error_end = text.find("POSSIBLE CORRECTIONS:")
                if error_end == -1:
                    error_end = len(text)
                text_ctrl.SetStyle(error_start, error_end, wx.TextAttr(wx.Colour(200, 0, 0)))
        
        if self.validation_result.warnings:
            warning_start = text.find("WARNINGS:")
            if warning_start != -1:
                warning_end = text.find("POSSIBLE CORRECTIONS:")
                if warning_end == -1:
                    warning_end = len(text)
                text_ctrl.SetStyle(warning_start, warning_end, wx.TextAttr(wx.Colour(200, 100, 0)))
        
        if self.validation_result.correction_suggestions:
            correction_start = text.find("POSSIBLE CORRECTIONS:")
            if correction_start != -1:
                text_ctrl.SetStyle(correction_start, len(text), wx.TextAttr(wx.Colour(0, 100, 200)))
        
        return text_ctrl
    
    def _create_correction_panel(self) -> wx.Panel:
        """Create the correction options panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Correction header
        header_label = wx.StaticText(panel, label="Automatic Correction Available")
        font = header_label.GetFont()
        font.SetWeight(wx.FONTWEIGHT_BOLD)
        header_label.SetFont(font)
        header_label.SetForegroundColour(wx.Colour(0, 100, 200))
        sizer.Add(header_label, 0, wx.ALL, 5)
        
        # Backup option
        self.backup_checkbox = wx.CheckBox(panel, label="Create backup before correction")
        self.backup_checkbox.SetValue(True)  # Default to creating backup
        sizer.Add(self.backup_checkbox, 0, wx.ALL, 5)
        
        # Correction preview (placeholder)
        preview_label = wx.StaticText(panel, label="Preview will be shown after clicking 'Preview Corrections'")
        preview_label.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(preview_label, 0, wx.ALL, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Add stretch space
        sizer.AddStretchSpacer()
        
        if self.validation_result.is_correctable:
            # Preview button
            preview_button = wx.Button(panel, wx.ID_ANY, "Preview Corrections")
            preview_button.Bind(wx.EVT_BUTTON, self._on_preview)
            sizer.Add(preview_button, 0, wx.ALL, 5)
            
            # Apply corrections button
            apply_button = wx.Button(panel, wx.ID_ANY, "Apply Corrections")
            apply_button.Bind(wx.EVT_BUTTON, self._on_apply)
            sizer.Add(apply_button, 0, wx.ALL, 5)
        
        # Close button
        close_button = wx.Button(panel, wx.ID_CLOSE, "Close")
        close_button.Bind(wx.EVT_BUTTON, self._on_close)
        sizer.Add(close_button, 0, wx.ALL, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _on_preview(self, event: wx.CommandEvent) -> None:
        """Handle preview corrections button click."""
        try:
            # Import here to avoid circular imports
            from .format_corrector import FormatCorrector

            corrector = FormatCorrector()
            self.correction_result = corrector.correct_format(self.current_content, self.file_type)

            if self.correction_result.success:
                # Show preview dialog
                preview_dialog = CorrectionPreviewDialog(self, self.correction_result)
                preview_dialog.ShowModal()
                preview_dialog.Destroy()
            else:
                wx.MessageBox(
                    f"Could not generate corrections:\n\n" + "\n".join(self.correction_result.errors),
                    "Correction Failed",
                    wx.OK | wx.ICON_ERROR
                )

        except Exception as e:
            wx.MessageBox(
                f"Error generating corrections: {str(e)}\n\nPlease check the file format and try again.",
                "Correction Error",
                wx.OK | wx.ICON_ERROR
            )
    
    def _on_apply(self, event: wx.CommandEvent) -> None:
        """Handle apply corrections button click."""
        try:
            if not self.correction_result:
                # Generate corrections first
                self._on_preview(event)
                if not self.correction_result or not self.correction_result.success:
                    return

            # Confirm with user
            message = f"Apply the following corrections?\n\n" + "\n".join(self.correction_result.changes_made)
            if hasattr(self, 'backup_checkbox') and self.backup_checkbox.GetValue():
                message += "\n\nA backup will be created before applying corrections."

            result = wx.MessageBox(message, "Confirm Corrections", wx.YES_NO | wx.ICON_QUESTION)

            if result == wx.YES:
                self.user_choice = 'apply'
                if hasattr(self, 'backup_checkbox') and self.backup_checkbox.GetValue():
                    self.user_choice = 'apply_with_backup'
                self.EndModal(wx.ID_OK)

        except Exception as e:
            wx.MessageBox(
                f"Error applying corrections: {str(e)}\n\nPlease try again or apply corrections manually.",
                "Correction Error",
                wx.OK | wx.ICON_ERROR
            )
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button click."""
        self.user_choice = 'close'
        self.EndModal(wx.ID_CLOSE)
    
    def get_correction_result(self) -> Optional[CorrectionResult]:
        """Get the correction result."""
        return self.correction_result
    
    def get_user_choice(self) -> Optional[str]:
        """Get the user's choice."""
        return self.user_choice
    
    def should_create_backup(self) -> bool:
        """Check if user wants to create a backup."""
        return hasattr(self, 'backup_checkbox') and self.backup_checkbox.GetValue()


class CorrectionPreviewDialog(wx.Dialog):
    """Dialog for previewing format corrections."""
    
    def __init__(self, parent: wx.Window, correction_result: CorrectionResult) -> None:
        """Initialize the correction preview dialog."""
        super().__init__(
            parent,
            title="Correction Preview",
            size=(600, 400),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )
        
        self.correction_result = correction_result
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Changes summary
        changes_label = wx.StaticText(self, label="Changes to be made:")
        font = changes_label.GetFont()
        font.SetWeight(wx.FONTWEIGHT_BOLD)
        changes_label.SetFont(font)
        main_sizer.Add(changes_label, 0, wx.ALL, 5)
        
        # Changes list
        changes_text = wx.TextCtrl(
            self,
            value="\n".join(f"• {change}" for change in self.correction_result.changes_made),
            style=wx.TE_MULTILINE | wx.TE_READONLY
        )
        main_sizer.Add(changes_text, 0, wx.EXPAND | wx.ALL, 5)
        
        # Corrected content preview
        preview_label = wx.StaticText(self, label="Corrected content preview:")
        font = preview_label.GetFont()
        font.SetWeight(wx.FONTWEIGHT_BOLD)
        preview_label.SetFont(font)
        main_sizer.Add(preview_label, 0, wx.ALL, 5)
        
        # Content preview
        content_text = wx.TextCtrl(
            self,
            value=self.correction_result.corrected_content,
            style=wx.TE_MULTILINE | wx.TE_READONLY
        )
        font = wx.Font(10, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
        content_text.SetFont(font)
        main_sizer.Add(content_text, 1, wx.EXPAND | wx.ALL, 5)
        
        # Close button
        close_button = wx.Button(self, wx.ID_CLOSE, "Close")
        close_button.Bind(wx.EVT_BUTTON, lambda e: self.EndModal(wx.ID_CLOSE))
        main_sizer.Add(close_button, 0, wx.ALIGN_CENTER | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
