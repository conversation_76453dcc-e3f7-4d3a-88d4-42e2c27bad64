<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-lightblue-10" Color="#0077a0" Opacity="0.1" />
      <SolidColorBrush x:Key="light-lightblue" Color="#0077a0" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-lightblue-10}" Geometry="F1M13.5,3.97V12c0,.828-1.791,1.5-4,1.5s-4-.672-4-1.5l-.494.115L5,4.879l.5-.208v-.7c0-.828,1.791-1.5,4-1.5S13.5,3.142,13.5,3.97Z" />
              <GeometryDrawing Brush="{DynamicResource light-lightblue}" Geometry="F1M5,3.97v.909l1,1V5.261a7.863,7.863,0,0,0,3.5.709A7.863,7.863,0,0,0,13,5.261V12c0,.29-1.227,1-3.5,1S6,12.29,6,12v-.879l-.994.994C5.134,13.353,7.33,14,9.5,14c2.236,0,4.5-.687,4.5-2V3.97C14,1.343,5,1.343,5,3.97Zm4.5,1c-2.273,0-3.5-.71-3.5-1s1.227-1,3.5-1,3.5.71,3.5,1S11.773,4.97,9.5,4.97Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3.647,10.646,5.293,9H0V8H5.293L3.647,6.354l.707-.708,2.5,2.5v.708l-2.5,2.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
