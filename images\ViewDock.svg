<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.light-blue-10{fill: #005dba; opacity: 0.1;}.light-blue{fill: #005dba; opacity: 1;}</style>
  </defs>
  <title>IconLightViewDock</title>
  <g id="canvas" class="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <path class="light-defaultgrey-10" d="M14.5,2.5v11H1.5V2.5Z" />
    <path class="light-defaultgrey" d="M14.5,14H1.5L1,13.5V2.5L1.5,2h13l.5.5v11ZM2,13H14V3H2Z" />
    <path class="light-blue-10" d="M6.5,11.5h-3v-7h3Z" />
    <path class="light-blue" d="M3,12V4H7v8Zm1-1H6V5H4Z" />
  </g>
</svg>
