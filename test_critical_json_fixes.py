#!/usr/bin/env python3
"""
Test the critical JSON fixes that were failing.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_critical_json_fixes():
    """Test the critical JSON fixes."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # The 7 JSON cases that were failing
    critical_json_cases = [
        {
            'name': 'Unescaped Quotes',
            'content': '{"message": "He said "hello""}',
        },
        {
            'name': 'Array Without Brackets',
            'content': '"item1", "item2", "item3"',
        },
        {
            'name': 'Mixed Array/Object',
            'content': '{"key": "value", "item2"}',
        },
        {
            'name': 'Unquoted String Values',
            'content': '{"key": value}',
        },
        {
            'name': 'Multiple Errors (Complex)',
            'content': '{key1: "value1", "key2": value2,}',
        },
        {
            'name': 'Comments in JSON',
            'content': '{"key": "value", // comment\n}',
        },
        {
            'name': 'Multiline Strings',
            'content': '{"key": "line1\nline2"}',
        },
        {
            'name': 'Simple Unquoted Value',
            'content': '{"name": John}',
        },
        {
            'name': 'Array of Unquoted Items',
            'content': 'apple, banana, cherry',
        },
        {
            'name': 'Complex Multiple Issues',
            'content': "{'name': John, 'age': value, 'active': True,}",
        }
    ]
    
    print("🔧 TESTING CRITICAL JSON FIXES")
    print("=" * 60)
    print("Testing the 7 JSON cases that were failing...")
    print()
    
    results = []
    
    for i, case in enumerate(critical_json_cases, 1):
        print(f"{i:2d}. {case['name']}")
        print("-" * 50)
        print(f"    Content: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], 'json')
        print(f"    Correctable: {validation_result.is_correctable}")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], 'json')
            
            if correction_result.success:
                print(f"    ✅ SUCCESS!")
                print(f"    Changes: {correction_result.changes_made}")
                
                # Verify the result is valid JSON
                try:
                    import json
                    parsed = json.loads(correction_result.corrected_content)
                    print(f"    ✅ Valid JSON!")
                    
                    # Show result preview
                    result_preview = correction_result.corrected_content[:80]
                    if len(correction_result.corrected_content) > 80:
                        result_preview += "..."
                    print(f"    Result: {repr(result_preview)}")
                    success = True
                    
                except json.JSONDecodeError as e:
                    print(f"    ❌ Invalid JSON: {e}")
                    
            else:
                print(f"    ❌ CORRECTION FAILED")
                print(f"    Errors: {correction_result.errors[:1]}...")  # Show first error
        else:
            print(f"    ❌ NOT DETECTED AS CORRECTABLE")
        
        results.append({
            'name': case['name'],
            'success': success
        })
        print()
    
    # Summary
    print("=" * 60)
    print("📊 CRITICAL JSON FIXES SUMMARY")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for r in results if r['success'])
    
    print(f"Critical JSON Tests: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL CRITICAL JSON FIXES WORKING!")
        print("JSON correction is now highly robust!")
    elif passed >= total * 0.8:
        print("\n🚀 MOST CRITICAL JSON FIXES WORKING!")
        print("Major improvement achieved!")
    elif passed >= total * 0.6:
        print("\n👍 GOOD PROGRESS on critical JSON fixes!")
    else:
        print("\n🔧 MORE WORK NEEDED on critical JSON cases.")
        
        print("\nStill failing:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}")
    
    print(f"\n📈 ESTIMATED COVERAGE IMPACT:")
    # Each JSON fix represents ~1.8% of total coverage (7 JSON gaps out of 56 total cases)
    improvement = passed * 1.25  # Rough estimate
    print(f"  JSON fixes working: {passed}/7")
    print(f"  Estimated coverage boost: +{improvement:.1f}%")
    print(f"  Projected total coverage: ~{60.7 + improvement:.1f}%")
    
    return passed, total

if __name__ == "__main__":
    print("🧪 CRITICAL JSON FIXES TEST")
    print("=" * 40)
    
    passed, total = test_critical_json_fixes()
    
    print(f"\n🎯 RESULT: {passed}/{total} critical JSON fixes working")
    
    if passed >= 8:  # 80% of 10 tests
        print("🎉 Ready for full coverage analysis!")
    elif passed >= 6:  # 60% of 10 tests
        print("🚀 Major progress! Continue with remaining fixes.")
    else:
        print("🔧 Debug remaining critical issues.")
