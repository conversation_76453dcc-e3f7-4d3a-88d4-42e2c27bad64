"""Memory management dialog for JEdit2.

This module provides a dialog for monitoring and controlling memory usage.
"""

import wx
import wx.grid
from typing import Dict, Optional, List, Any
from .memory_manager import MemoryManager, MemoryPoolType, MemoryStats


class MemoryDialog(wx.Dialog):
    """Dialog for managing memory usage."""
    
    def __init__(self, parent: wx.Window) -> None:
        """Initialize the dialog.
        
        Args:
            parent: Parent window
        """
        super().__init__(
            parent,
            title="Memory Manager",
            size=(800, 600)
        )
        
        self.memory_manager = MemoryManager()
        
        self._init_ui()
        self._create_layout()
        self._bind_events()
        
        # Start monitoring
        self.memory_manager.start_monitoring()
        
        # Start update timer
        self.update_timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self._on_update, self.update_timer)
        self.update_timer.Start(1000)  # Update every second
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main panel
        self.panel = wx.Panel(self)
        
        # Create memory usage gauge
        self.usage_gauge = wx.Gauge(
            self.panel,
            range=100,
            size=(300, 25)
        )
        
        # Create memory stats grid
        self.stats_grid = wx.grid.Grid(self.panel)
        self.stats_grid.CreateGrid(0, 2)
        self.stats_grid.SetColLabelValue(0, "Metric")
        self.stats_grid.SetColLabelValue(1, "Value")
        self.stats_grid.SetColSize(0, 200)
        self.stats_grid.SetColSize(1, 300)
        
        # Create pool stats grid
        self.pool_grid = wx.grid.Grid(self.panel)
        self.pool_grid.CreateGrid(0, 5)
        self.pool_grid.SetColLabelValue(0, "Pool")
        self.pool_grid.SetColLabelValue(1, "Total Size")
        self.pool_grid.SetColLabelValue(2, "Used Size")
        self.pool_grid.SetColLabelValue(3, "Free Size")
        self.pool_grid.SetColLabelValue(4, "Fragmentation")
        self.pool_grid.SetColSize(0, 100)
        self.pool_grid.SetColSize(1, 100)
        self.pool_grid.SetColSize(2, 100)
        self.pool_grid.SetColSize(3, 100)
        self.pool_grid.SetColSize(4, 100)
        
        # Create buttons
        self.optimize_btn = wx.Button(self.panel, label="Optimize")
        self.defrag_btn = wx.Button(self.panel, label="Defragment")
        self.gc_btn = wx.Button(self.panel, label="Garbage Collect")
        self.close_btn = wx.Button(self.panel, label="Close")
    

    
    def _create_layout(self) -> None:
        """Create the layout."""
        # Create sizers
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        gauge_sizer = wx.BoxSizer(wx.HORIZONTAL)
        grid_sizer = wx.BoxSizer(wx.HORIZONTAL)
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Add widgets to sizers
        gauge_sizer.Add(
            wx.StaticText(self.panel, label="Memory Usage:"),
            0,
            wx.ALIGN_CENTER_VERTICAL | wx.RIGHT,
            5
        )
        gauge_sizer.Add(self.usage_gauge, 1, wx.EXPAND)
        
        grid_sizer.Add(self.stats_grid, 1, wx.EXPAND | wx.ALL, 5)
        grid_sizer.Add(self.pool_grid, 1, wx.EXPAND | wx.ALL, 5)
        
        button_sizer.Add(self.optimize_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.defrag_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.gc_btn, 0, wx.ALL, 5)
        button_sizer.AddStretchSpacer()
        button_sizer.Add(self.close_btn, 0, wx.ALL, 5)
        
        main_sizer.Add(gauge_sizer, 0, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(grid_sizer, 1, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(button_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        self.panel.SetSizer(main_sizer)
    
    def _bind_events(self) -> None:
        """Bind events."""
        # Button events
        self.optimize_btn.Bind(wx.EVT_BUTTON, self._on_optimize)
        self.defrag_btn.Bind(wx.EVT_BUTTON, self._on_defragment)
        self.gc_btn.Bind(wx.EVT_BUTTON, self._on_gc)
        self.close_btn.Bind(wx.EVT_BUTTON, self._on_close)
    
    def _on_update(self, event: wx.TimerEvent) -> None:
        """Handle update timer event."""
        self._update_stats()
    
    def _update_stats(self) -> None:
        """Update memory statistics display."""
        stats = self.memory_manager.get_stats()
        
        # Update usage gauge
        usage_percent = (stats.used_memory / stats.total_memory) * 100
        self.usage_gauge.SetValue(int(usage_percent))
        
        # Update stats grid
        self.stats_grid.ClearGrid()
        if self.stats_grid.GetNumberRows() > 0:
            self.stats_grid.DeleteRows(0, self.stats_grid.GetNumberRows())
        
        self.stats_grid.AppendRows(8)
        self.stats_grid.SetCellValue(0, 0, "Total Memory")
        self.stats_grid.SetCellValue(0, 1, f"{stats.total_memory / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(1, 0, "Used Memory")
        self.stats_grid.SetCellValue(1, 1, f"{stats.used_memory / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(2, 0, "Free Memory")
        self.stats_grid.SetCellValue(2, 1, f"{stats.free_memory / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(3, 0, "Swap Used")
        self.stats_grid.SetCellValue(3, 1, f"{stats.swap_used / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(4, 0, "Swap Free")
        self.stats_grid.SetCellValue(4, 1, f"{stats.swap_free / 1024 / 1024:.1f} MB")
        self.stats_grid.SetCellValue(5, 0, "Fragmentation")
        self.stats_grid.SetCellValue(5, 1, f"{stats.fragmentation * 100:.1f}%")
        self.stats_grid.SetCellValue(6, 0, "GC Count")
        self.stats_grid.SetCellValue(6, 1, str(stats.gc_count))
        self.stats_grid.SetCellValue(7, 0, "GC Time")
        self.stats_grid.SetCellValue(7, 1, f"{stats.gc_time:.2f} s")
        
        # Update pool grid
        self.pool_grid.ClearGrid()
        if self.pool_grid.GetNumberRows() > 0:
            self.pool_grid.DeleteRows(0, self.pool_grid.GetNumberRows())
        
        self.pool_grid.AppendRows(len(stats.pool_stats))
        for i, (pool_type, pool) in enumerate(stats.pool_stats.items()):
            self.pool_grid.SetCellValue(i, 0, pool_type.value)
            self.pool_grid.SetCellValue(i, 1, f"{pool.total_size / 1024 / 1024:.1f} MB")
            self.pool_grid.SetCellValue(i, 2, f"{pool.used_size / 1024 / 1024:.1f} MB")
            self.pool_grid.SetCellValue(i, 3, f"{(pool.total_size - pool.used_size) / 1024 / 1024:.1f} MB")
            self.pool_grid.SetCellValue(i, 4, f"{pool.fragmentation * 100:.1f}%")
    
    def _on_save(self, event: wx.CommandEvent) -> None:
        """Handle save event."""
        with wx.FileDialog(
            self,
            "Save memory report",
            wildcard="Text files (*.txt)|*.txt|All files (*.*)|*.*",
            style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT
        ) as dialog:
            if dialog.ShowModal() == wx.ID_OK:
                try:
                    with open(dialog.GetPath(), "w") as file:
                        stats = self.memory_manager.get_stats()
                        file.write("Memory Report\n")
                        file.write("=============\n\n")
                        file.write(f"Total Memory: {stats.total_memory / 1024 / 1024:.1f} MB\n")
                        file.write(f"Used Memory: {stats.used_memory / 1024 / 1024:.1f} MB\n")
                        file.write(f"Free Memory: {stats.free_memory / 1024 / 1024:.1f} MB\n")
                        file.write(f"Swap Used: {stats.swap_used / 1024 / 1024:.1f} MB\n")
                        file.write(f"Swap Free: {stats.swap_free / 1024 / 1024:.1f} MB\n")
                        file.write(f"Fragmentation: {stats.fragmentation * 100:.1f}%\n")
                        file.write(f"GC Count: {stats.gc_count}\n")
                        file.write(f"GC Time: {stats.gc_time:.2f} s\n\n")
                        file.write("Pool Statistics\n")
                        file.write("===============\n\n")
                        for pool_type, pool in stats.pool_stats.items():
                            file.write(f"Pool: {pool_type.value}\n")
                            file.write(f"Total Size: {pool.total_size / 1024 / 1024:.1f} MB\n")
                            file.write(f"Used Size: {pool.used_size / 1024 / 1024:.1f} MB\n")
                            file.write(f"Free Size: {(pool.total_size - pool.used_size) / 1024 / 1024:.1f} MB\n")
                            file.write(f"Fragmentation: {pool.fragmentation * 100:.1f}%\n\n")
                except Exception as e:
                    wx.MessageBox(
                        str(e),
                        "Error",
                        wx.OK | wx.ICON_ERROR
                    )
    
    def _on_exit(self, event: wx.CommandEvent) -> None:
        """Handle exit event."""
        self.EndModal(wx.ID_CANCEL)
    
    def _on_refresh(self, event: wx.CommandEvent) -> None:
        """Handle refresh event."""
        self._update_stats()
    
    def _on_optimize(self, event: wx.CommandEvent) -> None:
        """Handle optimize event."""
        self.memory_manager.optimize_memory()
        self._update_stats()
        # Memory optimized - no status bar in dialog
    
    def _on_defragment(self, event: wx.CommandEvent) -> None:
        """Handle defragment event."""
        self.memory_manager.defragment_memory()
        self._update_stats()
        # Memory defragmented - no status bar in dialog
    
    def _on_gc(self, event: wx.CommandEvent) -> None:
        """Handle garbage collection event."""
        self.memory_manager._run_gc()
        self._update_stats()
        # Garbage collection completed - no status bar in dialog
    
    def _on_help(self, event: wx.CommandEvent) -> None:
        """Handle help event."""
        wx.MessageBox(
            "Memory Manager Help\n\n"
            "1. Memory Usage: Shows current memory usage\n"
            "2. Memory Statistics: Shows detailed memory stats\n"
            "3. Pool Statistics: Shows memory pool stats\n"
            "4. Optimize: Cleans up memory\n"
            "5. Defragment: Reduces memory fragmentation\n"
            "6. Garbage Collect: Forces garbage collection",
            "Help",
            wx.OK | wx.ICON_INFORMATION
        )
    
    def _on_about(self, event: wx.CommandEvent) -> None:
        """Handle about event."""
        wx.MessageBox(
            "Memory Manager\n\n"
            "A tool for monitoring and managing memory usage in JEdit2.",
            "About",
            wx.OK | wx.ICON_INFORMATION
        )
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close event."""
        self.EndModal(wx.ID_OK)
    
    def __del__(self) -> None:
        """Clean up when destroyed."""
        if hasattr(self, 'update_timer'):
            self.update_timer.Stop()
        if hasattr(self, 'memory_manager'):
            self.memory_manager.stop_monitoring() 