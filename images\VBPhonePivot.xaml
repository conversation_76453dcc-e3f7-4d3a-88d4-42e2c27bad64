<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M15.5,1.5V14H11v1H7.75L7,14.25V7h.5V1.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M16,1.5v13l-.5.5H9.816A2.966,2.966,0,0,0,10,14h5V2H8V7H7V1.5L7.5,1h8ZM12,4H11V3h1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13,13H10V12h3Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,12a1.987,1.987,0,0,0-1,.277V10H5v6H6v-.277A2,2,0,1,0,7,12Zm0,3a1,1,0,1,1,1-1A1,1,0,0,1,7,15ZM3,12v.277a2,2,0,1,0,0,3.446V16H4V12ZM2,15a1,1,0,1,1,1-1A1,1,0,0,1,2,15ZM9,9H0V8H9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
