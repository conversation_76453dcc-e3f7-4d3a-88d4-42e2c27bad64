"""Custom Filter Dialog for creating complex filter expressions."""

import wx
from typing import List, Optional, Dict, Any, Union
from .filter_criteria import (
    FilterCriterion, TextFilterCriterion, NumberFilterCriterion, 
    DateFilterCriterion, CompoundCriterion
)
from .filter_utils import detect_column_type
import logging

logger = logging.getLogger(__name__)


class CustomFilterDialog(wx.Dialog):
    """Dialog for creating custom compound filter expressions."""
    
    def __init__(self, parent: wx.Window, column_name: str, column_data: List[str]):
        """
        Initialize the custom filter dialog.
        
        Args:
            parent: Parent window
            column_name: Name of the column being filtered
            column_data: All values in the column for context
        """
        super().__init__(parent, title=f"Custom Filter - {column_name}", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.column_name = column_name
        self.column_data = column_data
        self.data_type = detect_column_type(column_data)
        self.filter_result: Optional[FilterCriterion] = None
        
        self._init_ui()
        self._bind_events()
        
        # Set initial size and center
        self.SetSize((450, 300))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Header
        header_panel = self._create_header_panel()
        main_sizer.Add(header_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        # Filter type selection
        type_panel = self._create_type_panel()
        main_sizer.Add(type_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        # Filter configuration
        self.notebook = wx.Notebook(self)
        
        # Text filter page
        self.text_panel = self._create_text_panel()
        self.notebook.AddPage(self.text_panel, "Text Filter")
        
        # Number filter page (if applicable)
        if self.data_type in ['number', 'mixed']:
            self.number_panel = self._create_number_panel()
            self.notebook.AddPage(self.number_panel, "Number Filter")
        
        # Date filter page (if applicable)
        if self.data_type in ['date', 'mixed']:
            self.date_panel = self._create_date_panel()
            self.notebook.AddPage(self.date_panel, "Date Filter")
        
        main_sizer.Add(self.notebook, 1, wx.EXPAND | wx.ALL, 10)
        
        # OK/Cancel buttons
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
    
    def _create_header_panel(self) -> wx.Panel:
        """Create the header panel with instructions."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        title = wx.StaticText(panel, label=f"Custom Filter for '{self.column_name}'")
        title.SetFont(title.GetFont().Bold())
        sizer.Add(title, 0, wx.BOTTOM, 5)
        
        info = wx.StaticText(panel, 
                           label="Create a custom filter condition for this column.")
        sizer.Add(info, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_type_panel(self) -> wx.Panel:
        """Create panel for filter type selection."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        label = wx.StaticText(panel, label="Filter Type:")
        sizer.Add(label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        choices = ["Text"]
        if self.data_type in ['number', 'mixed']:
            choices.append("Number")
        if self.data_type in ['date', 'mixed']:
            choices.append("Date")
        
        self.type_choice = wx.Choice(panel, choices=choices)
        self.type_choice.SetSelection(0)
        sizer.Add(self.type_choice, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_text_panel(self) -> wx.Panel:
        """Create text filter controls."""
        panel = wx.Panel(self.notebook)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operation choice
        op_sizer = wx.BoxSizer(wx.HORIZONTAL)
        op_label = wx.StaticText(panel, label="Operation:")
        op_sizer.Add(op_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        op_choices = [
            "Contains", "Does not contain", "Equals", "Does not equal",
            "Begins with", "Does not begin with", "Ends with", "Does not end with",
            "Is empty", "Is not empty", "Matches pattern"
        ]
        self.text_op_choice = wx.Choice(panel, choices=op_choices)
        self.text_op_choice.SetSelection(0)
        op_sizer.Add(self.text_op_choice, 1)
        
        sizer.Add(op_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Value input
        value_sizer = wx.BoxSizer(wx.HORIZONTAL)
        value_label = wx.StaticText(panel, label="Value:")
        value_sizer.Add(value_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        self.text_value = wx.TextCtrl(panel)
        value_sizer.Add(self.text_value, 1)
        
        sizer.Add(value_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Case sensitive checkbox
        self.case_sensitive = wx.CheckBox(panel, label="Case sensitive")
        sizer.Add(self.case_sensitive, 0, wx.BOTTOM, 10)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_number_panel(self) -> wx.Panel:
        """Create number filter controls."""
        panel = wx.Panel(self.notebook)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operation choice
        op_sizer = wx.BoxSizer(wx.HORIZONTAL)
        op_label = wx.StaticText(panel, label="Operation:")
        op_sizer.Add(op_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        op_choices = [
            "Equals", "Does not equal", "Greater than", "Greater or equal",
            "Less than", "Less or equal", "Between", "Not between"
        ]
        self.number_op_choice = wx.Choice(panel, choices=op_choices)
        self.number_op_choice.SetSelection(0)
        op_sizer.Add(self.number_op_choice, 1)
        
        sizer.Add(op_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Value inputs
        value1_sizer = wx.BoxSizer(wx.HORIZONTAL)
        value1_label = wx.StaticText(panel, label="Value:")
        value1_sizer.Add(value1_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        self.number_value1 = wx.TextCtrl(panel)
        value1_sizer.Add(self.number_value1, 1)
        
        sizer.Add(value1_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Second value for between operations
        value2_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.number_value2_label = wx.StaticText(panel, label="To:")
        value2_sizer.Add(self.number_value2_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        self.number_value2 = wx.TextCtrl(panel)
        value2_sizer.Add(self.number_value2, 1)
        
        sizer.Add(value2_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Initially hide second value
        self.number_value2_label.Show(False)
        self.number_value2.Show(False)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_date_panel(self) -> wx.Panel:
        """Create date filter controls."""
        panel = wx.Panel(self.notebook)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operation choice
        op_sizer = wx.BoxSizer(wx.HORIZONTAL)
        op_label = wx.StaticText(panel, label="Operation:")
        op_sizer.Add(op_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        op_choices = [
            "Equals", "Does not equal", "Before", "After", "Between", "Not between",
            "Today", "Yesterday", "Tomorrow", "This week", "Last week", "Next week",
            "This month", "Last month", "Next month", "This year", "Last year", "Next year"
        ]
        self.date_op_choice = wx.Choice(panel, choices=op_choices)
        self.date_op_choice.SetSelection(0)
        op_sizer.Add(self.date_op_choice, 1)
        
        sizer.Add(op_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Date inputs
        date1_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.date1_label = wx.StaticText(panel, label="Date:")
        date1_sizer.Add(self.date1_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        self.date1_picker = wx.adv.DatePickerCtrl(panel)
        date1_sizer.Add(self.date1_picker, 1)
        
        sizer.Add(date1_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Second date for between operations
        date2_sizer = wx.BoxSizer(wx.HORIZONTAL)
        self.date2_label = wx.StaticText(panel, label="To:")
        date2_sizer.Add(self.date2_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 10)
        
        self.date2_picker = wx.adv.DatePickerCtrl(panel)
        date2_sizer.Add(self.date2_picker, 1)
        
        sizer.Add(date2_sizer, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Initially show date inputs for simple operations
        self.date2_label.Show(False)
        self.date2_picker.Show(False)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the OK/Cancel button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 10)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.type_choice.Bind(wx.EVT_CHOICE, self._on_type_change)
        
        # Text panel events
        self.text_op_choice.Bind(wx.EVT_CHOICE, self._on_text_op_change)
        
        # Number panel events  
        if hasattr(self, 'number_op_choice'):
            self.number_op_choice.Bind(wx.EVT_CHOICE, self._on_number_op_change)
        
        # Date panel events
        if hasattr(self, 'date_op_choice'):
            self.date_op_choice.Bind(wx.EVT_CHOICE, self._on_date_op_change)
        
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _on_type_change(self, event: wx.CommandEvent) -> None:
        """Handle filter type change."""
        selection = self.type_choice.GetSelection()
        self.notebook.SetSelection(selection)
    
    def _on_text_op_change(self, event: wx.CommandEvent) -> None:
        """Handle text operation change."""
        selection = self.text_op_choice.GetSelection()
        # Disable value input for empty/not empty operations
        enable_value = selection not in [8, 9]  # "Is empty", "Is not empty"
        self.text_value.Enable(enable_value)
    
    def _on_number_op_change(self, event: wx.CommandEvent) -> None:
        """Handle number operation change."""
        selection = self.number_op_choice.GetSelection()
        show_value2 = selection in [6, 7]  # "Between", "Not between"
        self.number_value2_label.Show(show_value2)
        self.number_value2.Show(show_value2)
        self.number_panel.Layout()
    
    def _on_date_op_change(self, event: wx.CommandEvent) -> None:
        """Handle date operation change."""
        selection = self.date_op_choice.GetSelection()
        show_date1 = selection in [0, 1, 2, 3, 4, 5]  # Operations that need a date
        show_date2 = selection in [4, 5]  # "Between", "Not between"
        
        self.date1_label.Show(show_date1)
        self.date1_picker.Show(show_date1)
        self.date2_label.Show(show_date2)
        self.date2_picker.Show(show_date2)
        self.date_panel.Layout()
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        try:
            filter_type = self.type_choice.GetStringSelection()
            
            if filter_type == "Text":
                self.filter_result = self._create_text_criterion()
            elif filter_type == "Number":
                self.filter_result = self._create_number_criterion()
            elif filter_type == "Date":
                self.filter_result = self._create_date_criterion()
            
            if self.filter_result:
                self.EndModal(wx.ID_OK)
        except Exception as e:
            wx.MessageBox(f"Error creating filter: {str(e)}", "Filter Error", 
                         wx.OK | wx.ICON_ERROR)
    
    def _create_text_criterion(self) -> TextFilterCriterion:
        """Create text criterion from current settings."""
        op_map = {
            0: TextFilterCriterion.CONTAINS,
            1: TextFilterCriterion.NOT_CONTAINS,
            2: TextFilterCriterion.EQUALS,
            3: TextFilterCriterion.NOT_EQUALS,
            4: TextFilterCriterion.BEGINS_WITH,
            5: TextFilterCriterion.NOT_BEGINS_WITH,
            6: TextFilterCriterion.ENDS_WITH,
            7: TextFilterCriterion.NOT_ENDS_WITH,
            8: TextFilterCriterion.IS_EMPTY,
            9: TextFilterCriterion.IS_NOT_EMPTY,
            10: TextFilterCriterion.MATCHES_PATTERN
        }
        
        op_selection = self.text_op_choice.GetSelection()
        operator = op_map.get(op_selection, TextFilterCriterion.CONTAINS)
        value = self.text_value.GetValue()
        case_sensitive = self.case_sensitive.GetValue()
        
        # Validate value for operations that require it
        if operator not in [TextFilterCriterion.IS_EMPTY, TextFilterCriterion.IS_NOT_EMPTY]:
            if not value.strip():
                raise ValueError("Value is required for this operation")
        
        return TextFilterCriterion(operator, value, case_sensitive)
    
    def _create_number_criterion(self) -> NumberFilterCriterion:
        """Create number criterion from current settings."""
        op_map = {
            0: NumberFilterCriterion.EQUALS,
            1: NumberFilterCriterion.NOT_EQUALS,
            2: NumberFilterCriterion.GREATER_THAN,
            3: NumberFilterCriterion.GREATER_EQUAL,
            4: NumberFilterCriterion.LESS_THAN,
            5: NumberFilterCriterion.LESS_EQUAL,
            6: NumberFilterCriterion.BETWEEN,
            7: NumberFilterCriterion.NOT_BETWEEN
        }
        
        op_selection = self.number_op_choice.GetSelection()
        operator = op_map.get(op_selection, NumberFilterCriterion.EQUALS)
        
        try:
            value1 = float(self.number_value1.GetValue())
        except ValueError:
            raise ValueError("First value must be a valid number")
        
        value2 = None
        if operator in [NumberFilterCriterion.BETWEEN, NumberFilterCriterion.NOT_BETWEEN]:
            try:
                value2 = float(self.number_value2.GetValue())
            except ValueError:
                raise ValueError("Second value must be a valid number for range operations")
        
        return NumberFilterCriterion(operator, value1, value2)
    
    def _create_date_criterion(self) -> DateFilterCriterion:
        """Create date criterion from current settings."""
        op_map = {
            0: DateFilterCriterion.EQUALS,
            1: DateFilterCriterion.NOT_EQUALS,
            2: DateFilterCriterion.BEFORE,
            3: DateFilterCriterion.AFTER,
            4: DateFilterCriterion.BETWEEN,
            5: DateFilterCriterion.NOT_BETWEEN,
            6: DateFilterCriterion.TODAY,
            7: DateFilterCriterion.YESTERDAY,
            8: DateFilterCriterion.TOMORROW,
            9: DateFilterCriterion.THIS_WEEK,
            10: DateFilterCriterion.LAST_WEEK,
            11: DateFilterCriterion.NEXT_WEEK,
            12: DateFilterCriterion.THIS_MONTH,
            13: DateFilterCriterion.LAST_MONTH,
            14: DateFilterCriterion.NEXT_MONTH,
            15: DateFilterCriterion.THIS_YEAR,
            16: DateFilterCriterion.LAST_YEAR,
            17: DateFilterCriterion.NEXT_YEAR
        }
        
        op_selection = self.date_op_choice.GetSelection()
        operator = op_map.get(op_selection, DateFilterCriterion.EQUALS)
        
        date1 = None
        date2 = None
        
        # Get dates for operations that need them
        if operator in [DateFilterCriterion.EQUALS, DateFilterCriterion.NOT_EQUALS, 
                       DateFilterCriterion.BEFORE, DateFilterCriterion.AFTER,
                       DateFilterCriterion.BETWEEN, DateFilterCriterion.NOT_BETWEEN]:
            date1 = self.date1_picker.GetValue()
            if date1.IsValid():
                date1 = date1.Format('%Y-%m-%d')
                from datetime import datetime
                date1 = datetime.strptime(date1, '%Y-%m-%d')
            else:
                raise ValueError("Please select a valid date")
            
            if operator in [DateFilterCriterion.BETWEEN, DateFilterCriterion.NOT_BETWEEN]:
                date2 = self.date2_picker.GetValue()
                if date2.IsValid():
                    date2 = date2.Format('%Y-%m-%d')
                    date2 = datetime.strptime(date2, '%Y-%m-%d')
                else:
                    raise ValueError("Please select a valid end date")
        
        return DateFilterCriterion(operator, date1, date2)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filter_result = None
        self.EndModal(wx.ID_CANCEL)
    
    def get_filter_result(self) -> Optional[FilterCriterion]:
        """Get the filter result after dialog is closed."""
        return self.filter_result 