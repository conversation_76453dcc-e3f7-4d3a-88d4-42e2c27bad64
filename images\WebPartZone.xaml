<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M2,5H1V4H2Zm7,8h1V12H9ZM2,6H1V7H2Zm6,6H7v1H8ZM4,2H3V3H4ZM8,2H7V3H8ZM6,2H5V3H6ZM2,2H1.5L1,2.5V3H2ZM13,7h1V6H13ZM9,3h1V2H9Zm4.5-1H13V3h1V2.5ZM13,5h1V4H13Zm0,6h1V10H13Zm0-2h1V8H13Zm-2,4h1V12H11Zm2,0h.5l.5-.5V12H13ZM6,12H5v1H6ZM2,10H1v1H2ZM2,8H1V9H2Zm2,4H3v1H4ZM2,12H1v.5l.5.5H2Zm9-9h1V2H11Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M7.5,4.5v6h-4v-6Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,4.5v6l-.5.5h-4L3,10.5v-6L3.5,4h4ZM7,10V5H4v5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12,11H10V6h2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M6,9H5V6H6Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
