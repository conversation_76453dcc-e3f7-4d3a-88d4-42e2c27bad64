"""Backup management for JEdit2.

This module provides backup functionality for protecting files before corrections.
"""

import os
import shutil
import datetime
from typing import Optional, List, Dict
from pathlib import Path


class BackupManager:
    """Manages file backups for format corrections."""
    
    def __init__(self, backup_dir: Optional[str] = None):
        """Initialize backup manager.
        
        Args:
            backup_dir: Directory for backups. If None, uses default location.
        """
        if backup_dir is None:
            # Default backup directory in user's home
            home = Path.home()
            self.backup_dir = home / ".jedit2" / "backups"
        else:
            self.backup_dir = Path(backup_dir)
        
        # Ensure backup directory exists
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Maximum number of backups to keep per file
        self.max_backups_per_file = 10
    
    def create_backup(self, file_path: str, content: str) -> str:
        """Create a backup of file content.
        
        Args:
            file_path: Original file path (used for naming)
            content: Content to backup
            
        Returns:
            Path to the created backup file
        """
        # Generate backup filename with timestamp
        original_name = Path(file_path).name if file_path else "untitled"
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{original_name}.backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        
        # Write backup content
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Clean up old backups for this file
        self._cleanup_old_backups(original_name)
        
        return str(backup_path)
    
    def create_file_backup(self, file_path: str) -> str:
        """Create a backup of an existing file.
        
        Args:
            file_path: Path to the file to backup
            
        Returns:
            Path to the created backup file
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return self.create_backup(file_path, content)
    
    def list_backups(self, original_name: Optional[str] = None) -> List[Dict[str, str]]:
        """List available backups.
        
        Args:
            original_name: If provided, only list backups for this file
            
        Returns:
            List of backup info dictionaries
        """
        backups = []
        
        for backup_file in self.backup_dir.glob("*.backup_*"):
            # Parse backup filename
            name_parts = backup_file.name.split('.backup_')
            if len(name_parts) != 2:
                continue
            
            file_name = name_parts[0]
            timestamp_str = name_parts[1]
            
            # Filter by original name if specified
            if original_name and file_name != original_name:
                continue
            
            # Parse timestamp
            try:
                timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            except ValueError:
                continue
            
            backup_info = {
                'file_name': file_name,
                'backup_path': str(backup_file),
                'timestamp': timestamp.isoformat(),
                'size': backup_file.stat().st_size
            }
            backups.append(backup_info)
        
        # Sort by timestamp (newest first)
        backups.sort(key=lambda x: x['timestamp'], reverse=True)
        return backups
    
    def restore_backup(self, backup_path: str, target_path: str) -> bool:
        """Restore a backup to a target location.
        
        Args:
            backup_path: Path to the backup file
            target_path: Where to restore the backup
            
        Returns:
            True if successful, False otherwise
        """
        try:
            shutil.copy2(backup_path, target_path)
            return True
        except Exception:
            return False
    
    def delete_backup(self, backup_path: str) -> bool:
        """Delete a specific backup.
        
        Args:
            backup_path: Path to the backup file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            os.remove(backup_path)
            return True
        except Exception:
            return False
    
    def _cleanup_old_backups(self, original_name: str) -> None:
        """Clean up old backups for a file, keeping only the most recent ones.
        
        Args:
            original_name: Name of the original file
        """
        backups = self.list_backups(original_name)
        
        # Keep only the most recent backups
        if len(backups) > self.max_backups_per_file:
            old_backups = backups[self.max_backups_per_file:]
            for backup in old_backups:
                self.delete_backup(backup['backup_path'])
    
    def get_backup_dir(self) -> str:
        """Get the backup directory path.
        
        Returns:
            Path to the backup directory
        """
        return str(self.backup_dir)
    
    def cleanup_all_backups(self, older_than_days: int = 30) -> int:
        """Clean up all backups older than specified days.
        
        Args:
            older_than_days: Delete backups older than this many days
            
        Returns:
            Number of backups deleted
        """
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=older_than_days)
        deleted_count = 0
        
        for backup_file in self.backup_dir.glob("*.backup_*"):
            try:
                # Parse timestamp from filename
                name_parts = backup_file.name.split('.backup_')
                if len(name_parts) != 2:
                    continue
                
                timestamp_str = name_parts[1]
                timestamp = datetime.datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                
                if timestamp < cutoff_date:
                    backup_file.unlink()
                    deleted_count += 1
            except (ValueError, OSError):
                continue
        
        return deleted_count
    
    def get_backup_size(self) -> int:
        """Get total size of all backups in bytes.
        
        Returns:
            Total size in bytes
        """
        total_size = 0
        for backup_file in self.backup_dir.glob("*.backup_*"):
            try:
                total_size += backup_file.stat().st_size
            except OSError:
                continue
        return total_size
