#!/usr/bin/env python3
"""
Test the aggressive improvements to format correction coverage.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_aggressive_improvements():
    """Test the aggressively improved format corrections."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # Test the previously failing cases
    aggressive_test_cases = [
        # JSON cases that were failing
        {
            'name': 'JSON: Missing Opening Brace',
            'content': '"key": "value"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Missing Closing Brace',
            'content': '{"key": "value"',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Empty Content',
            'content': '',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Whitespace Only',
            'content': '   \n\t  ',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Comments',
            'content': '{"key": "value", // comment\n"other": "data"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Unescaped Quotes',
            'content': '{"message": "He said "hello""}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Mixed Array/Object',
            'content': '{"key": "value", "item2"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Multiline Strings',
            'content': '{"key": "line1\nline2"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Multiple Complex Issues',
            'content': '{key1: "value1", "key2": value2, // comment\n"key3": True,}',
            'file_type': 'json'
        },
        
        # YAML cases that were failing
        {
            'name': 'YAML: Unmatched Quotes',
            'content': 'key: "value',
            'file_type': 'yaml'
        },
        {
            'name': 'YAML: Inconsistent Indentation',
            'content': 'key1: value1\n   key2: value2\n key3: value3',
            'file_type': 'yaml'
        },
        {
            'name': 'YAML: Missing Colon',
            'content': 'key value\nother: data',
            'file_type': 'yaml'
        },
        
        # Additional edge cases
        {
            'name': 'JSON: Unquoted String Values',
            'content': '{"name": John, "age": 30}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Complex Nested Issues',
            'content': '{"user": {"name": John "age": 30}, "active": True,}',
            'file_type': 'json'
        }
    ]
    
    print("🚀 TESTING AGGRESSIVE FORMAT CORRECTION IMPROVEMENTS")
    print("=" * 80)
    print("Testing previously failing cases with new aggressive fixes...")
    print()
    
    results = []
    
    for i, case in enumerate(aggressive_test_cases, 1):
        print(f"{i:2d}. {case['name']}")
        print("-" * 70)
        content_preview = repr(case['content'][:60])
        if len(case['content']) > 60:
            content_preview += "..."
        print(f"    Content: {content_preview}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], case['file_type'])
        print(f"    Correctable: {validation_result.is_correctable}")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], case['file_type'])
            
            if correction_result.success:
                print(f"    ✅ SUCCESS!")
                print(f"    Changes: {correction_result.changes_made}")
                
                # Show result preview
                result_preview = correction_result.corrected_content[:80]
                if len(correction_result.corrected_content) > 80:
                    result_preview += "..."
                print(f"    Result: {repr(result_preview)}")
                
                # Verify the result for JSON
                if case['file_type'] == 'json':
                    try:
                        import json
                        parsed = json.loads(correction_result.corrected_content)
                        print(f"    ✅ Valid JSON!")
                        success = True
                    except json.JSONDecodeError as e:
                        print(f"    ❌ Invalid JSON: {e}")
                elif case['file_type'] == 'yaml':
                    try:
                        import yaml
                        parsed = yaml.safe_load(correction_result.corrected_content)
                        print(f"    ✅ Valid YAML!")
                        success = True
                    except yaml.YAMLError as e:
                        print(f"    ❌ Invalid YAML: {e}")
                else:
                    success = True
                    
            else:
                print(f"    ❌ CORRECTION FAILED")
                print(f"    Errors: {correction_result.errors[:2]}...")  # Show first 2 errors
        else:
            print(f"    ❌ NOT DETECTED AS CORRECTABLE")
            if validation_result.warnings:
                print(f"    Warnings: {validation_result.warnings[:1]}...")
        
        results.append({
            'name': case['name'],
            'success': success,
            'format': case['file_type']
        })
        print()
    
    # Summary
    print("=" * 80)
    print("📊 AGGRESSIVE IMPROVEMENTS SUMMARY")
    print("=" * 80)
    
    by_format = {}
    for result in results:
        format_name = result['format'].upper()
        if format_name not in by_format:
            by_format[format_name] = {'total': 0, 'passed': 0}
        by_format[format_name]['total'] += 1
        if result['success']:
            by_format[format_name]['passed'] += 1
    
    total_tests = len(results)
    total_passed = sum(1 for r in results if r['success'])
    
    print(f"Overall Results:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {total_passed}")
    print(f"  Failed: {total_tests - total_passed}")
    print(f"  Success Rate: {(total_passed/total_tests)*100:.1f}%")
    
    print(f"\nBy Format:")
    for format_name, stats in by_format.items():
        rate = (stats['passed']/stats['total'])*100
        print(f"  {format_name}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
    
    if total_passed < total_tests:
        print(f"\n❌ REMAINING FAILURES:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}")
    
    print(f"\n📈 ESTIMATED COVERAGE IMPACT:")
    print(f"  Previous Coverage: ~51.8%")
    # Rough estimate: each fix covers ~2% of total cases
    estimated_improvement = total_passed * 1.5
    estimated_new_coverage = min(51.8 + estimated_improvement, 95)
    print(f"  Estimated New Coverage: ~{estimated_new_coverage:.1f}%")
    print(f"  Improvement: +{estimated_improvement:.1f}%")
    
    return total_passed, total_tests

if __name__ == "__main__":
    print("🧪 AGGRESSIVE FORMAT CORRECTION IMPROVEMENTS TEST")
    print("=" * 60)
    
    passed, total = test_aggressive_improvements()
    
    print(f"\n🎯 FINAL RESULT: {passed}/{total} aggressive improvements working")
    
    if passed >= total * 0.9:  # 90% success rate
        print("🎉 OUTSTANDING! Ready for full coverage analysis!")
    elif passed >= total * 0.75:  # 75% success rate
        print("🚀 EXCELLENT PROGRESS! Major coverage improvement achieved!")
    elif passed >= total * 0.5:  # 50% success rate
        print("👍 GOOD PROGRESS! Continue with remaining fixes.")
    else:
        print("🔧 MORE WORK NEEDED. Debug failing cases.")
