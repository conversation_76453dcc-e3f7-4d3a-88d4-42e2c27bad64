<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,11.025H10.1L8.707,9.637,7.318,11.025H5.5a4.727,4.727,0,0,1,4.5-4.5A4.5,4.5,0,0,1,14.5,11.025Zm-2-7a2.5,2.5,0,1,0-2.5,2.5A2.5,2.5,0,0,0,12.5,4.025Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M11.832,6.384a2.993,2.993,0,1,0-3.664,0A5,5,0,0,0,5,11.025H6a4,4,0,1,1,8,0h1A5.065,5.065,0,0,0,11.832,6.384ZM8,4.025a2,2,0,1,1,2,2A2,2,0,0,1,8,4.025Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,12.025v3H4v-3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.828,13.172v.707L8.707,16,8,15.293l1.768-1.768L8,11.758l.707-.707ZM2.293,11.051.172,13.172v.707L2.293,16,3,15.293,1.232,13.525,3,11.758Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
