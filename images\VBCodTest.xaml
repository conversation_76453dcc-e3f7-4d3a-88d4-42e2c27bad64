<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M1.66,13,2.6,11.5v-3h2v3L5.535,13Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2.1,9h-1V8h5V9h-1v2.356l1.174,1.879L5.848,13h-.9l-.772-1.235L4.1,11.5V9h-1v2.5l-.076.265L2.25,13h-.9l-.424.235L2.1,11.356Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M8,8h4V9H8ZM9,7h4V6H9ZM8,2H4V3H8ZM6,5h4V4H6Zm5,0h3V4H11ZM9,2V3h5V2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M6.62,14.735A.5.5,0,0,1,6.2,15.5H1a.5.5,0,0,1-.424-.765L1.348,13.5h4.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6.2,16H1a1,1,0,0,1-.848-1.53l.772-1.235L1.348,13h4.5l.424.235.772,1.235A1,1,0,0,1,6.2,16Zm-4.57-2L1,15H6.2L5.57,14Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
