<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M8.5,10.808V14.5H.5v-9h8V6.565L5.54,9.525l-1.9-1.9L1.522,9.75,4.772,13H6.308Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,3.5v9H10V9.308L11.558,7.75,10,6.192V5.086l-1-1V3.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,3.5v9l-.5.5H10V12h4V4H9V3h5.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9,10.308V14.5l-.5.5H.5L0,14.5v-9L.5,5h8l.5.5v.565l-1,1V6H1v8H8V11.308Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M6.5,4a2,2,0,0,0-4,0H1V6H8V4Zm-2-1a1,1,0,1,1-1,1A1,1,0,0,1,4.5,3Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.5,2a2,2,0,0,0-4,0H7v.359A3.008,3.008,0,0,1,7.329,3H9V4h5V2Zm-2,1a1,1,0,1,1,1-1A1,1,0,0,1,10.5,3Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M10.144,7.75,5.894,12H5.187L2.937,9.75l.707-.707,1.9,1.9,3.9-3.9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
