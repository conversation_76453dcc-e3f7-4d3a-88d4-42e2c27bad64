<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.422,14.008l.385.385a7.1,7.1,0,0,1-.908.338A6.964,6.964,0,0,1,8,15c-.192,0-7-6.807-7-7a6.988,6.988,0,0,1,.268-1.9,7.078,7.078,0,0,1,.339-.91l.385.385a6.486,6.486,0,1,1,8.43,8.43Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,8A6.988,6.988,0,0,0,1.607,5.193L2.414,6H4.7a10.514,10.514,0,0,0-.2,2c0,.03,0,.06,0,.091L5.6,9.181A8.6,8.6,0,0,1,5.5,8a9.487,9.487,0,0,1,.222-2h4.556A9.487,9.487,0,0,1,10.5,8a9.487,9.487,0,0,1-.222,2H6.414l1,1h2.591a9.4,9.4,0,0,1-.818,1.773l.727.727A10.392,10.392,0,0,0,11.053,11h2.135a6.011,6.011,0,0,1-3.153,2.621l.772.772A6.994,6.994,0,0,0,15,8ZM4.946,5H2.812a6.023,6.023,0,0,1,3.4-2.727A10.517,10.517,0,0,0,4.946,5ZM6,5A9.481,9.481,0,0,1,7.628,2.019C7.752,2.011,7.874,2,8,2s.249.011.373.019A9.479,9.479,0,0,1,10.005,5ZM9.789,2.273A6.021,6.021,0,0,1,13.188,5H11.053A10.484,10.484,0,0,0,9.789,2.273ZM11.3,10a10.2,10.2,0,0,0,0-4H13.65a5.889,5.889,0,0,1,0,4Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,15l-.354-.146-5.5-5.5L1,9V6l9,9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
