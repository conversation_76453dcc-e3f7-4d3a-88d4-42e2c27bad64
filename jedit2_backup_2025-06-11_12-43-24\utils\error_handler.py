"""Error handler for JEdit2.

This module provides error handling functionality for JEdit2.
"""

import wx
import logging
import traceback
import threading
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum


class ErrorLevel(Enum):
    """Error levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories."""
    UI = "ui"
    DATA = "data"
    FILE = "file"
    FORMULA = "formula"
    VALIDATION = "validation"
    SYSTEM = "system"
    MEMORY = "memory"
    NETWORK = "network"
    STYLE = "style"
    APPLICATION = "application"


class ErrorHandler:
    """Error handler for JEdit2."""
    
    _instance: Optional["ErrorHandler"] = None
    _logger: Optional[logging.Logger] = None
    _error_history: List[Dict[str, Any]] = []
    _max_history_size = 1000
    _lock = threading.RLock()  # Use RLock to allow recursive locking
    
    @classmethod
    def get_instance(cls) -> "ErrorHandler":
        """Get the singleton instance."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
            return cls._instance
    
    @classmethod
    def setup_logging(cls) -> None:
        """Set up logging."""
        if cls._logger is None:
            cls._logger = logging.getLogger("jedit2")
            cls._logger.setLevel(logging.INFO)
            
            # Create file handler
            file_handler = logging.FileHandler("jedit2.log")
            file_handler.setLevel(logging.INFO)
            
            # Create console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # Create formatter
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # Add handlers
            cls._logger.addHandler(file_handler)
            cls._logger.addHandler(console_handler)
    
    @classmethod
    def log_error(
        cls,
        error: Exception,
        level: ErrorLevel,
        category: ErrorCategory,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log an error.
        
        Args:
            error: The error to log
            level: Error level
            category: Error category
            context: Additional context
        """
        if cls._logger is None:
            cls.setup_logging()
        
        # Create error record
        error_record = {
            "timestamp": datetime.now().isoformat(),
            "level": level.value,
            "category": category.value,
            "message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {}
        }
        
        # Add to history (thread-safe)
        with cls._lock:
            cls._error_history.append(error_record)
            if len(cls._error_history) > cls._max_history_size:
                cls._error_history.pop(0)
        
        # Log error
        if cls._logger:
            cls._logger.error(
                f"{level.value.upper()} - {category.value}: {str(error)}",
                extra={"error_record": error_record}
            )
        
        # Show error dialog for errors and critical errors
        if level in (ErrorLevel.ERROR, ErrorLevel.CRITICAL):
            cls.show_error_dialog(error, level, category, context)
    
    @classmethod
    def log_warning(
        cls,
        message: str,
        category: ErrorCategory,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a warning.
        
        Args:
            message: Warning message
            category: Error category
            context: Additional context
        """
        if cls._logger is None:
            cls.setup_logging()
        
        # Create warning record
        warning_record = {
            "timestamp": datetime.now().isoformat(),
            "level": ErrorLevel.WARNING.value,
            "category": category.value,
            "message": message,
            "context": context or {}
        }
        
        # Add to history (thread-safe)
        with cls._lock:
            cls._error_history.append(warning_record)
            if len(cls._error_history) > cls._max_history_size:
                cls._error_history.pop(0)
        
        # Log warning
        if cls._logger:
            cls._logger.warning(
                f"WARNING - {category.value}: {message}",
                extra={"warning_record": warning_record}
            )
    
    @classmethod
    def log_info(
        cls,
        message: str,
        category: ErrorCategory,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log an info message.
        
        Args:
            message: Info message
            category: Error category
            context: Additional context
        """
        if cls._logger is None:
            cls.setup_logging()
        
        # Create info record
        info_record = {
            "timestamp": datetime.now().isoformat(),
            "level": ErrorLevel.INFO.value,
            "category": category.value,
            "message": message,
            "context": context or {}
        }
        
        # Add to history (thread-safe)
        with cls._lock:
            cls._error_history.append(info_record)
            if len(cls._error_history) > cls._max_history_size:
                cls._error_history.pop(0)
        
        # Log info
        if cls._logger:
            cls._logger.info(
                f"INFO - {category.value}: {message}",
                extra={"info_record": info_record}
            )
    
    @classmethod
    def show_error_dialog(
        cls,
        error: Exception,
        level: ErrorLevel,
        category: ErrorCategory,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Show an error dialog.
        
        Args:
            error: The error to show
            level: Error level
            category: Error category
            context: Additional context
        """
        # Create error message
        message = f"{level.value.upper()} - {category.value}\n\n{str(error)}"
        
        # Add context if available
        if context:
            message += "\n\nContext:\n"
            for key, value in context.items():
                message += f"{key}: {value}\n"
        
        # Show dialog
        wx.MessageBox(
            message,
            "Error",
            wx.OK | wx.ICON_ERROR
        )
    
    @classmethod
    def get_error_history(cls) -> List[Dict[str, Any]]:
        """Get the error history.
        
        Returns:
            List of error records
        """
        with cls._lock:
            return cls._error_history.copy()
    
    @classmethod
    def clear_error_history(cls) -> None:
        """Clear the error history."""
        with cls._lock:
            cls._error_history.clear()
    
    @classmethod
    def get_error_stats(cls) -> Dict[str, int]:
        """Get error statistics.
        
        Returns:
            Dictionary of error statistics
        """
        with cls._lock:
            stats = {
                "total": len(cls._error_history),
                "info": 0,
                "warning": 0,
                "error": 0,
                "critical": 0
            }
            
            for record in cls._error_history:
                stats[record["level"]] += 1
            
            return stats 