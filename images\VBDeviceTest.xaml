<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M13.2,10.74a.491.491,0,0,1-.438.26H7.239A.471.471,0,0,1,7,10.933a.534.534,0,0,1-.2-.193.488.488,0,0,1,.017-.509L7,9.953,7.6,9H12.4l.786,1.23A.49.49,0,0,1,13.2,10.74Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M14.076,11.221A1.48,1.48,0,0,1,12.761,12H7.239A1.51,1.51,0,0,1,7,11.968V10.933A.471.471,0,0,0,7.239,11h5.522a.5.5,0,0,0,.421-.77L12.4,9H7.6L7,9.953V8.182L7.33,8h5.34l.422.23.933,1.462A1.484,1.484,0,0,1,14.076,11.221Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M7.649,8,8.5,6.667V2.5h3V6.667L12.351,8Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M13.091,8.23,12.67,8h-.913l-.679-1.063L11,6.667V3H9V6.667l-.078.27L8.243,8H7.33L7,8.182V8.093L8,6.521V3H7V2h6V3H12V6.521Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4,14H2V13H4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,15.5H.5v-8h5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.5,7H.5L0,7.5v8l.5.5h5l.5-.5v-8ZM5,15H1V8H5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
