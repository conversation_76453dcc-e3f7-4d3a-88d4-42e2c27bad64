#!/usr/bin/env python3
"""
Test AI Integration
Test the AI functionality after our fixes.
"""

import sys
import os
sys.path.insert(0, '.')

def test_ai_components():
    """Test each AI component individually."""
    print("🧪 Testing AI Components")
    print("=" * 50)
    
    # Test 1: Basic AI Manager
    print("\n1. Testing Basic AI Manager...")
    try:
        from jedit2.utils.ai_manager import AIManager
        from jedit2.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        api_key = config.get_setting('api_key')
        
        if not api_key:
            print("❌ No API key configured")
            return False
            
        ai = AIManager(api_key=api_key)
        commands, raw = ai.get_ai_response('open test.csv')
        
        if commands and len(commands) > 0:
            print("✅ Basic AI Manager working")
            print(f"   Commands: {commands}")
        else:
            print("❌ Basic AI Manager failed")
            return False
            
    except Exception as e:
        print(f"❌ Basic AI Manager error: {e}")
        return False
    
    # Test 2: Improved AI Manager
    print("\n2. Testing Improved AI Manager...")
    try:
        from jedit2.utils.ai_manager_improved import ImprovedAIManager
        
        improved_ai = ImprovedAIManager(ai)
        commands, raw = improved_ai.get_ai_response_robust('open test.csv')
        
        if commands and len(commands) > 0:
            print("✅ Improved AI Manager working")
            print(f"   Commands: {commands}")
        else:
            print("❌ Improved AI Manager failed")
            return False
            
    except Exception as e:
        print(f"❌ Improved AI Manager error: {e}")
        return False
    
    # Test 3: Production AI Manager
    print("\n3. Testing Production AI Manager...")
    try:
        from jedit2.utils.ai_manager_production import ProductionAIManager
        
        prod_ai = ProductionAIManager(ai)
        commands, raw = prod_ai.get_ai_response_robust('open test.csv')
        
        if commands and len(commands) > 0:
            print("✅ Production AI Manager working")
            print(f"   Commands: {commands}")
        else:
            print("❌ Production AI Manager failed")
            return False
            
    except Exception as e:
        print(f"❌ Production AI Manager error: {e}")
        return False
    
    print("\n🎉 All AI components working correctly!")
    return True

def test_main_window_integration():
    """Test the main window AI integration."""
    print("\n🏠 Testing Main Window Integration")
    print("=" * 50)
    
    try:
        # Import without starting the GUI
        import wx
        app = wx.App(False)  # Don't show GUI
        
        from main_window import MainWindow
        
        # Create main window (but don't show it)
        main_window = MainWindow()
        
        # Test AI manager integration
        if hasattr(main_window, 'ai_manager') and main_window.ai_manager:
            print("✅ Main window has AI manager")
            
            # Test if production AI manager is integrated
            if hasattr(main_window.ai_manager, 'get_ai_response_robust'):
                print("✅ Production AI manager integrated")
                
                # Test a simple query
                try:
                    commands, raw = main_window.ai_manager.get_ai_response_robust('open test.csv')
                    if commands:
                        print("✅ AI query working in main window")
                        print(f"   Commands: {commands}")
                    else:
                        print("❌ AI query failed in main window")
                        return False
                except Exception as e:
                    print(f"❌ AI query error in main window: {e}")
                    return False
            else:
                print("⚠️  Production AI manager not integrated, using basic AI")
                
        else:
            print("❌ Main window has no AI manager")
            return False
            
        app.Destroy()
        print("\n🎉 Main window integration working!")
        return True
        
    except Exception as e:
        print(f"❌ Main window integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 JEdit2 AI Integration Test")
    print("=" * 60)
    
    # Test components
    components_ok = test_ai_components()
    
    if components_ok:
        # Test main window integration
        integration_ok = test_main_window_integration()
        
        if integration_ok:
            print("\n✅ ALL TESTS PASSED!")
            print("🎉 AI integration is working correctly!")
        else:
            print("\n❌ Integration tests failed")
    else:
        print("\n❌ Component tests failed")
