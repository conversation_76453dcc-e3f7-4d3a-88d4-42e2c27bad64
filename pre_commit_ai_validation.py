#!/usr/bin/env python3
"""
Pre-commit hook for AI command validation.
This ensures that all AI capabilities have proper method mappings before code is committed.
"""

import sys
import subprocess
from pathlib import Path


def run_ai_validation():
    """Run the AI command validation"""
    try:
        # Run the validation script
        result = subprocess.run(
            [sys.executable, "validate_ai_commands.py"],
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running AI validation: {e}")
        return False


def main():
    """Main pre-commit hook function"""
    print("🔍 Running AI Command Validation...")
    
    if run_ai_validation():
        print("✅ AI command validation passed!")
        return 0
    else:
        print("❌ AI command validation failed!")
        print("\nPlease fix the AI command mapping issues before committing.")
        print("Run 'python validate_ai_commands.py' for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
