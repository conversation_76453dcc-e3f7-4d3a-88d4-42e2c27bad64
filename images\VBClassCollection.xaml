<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M0,0H16V16H0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15,1.5v11l-.5.5H13V12h1V2H4V3H3V1.5L3.5,1h11Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13,12V3.75L12.25,3H4V2H14V12Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M12,14H2V4H12Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.5,3H1.5L1,3.5v11l.5.5h11l.5-.5V3.5ZM12,14H2V4H12Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8.023,8.307V8H7v3H8.976l.672-.672H10l.625.625v.354L9,12.932H8.648l-.625-.625V12H6.5L6,11.5V8H5.229l-.615.614H4.261l-1-1V7.261l2.25-2.25h.353l1,1v.353L6.229,7H8.976l.672-.672H10l.625.625v.354L9,8.932H8.648Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
