<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,7.5a4,4,0,1,1-4-4A4.012,4.012,0,0,1,9.5,7.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5.5,3A4.5,4.5,0,1,0,10,7.5,4.481,4.481,0,0,0,5.5,3Zm0,8A3.543,3.543,0,0,1,2,7.5,3.543,3.543,0,0,1,5.5,4,3.543,3.543,0,0,1,9,7.5,3.543,3.543,0,0,1,5.5,11Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7,7.5A1.538,1.538,0,0,1,5.5,9,1.538,1.538,0,0,1,4,7.5,1.538,1.538,0,0,1,5.5,6,1.538,1.538,0,0,1,7,7.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15.354,7.146v.708l-2,2-.708-.708L13.793,8H9.75V7h4.043L12.646,5.854l.708-.708Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13,1.5V4.25H12V2.707L9.354,5.354l-.708-.708L11.293,2H9.75V1H12.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13,10.75V13.5l-.5.5H9.75V13h1.543L8.646,10.354l.708-.708L12,12.293V10.75Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
