<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M6,11.5A2.25,2.25,0,1,1,8.25,9.25,2.25,2.25,0,0,1,6,11.5Zm0,0A3.5,3.5,0,0,0,2.5,15h7A3.5,3.5,0,0,0,6,11.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7.7,11.394a2.75,2.75,0,1,0-3.4,0A4,4,0,0,0,2,15H3a3,3,0,0,1,6,0h1A4,4,0,0,0,7.7,11.394ZM4.25,9.25A1.75,1.75,0,1,1,6,11,1.752,1.752,0,0,1,4.25,9.25Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M8.809,6.791A3.434,3.434,0,0,1,11,6a3.5,3.5,0,0,1,3.5,3.5H9.733c0-.083.017-.166.017-.25A3.72,3.72,0,0,0,8.809,6.791ZM11,6A2.25,2.25,0,1,0,8.75,3.75,2.25,2.25,0,0,0,11,6Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.7,5.894a2.75,2.75,0,1,0-3.4,0,3.9,3.9,0,0,0-.845.544,3.7,3.7,0,0,1,.679.762A2.9,2.9,0,0,1,11,6.5a3,3,0,0,1,3,3h1A4,4,0,0,0,12.7,5.894ZM11,5.5a1.75,1.75,0,1,1,1.75-1.75A1.752,1.752,0,0,1,11,5.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
