<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M9,11v1H6V11H7V7H8v4Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M10.5,2,9,.5H5.5v6h5Zm4,8L13,8.5H9.5v6h5Zm-9,0L4,8.5H.5v6h5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M10.854,1.646l-1.5-1.5L9,0H5.5L5,.5v6l.5.5h5l.5-.5V2ZM10,6H6V1H8V3h2Zm4.854,3.646-1.5-1.5L13,8H9.5L9,8.5v6l.5.5h5l.5-.5V10ZM14,14H10V9h2v2h2ZM5.854,9.646l-1.5-1.5L4,8H.5L0,8.5v6l.5.5h5l.5-.5V10ZM5,14H1V9H3v2H5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
