<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-red-10" Color="#c50b17" Opacity="0.1" />
      <SolidColorBrush x:Key="light-red" Color="#c50b17" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-red-10}" Geometry="F1M1.5,4.5v5.88l.65-.65L3.41,11H7.59L8.85,9.73l2.77,2.77H14.5v-8Z" />
              <GeometryDrawing Brush="{DynamicResource light-red}" Geometry="F1M5,7V8h6V7Zm9.5-3H1.5L1,4.5v6.38l1-1V8H14v4H11.12l.73.73V13H14.5l.5-.5v-8ZM2,7V5H14V7Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.5,2h-5L5,2.5V4H6V3h4V4h1V2.5ZM11,6V9h1V6ZM4,6V9H5V6Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7,12v3H4V12Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.854,13.146v.708l-2,2-.708-.708L9.793,13.5,8.146,11.854l.708-.708Zm-8.708-2-2,2v.708l2,2,.708-.708L1.207,13.5l1.647-1.646Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
