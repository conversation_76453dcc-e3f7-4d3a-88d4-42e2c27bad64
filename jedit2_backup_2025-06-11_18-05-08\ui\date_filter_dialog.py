"""Date Filter Dialog for advanced date filtering functionality."""

import wx
import wx.adv
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, List
from .filter_criteria import DateFilterCriterion
from .filter_utils import parse_date, calculate_statistics
import logging

logger = logging.getLogger(__name__)


class DateFilterDialog(wx.Dialog):
    """Dialog for creating advanced date filters."""
    
    def __init__(self, parent: wx.Window, column_name: str, column_data: List[str], 
                 current_criterion: Optional[DateFilterCriterion] = None):
        """
        Initialize the date filter dialog.
        
        Args:
            parent: Parent window
            column_name: Name of the column being filtered
            column_data: All values in the column for statistics
            current_criterion: Existing date filter criterion (if any)
        """
        super().__init__(parent, title=f"Date Filters - {column_name}", 
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.column_name = column_name
        self.column_data = column_data
        self.current_criterion = current_criterion
        self.filter_result: Optional[DateFilterCriterion] = None
        
        # Calculate statistics for the column
        self.stats = calculate_statistics(column_data, 'date')
        
        self._init_ui()
        self._bind_events()
        self._populate_current_values()
        
        # Set initial size and center
        self.SetSize((450, 500))
        self.CenterOnParent()
    
    def _init_ui(self) -> None:
        """Initialize the user interface components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title_label = wx.StaticText(self, label=f"Show rows where '{self.column_name}':")
        title_font = title_label.GetFont()
        title_font.SetWeight(wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL, 10)
        
        # Statistics panel
        stats_panel = self._create_statistics_panel()
        main_sizer.Add(stats_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Filter condition panel
        condition_panel = self._create_condition_panel()
        main_sizer.Add(condition_panel, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, 10)
        
        # Button panel
        button_panel = self._create_button_panel()
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
    
    def _create_statistics_panel(self) -> wx.Panel:
        """Create the statistics panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Statistics title
        stats_label = wx.StaticText(panel, label="Column Statistics:")
        stats_font = stats_label.GetFont()
        stats_font.SetWeight(wx.FONTWEIGHT_BOLD)
        stats_label.SetFont(stats_font)
        sizer.Add(stats_label, 0, wx.BOTTOM, 5)
        
        # Statistics content
        stats_text = []
        if 'date_count' in self.stats and self.stats['date_count'] > 0:
            stats_text.append(f"Count: {self.stats['date_count']}")
            if 'earliest_date' in self.stats:
                earliest = self.stats['earliest_date'].strftime('%Y-%m-%d')
                stats_text.append(f"Earliest: {earliest}")
            if 'latest_date' in self.stats:
                latest = self.stats['latest_date'].strftime('%Y-%m-%d')
                stats_text.append(f"Latest: {latest}")
        else:
            stats_text.append("No date values found in column")
        
        stats_str = " • ".join(stats_text)
        stats_content = wx.StaticText(panel, label=stats_str)
        stats_content.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(stats_content, 0, wx.BOTTOM, 10)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_condition_panel(self) -> wx.Panel:
        """Create the condition selection panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Operator choice
        operator_label = wx.StaticText(panel, label="Condition:")
        sizer.Add(operator_label, 0, wx.BOTTOM, 5)
        
        self.operator_choice = wx.Choice(panel, choices=[
            # Specific date comparisons
            "Equals",
            "Does Not Equal",
            "Before",
            "After", 
            "Between",
            "Not Between",
            # Relative dates
            "Today",
            "Yesterday",
            "Tomorrow",
            "This Week",
            "Last Week",
            "Next Week",
            "This Month",
            "Last Month", 
            "Next Month",
            "This Quarter",
            "Last Quarter",
            "Next Quarter",
            "This Year",
            "Last Year",
            "Next Year"
        ])
        self.operator_choice.SetSelection(0)  # Default to "Equals"
        sizer.Add(self.operator_choice, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Date inputs panel
        dates_panel = self._create_dates_panel()
        sizer.Add(dates_panel, 0, wx.EXPAND | wx.BOTTOM, 10)
        
        # Help text
        self.help_text = wx.StaticText(panel, label="")
        self.help_text.SetForegroundColour(wx.Colour(100, 100, 100))
        sizer.Add(self.help_text, 0, wx.EXPAND)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_dates_panel(self) -> wx.Panel:
        """Create the date inputs panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # First date
        self.date1_label = wx.StaticText(panel, label="Date:")
        sizer.Add(self.date1_label, 0, wx.BOTTOM, 2)
        
        self.date1_picker = wx.adv.DatePickerCtrl(panel, style=wx.adv.DP_DROPDOWN | wx.adv.DP_SHOWCENTURY)
        sizer.Add(self.date1_picker, 0, wx.EXPAND | wx.BOTTOM, 5)
        
        # Second date (for between operations)
        self.date2_label = wx.StaticText(panel, label="And:")
        sizer.Add(self.date2_label, 0, wx.BOTTOM, 2)
        
        self.date2_picker = wx.adv.DatePickerCtrl(panel, style=wx.adv.DP_DROPDOWN | wx.adv.DP_SHOWCENTURY)
        sizer.Add(self.date2_picker, 0, wx.EXPAND)
        
        panel.SetSizer(sizer)
        return panel
    
    def _create_button_panel(self) -> wx.Panel:
        """Create the button panel."""
        panel = wx.Panel(self)
        sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        sizer.AddStretchSpacer()
        
        self.cancel_btn = wx.Button(panel, wx.ID_CANCEL, "Cancel")
        sizer.Add(self.cancel_btn, 0, wx.RIGHT, 5)
        
        self.ok_btn = wx.Button(panel, wx.ID_OK, "OK")
        self.ok_btn.SetDefault()
        sizer.Add(self.ok_btn, 0)
        
        panel.SetSizer(sizer)
        return panel
    
    def _bind_events(self) -> None:
        """Bind event handlers."""
        self.operator_choice.Bind(wx.EVT_CHOICE, self._on_operator_change)
        self.ok_btn.Bind(wx.EVT_BUTTON, self._on_ok)
        self.cancel_btn.Bind(wx.EVT_BUTTON, self._on_cancel)
    
    def _populate_current_values(self) -> None:
        """Populate the dialog with current criterion values."""
        if self.current_criterion:
            # Map operators to choice indices
            operator_map = {
                DateFilterCriterion.EQUALS: 0,
                DateFilterCriterion.NOT_EQUALS: 1,
                DateFilterCriterion.BEFORE: 2,
                DateFilterCriterion.AFTER: 3,
                DateFilterCriterion.BETWEEN: 4,
                DateFilterCriterion.NOT_BETWEEN: 5,
                DateFilterCriterion.TODAY: 6,
                DateFilterCriterion.YESTERDAY: 7,
                DateFilterCriterion.TOMORROW: 8,
                DateFilterCriterion.THIS_WEEK: 9,
                DateFilterCriterion.LAST_WEEK: 10,
                DateFilterCriterion.NEXT_WEEK: 11,
                DateFilterCriterion.THIS_MONTH: 12,
                DateFilterCriterion.LAST_MONTH: 13,
                DateFilterCriterion.NEXT_MONTH: 14,
                DateFilterCriterion.THIS_QUARTER: 15,
                DateFilterCriterion.LAST_QUARTER: 16,
                DateFilterCriterion.NEXT_QUARTER: 17,
                DateFilterCriterion.THIS_YEAR: 18,
                DateFilterCriterion.LAST_YEAR: 19,
                DateFilterCriterion.NEXT_YEAR: 20
            }
            
            index = operator_map.get(self.current_criterion.operator, 0)
            self.operator_choice.SetSelection(index)
            
            if self.current_criterion.date1:
                wx_date = wx.DateTime()
                wx_date.Set(self.current_criterion.date1.day, 
                           self.current_criterion.date1.month - 1,  # wx uses 0-based months
                           self.current_criterion.date1.year)
                self.date1_picker.SetValue(wx_date)
                
            if self.current_criterion.date2:
                wx_date2 = wx.DateTime()
                wx_date2.Set(self.current_criterion.date2.day,
                            self.current_criterion.date2.month - 1,
                            self.current_criterion.date2.year)
                self.date2_picker.SetValue(wx_date2)
        
        self._update_ui_state()
    
    def _on_operator_change(self, event: wx.CommandEvent) -> None:
        """Handle operator selection changes."""
        self._update_ui_state()
    
    def _update_ui_state(self) -> None:
        """Update UI state based on selected operator."""
        selection = self.operator_choice.GetSelection()
        
        # Show/hide controls based on operator
        specific_date_ops = [0, 1, 2, 3]  # equals, not equals, before, after
        range_ops = [4, 5]  # between, not between
        relative_ops = list(range(6, 21))  # all relative date operations
        
        # Date 1
        if selection in specific_date_ops + range_ops:
            self.date1_label.Show()
            self.date1_picker.Show()
            if selection in specific_date_ops:
                self.date1_label.SetLabel("Date:")
            else:
                self.date1_label.SetLabel("From:")
        else:
            self.date1_label.Hide()
            self.date1_picker.Hide()
        
        # Date 2
        if selection in range_ops:
            self.date2_label.Show()
            self.date2_picker.Show()
            self.date2_label.SetLabel("To:")
        else:
            self.date2_label.Hide()
            self.date2_picker.Hide()
        
        # Update help text
        today = datetime.now().date()
        help_texts = {
            0: "Show rows where the date equals the specified date",
            1: "Show rows where the date does not equal the specified date",
            2: "Show rows where the date is before the specified date",
            3: "Show rows where the date is after the specified date",
            4: "Show rows where the date is between the two specified dates (inclusive)",
            5: "Show rows where the date is not between the two specified dates",
            6: f"Show rows with today's date ({today.strftime('%Y-%m-%d')})",
            7: f"Show rows with yesterday's date ({(today - timedelta(days=1)).strftime('%Y-%m-%d')})",
            8: f"Show rows with tomorrow's date ({(today + timedelta(days=1)).strftime('%Y-%m-%d')})",
            9: "Show rows with dates in the current week",
            10: "Show rows with dates in the last week",
            11: "Show rows with dates in the next week",
            12: "Show rows with dates in the current month",
            13: "Show rows with dates in the last month",
            14: "Show rows with dates in the next month",
            15: "Show rows with dates in the current quarter",
            16: "Show rows with dates in the last quarter",
            17: "Show rows with dates in the next quarter",
            18: "Show rows with dates in the current year",
            19: "Show rows with dates in the last year",
            20: "Show rows with dates in the next year"
        }
        
        help_text = help_texts.get(selection, "")
        self.help_text.SetLabel(help_text)
        self.help_text.Wrap(400)
        
        # Force layout update
        self.Layout()
    
    def _wx_date_to_datetime(self, wx_date: wx.DateTime) -> datetime:
        """Convert wx.DateTime to Python datetime."""
        return datetime(wx_date.GetYear(), wx_date.GetMonth() + 1, wx_date.GetDay())
    
    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Handle OK button click."""
        selection = self.operator_choice.GetSelection()
        
        # Map choice indices to operators
        operators = [
            DateFilterCriterion.EQUALS,
            DateFilterCriterion.NOT_EQUALS,
            DateFilterCriterion.BEFORE,
            DateFilterCriterion.AFTER,
            DateFilterCriterion.BETWEEN,
            DateFilterCriterion.NOT_BETWEEN,
            DateFilterCriterion.TODAY,
            DateFilterCriterion.YESTERDAY,
            DateFilterCriterion.TOMORROW,
            DateFilterCriterion.THIS_WEEK,
            DateFilterCriterion.LAST_WEEK,
            DateFilterCriterion.NEXT_WEEK,
            DateFilterCriterion.THIS_MONTH,
            DateFilterCriterion.LAST_MONTH,
            DateFilterCriterion.NEXT_MONTH,
            DateFilterCriterion.THIS_QUARTER,
            DateFilterCriterion.LAST_QUARTER,
            DateFilterCriterion.NEXT_QUARTER,
            DateFilterCriterion.THIS_YEAR,
            DateFilterCriterion.LAST_YEAR,
            DateFilterCriterion.NEXT_YEAR
        ]
        
        operator = operators[selection]
        
        # Get dates based on operator type
        date1 = None
        date2 = None
        
        # Validate and get dates for specific date operations
        if operator in [DateFilterCriterion.EQUALS, DateFilterCriterion.NOT_EQUALS,
                       DateFilterCriterion.BEFORE, DateFilterCriterion.AFTER]:
            # Single date operators
            try:
                wx_date = self.date1_picker.GetValue()
                if not wx_date.IsValid():
                    wx.MessageBox("Please select a valid date.", "Invalid Input", 
                                wx.OK | wx.ICON_WARNING)
                    return
                date1 = self._wx_date_to_datetime(wx_date)
            except Exception as e:
                wx.MessageBox("Please select a valid date.", "Invalid Input", 
                            wx.OK | wx.ICON_WARNING)
                return
                
        elif operator in [DateFilterCriterion.BETWEEN, DateFilterCriterion.NOT_BETWEEN]:
            # Range operators
            try:
                wx_date1 = self.date1_picker.GetValue()
                wx_date2 = self.date2_picker.GetValue()
                
                if not wx_date1.IsValid() or not wx_date2.IsValid():
                    wx.MessageBox("Please select valid dates for both fields.", "Invalid Input", 
                                wx.OK | wx.ICON_WARNING)
                    return
                    
                date1 = self._wx_date_to_datetime(wx_date1)
                date2 = self._wx_date_to_datetime(wx_date2)
                
                # Check for reasonable date range (not more than 200 years apart)
                date_diff = abs((date2 - date1).days)
                if date_diff > 73000:  # ~200 years
                    wx.MessageBox("Date range is too large (more than 200 years).\nPlease select a smaller range.", "Invalid Range", 
                                wx.OK | wx.ICON_WARNING)
                    return
                
                # Warn if range is inverted
                if date1 > date2:
                    result = wx.MessageBox(f"The date range appears to be inverted ({date1.strftime('%Y-%m-%d')} to {date2.strftime('%Y-%m-%d')}).\nContinue anyway?", 
                                         "Date Range Check", wx.YES_NO | wx.ICON_QUESTION)
                    if result != wx.YES:
                        return
                        
            except Exception as e:
                import logging
                logging.getLogger(__name__).error(f"Date validation error: {e}")
                wx.MessageBox("Error processing the selected dates.\nPlease try selecting the dates again.", "Date Error", 
                            wx.OK | wx.ICON_ERROR)
                return
        
        # Create the filter criterion
        self.filter_result = DateFilterCriterion(
            operator=operator,
            date1=date1,
            date2=date2
        )
        
        self.EndModal(wx.ID_OK)
    
    def _on_cancel(self, event: wx.CommandEvent) -> None:
        """Handle Cancel button click."""
        self.filter_result = None
        self.EndModal(wx.ID_CANCEL)
    
    def get_filter_result(self) -> Optional[DateFilterCriterion]:
        """Get the filter result after dialog is closed."""
        return self.filter_result
