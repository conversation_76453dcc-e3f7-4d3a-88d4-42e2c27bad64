"""AI Settings Dialog for JEdit2.

This module provides the AISettingsDialog class, which allows users
to configure their Google Gemini API key.
"""

import wx
import wx.adv

from jedit2.utils.ai_manager import AIManager

class AISettingsDialog(wx.Dialog):
    """Dialog for managing AI settings."""

    def __init__(self, parent: wx.Window, config_manager):
        """
        Initializes the AISettingsDialog.
        
        Args:
            parent: The parent window.
            config_manager: The application's configuration manager.
        """
        super().__init__(parent, title="AI Settings")
        self._config_manager = config_manager
        
        self._init_ui()
        self.CenterOnParent()

    def _init_ui(self) -> None:
        """Initializes the UI components of the dialog."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # API Key Section
        api_key_sizer = wx.StaticBoxSizer(wx.VERTICAL, self, "Google AI Studio API Key")
        
        # Hyperlink to get API key
        api_key_hyperlink = wx.adv.HyperlinkCtrl(api_key_sizer.GetStaticBox(), wx.ID_ANY,
                                                 "Get your API key from Google AI Studio",
                                                 "https://makersuite.google.com/app/apikey")
        api_key_sizer.Add(api_key_hyperlink, 0, wx.ALL, 5)

        # API Key Input
        self.api_key_input = wx.TextCtrl(api_key_sizer.GetStaticBox(), style=wx.TE_PASSWORD)
        current_key = self._config_manager.get_setting("api_key")
        if current_key:
            self.api_key_input.SetValue(current_key)
        api_key_sizer.Add(self.api_key_input, 0, wx.EXPAND | wx.ALL, 5)
        
        main_sizer.Add(api_key_sizer, 0, wx.EXPAND | wx.ALL, 10)

        # Buttons
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Test Connection Button
        test_button = wx.Button(self, label="Test Connection")
        self.Bind(wx.EVT_BUTTON, self._on_test_connection, test_button)
        button_sizer.Add(test_button, 0, wx.RIGHT, 10)

        # Standard Buttons
        std_button_sizer = self.CreateButtonSizer(wx.OK | wx.CANCEL)
        button_sizer.Add(std_button_sizer)

        main_sizer.Add(button_sizer, 0, wx.ALIGN_CENTER | wx.ALL, 10)

        self.SetSizer(main_sizer)
        main_sizer.Fit(self)

        self.Bind(wx.EVT_BUTTON, self._on_ok, id=wx.ID_OK)

    def _on_test_connection(self, event: wx.CommandEvent) -> None:
        """Tests the entered API key."""
        api_key = self.api_key_input.GetValue()
        if not api_key:
            wx.MessageBox("Please enter an API key to test.", "No API Key", wx.OK | wx.ICON_WARNING)
            return

        # Show a busy cursor while testing
        with wx.BusyCursor():
            is_valid = AIManager.test_connection(api_key)

        if is_valid:
            wx.MessageBox("Connection successful! The API key is valid.", "Success", wx.OK | wx.ICON_INFORMATION)
        else:
            wx.MessageBox("Connection failed. Please check your API key and internet connection.", "Failure", wx.OK | wx.ICON_ERROR)

    def _on_ok(self, event: wx.CommandEvent) -> None:
        """Saves the API key and closes the dialog."""
        new_key = self.api_key_input.GetValue()
        if new_key:
            self._config_manager.set_setting("api_key", new_key)
            # No need for a message here, as the user can test it.
        
        # Check if the key has changed and if it's different from the saved one
        current_key = self._config_manager.get_setting("api_key")
        if new_key != current_key:
            self._config_manager.set_setting("api_key", new_key)
            wx.MessageBox("API key saved. Please restart the application for the changes to take full effect.", "API Key Changed", wx.OK | wx.ICON_INFORMATION)

        self.EndModal(wx.ID_OK) 