<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-yellow" Color="#996f00" Opacity="1" />
      <SolidColorBrush x:Key="white" Color="#ffffff" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M12.6,3.4A6.505,6.505,0,0,0,1.62,9.27a6.487,6.487,0,0,0,4.82,5.04l.62-1.12L8.27,11l.55-1,1.62-2.95L10.47,7h2.06l1.3,2.37.35.63a6.44,6.44,0,0,0,.32-2A6.509,6.509,0,0,0,12.6,3.4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M12.95,3.05A7,7,0,1,0,6.19,14.77l.25-.46.62-1.12A9.425,9.425,0,0,1,6,11H8.27l.55-1H5.72A9.648,9.648,0,0,1,5.5,8a9.648,9.648,0,0,1,.22-2h4.56a9.517,9.517,0,0,1,.16,1.05L10.47,7h.98c-.03-.34-.09-.67-.15-1h2.35a5.787,5.787,0,0,1,.18,3.37l.35.63.32.58A6.927,6.927,0,0,0,15,8,7.019,7.019,0,0,0,12.95,3.05ZM4.7,10H2.35a5.889,5.889,0,0,1,0-4H4.7A10.354,10.354,0,0,0,4.7,10Zm1.51,3.73A6.067,6.067,0,0,1,2.81,11H4.95A10.256,10.256,0,0,0,6.21,13.73ZM4.95,5H2.81a6.067,6.067,0,0,1,3.4-2.73A10.4,10.4,0,0,0,4.95,5ZM6,5A9.525,9.525,0,0,1,7.63,2.02C7.75,2.01,7.87,2,8,2s.25.01.37.02A9.327,9.327,0,0,1,10,5Zm5.05,0A10.577,10.577,0,0,0,9.79,2.27,6.067,6.067,0,0,1,13.19,5Z" />
              <GeometryDrawing Brush="{DynamicResource light-yellow}" Geometry="F1M15.5,16h-8l-.439-.739,4-7.261h.878l4,7.261Z" />
              <GeometryDrawing Brush="{DynamicResource white}" Geometry="F1M12,13H11V10h1Zm.25,1.5a.75.75,0,1,1-.75-.75A.75.75,0,0,1,12.25,14.5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
