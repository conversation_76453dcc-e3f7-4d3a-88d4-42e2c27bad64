"""Tests for utility functions in JEdit2."""
import unittest
from jedit2.utils.validation import detect_file_format

class TestFileFormatDetection(unittest.TestCase):
    def test_csv_detection(self):
        self.assertEqual(detect_file_format("data.csv"), "csv")
        self.assertEqual(detect_file_format("DATA.CSV"), "csv")
    def test_json_detection(self):
        self.assertEqual(detect_file_format("file.json"), "json")
        self.assertEqual(detect_file_format("test.JSON"), "json")
    def test_yaml_detection(self):
        self.assertEqual(detect_file_format("config.yaml"), "yaml")
        self.assertEqual(detect_file_format("config.yml"), "yaml")
        self.assertEqual(detect_file_format("CONFIG.YML"), "yaml")
    def test_unknown_detection(self):
        self.assertEqual(detect_file_format("notes.txt"), "unknown")
        self.assertEqual(detect_file_format("noext"), "unknown")

if __name__ == "__main__":
    unittest.main() 