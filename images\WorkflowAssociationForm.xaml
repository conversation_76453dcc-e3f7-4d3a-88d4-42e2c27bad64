<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-green" Color="#1f801f" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13.5,4.5V15H7.242c.054-.081.106-.163.155-.248l.5-.865L6.366,13h.548L8,11.914V8H5v.263A4.443,4.443,0,0,0,3.5,8,4.488,4.488,0,0,0,2,8.257V6H4V4H6V1.5h4.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,4.5v10l-.5.5H7.23c.056-.083.117-.161.167-.248L7.832,14H13V5H10V2H6V1h4.5l.354.146,3,3ZM2,8.276a4.485,4.485,0,0,1,1-.225V6H2Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M3.5,9A3.5,3.5,0,0,1,6,10.06V9H7v2.5l-.5.5H4V11H5.5a2.5,2.5,0,1,0,.167,2.75l.865.5A3.5,3.5,0,1,1,3.5,9Z" />
              <GeometryDrawing Brush="{DynamicResource light-green}" Geometry="F1M5,3H3V5H2V3H0V2H2V0H3V2H5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
