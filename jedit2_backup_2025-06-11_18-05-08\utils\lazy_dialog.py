"""Lazy loading dialog module for JEdit2.

This module provides a dialog for managing lazy loading settings and viewing loading statistics.
"""

import wx
import wx.grid
from typing import Optional, Dict, Any
from .lazy_loader import <PERSON><PERSON><PERSON><PERSON><PERSON>, LoadStrategy, LoadStats


class LazyDialog(wx.Dialog):
    """Dialog for managing lazy loading settings and viewing loading statistics."""
    
    def __init__(
        self,
        parent: wx.Window,
        lazy_loader: <PERSON><PERSON><PERSON>oa<PERSON>,
        title: str = "Lazy Loading Settings",
        size: tuple = (600, 400)
    ) -> None:
        """Initialize the dialog.
        
        Args:
            parent: Parent window
            lazy_loader: Lazy loader instance
            title: Dialog title
            size: Dialog size
        """
        super().__init__(parent, title=title, size=size)
        
        self.lazy_loader = lazy_loader
        self._init_ui()
        self._bind_events()
        self._populate_grid()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create notebook
        notebook = wx.Notebook(self)
        
        # Create settings page
        settings_panel = wx.Panel(notebook)
        settings_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Strategy selection
        strategy_sizer = wx.BoxSizer(wx.HORIZONTAL)
        strategy_label = wx.StaticText(settings_panel, label="Loading Strategy:")
        self.strategy_choice = wx.Choice(
            settings_panel,
            choices=[s.value for s in LoadStrategy]
        )
        self.strategy_choice.SetSelection(0)
        strategy_sizer.Add(strategy_label, 0, wx.ALL | wx.CENTER, 5)
        strategy_sizer.Add(self.strategy_choice, 1, wx.ALL | wx.EXPAND, 5)
        settings_sizer.Add(strategy_sizer, 0, wx.EXPAND)
        
        # Chunk size
        chunk_sizer = wx.BoxSizer(wx.HORIZONTAL)
        chunk_label = wx.StaticText(settings_panel, label="Chunk Size (KB):")
        self.chunk_size = wx.SpinCtrl(
            settings_panel,
            min=1,
            max=1024,
            initial=1024
        )
        chunk_sizer.Add(chunk_label, 0, wx.ALL | wx.CENTER, 5)
        chunk_sizer.Add(self.chunk_size, 1, wx.ALL | wx.EXPAND, 5)
        settings_sizer.Add(chunk_sizer, 0, wx.EXPAND)
        
        # Max chunks
        max_chunks_sizer = wx.BoxSizer(wx.HORIZONTAL)
        max_chunks_label = wx.StaticText(settings_panel, label="Max Chunks:")
        self.max_chunks = wx.SpinCtrl(
            settings_panel,
            min=1,
            max=1000,
            initial=100
        )
        max_chunks_sizer.Add(max_chunks_label, 0, wx.ALL | wx.CENTER, 5)
        max_chunks_sizer.Add(self.max_chunks, 1, wx.ALL | wx.EXPAND, 5)
        settings_sizer.Add(max_chunks_sizer, 0, wx.EXPAND)
        
        # Look ahead
        look_ahead_sizer = wx.BoxSizer(wx.HORIZONTAL)
        look_ahead_label = wx.StaticText(settings_panel, label="Look Ahead:")
        self.look_ahead = wx.SpinCtrl(
            settings_panel,
            min=0,
            max=100,
            initial=5
        )
        look_ahead_sizer.Add(look_ahead_label, 0, wx.ALL | wx.CENTER, 5)
        look_ahead_sizer.Add(self.look_ahead, 1, wx.ALL | wx.EXPAND, 5)
        settings_sizer.Add(look_ahead_sizer, 0, wx.EXPAND)
        
        settings_panel.SetSizer(settings_sizer)
        notebook.AddPage(settings_panel, "Settings")
        
        # Create statistics page
        stats_panel = wx.Panel(notebook)
        stats_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Create grid
        self.stats_grid = wx.grid.Grid(stats_panel)
        self.stats_grid.CreateGrid(8, 2)
        self.stats_grid.SetColLabelValue(0, "Metric")
        self.stats_grid.SetColLabelValue(1, "Value")
        
        # Set row labels
        self.stats_grid.SetRowLabelValue(0, "Total Chunks")
        self.stats_grid.SetRowLabelValue(1, "Loaded Chunks")
        self.stats_grid.SetRowLabelValue(2, "Total Size")
        self.stats_grid.SetRowLabelValue(3, "Loaded Size")
        self.stats_grid.SetRowLabelValue(4, "Load Time")
        self.stats_grid.SetRowLabelValue(5, "Last Load")
        self.stats_grid.SetRowLabelValue(6, "Cache Hits")
        self.stats_grid.SetRowLabelValue(7, "Cache Misses")
        
        # Set column widths
        self.stats_grid.SetColSize(0, 150)
        self.stats_grid.SetColSize(1, 200)
        
        stats_sizer.Add(self.stats_grid, 1, wx.ALL | wx.EXPAND, 5)
        stats_panel.SetSizer(stats_sizer)
        notebook.AddPage(stats_panel, "Statistics")
        
        main_sizer.Add(notebook, 1, wx.ALL | wx.EXPAND, 5)
        
        # Create buttons
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.apply_btn = wx.Button(self, label="Apply")
        self.clear_btn = wx.Button(self, label="Clear Cache")
        self.close_btn = wx.Button(self, label="Close")
        
        button_sizer.Add(self.apply_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.clear_btn, 0, wx.ALL, 5)
        button_sizer.AddStretchSpacer()
        button_sizer.Add(self.close_btn, 0, wx.ALL, 5)
        
        main_sizer.Add(button_sizer, 0, wx.EXPAND | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
    
    def _bind_events(self) -> None:
        """Bind events."""
        self.apply_btn.Bind(wx.EVT_BUTTON, self._on_apply)
        self.clear_btn.Bind(wx.EVT_BUTTON, self._on_clear)
        self.close_btn.Bind(wx.EVT_BUTTON, self._on_close)
    
    def _populate_grid(self) -> None:
        """Populate the statistics grid."""
        stats = self.lazy_loader.get_stats()
        
        self.stats_grid.SetCellValue(0, 1, str(stats.total_chunks))
        self.stats_grid.SetCellValue(1, 1, str(stats.loaded_chunks))
        self.stats_grid.SetCellValue(2, 1, f"{stats.total_size / 1024:.2f} KB")
        self.stats_grid.SetCellValue(3, 1, f"{stats.loaded_size / 1024:.2f} KB")
        self.stats_grid.SetCellValue(4, 1, f"{stats.load_time:.2f} s")
        self.stats_grid.SetCellValue(5, 1, f"{stats.last_load:.2f} s")
        self.stats_grid.SetCellValue(6, 1, str(stats.hits))
        self.stats_grid.SetCellValue(7, 1, str(stats.misses))
    
    def _on_apply(self, event: wx.CommandEvent) -> None:
        """Handle apply button event."""
        try:
            # Update settings
            strategy = LoadStrategy(self.strategy_choice.GetStringSelection())
            chunk_size = self.chunk_size.GetValue() * 1024  # Convert to bytes
            max_chunks = self.max_chunks.GetValue()
            
            self.lazy_loader.set_strategy(strategy)
            self.lazy_loader.set_chunk_size(chunk_size)
            self.lazy_loader.set_max_chunks(max_chunks)
            
            wx.MessageBox(
                "Settings applied successfully.",
                "Success",
                wx.OK | wx.ICON_INFORMATION
            )
        except Exception as e:
            wx.MessageBox(
                str(e),
                "Error",
                wx.OK | wx.ICON_ERROR
            )
    
    def _on_clear(self, event: wx.CommandEvent) -> None:
        """Handle clear button event."""
        if wx.MessageBox(
            "Clear the chunk cache?",
            "Confirm",
            wx.YES_NO | wx.ICON_QUESTION
        ) == wx.YES:
            self.lazy_loader.clear_cache()
            self._populate_grid()
    
    def _on_close(self, event: wx.CommandEvent) -> None:
        """Handle close button event."""
        self.EndModal(wx.ID_CLOSE) 