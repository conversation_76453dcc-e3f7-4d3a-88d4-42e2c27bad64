<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-purple-10" Color="#6936aa" Opacity="0.1" />
      <SolidColorBrush x:Key="light-purple" Color="#6936aa" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M.4,12.51l1.313-.263a.678.678,0,0,0,.532-.532L2.51,10.4a.5.5,0,0,1,.98,0l.263,1.313a.678.678,0,0,0,.532.532L5.6,12.51a.5.5,0,0,1,0,.98l-1.313.263a.678.678,0,0,0-.532.532L3.49,15.6a.5.5,0,0,1-.98,0l-.263-1.313a.678.678,0,0,0-.532-.532L.4,13.49a.5.5,0,0,1,0-.98Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-purple-10}" Geometry="F1M10.019,2.549,14.5,5.031V10.44l-4.5,2.6-4.5-2.6V5.031Z" />
                <GeometryDrawing Brush="{DynamicResource light-purple}" Geometry="F1M14.75,4.6,10.25,2h-.5L5.25,4.6,5,5.031V10.44l.25.433,4.5,2.6h.5l4.5-2.6L15,10.44V5.031ZM14,10.151l-3.5,2.021V7.825h-1v4.347L6,10.151V5.32l4-2.31,4,2.31Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-purple}" Geometry="F1M10.263,8.119H9.757L5.241,5.462,5.249,4.6,9.75,2h.5l4.5,2.6.028.886ZM6.5,5.04l3.517,2.068,3.515-2.05-3.51-2.012Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
