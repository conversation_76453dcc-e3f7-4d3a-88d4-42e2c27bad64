#!/usr/bin/env python3
"""
Test the critical fixes for the remaining failing cases.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_critical_fixes():
    """Test the critical fixes for previously failing cases."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    # Focus on the 4 cases that were failing
    critical_cases = [
        {
            'name': 'JSON: Missing Commas Between Properties',
            'content': '{"name": "John" "age": 30 "city": "Boston"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Array Without Brackets',
            'content': '"apple", "banana", "cherry"',
            'file_type': 'json'
        },
        {
            'name': 'CSV: Quote Escaping Issues',
            'content': 'name,description\nJohn,"He said "hello" to me"\nJane,"She\'s nice"',
            'file_type': 'csv'
        },
        {
            'name': 'JSON: Complex Missing Commas',
            'content': '{"name": "John" "age": 30 "active": true "city": "Boston"}',
            'file_type': 'json'
        },
        {
            'name': 'JSON: Simple Array Case',
            'content': '"red", "green", "blue"',
            'file_type': 'json'
        }
    ]
    
    print("🔧 TESTING CRITICAL FIXES")
    print("=" * 60)
    print("Testing fixes for previously failing cases...")
    print()
    
    results = []
    
    for i, case in enumerate(critical_cases, 1):
        print(f"{i}. {case['name']}")
        print("-" * 50)
        print(f"   Content: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], case['file_type'])
        print(f"   Correctable: {validation_result.is_correctable}")
        
        if validation_result.warnings:
            print(f"   Warnings: {validation_result.warnings}")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], case['file_type'])
            
            if correction_result.success:
                print(f"   ✅ SUCCESS!")
                print(f"   Changes: {correction_result.changes_made}")
                
                # Verify the result
                result = correction_result.corrected_content
                print(f"   Result: {repr(result[:100])}{'...' if len(result) > 100 else ''}")
                
                # For JSON, verify it's valid
                if case['file_type'] == 'json':
                    try:
                        import json
                        parsed = json.loads(result)
                        print(f"   ✅ Valid JSON with {len(parsed) if isinstance(parsed, (dict, list)) else 'scalar'} items")
                    except json.JSONDecodeError as e:
                        print(f"   ❌ Invalid JSON: {e}")
                        success = False
                    else:
                        success = True
                else:
                    success = True
                    
            else:
                print(f"   ❌ CORRECTION FAILED")
                print(f"   Errors: {correction_result.errors}")
        else:
            print(f"   ❌ NOT DETECTED AS CORRECTABLE")
        
        results.append({
            'name': case['name'],
            'success': success
        })
        print()
    
    # Summary
    print("=" * 60)
    print("📊 CRITICAL FIXES SUMMARY")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for r in results if r['success'])
    
    print(f"Critical Tests: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL CRITICAL FIXES WORKING!")
        print("The format correction system is now highly robust!")
    elif passed >= total * 0.8:
        print("\n👍 MOST CRITICAL FIXES WORKING!")
        print("System is ready for production with minor gaps.")
    else:
        print("\n🔧 MORE WORK NEEDED on critical cases.")
        
        print("\nFailed cases:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}")
    
    return passed, total

if __name__ == "__main__":
    print("🧪 CRITICAL FIXES TEST")
    print("=" * 40)
    
    passed, total = test_critical_fixes()
    
    print(f"\n🎯 RESULT: {passed}/{total} critical fixes working")
    
    if passed == total:
        print("🚀 Ready to run full coverage analysis!")
    else:
        print("🔧 Debug remaining issues before full test.")
