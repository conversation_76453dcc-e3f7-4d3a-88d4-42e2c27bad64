#!/usr/bin/env python3
"""
Integration test for format correction functionality in the main application.
"""

import sys
import os
import wx

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class FormatCorrectionIntegrationTest(wx.App):
    """Test application for format correction integration."""
    
    def OnInit(self):
        """Initialize the test application."""
        try:
            from jedit2.main_window import MainWindow
            
            # Create main window
            self.frame = MainWindow()
            self.frame.Show()
            
            # Test format correction after a short delay
            wx.CallAfter(self.test_format_correction_integration)
            
            return True
        except Exception as e:
            print(f"❌ Failed to initialize test app: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_format_correction_integration(self):
        """Test the format correction integration."""
        print("🧪 Testing Format Correction Integration")
        print("=" * 60)
        
        try:
            # Test 1: Check if all correction components are available
            print("🔧 Checking correction components...")
            
            if hasattr(self.frame, 'format_corrector'):
                print("✅ format_corrector is available")
            else:
                print("❌ format_corrector not found")
                return
            
            if hasattr(self.frame, 'backup_manager'):
                print("✅ backup_manager is available")
            else:
                print("❌ backup_manager not found")
                return
            
            if hasattr(self.frame, '_apply_format_correction'):
                print("✅ _apply_format_correction method is available")
            else:
                print("❌ _apply_format_correction method not found")
                return
            
            # Test 2: Create test content with errors
            print("\n📄 Testing with JSON content containing errors...")
            
            # Get the current page or create one
            if self.frame.notebook.GetPageCount() == 0:
                from jedit2.view_container import ViewContainer
                new_tab = ViewContainer(self.frame.notebook)
                self.frame.notebook.AddPage(new_tab, "test_errors.json")
            
            current_page = self.frame.notebook.GetCurrentPage()
            
            # Add JSON content with errors
            test_json_with_errors = '''{
    "name": "Test File",
    "version": 1.0,
    "features": ["validation", "testing"],
    "valid": true,
}'''  # Trailing comma error
            
            if hasattr(current_page, 'text_view'):
                current_page.text_view.SetText(test_json_with_errors)
                print("✅ Test JSON content with errors added")
            else:
                print("❌ No text view available")
                return
            
            # Test 3: Validate and check if correction is offered
            print("\n🔍 Testing validation with correction detection...")
            
            result = self.frame.format_validator.validate_format(test_json_with_errors, "json")
            print(f"✅ Validation completed:")
            print(f"   Valid: {result.is_valid}")
            print(f"   Correctable: {result.is_correctable}")
            print(f"   Errors: {len(result.errors)}")
            print(f"   Warnings: {len(result.warnings)}")
            print(f"   Suggestions: {result.correction_suggestions}")
            
            # Test 4: Test correction generation
            print("\n🔧 Testing correction generation...")
            
            correction_result = self.frame.format_corrector.correct_format(test_json_with_errors, "json")
            print(f"✅ Correction completed:")
            print(f"   Success: {correction_result.success}")
            print(f"   Changes made: {correction_result.changes_made}")
            if correction_result.errors:
                print(f"   Errors: {correction_result.errors}")
            
            # Test 5: Test backup creation
            print("\n💾 Testing backup creation...")
            
            backup_path = self.frame.backup_manager.create_backup("test_errors.json", test_json_with_errors)
            print(f"✅ Backup created: {backup_path}")
            
            # Test 6: Simulate the full correction dialog workflow
            print("\n🖼️ Testing correction dialog workflow...")
            
            # This would normally be done through the UI, but we can test the logic
            if result.is_correctable:
                print("✅ Correction dialog would be shown")
                print("   User would see:")
                print(f"   - Validation errors: {result.errors}")
                print(f"   - Correction suggestions: {result.correction_suggestions}")
                print(f"   - Preview of corrections: {correction_result.changes_made}")
                print("   - Option to create backup")
                print("   - Apply corrections button")
            else:
                print("❌ No correction options would be available")
            
            print(f"\n🎉 Format correction integration testing completed!")
            print(f"💡 Manual test steps:")
            print(f"   1. Open one of the test files created earlier")
            print(f"   2. Go to Tools > Format Validation")
            print(f"   3. Check if the new correction dialog appears")
            print(f"   4. Try 'Preview Corrections' button")
            print(f"   5. Try 'Apply Corrections' with backup option")
            
            # Close after a delay
            wx.CallLater(5000, self.frame.Close)
            
        except Exception as e:
            print(f"❌ Integration test failed: {e}")
            import traceback
            traceback.print_exc()


def test_correction_components():
    """Test individual correction components."""
    print("🔧 Testing Format Correction Components")
    print("=" * 50)
    
    try:
        # Test imports
        from jedit2.utils.format_corrector import FormatCorrector, CorrectionResult
        from jedit2.utils.format_correction_dialog import FormatCorrectionDialog
        from jedit2.utils.backup_manager import BackupManager
        from jedit2.utils.format_validator import ValidationResult
        
        print("✅ All correction components imported successfully")
        
        # Test ValidationResult with new fields
        result = ValidationResult(
            is_valid=False,
            errors=["Test error"],
            warnings=["Test warning"],
            is_correctable=True,
            correction_suggestions=["Fix test issue"]
        )
        print("✅ Extended ValidationResult works correctly")
        print(f"   Is correctable: {result.is_correctable}")
        print(f"   Suggestions: {result.correction_suggestions}")
        
        # Test CorrectionResult
        correction = CorrectionResult(
            success=True,
            corrected_content="Fixed content",
            changes_made=["Fixed issue 1", "Fixed issue 2"],
            errors=[],
            warnings=[]
        )
        print("✅ CorrectionResult works correctly")
        print(f"   Success: {correction.success}")
        print(f"   Changes: {correction.changes_made}")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Format Correction Integration Test Suite")
    print("=" * 60)
    
    # Test components first
    if not test_correction_components():
        print("❌ Component tests failed. Exiting.")
        sys.exit(1)
    
    print("\n🖼️ Starting GUI Integration Test...")
    print("=" * 40)
    
    # Run the GUI integration test
    app = FormatCorrectionIntegrationTest()
    app.MainLoop()
