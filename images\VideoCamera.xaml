<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M15.5,4.555v6.89a.5.5,0,0,1-.817.387L11.5,9.567V12.5H.5v-9h11V6.433l3.183-2.265A.5.5,0,0,1,15.5,4.555Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M15.428,3.651a.973.973,0,0,0-1.034.11L12,5.463V3.5L11.5,3H.5L0,3.5v9l.5.5h11l.5-.5V10.536l2.367,1.683a.992.992,0,0,0,.631.227,1.009,1.009,0,0,0,.43-.1.992.992,0,0,0,.572-.9V4.555A.992.992,0,0,0,15.428,3.651ZM11,12H1V4H11Zm3.974-.575L12,9.31V6.69l3-2.135Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
