#!/usr/bin/env python3
"""
JEdit2 AI System Integration

Complete integration of all AI improvement components:
- Enhanced AI Manager with testing framework
- Automated testing pipeline
- Production monitoring system
- Real-time feedback loops

This provides the main entry point for the integrated AI system.
"""

import logging
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Import all our enhanced components
try:
    from .ai_manager_enhanced import EnhancedAIManager
    from .automated_testing_pipeline import AutomatedTestingPipeline
    from .production_monitor import ProductionMonitor
except ImportError:
    try:
        from ai_manager_enhanced import EnhancedAIManager
        from automated_testing_pipeline import AutomatedTestingPipeline
        from production_monitor import ProductionMonitor
    except ImportError:
        # Create minimal stubs if components are missing
        class EnhancedAIManager:
            def __init__(self, *args, **kwargs):
                pass
            def get_ai_response_enhanced(self, query, validate_state=True, before_state=None):
                return None, "Enhanced AI Manager not available", {}

        class AutomatedTestingPipeline:
            def __init__(self, *args, **kwargs):
                pass
            def start_pipeline(self):
                pass
            def stop_pipeline(self):
                pass
            def get_pipeline_status(self):
                return {"is_running": False, "tests_in_queue": 0, "tests_executed": 0}

        class ProductionMonitor:
            def __init__(self, *args, **kwargs):
                pass
            def start_monitoring(self):
                pass
            def stop_monitoring(self):
                pass
            def record_query_result(self, **kwargs):
                pass
            def get_monitoring_status(self):
                return {"is_monitoring": False, "metrics_collected": 0, "active_alerts": 0}
            def get_current_metrics(self):
                class MockMetrics:
                    success_rate = 1.0
                    average_response_time = 0.0
                    total_queries = 0
                return MockMetrics()
            def register_alert_handler(self, level, handler):
                pass


class JEdit2AISystem:
    """
    Complete integrated AI system for JEdit2.

    Combines all improvement components into a unified system:
    - Enhanced AI Manager with real-time testing
    - Automated testing pipeline with continuous validation
    - Production monitoring with adaptive feedback loops
    - Comprehensive reporting and analytics
    """

    def __init__(self, main_window, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the complete AI system.

        Args:
            main_window: JEdit2 main window instance
            config: System configuration
        """
        self.logger = logging.getLogger(__name__)
        self.main_window = main_window

        # System configuration
        self.config = self._initialize_config(config or {})

        # Core components
        self.enhanced_ai_manager = None
        self.testing_pipeline = None
        self.production_monitor = None

        # System state
        self.is_initialized = False
        self.is_running = False
        self.initialization_time = None

        # Performance tracking
        self.system_metrics = {
            "total_queries_processed": 0,
            "system_uptime": 0.0,
            "components_status": {},
            "last_health_check": None,
        }

    def _initialize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize system configuration with defaults."""
        default_config = {
            "enable_enhanced_ai": True,
            "enable_automated_testing": True,
            "enable_production_monitoring": True,
            "testing_config": {
                "execution_schedule": "hourly",
                "parallel_execution": True,
                "max_parallel_tests": 3,
                "enable_auto_testing": True,
            },
            "monitoring_config": {
                "monitoring_interval": 60.0,
                "enable_automated_feedback": True,
                "performance_thresholds": {
                    "success_rate_warning": 0.95,
                    "success_rate_error": 0.90,
                    "response_time_warning": 5.0,
                    "response_time_error": 10.0,
                },
            },
            "integration_config": {
                "enable_shadow_mode": False,  # Run alongside existing system
                "enable_real_time_validation": True,
                "enable_performance_monitoring": True,
                "log_level": "INFO",
            },
        }

        # Deep merge configurations
        def deep_merge(default, override):
            for key, value in override.items():
                if (
                    key in default
                    and isinstance(default[key], dict)
                    and isinstance(value, dict)
                ):
                    deep_merge(default[key], value)
                else:
                    default[key] = value

        deep_merge(default_config, config)
        return default_config

    def initialize_system(self) -> bool:
        """
        Initialize all system components.

        Returns:
            True if successful, False otherwise
        """
        if self.is_initialized:
            self.logger.warning("System already initialized")
            return True

        try:
            self.logger.info("Initializing JEdit2 AI System...")

            # Initialize Enhanced AI Manager
            if self.config["enable_enhanced_ai"]:
                success = self._initialize_enhanced_ai_manager()
                if not success:
                    self.logger.error("Failed to initialize Enhanced AI Manager")
                    return False
                self.system_metrics["components_status"]["enhanced_ai"] = "initialized"

            # Initialize Automated Testing Pipeline
            if self.config["enable_automated_testing"]:
                success = self._initialize_testing_pipeline()
                if not success:
                    self.logger.error("Failed to initialize Testing Pipeline")
                    return False
                self.system_metrics["components_status"][
                    "testing_pipeline"
                ] = "initialized"

            # Initialize Production Monitoring
            if self.config["enable_production_monitoring"]:
                success = self._initialize_production_monitor()
                if not success:
                    self.logger.error("Failed to initialize Production Monitor")
                    return False
                self.system_metrics["components_status"][
                    "production_monitor"
                ] = "initialized"

            # Cross-component integration
            self._setup_component_integration()

            self.is_initialized = True
            self.initialization_time = datetime.now()

            self.logger.info("JEdit2 AI System initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"System initialization failed: {e}")
            return False

    def _initialize_enhanced_ai_manager(self) -> bool:
        """Initialize the enhanced AI manager."""
        try:
            # Create enhanced AI manager
            self.enhanced_ai_manager = EnhancedAIManager(
                base_ai_manager=self.main_window.ai_manager,
                enable_testing=True,
                testing_config=self.config["testing_config"],
            )

            # Integrate with main window if not in shadow mode
            if not self.config["integration_config"]["enable_shadow_mode"]:
                # Direct integration without separate function
                self.main_window.enhanced_ai_manager = self.enhanced_ai_manager

            self.logger.info("Enhanced AI Manager initialized")
            return True

        except Exception as e:
            self.logger.error(f"Enhanced AI Manager initialization failed: {e}")
            return False

    def _initialize_testing_pipeline(self) -> bool:
        """Initialize the automated testing pipeline."""
        try:
            # Use enhanced AI manager if available, otherwise fallback
            ai_manager = self.enhanced_ai_manager or self.main_window.ai_manager

            self.testing_pipeline = AutomatedTestingPipeline(
                ai_manager=ai_manager, config=self.config["testing_config"]
            )

            self.logger.info("Automated Testing Pipeline initialized")
            return True

        except Exception as e:
            self.logger.error(f"Testing Pipeline initialization failed: {e}")
            return False

    def _initialize_production_monitor(self) -> bool:
        """Initialize the production monitoring system."""
        try:
            self.production_monitor = ProductionMonitor(
                config=self.config["monitoring_config"]
            )

            # Register integration-specific alert handlers
            self._setup_monitoring_integration()

            self.logger.info("Production Monitor initialized")
            return True

        except Exception as e:
            self.logger.error(f"Production Monitor initialization failed: {e}")
            return False

    def _setup_component_integration(self) -> None:
        """Setup integration between components."""
        try:
            # Connect enhanced AI manager to production monitor
            if self.enhanced_ai_manager and self.production_monitor:
                # Hook AI responses to monitoring
                original_get_response = (
                    self.enhanced_ai_manager.get_ai_response_enhanced
                )

                def monitored_get_response(
                    query, validate_state=True, before_state=None
                ):
                    start_time = datetime.now()
                    result = original_get_response(query, validate_state, before_state)

                    # Extract monitoring data
                    commands, raw_response, analysis_data = result
                    success = commands is not None
                    response_time = (datetime.now() - start_time).total_seconds()

                    # Report to monitor
                    self.production_monitor.record_query_result(
                        query=query,
                        success=success,
                        response_time=response_time,
                        analysis_data=analysis_data,
                    )

                    return result

                # Replace method
                self.enhanced_ai_manager.get_ai_response_enhanced = (
                    monitored_get_response
                )

            # Connect testing pipeline to monitoring
            if self.testing_pipeline and self.production_monitor:
                # Future: Could add test results to monitoring
                pass

            self.logger.info("Component integration completed")

        except Exception as e:
            self.logger.error(f"Component integration failed: {e}")

    def _setup_monitoring_integration(self) -> None:
        """Setup monitoring integration with system-level handlers."""
        if not self.production_monitor:
            return

        try:
            # Register system-level alert handlers
            from production_monitor import AlertLevel

            def system_critical_handler(alert):
                self.logger.critical(f"SYSTEM CRITICAL: {alert.message}")
                # Could trigger emergency procedures

            def system_error_handler(alert):
                self.logger.error(f"SYSTEM ERROR: {alert.message}")
                # Could trigger automatic recovery

            self.production_monitor.register_alert_handler(
                AlertLevel.CRITICAL, system_critical_handler
            )
            self.production_monitor.register_alert_handler(
                AlertLevel.ERROR, system_error_handler
            )

            self.logger.info("Monitoring integration handlers registered")

        except Exception as e:
            self.logger.error(f"Monitoring integration setup failed: {e}")

    def start_system(self) -> bool:
        """
        Start all system components.

        Returns:
            True if successful, False otherwise
        """
        if not self.is_initialized:
            self.logger.error("System not initialized - call initialize_system() first")
            return False

        if self.is_running:
            self.logger.warning("System already running")
            return True

        try:
            self.logger.info("Starting JEdit2 AI System...")

            # Start production monitoring first (for early detection)
            if self.production_monitor:
                self.production_monitor.start_monitoring()
                self.system_metrics["components_status"][
                    "production_monitor"
                ] = "running"
                self.logger.info("Production Monitor started")

            # Start testing pipeline
            if self.testing_pipeline:
                self.testing_pipeline.start_pipeline()
                self.system_metrics["components_status"]["testing_pipeline"] = "running"
                self.logger.info("Testing Pipeline started")

            # Enhanced AI manager is always running (integrated with main window)
            if self.enhanced_ai_manager:
                self.system_metrics["components_status"]["enhanced_ai"] = "running"
                self.logger.info("Enhanced AI Manager active")

            self.is_running = True
            self.logger.info("JEdit2 AI System started successfully")

            # Run initial health check
            self.run_health_check()

            return True

        except Exception as e:
            self.logger.error(f"System startup failed: {e}")
            return False

    def stop_system(self) -> bool:
        """
        Stop all system components.

        Returns:
            True if successful, False otherwise
        """
        if not self.is_running:
            self.logger.warning("System not running")
            return True

        try:
            self.logger.info("Stopping JEdit2 AI System...")

            # Stop testing pipeline
            if self.testing_pipeline:
                self.testing_pipeline.stop_pipeline()
                self.system_metrics["components_status"]["testing_pipeline"] = "stopped"
                self.logger.info("Testing Pipeline stopped")

            # Stop production monitoring
            if self.production_monitor:
                self.production_monitor.stop_monitoring()
                self.system_metrics["components_status"][
                    "production_monitor"
                ] = "stopped"
                self.logger.info("Production Monitor stopped")

            # Enhanced AI manager remains active (part of main window)
            if self.enhanced_ai_manager:
                self.system_metrics["components_status"]["enhanced_ai"] = "standby"
                self.logger.info("Enhanced AI Manager on standby")

            self.is_running = False
            self.logger.info("JEdit2 AI System stopped successfully")

            return True

        except Exception as e:
            self.logger.error(f"System shutdown failed: {e}")
            return False

    def run_health_check(self) -> Dict[str, Any]:
        """
        Run comprehensive system health check.

        Returns:
            Health check results
        """
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "components": {},
            "metrics": {},
            "alerts": [],
            "recommendations": [],
        }

        try:
            # Check Enhanced AI Manager
            if self.enhanced_ai_manager:
                # Test with a simple query
                try:
                    test_query = "test system health"
                    start_time = datetime.now()
                    result = self.enhanced_ai_manager.get_ai_response_enhanced(
                        test_query
                    )
                    response_time = (datetime.now() - start_time).total_seconds()

                    health_status["components"]["enhanced_ai"] = {
                        "status": "healthy",
                        "response_time": response_time,
                        "last_check": datetime.now().isoformat(),
                    }
                except Exception as e:
                    health_status["components"]["enhanced_ai"] = {
                        "status": "error",
                        "error": str(e),
                        "last_check": datetime.now().isoformat(),
                    }
                    health_status["overall_status"] = "degraded"

            # Check Testing Pipeline
            if self.testing_pipeline:
                try:
                    pipeline_status = self.testing_pipeline.get_pipeline_status()
                    health_status["components"]["testing_pipeline"] = {
                        "status": (
                            "healthy" if pipeline_status["is_running"] else "inactive"
                        ),
                        "tests_in_queue": pipeline_status["tests_in_queue"],
                        "tests_executed": pipeline_status["tests_executed"],
                    }
                except Exception as e:
                    health_status["components"]["testing_pipeline"] = {
                        "status": "error",
                        "error": str(e),
                    }
                    health_status["overall_status"] = "degraded"

            # Check Production Monitor
            if self.production_monitor:
                try:
                    monitor_status = self.production_monitor.get_monitoring_status()
                    current_metrics = self.production_monitor.get_current_metrics()

                    health_status["components"]["production_monitor"] = {
                        "status": (
                            "healthy" if monitor_status["is_monitoring"] else "inactive"
                        ),
                        "metrics_collected": monitor_status["metrics_collected"],
                        "active_alerts": monitor_status["active_alerts"],
                    }

                    health_status["metrics"] = {
                        "success_rate": current_metrics.success_rate,
                        "average_response_time": current_metrics.average_response_time,
                        "total_queries": current_metrics.total_queries,
                    }

                    # Check for concerning metrics
                    if current_metrics.success_rate < 0.95:
                        health_status["alerts"].append(
                            f"Low success rate: {current_metrics.success_rate:.1%}"
                        )
                        health_status["overall_status"] = "warning"

                    if current_metrics.average_response_time > 5.0:
                        health_status["alerts"].append(
                            f"High response time: {current_metrics.average_response_time:.2f}s"
                        )
                        if health_status["overall_status"] == "healthy":
                            health_status["overall_status"] = "warning"

                except Exception as e:
                    health_status["components"]["production_monitor"] = {
                        "status": "error",
                        "error": str(e),
                    }
                    health_status["overall_status"] = "degraded"

            # Generate recommendations
            if health_status["overall_status"] != "healthy":
                if any(
                    comp.get("status") == "error"
                    for comp in health_status["components"].values()
                ):
                    health_status["recommendations"].append(
                        "Check system logs for error details"
                    )
                    health_status["recommendations"].append(
                        "Consider restarting failed components"
                    )

                if health_status["metrics"].get("success_rate", 1.0) < 0.95:
                    health_status["recommendations"].append(
                        "Review recent AI queries for failure patterns"
                    )
                    health_status["recommendations"].append(
                        "Check if additional training data is needed"
                    )

            # Update system metrics
            self.system_metrics["last_health_check"] = datetime.now().isoformat()

            self.logger.info(
                f"Health check completed: {health_status['overall_status']}"
            )

        except Exception as e:
            health_status["overall_status"] = "error"
            health_status["error"] = str(e)
            self.logger.error(f"Health check failed: {e}")

        return health_status

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            "system_info": {
                "initialized": self.is_initialized,
                "running": self.is_running,
                "initialization_time": (
                    self.initialization_time.isoformat()
                    if self.initialization_time
                    else None
                ),
                "uptime": (
                    (datetime.now() - self.initialization_time).total_seconds()
                    if self.initialization_time
                    else 0
                ),
            },
            "components": self.system_metrics["components_status"].copy(),
            "configuration": self.config,
            "metrics": self.system_metrics.copy(),
        }

        # Add component-specific status
        if self.enhanced_ai_manager:
            status["enhanced_ai_metrics"] = self.enhanced_ai_manager.metrics

        if self.testing_pipeline:
            status["testing_pipeline_status"] = (
                self.testing_pipeline.get_pipeline_status()
            )

        if self.production_monitor:
            status["monitoring_status"] = (
                self.production_monitor.get_monitoring_status()
            )

        return status

    def generate_system_report(self) -> Dict[str, Any]:
        """Generate comprehensive system report."""
        report = {
            "report_info": {
                "generated_at": datetime.now().isoformat(),
                "system_version": "1.0.0",
                "report_type": "comprehensive_system_status",
            },
            "system_status": self.get_system_status(),
            "health_check": self.run_health_check(),
            "component_reports": {},
        }

        # Add component-specific reports
        try:
            if self.enhanced_ai_manager:
                report["component_reports"][
                    "enhanced_ai"
                ] = self.enhanced_ai_manager.get_comprehensive_report()

            if self.testing_pipeline:
                # Get recent test executions for report
                recent_tests = self.testing_pipeline.executed_tests[
                    -10:
                ]  # Last 10 tests
                if recent_tests:
                    test_report = self.testing_pipeline.generate_execution_report(
                        recent_tests
                    )
                    report["component_reports"]["testing_pipeline"] = {
                        "recent_execution": test_report.__dict__,
                        "pipeline_status": self.testing_pipeline.get_pipeline_status(),
                    }

            if self.production_monitor:
                report["component_reports"]["production_monitor"] = {
                    "alert_summary": self.production_monitor.get_alert_summary(),
                    "feedback_summary": self.production_monitor.get_feedback_summary(),
                    "current_metrics": self.production_monitor.get_current_metrics().__dict__,
                }

        except Exception as e:
            report["report_generation_error"] = str(e)
            self.logger.error(f"Report generation error: {e}")

        return report

    def export_system_report(self, filepath: str) -> bool:
        """Export comprehensive system report to file."""
        try:
            report = self.generate_system_report()

            # Ensure directory exists
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)

            with open(filepath, "w") as f:
                json.dump(report, f, indent=2, default=str)

            self.logger.info(f"System report exported to {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export system report: {e}")
            return False


def integrate_ai_system(
    main_window, config: Optional[Dict[str, Any]] = None
) -> JEdit2AISystem:
    """
    Main integration function for JEdit2 AI System.

    Args:
        main_window: JEdit2 main window instance
        config: System configuration

    Returns:
        Initialized AI system instance
    """
    logger = logging.getLogger(__name__)

    try:
        # Create and initialize the system
        ai_system = JEdit2AISystem(main_window, config)

        if not ai_system.initialize_system():
            logger.error("Failed to initialize AI system")
            return None

        # Start the system
        if not ai_system.start_system():
            logger.error("Failed to start AI system")
            return None

        # Add system reference to main window
        main_window.ai_system = ai_system

        # Add convenient methods to main window
        main_window.get_ai_system_status = ai_system.get_system_status
        main_window.get_ai_system_report = ai_system.generate_system_report
        main_window.run_ai_health_check = ai_system.run_health_check

        logger.info("JEdit2 AI System integration completed successfully")
        return ai_system

    except Exception as e:
        logger.error(f"AI system integration failed: {e}")
        return None


def integrate_ai_system(main_window, config: Optional[Dict[str, Any]] = None) -> Optional[JEdit2AISystem]:
    """
    Create and integrate the complete AI system.

    Args:
        main_window: JEdit2 main window instance
        config: System configuration

    Returns:
        JEdit2AISystem instance or None if integration fails
    """
    try:
        ai_system = JEdit2AISystem(main_window, config)
        if ai_system.initialize():
            return ai_system
        else:
            return None
    except Exception as e:
        logging.error(f"Failed to integrate AI system: {e}")
        return None


def main():
    """Demo the complete AI system integration."""
    logging.basicConfig(level=logging.INFO, format="%(levelname)s - %(message)s")

    print("🚀 JEdit2 AI System Integration - Demo Mode")
    print("=" * 60)

    # Mock main window for demo
    class MockMainWindow:
        def __init__(self):
            from ai_manager import AIManager  # Basic AI manager

            self.ai_manager = AIManager()

    # Create mock main window
    main_window = MockMainWindow()

    # Integration configuration
    config = {
        "enable_enhanced_ai": True,
        "enable_automated_testing": True,
        "enable_production_monitoring": True,
        "integration_config": {
            "enable_shadow_mode": True,  # Safe demo mode
            "log_level": "INFO",
        },
        "testing_config": {
            "execution_schedule": "manual",  # No auto-scheduling for demo
            "parallel_execution": False,  # Simpler for demo
        },
        "monitoring_config": {
            "monitoring_interval": 5.0,  # Fast monitoring for demo
            "enable_automated_feedback": True,
        },
    }

    # Integrate the AI system
    ai_system = integrate_ai_system(main_window, config)

    if ai_system:
        print("✅ AI System integrated successfully")

        # Run health check
        health = ai_system.run_health_check()
        print(f"✅ Health Check: {health['overall_status']}")

        # Get system status
        status = ai_system.get_system_status()
        print(f"✅ System Status: {len(status['components'])} components active")

        # Generate and export report
        report_path = "ai_system_demo_report.json"
        if ai_system.export_system_report(report_path):
            print(f"✅ System report exported to {report_path}")

        # Simulate some AI usage
        if ai_system.enhanced_ai_manager:
            print("\n🔄 Testing Enhanced AI Manager...")
            try:
                result = ai_system.enhanced_ai_manager.get_ai_response_enhanced(
                    "test query"
                )
                print(f"✅ AI Response generated: {result[0] is not None}")
            except Exception as e:
                print(f"❌ AI Test failed: {e}")

        # Show final status
        final_status = ai_system.get_system_status()
        print(f"\n📊 Final System Status:")
        print(f"Initialized: {final_status['system_info']['initialized']}")
        print(f"Running: {final_status['system_info']['running']}")
        print(f"Components: {len(final_status['components'])}")

        # Cleanup
        ai_system.stop_system()
        print("✅ System stopped cleanly")

    else:
        print("❌ AI System integration failed")

    print("\n✅ JEdit2 AI System Integration demo completed")


if __name__ == "__main__":
    main()
