<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M10.5,8.5a3,3,0,1,1-3-3A3,3,0,0,1,10.5,8.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M7.5,5A3.5,3.5,0,1,0,11,8.5,3.5,3.5,0,0,0,7.5,5Zm0,6A2.5,2.5,0,1,1,10,8.5,2.5,2.5,0,0,1,7.5,11Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M4.051,8A3.443,3.443,0,0,0,4,8.5a3.443,3.443,0,0,0,.051.5H3v3H2V8.5L2.5,8Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8,5.051A3.443,3.443,0,0,0,7.5,5a3.443,3.443,0,0,0-.5.051V4H8Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M13,8.5V12H12V9H10.949A3.443,3.443,0,0,0,11,8.5a3.443,3.443,0,0,0-.051-.5H12.5Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M5.5,1.5h4v2h-4Zm-5,13h4v-2H.5Zm10-2v2h4v-2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M10,1.5,9.5,1h-4L5,1.5v2l.5.5h4l.5-.5ZM9,3H6V2H9ZM.5,12l-.5.5v2l.5.5h4l.5-.5v-2L4.5,12ZM4,14H1V13H4Zm10.5-2h-4l-.5.5v2l.5.5h4l.5-.5v-2ZM14,14H11V13h3Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
