"""LibreOffice Calc Adapter for JEdit2.

This module provides an adapter that maps JEdit2 spreadsheet operations
to LibreOffice Calc UNO commands, preserving all existing functionality.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

try:
    from com.sun.star.beans import PropertyValue
    from com.sun.star.table import CellRangeAddress
    from com.sun.star.awt import Rectangle
    UNO_AVAILABLE = True
except ImportError:
    UNO_AVAILABLE = False


class CalcAdapter:
    """Adapter for LibreOffice Calc spreadsheet operations."""
    
    def __init__(self, document):
        """Initialize Calc adapter.
        
        Args:
            document: LibreOffice Calc document object
        """
        self.document = document
        self.controller = document.getCurrentController()
        self.sheets = document.getSheets()
        self.active_sheet = self.sheets.getByIndex(0)
        self.logger = logging.getLogger(__name__)
        
    def get_active_sheet(self):
        """Get the currently active sheet."""
        try:
            return self.controller.getActiveSheet()
        except Exception:
            return self.active_sheet
    
    def set_active_sheet(self, sheet_index: int):
        """Set the active sheet by index."""
        try:
            sheet = self.sheets.getByIndex(sheet_index)
            self.controller.setActiveSheet(sheet)
            self.active_sheet = sheet
        except Exception as e:
            self.logger.error(f"Failed to set active sheet {sheet_index}: {e}")
    
    def get_cell_value(self, row: int, col: int) -> Any:
        """Get cell value at specified position.
        
        Args:
            row: Row index (0-based)
            col: Column index (0-based)
            
        Returns:
            Cell value
        """
        try:
            sheet = self.get_active_sheet()
            cell = sheet.getCellByPosition(col, row)
            return cell.getValue()
        except Exception as e:
            self.logger.error(f"Failed to get cell value at ({row}, {col}): {e}")
            return None
    
    def set_cell_value(self, row: int, col: int, value: Any):
        """Set cell value at specified position.
        
        Args:
            row: Row index (0-based)
            col: Column index (0-based)
            value: Value to set
        """
        try:
            sheet = self.get_active_sheet()
            cell = sheet.getCellByPosition(col, row)
            
            if isinstance(value, (int, float)):
                cell.setValue(value)
            else:
                cell.setString(str(value))
                
        except Exception as e:
            self.logger.error(f"Failed to set cell value at ({row}, {col}): {e}")
    
    def get_cell_formula(self, row: int, col: int) -> str:
        """Get cell formula at specified position."""
        try:
            sheet = self.get_active_sheet()
            cell = sheet.getCellByPosition(col, row)
            return cell.getFormula()
        except Exception as e:
            self.logger.error(f"Failed to get cell formula at ({row}, {col}): {e}")
            return ""
    
    def set_cell_formula(self, row: int, col: int, formula: str):
        """Set cell formula at specified position."""
        try:
            sheet = self.get_active_sheet()
            cell = sheet.getCellByPosition(col, row)
            cell.setFormula(formula)
        except Exception as e:
            self.logger.error(f"Failed to set cell formula at ({row}, {col}): {e}")
    
    def apply_bold_formatting(self, start_row: int, start_col: int, 
                            end_row: int, end_col: int):
        """Apply bold formatting to range."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Get current weight and toggle
            current_weight = range_obj.getPropertyValue("CharWeight")
            new_weight = 700 if current_weight < 700 else 400  # Bold or normal
            
            range_obj.setPropertyValue("CharWeight", new_weight)
            
        except Exception as e:
            self.logger.error(f"Failed to apply bold formatting: {e}")
    
    def apply_italic_formatting(self, start_row: int, start_col: int,
                              end_row: int, end_col: int):
        """Apply italic formatting to range."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Toggle italic
            from com.sun.star.awt.FontSlant import NONE, ITALIC
            current_slant = range_obj.getPropertyValue("CharPosture")
            new_slant = ITALIC if current_slant == NONE else NONE
            
            range_obj.setPropertyValue("CharPosture", new_slant)
            
        except Exception as e:
            self.logger.error(f"Failed to apply italic formatting: {e}")
    
    def apply_underline_formatting(self, start_row: int, start_col: int,
                                 end_row: int, end_col: int):
        """Apply underline formatting to range."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Toggle underline
            from com.sun.star.awt.FontUnderline import NONE, SINGLE
            current_underline = range_obj.getPropertyValue("CharUnderline")
            new_underline = SINGLE if current_underline == NONE else NONE
            
            range_obj.setPropertyValue("CharUnderline", new_underline)
            
        except Exception as e:
            self.logger.error(f"Failed to apply underline formatting: {e}")
    
    def set_alignment(self, start_row: int, start_col: int, end_row: int, end_col: int,
                     alignment: str):
        """Set text alignment for range.
        
        Args:
            alignment: 'left', 'center', or 'right'
        """
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            from com.sun.star.table.CellHoriJustify import LEFT, CENTER, RIGHT
            alignment_map = {
                "left": LEFT,
                "center": CENTER,
                "right": RIGHT
            }
            
            if alignment in alignment_map:
                range_obj.setPropertyValue("HoriJustify", alignment_map[alignment])
                
        except Exception as e:
            self.logger.error(f"Failed to set alignment: {e}")
    
    def format_as_currency(self, start_row: int, start_col: int,
                          end_row: int, end_col: int):
        """Format cells as currency."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Apply currency format
            range_obj.setPropertyValue("NumberFormat", 5)  # Currency format
            
        except Exception as e:
            self.logger.error(f"Failed to format as currency: {e}")
    
    def format_as_percent(self, start_row: int, start_col: int,
                         end_row: int, end_col: int):
        """Format cells as percentage."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Apply percentage format
            range_obj.setPropertyValue("NumberFormat", 10)  # Percentage format
            
        except Exception as e:
            self.logger.error(f"Failed to format as percentage: {e}")
    
    def format_with_comma_separator(self, start_row: int, start_col: int,
                                   end_row: int, end_col: int):
        """Format cells with thousands separator."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            
            # Apply number format with thousands separator
            range_obj.setPropertyValue("NumberFormat", 2)  # Number with separator
            
        except Exception as e:
            self.logger.error(f"Failed to format with comma separator: {e}")
    
    def increase_decimal_places(self, start_row: int, start_col: int,
                               end_row: int, end_col: int):
        """Increase decimal places for range."""
        try:
            # Use UNO command for decimal place adjustment
            dispatcher = self.controller.getFrame().getDispatcher()
            
            # Select the range first
            self.select_range(start_row, start_col, end_row, end_col)
            
            # Execute increase decimal command
            dispatcher.executeDispatch(
                self.controller.getFrame(),
                ".uno:NumberFormatIncDecimals",
                "",
                0,
                ()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to increase decimal places: {e}")
    
    def decrease_decimal_places(self, start_row: int, start_col: int,
                               end_row: int, end_col: int):
        """Decrease decimal places for range."""
        try:
            # Use UNO command for decimal place adjustment
            dispatcher = self.controller.getFrame().getDispatcher()
            
            # Select the range first
            self.select_range(start_row, start_col, end_row, end_col)
            
            # Execute decrease decimal command
            dispatcher.executeDispatch(
                self.controller.getFrame(),
                ".uno:NumberFormatDecDecimals",
                "",
                0,
                ()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to decrease decimal places: {e}")
    
    def insert_row(self, row_index: int):
        """Insert row at specified index."""
        try:
            sheet = self.get_active_sheet()
            rows = sheet.getRows()
            rows.insertByIndex(row_index, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to insert row at {row_index}: {e}")
    
    def delete_row(self, row_index: int):
        """Delete row at specified index."""
        try:
            sheet = self.get_active_sheet()
            rows = sheet.getRows()
            rows.removeByIndex(row_index, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to delete row at {row_index}: {e}")
    
    def insert_column(self, col_index: int):
        """Insert column at specified index."""
        try:
            sheet = self.get_active_sheet()
            columns = sheet.getColumns()
            columns.insertByIndex(col_index, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to insert column at {col_index}: {e}")
    
    def delete_column(self, col_index: int):
        """Delete column at specified index."""
        try:
            sheet = self.get_active_sheet()
            columns = sheet.getColumns()
            columns.removeByIndex(col_index, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to delete column at {col_index}: {e}")
    
    def select_range(self, start_row: int, start_col: int, end_row: int, end_col: int):
        """Select a range of cells."""
        try:
            sheet = self.get_active_sheet()
            range_obj = sheet.getCellRangeByPosition(start_col, start_row, end_col, end_row)
            self.controller.select(range_obj)
            
        except Exception as e:
            self.logger.error(f"Failed to select range: {e}")
    
    def copy_range(self, start_row: int, start_col: int, end_row: int, end_col: int):
        """Copy a range of cells to clipboard."""
        try:
            # Select range and copy
            self.select_range(start_row, start_col, end_row, end_col)
            
            dispatcher = self.controller.getFrame().getDispatcher()
            dispatcher.executeDispatch(
                self.controller.getFrame(),
                ".uno:Copy",
                "",
                0,
                ()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to copy range: {e}")
    
    def paste_to_position(self, row: int, col: int):
        """Paste clipboard content to specified position."""
        try:
            # Select target cell
            self.select_range(row, col, row, col)
            
            dispatcher = self.controller.getFrame().getDispatcher()
            dispatcher.executeDispatch(
                self.controller.getFrame(),
                ".uno:Paste",
                "",
                0,
                ()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to paste to position ({row}, {col}): {e}")
    
    def transpose_data(self, source_start_row: int, source_start_col: int,
                      source_end_row: int, source_end_col: int,
                      target_row: int, target_col: int):
        """Transpose data from source range to target position."""
        try:
            # Copy source range
            self.copy_range(source_start_row, source_start_col, 
                           source_end_row, source_end_col)
            
            # Select target position
            self.select_range(target_row, target_col, target_row, target_col)
            
            # Paste special with transpose
            dispatcher = self.controller.getFrame().getDispatcher()
            
            # Create property for transpose operation
            transpose_prop = PropertyValue()
            transpose_prop.Name = "Flags"
            transpose_prop.Value = "T"  # Transpose flag
            
            dispatcher.executeDispatch(
                self.controller.getFrame(),
                ".uno:PasteSpecial",
                "",
                0,
                (transpose_prop,)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to transpose data: {e}")
    
    def sort_column(self, col_index: int, ascending: bool = True):
        """Sort data by specified column."""
        try:
            # Get data range
            sheet = self.get_active_sheet()
            used_range = sheet.getCellRangeByName("A1:Z1000")  # Adjust as needed
            
            # Select the range
            self.controller.select(used_range)
            
            # Create sort descriptor
            sort_fields = used_range.createSortDescriptor()
            
            # Configure sort
            sort_fields[0].Field = col_index
            sort_fields[0].SortAscending = ascending
            
            # Perform sort
            used_range.sort(sort_fields)
            
        except Exception as e:
            self.logger.error(f"Failed to sort column {col_index}: {e}")
    
    def get_data_as_array(self, start_row: int = 0, start_col: int = 0,
                         end_row: int = 100, end_col: int = 100) -> List[List[str]]:
        """Get data from sheet as 2D array."""
        try:
            sheet = self.get_active_sheet()
            data = []
            
            for row in range(start_row, end_row + 1):
                row_data = []
                for col in range(start_col, end_col + 1):
                    cell = sheet.getCellByPosition(col, row)
                    value = cell.getString() if cell.getType().value == "TEXT" else str(cell.getValue())
                    row_data.append(value)
                data.append(row_data)
                
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to get data as array: {e}")
            return []
    
    def set_data_from_array(self, data: List[List[str]], start_row: int = 0, start_col: int = 0):
        """Set data from 2D array to sheet."""
        try:
            sheet = self.get_active_sheet()
            
            for row_idx, row_data in enumerate(data):
                for col_idx, value in enumerate(row_data):
                    self.set_cell_value(start_row + row_idx, start_col + col_idx, value)
                    
        except Exception as e:
            self.logger.error(f"Failed to set data from array: {e}")
    
    def find_and_replace(self, search_text: str, replace_text: str) -> int:
        """Find and replace text in the sheet.
        
        Returns:
            Number of replacements made
        """
        try:
            sheet = self.get_active_sheet()
            
            # Create search descriptor
            search_desc = sheet.createSearchDescriptor()
            search_desc.setSearchString(search_text)
            search_desc.setReplaceString(replace_text)
            
            # Perform replace all
            return sheet.replaceAll(search_desc)
            
        except Exception as e:
            self.logger.error(f"Failed to find and replace: {e}")
            return 0
    
    def get_selection_info(self) -> Dict[str, Any]:
        """Get information about current selection."""
        try:
            selection = self.controller.getSelection()
            
            if hasattr(selection, 'getRangeAddress'):
                addr = selection.getRangeAddress()
                return {
                    'start_row': addr.StartRow,
                    'start_col': addr.StartColumn,
                    'end_row': addr.EndRow,
                    'end_col': addr.EndColumn,
                    'sheet': addr.Sheet
                }
            else:
                return {'start_row': 0, 'start_col': 0, 'end_row': 0, 'end_col': 0, 'sheet': 0}
                
        except Exception as e:
            self.logger.error(f"Failed to get selection info: {e}")
            return {'start_row': 0, 'start_col': 0, 'end_row': 0, 'end_col': 0, 'sheet': 0} 