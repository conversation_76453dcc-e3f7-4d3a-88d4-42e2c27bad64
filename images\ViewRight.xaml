<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey-25" Color="#212121" Opacity="0.25" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M13.5,4.82,8,8v6.35L2.5,11.18V4.82L8,1.65Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14,4.82l-.25-.43L8.25,1.22h-.5L2.25,4.39,2,4.82v6.36l.25.43,5.5,3.17h.5L8,14.35V8l5.5-3.18ZM8.5,2.51l4,2.31-4,2.31ZM7,8,3,10.31V5.69ZM3.5,4.82l4-2.31V7.13Zm4,8.67-4-2.31,4-2.31Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M13.5,4.825v6.35L8,14.351V8Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-25}" Geometry="F1M13.75,10.742l-.5.866L7.75,8.433l.5-.866Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8.25,14.784l-.75-.433V8l.25-.433,5.5-3.175.75.433v6.35l-.25.433Zm.25-6.5v5.2l4.5-2.6v-5.2Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
