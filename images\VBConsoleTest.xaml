<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10,9v1h2V9Zm0-.646L8.409,9.945,7.7,9.237,8.939,8,7.7,6.763l.707-.708L10,7.646Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14.5,2.5V12l-7.933-.022L6,11.07V10H7V7H4.5V2.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,2H4.5L4,2.5V7H5V5h9v6H6v.356L6.4,12h8.1l.5-.5v-9ZM5,4V3h9V4Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M1.563,13,2.5,11.5v-3h2v3L5.438,13Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M2,9H1V8H6V9H5v2.356l1.174,1.879L5.75,13h-.9l-.772-1.235L4,11.5V9H3v2.5l-.076.265L2.152,13h-.9l-.424.235L2,11.356Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M6.522,14.735A.5.5,0,0,1,6.1,15.5H.9a.5.5,0,0,1-.424-.765L1.25,13.5h4.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M6.1,16H.9a1,1,0,0,1-.848-1.53l.772-1.235L1.25,13h4.5l.424.235.772,1.235A1,1,0,0,1,6.1,16ZM1.527,14,.9,15H6.1l-.625-1Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
