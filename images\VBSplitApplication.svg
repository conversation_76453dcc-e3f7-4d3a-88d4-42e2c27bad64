<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.canvas{fill: none; opacity: 0;}.light-defaultgrey-10{fill: #212121; opacity: 0.1;}.light-defaultgrey{fill: #212121; opacity: 1;}.light-blue{fill: #005dba; opacity: 1;}.cls-1{opacity:0.75;}</style>
  </defs>
  <title>IconLightVBSplitApplication</title>
  <g id="canvas" class="canvas">
    <path class="canvas" d="M16,16H0V0H16Z" />
  </g>
  <g id="level-1">
    <g class="cls-1">
      <path class="light-defaultgrey-10" d="M14.5,5v8H9V10H1.5V5Z" />
      <path class="light-defaultgrey" d="M15,5.5v8l-.5.5H9V13h5V6H2v4H1V5.5L1.5,5h13Z" />
    </g>
    <path class="light-defaultgrey-10" d="M14,3V5H2V3Z" />
    <path class="light-defaultgrey" d="M14.5,6H1.5L1,5.5v-3L1.5,2h13l.5.5v3ZM2,5H14V3H2Z" />
    <path class="light-blue" d="M6,15H8v1H6ZM0,16H5V15H0Zm6-2H8V13H6ZM0,14H5V13H0Zm6-2H8V11H6ZM0,12H5V11H0Z" />
  </g>
</svg>
