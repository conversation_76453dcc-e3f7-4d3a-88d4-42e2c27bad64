"""Format Adapter for JEdit2 LibreOffice Integration.

This module handles conversion between different file formats and LibreOffice.
"""

import json
import yaml
import csv
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import tempfile


class FormatAdapter:
    """Adapter for handling different file formats with LibreOffice."""
    
    def __init__(self):
        """Initialize format adapter."""
        self.logger = logging.getLogger(__name__)
        
    def detect_format(self, file_path: str) -> str:
        """Detect file format from extension.
        
        Args:
            file_path: Path to file
            
        Returns:
            Format string ('csv', 'json', 'yaml', 'ods', 'xlsx', etc.)
        """
        ext = Path(file_path).suffix.lower()
        
        format_map = {
            '.csv': 'csv',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.ods': 'ods',
            '.xlsx': 'xlsx',
            '.xls': 'xls',
            '.odt': 'odt',
            '.docx': 'docx',
            '.doc': 'doc',
            '.txt': 'txt'
        }
        
        return format_map.get(ext, 'unknown')
    
    def csv_to_calc_data(self, file_path: str) -> List[List[str]]:
        """Convert CSV file to data array for Calc.
        
        Args:
            file_path: Path to CSV file
            
        Returns:
            2D array of data
        """
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    data.append(row)
            
            self.logger.info(f"Converted CSV file with {len(data)} rows")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to convert CSV file: {e}")
            return []
    
    def json_to_calc_data(self, file_path: str) -> List[List[str]]:
        """Convert JSON file to data array for Calc.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            2D array of data
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Convert JSON to tabular format
            data = self._json_to_tabular(json_data)
            
            self.logger.info(f"Converted JSON file with {len(data)} rows")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to convert JSON file: {e}")
            return []
    
    def yaml_to_calc_data(self, file_path: str) -> List[List[str]]:
        """Convert YAML file to data array for Calc.
        
        Args:
            file_path: Path to YAML file
            
        Returns:
            2D array of data
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)
            
            # Convert YAML to tabular format
            data = self._yaml_to_tabular(yaml_data)
            
            self.logger.info(f"Converted YAML file with {len(data)} rows")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to convert YAML file: {e}")
            return []
    
    def calc_data_to_csv(self, data: List[List[str]], file_path: str) -> bool:
        """Convert Calc data array to CSV file.
        
        Args:
            data: 2D array of data
            file_path: Output CSV file path
            
        Returns:
            True if successful
        """
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for row in data:
                    writer.writerow(row)
            
            self.logger.info(f"Exported {len(data)} rows to CSV file")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export CSV file: {e}")
            return False
    
    def calc_data_to_json(self, data: List[List[str]], file_path: str) -> bool:
        """Convert Calc data array to JSON file.
        
        Args:
            data: 2D array of data
            file_path: Output JSON file path
            
        Returns:
            True if successful
        """
        try:
            # Convert tabular data to JSON structure
            json_data = self._tabular_to_json(data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Exported data to JSON file")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export JSON file: {e}")
            return False
    
    def calc_data_to_yaml(self, data: List[List[str]], file_path: str) -> bool:
        """Convert Calc data array to YAML file.
        
        Args:
            data: 2D array of data
            file_path: Output YAML file path
            
        Returns:
            True if successful
        """
        try:
            # Convert tabular data to YAML structure
            yaml_data = self._tabular_to_yaml(data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(yaml_data, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"Exported data to YAML file")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export YAML file: {e}")
            return False
    
    def _json_to_tabular(self, json_data: Any) -> List[List[str]]:
        """Convert JSON data to tabular format."""
        if isinstance(json_data, list):
            return self._list_to_tabular(json_data)
        elif isinstance(json_data, dict):
            return self._dict_to_tabular(json_data)
        else:
            # Single value
            return [["Value"], [str(json_data)]]
    
    def _yaml_to_tabular(self, yaml_data: Any) -> List[List[str]]:
        """Convert YAML data to tabular format."""
        return self._json_to_tabular(yaml_data)  # Same logic as JSON
    
    def _list_to_tabular(self, data_list: List[Any]) -> List[List[str]]:
        """Convert list to tabular format."""
        if not data_list:
            return []
        
        # Check if list contains dictionaries
        if isinstance(data_list[0], dict):
            # Get all possible keys
            all_keys = set()
            for item in data_list:
                if isinstance(item, dict):
                    all_keys.update(item.keys())
            
            headers = sorted(all_keys)
            rows = [headers]
            
            for item in data_list:
                if isinstance(item, dict):
                    row = [str(item.get(key, '')) for key in headers]
                    rows.append(row)
                else:
                    row = [str(item)] + [''] * (len(headers) - 1)
                    rows.append(row)
            
            return rows
        else:
            # Simple list
            return [["Value"]] + [[str(item)] for item in data_list]
    
    def _dict_to_tabular(self, data_dict: Dict[str, Any]) -> List[List[str]]:
        """Convert dictionary to tabular format."""
        rows = [["Key", "Value"]]
        
        for key, value in data_dict.items():
            if isinstance(value, (dict, list)):
                # Nested structure - flatten to string
                value_str = str(value)
            else:
                value_str = str(value)
            
            rows.append([str(key), value_str])
        
        return rows
    
    def _tabular_to_json(self, data: List[List[str]]) -> Any:
        """Convert tabular data to JSON structure."""
        if not data:
            return []
        
        headers = data[0]
        rows = data[1:]
        
        result = []
        for row in rows:
            item = {}
            for i, header in enumerate(headers):
                value = row[i] if i < len(row) else ''
                item[header] = value
            result.append(item)
        
        return result
    
    def _tabular_to_yaml(self, data: List[List[str]]) -> Any:
        """Convert tabular data to YAML structure."""
        return self._tabular_to_json(data)  # Same structure as JSON
    
    def create_temp_file(self, data: List[List[str]], format_type: str) -> Optional[str]:
        """Create temporary file with data in specified format.
        
        Args:
            data: 2D array of data
            format_type: Format ('csv', 'json', 'yaml')
            
        Returns:
            Path to temporary file or None if failed
        """
        try:
            if format_type == 'csv':
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
                writer = csv.writer(temp_file)
                for row in data:
                    writer.writerow(row)
                temp_file.close()
                return temp_file.name
                
            elif format_type == 'json':
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                json_data = self._tabular_to_json(data)
                json.dump(json_data, temp_file, indent=2)
                temp_file.close()
                return temp_file.name
                
            elif format_type == 'yaml':
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
                yaml_data = self._tabular_to_yaml(data)
                yaml.dump(yaml_data, temp_file, default_flow_style=False)
                temp_file.close()
                return temp_file.name
                
            else:
                self.logger.error(f"Unsupported format type: {format_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create temp file: {e}")
            return None 