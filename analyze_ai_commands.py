#!/usr/bin/env python3
"""
<PERSON>ript to analyze AI command naming consistency and find potential issues.
This helps prevent the SORT_COLUMN_ASCENDING type issues by systematically
checking for mismatches between capability names and method names.
"""

import re
import ast
import os
from typing import Dict, List, Set, Tuple


def extract_capabilities_from_ai_manager() -> List[str]:
    """Extract all capability names from ai_manager.py"""
    capabilities = []
    
    try:
        with open('jedit2/utils/ai_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Find the CAPABILITIES list
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name) and target.id == 'CAPABILITIES':
                        if isinstance(node.value, ast.List):
                            for item in node.value.elts:
                                if isinstance(item, ast.Dict):
                                    for key, value in zip(item.keys, item.values):
                                        if (isinstance(key, ast.Constant) and key.value == 'name' and
                                            isinstance(value, ast.Constant)):
                                            capabilities.append(value.value)
                                            
    except Exception as e:
        print(f"Error reading ai_manager.py: {e}")
        
    return capabilities


def extract_ai_methods_from_main_window() -> List[str]:
    """Extract all _ai_ method names from main_window.py"""
    methods = []
    
    try:
        with open('jedit2/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Find all method definitions starting with _ai_
        pattern = r'def\s+(_ai_\w+)\s*\('
        matches = re.findall(pattern, content)
        methods.extend(matches)
        
    except Exception as e:
        print(f"Error reading main_window.py: {e}")
        
    return methods


def capability_to_method_name(capability: str) -> str:
    """Convert a capability name to expected method name using the current logic"""
    return f"_ai_{capability.lower()}"


def find_special_cases_in_code() -> Dict[str, str]:
    """Find existing special case mappings in the code"""
    special_cases = {}
    
    try:
        with open('jedit2/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Look for patterns like: if capability == "SOMETHING":
        #                             self._ai_something_else(...)
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if 'if capability ==' in line or 'elif capability ==' in line:
                # Extract capability name
                capability_match = re.search(r'capability\s*==\s*["\']([^"\']+)["\']', line)
                if capability_match:
                    capability = capability_match.group(1)
                    
                    # Look for the method call in the next few lines
                    for j in range(i+1, min(i+5, len(lines))):
                        method_match = re.search(r'self\.(_ai_\w+)\s*\(', lines[j])
                        if method_match:
                            method = method_match.group(1)
                            special_cases[capability] = method
                            break
                            
    except Exception as e:
        print(f"Error analyzing special cases: {e}")
        
    return special_cases


def analyze_command_consistency():
    """Main analysis function"""
    print("🔍 Analyzing AI Command Consistency")
    print("=" * 50)
    
    # Get data
    capabilities = extract_capabilities_from_ai_manager()
    methods = extract_ai_methods_from_main_window()
    special_cases = find_special_cases_in_code()
    
    print(f"📋 Found {len(capabilities)} capabilities")
    print(f"🔧 Found {len(methods)} _ai_ methods")
    print(f"⚡ Found {len(special_cases)} special case mappings")
    print()
    
    # Convert to sets for easier analysis
    method_set = set(methods)
    
    # Analyze each capability
    issues = []
    working = []
    
    for capability in capabilities:
        expected_method = capability_to_method_name(capability)
        
        if capability in special_cases:
            actual_method = special_cases[capability]
            if actual_method in method_set:
                working.append((capability, actual_method, "special_case"))
            else:
                issues.append((capability, expected_method, actual_method, "special_case_missing_method"))
        elif expected_method in method_set:
            working.append((capability, expected_method, "direct_mapping"))
        else:
            issues.append((capability, expected_method, None, "missing_method"))
    
    # Report results
    print("✅ WORKING COMMANDS:")
    print("-" * 30)
    for capability, method, mapping_type in working:
        print(f"  {capability:<30} → {method:<30} ({mapping_type})")
    
    print(f"\n❌ ISSUES FOUND ({len(issues)}):")
    print("-" * 30)
    for capability, expected, actual, issue_type in issues:
        if issue_type == "missing_method":
            print(f"  {capability:<30} → {expected:<30} (METHOD MISSING)")
        elif issue_type == "special_case_missing_method":
            print(f"  {capability:<30} → {actual:<30} (SPECIAL CASE METHOD MISSING)")
    
    # Find orphaned methods (methods without capabilities)
    used_methods = set()
    for capability, method, _ in working:
        used_methods.add(method)
    for _, _, actual, _ in issues:
        if actual:
            used_methods.add(actual)
    
    orphaned = method_set - used_methods
    if orphaned:
        print(f"\n🔍 ORPHANED METHODS ({len(orphaned)}):")
        print("-" * 30)
        for method in sorted(orphaned):
            print(f"  {method}")
    
    return issues, working, orphaned


if __name__ == "__main__":
    issues, working, orphaned = analyze_command_consistency()
    
    print(f"\n📊 SUMMARY:")
    print(f"  ✅ Working: {len(working)}")
    print(f"  ❌ Issues: {len(issues)}")
    print(f"  🔍 Orphaned: {len(orphaned)}")
    
    if issues:
        print(f"\n🛠️  RECOMMENDED FIXES:")
        print("-" * 30)
        for capability, expected, actual, issue_type in issues:
            if issue_type == "missing_method":
                print(f"  Add special case for '{capability}' or implement method '{expected}'")
