#!/usr/bin/env python3
"""
Test the critical fixes for JSON and YAML that MUST work for production.
"""

import os
import sys

# Add the jedit2 package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jedit2'))

from jedit2.utils.format_validator import FormatValidator
from jedit2.utils.format_corrector import FormatCorrector

def test_critical_json_fixes():
    """Test the 3 critical JSON cases."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    critical_json = [
        {
            'name': 'Mixed Array/Object',
            'content': '{"key": "value", "item2"}',
            'description': 'Object with orphaned string value'
        },
        {
            'name': 'Nested Object Fragment',
            'content': '"outer": {"inner": "value"',
            'description': 'Nested object missing closing braces'
        },
        {
            'name': 'Multiple Errors',
            'content': '{key1: "value1", "key2": value2,}',
            'description': 'Multiple issues: unquoted key, unquoted value, trailing comma'
        }
    ]
    
    print("🔥 CRITICAL JSON FIXES TEST")
    print("=" * 60)
    print("These 3 cases MUST be fixed for production readiness...")
    print()
    
    json_results = []
    
    for i, case in enumerate(critical_json, 1):
        print(f"{i}. {case['name']}")
        print("-" * 50)
        print(f"   Content: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], 'json')
        print(f"   Correctable: {validation_result.is_correctable}")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], 'json')
            
            if correction_result.success:
                print(f"   ✅ SUCCESS!")
                print(f"   Changes: {correction_result.changes_made}")
                
                # Verify the result is valid JSON
                try:
                    import json
                    parsed = json.loads(correction_result.corrected_content)
                    print(f"   ✅ Valid JSON!")
                    success = True
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid JSON: {e}")
                    
            else:
                print(f"   ❌ CORRECTION FAILED")
                print(f"   Errors: {correction_result.errors[:1]}...")
        else:
            print(f"   ❌ NOT DETECTED AS CORRECTABLE")
        
        json_results.append(success)
        print()
    
    return json_results

def test_critical_yaml_fixes():
    """Test the 9 critical YAML cases."""
    
    validator = FormatValidator()
    corrector = FormatCorrector()
    
    critical_yaml = [
        {
            'name': 'Mixed Tabs/Spaces',
            'content': 'key1: value1\n\tkey2: value2\n  key3: value3'
        },
        {
            'name': 'Wrong Indentation Level',
            'content': 'parent:\nchild: value'
        },
        {
            'name': 'Inconsistent Indentation',
            'content': 'key1: value1\n   key2: value2\n key3: value3'
        },
        {
            'name': 'Missing Colon',
            'content': 'key value'
        },
        {
            'name': 'Invalid List Syntax',
            'content': 'items:\n- item1\n item2'
        },
        {
            'name': 'Invalid Mapping',
            'content': 'key: value: extra'
        },
        {
            'name': 'Multiline String Issues',
            'content': 'key: |\n  line1\n line2'
        }
    ]
    
    print("🔥 CRITICAL YAML FIXES TEST")
    print("=" * 60)
    print("These YAML cases MUST be fixed for production readiness...")
    print()
    
    yaml_results = []
    
    for i, case in enumerate(critical_yaml, 1):
        print(f"{i}. {case['name']}")
        print("-" * 50)
        print(f"   Content: {repr(case['content'])}")
        
        # Test validation
        validation_result = validator.validate_format(case['content'], 'yaml')
        print(f"   Correctable: {validation_result.is_correctable}")
        
        success = False
        if validation_result.is_correctable:
            # Test correction
            correction_result = corrector.correct_format(case['content'], 'yaml')
            
            if correction_result.success:
                print(f"   ✅ SUCCESS!")
                print(f"   Changes: {correction_result.changes_made}")
                
                # Verify the result is valid YAML
                try:
                    import yaml
                    parsed = yaml.safe_load(correction_result.corrected_content)
                    print(f"   ✅ Valid YAML!")
                    success = True
                    
                except yaml.YAMLError as e:
                    print(f"   ❌ Invalid YAML: {e}")
                    
            else:
                print(f"   ❌ CORRECTION FAILED")
                print(f"   Errors: {correction_result.errors[:1]}...")
        else:
            print(f"   ❌ NOT DETECTED AS CORRECTABLE")
        
        yaml_results.append(success)
        print()
    
    return yaml_results

if __name__ == "__main__":
    print("🧪 CRITICAL FIXES TEST - PRODUCTION READINESS")
    print("=" * 70)
    print("Testing the critical cases that MUST be fixed for 85%+ coverage")
    print()
    
    # Test JSON critical cases
    json_results = test_critical_json_fixes()
    
    # Test YAML critical cases  
    yaml_results = test_critical_yaml_fixes()
    
    # Summary
    print("=" * 70)
    print("📊 CRITICAL FIXES SUMMARY")
    print("=" * 70)
    
    json_passed = sum(json_results)
    yaml_passed = sum(yaml_results)
    total_passed = json_passed + yaml_passed
    total_cases = len(json_results) + len(yaml_results)
    
    print(f"JSON Critical Cases: {json_passed}/3 fixed ({(json_passed/3)*100:.1f}%)")
    print(f"YAML Critical Cases: {yaml_passed}/7 fixed ({(yaml_passed/7)*100:.1f}%)")
    print(f"Total Critical Cases: {total_passed}/{total_cases} fixed ({(total_passed/total_cases)*100:.1f}%)")
    
    # Estimate coverage impact
    current_coverage = 71.4
    # Each JSON case = ~1.8%, each YAML case = ~1.8%
    coverage_boost = (json_passed * 1.8) + (yaml_passed * 1.8)
    estimated_coverage = current_coverage + coverage_boost
    
    print(f"\nEstimated Coverage Impact:")
    print(f"  Current: 71.4%")
    print(f"  Boost: +{coverage_boost:.1f}%")
    print(f"  Projected: {estimated_coverage:.1f}%")
    
    if estimated_coverage >= 85:
        print("\n🎉 PRODUCTION READY! 85%+ coverage achieved!")
    elif estimated_coverage >= 80:
        print("\n🚀 EXCELLENT! 80%+ coverage - very close to production ready!")
    elif total_passed >= 8:  # 80% of critical cases
        print("\n👍 GOOD PROGRESS! Most critical cases fixed!")
    else:
        print("\n🔧 MORE WORK NEEDED on critical cases.")
    
    print(f"\n🎯 RESULT: {total_passed}/{total_cases} critical cases fixed")
