<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-shadow" Color="#000000" Opacity="0.1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_2">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M12.5,3.5V7.341l.5-.5v3.866l-.5.5V14.5H2.5V3.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M13,10.707V14.5l-.5.5H2.5L2,14.5V3.5L2.5,3h10l.5.5V7.341l-1,1V4H3V14h9V11.707Z" />
              </DrawingGroup>
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-shadow}" Geometry="F1M14.854,8.854l-5,5H9.146l-3-3,.708-.708L9.5,12.793l4.646-4.647Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M14.854,7.854l-5,5H9.146l-3-3,.708-.708L9.5,11.793l4.646-4.647Z" />
              <GeometryDrawing Brush="{DynamicResource light-shadow}" Geometry="F1M9.5,3a2,2,0,0,0-4,0H4V5h7V3Zm-2-1a1,1,0,1,1-1,1A1,1,0,0,1,7.5,2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M9.5,2a2,2,0,0,0-4,0H4V4h7V2Zm-2-1a1,1,0,1,1-1,1A1,1,0,0,1,7.5,1Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
