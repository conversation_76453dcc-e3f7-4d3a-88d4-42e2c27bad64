"""High-quality SVG icons for JEdit2.

This module provides scalable vector graphics icons optimized for
Windows 11 and Apple Silicon Mac platforms.
"""

import wx
from typing import Dict, Optional


def create_svg_paste_icon() -> wx.BitmapBundle:
    """Create SVG icon for paste operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Clipboard -->
            <rect x="3" y="2" width="8" height="12" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="5" y="0" width="4" height="2" rx="1"/>
            <!-- Content lines -->
            <line x1="5" y1="5" x2="9" y2="5" stroke="currentColor" stroke-width="1"/>
            <line x1="5" y1="7" x2="9" y2="7" stroke="currentColor" stroke-width="1"/>
            <line x1="5" y1="9" x2="7" y2="9" stroke="currentColor" stroke-width="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data.encode('utf-8'), wx.Size(16, 16))


def create_svg_copy_icon() -> wx.BitmapBundle:
    """Create SVG icon for copy operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke="currentColor" stroke-width="1">
            <!-- Back document -->
            <rect x="2" y="2" width="9" height="11" rx="1"/>
            <!-- Front document -->
            <rect x="5" y="3" width="9" height="11" rx="1" fill="white"/>
            <!-- Content lines in front document -->
            <line x1="7" y1="6" x2="12" y2="6" stroke="currentColor"/>
            <line x1="7" y1="8" x2="12" y2="8" stroke="currentColor"/>
            <line x1="7" y1="10" x2="10" y2="10" stroke="currentColor"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data.encode('utf-8'), wx.Size(16, 16))


def create_svg_cut_icon() -> wx.BitmapBundle:
    """Create SVG icon for cut operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Scissors -->
            <circle cx="4" cy="4" r="1.5"/>
            <circle cx="4" cy="12" r="1.5"/>
            <path d="M4 4 L12 8 L4 12" fill="none" stroke="currentColor" stroke-width="1"/>
            <!-- Cut line -->
            <line x1="10" y1="6" x2="14" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="2,1"/>
            <line x1="10" y1="10" x2="14" y2="10" stroke="currentColor" stroke-width="2" stroke-dasharray="2,1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_bold_icon() -> wx.BitmapBundle:
    """Create SVG icon for bold formatting."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <path d="M3 2v12h6c2.2 0 4-1.3 4-3 0-1.2-.7-2.3-1.7-2.7C12.3 7.9 13 6.9 13 5.5 13 3.3 11.2 2 9 2H3zm2 2h4c1.1 0 2 .4 2 1.5S10.1 7 9 7H5V4zm0 5h4.5c1.2 0 2.5.4 2.5 1.5S10.7 12 9.5 12H5V9z"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_italic_icon() -> wx.BitmapBundle:
    """Create SVG icon for italic formatting."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <path d="M6 2v2h2.5l-3 8H3v2h6v-2H6.5l3-8H12V2H6z"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_underline_icon() -> wx.BitmapBundle:
    """Create SVG icon for underline formatting."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <path d="M4 2v6c0 2.2 1.8 4 4 4s4-1.8 4-4V2h-1.5v6c0 1.4-1.1 2.5-2.5 2.5S5.5 9.4 5.5 8V2H4z"/>
            <rect x="3" y="14" width="10" height="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_align_left_icon() -> wx.BitmapBundle:
    """Create SVG icon for left alignment."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <rect x="2" y="3" width="12" height="1"/>
            <rect x="2" y="6" width="8" height="1"/>
            <rect x="2" y="9" width="10" height="1"/>
            <rect x="2" y="12" width="6" height="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_align_center_icon() -> wx.BitmapBundle:
    """Create SVG icon for center alignment."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <rect x="2" y="3" width="12" height="1"/>
            <rect x="4" y="6" width="8" height="1"/>
            <rect x="3" y="9" width="10" height="1"/>
            <rect x="5" y="12" width="6" height="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_align_right_icon() -> wx.BitmapBundle:
    """Create SVG icon for right alignment."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <rect x="2" y="3" width="12" height="1"/>
            <rect x="6" y="6" width="8" height="1"/>
            <rect x="4" y="9" width="10" height="1"/>
            <rect x="8" y="12" width="6" height="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_comma_style_icon() -> wx.BitmapBundle:
    """Create SVG icon for comma number style."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <text x="1" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="bold">1,000</text>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_percent_style_icon() -> wx.BitmapBundle:
    """Create SVG icon for percent style."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Percent symbol -->
            <circle cx="4" cy="4" r="2" fill="none" stroke="currentColor" stroke-width="1"/>
            <circle cx="12" cy="12" r="2" fill="none" stroke="currentColor" stroke-width="1"/>
            <line x1="3" y1="13" x2="13" y2="3" stroke="currentColor" stroke-width="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_increase_decimal_icon() -> wx.BitmapBundle:
    """Create SVG icon for increasing decimal places."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Base number -->
            <text x="1" y="12" font-family="Arial, sans-serif" font-size="8" font-weight="bold">1.0</text>
            <!-- Plus sign -->
            <rect x="9" y="7" width="3" height="1"/>
            <rect x="10" y="6" width="1" height="3"/>
            <!-- Decorative decimal points -->
            <circle cx="13" cy="9" r="0.5"/>
            <circle cx="14" cy="9" r="0.5"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_decrease_decimal_icon() -> wx.BitmapBundle:
    """Create SVG icon for decreasing decimal places."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Base number -->
            <text x="1" y="12" font-family="Arial, sans-serif" font-size="7" font-weight="bold">1.00</text>
            <!-- Minus sign -->
            <rect x="9" y="7" width="3" height="1"/>
            <!-- Arrow pointing left -->
            <path d="M13 6 L12 7.5 L13 9" fill="none" stroke="currentColor" stroke-width="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_transpose_icon() -> wx.BitmapBundle:
    """Create SVG icon for transpose operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke="currentColor" stroke-width="1">
            <!-- Original matrix -->
            <rect x="1" y="3" width="5" height="3"/>
            <line x1="3" y1="3" x2="3" y2="6"/>
            <line x1="1" y1="4.5" x2="6" y2="4.5"/>
            
            <!-- Arrow -->
            <path d="M7 4.5 L9 4.5" stroke="currentColor" stroke-width="1"/>
            <path d="M8.5 4 L9 4.5 L8.5 5" stroke="currentColor" stroke-width="1"/>
            
            <!-- Transposed matrix -->
            <rect x="10" y="1" width="3" height="5"/>
            <line x1="11.5" y1="1" x2="11.5" y2="6"/>
            <line x1="10" y1="3" x2="13" y2="3"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_insert_icon() -> wx.BitmapBundle:
    """Create SVG icon for insert operations."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Plus sign -->
            <rect x="7" y="3" width="2" height="10"/>
            <rect x="3" y="7" width="10" height="2"/>
            <!-- Grid lines -->
            <line x1="1" y1="1" x2="15" y2="1" stroke="currentColor" stroke-width="0.5" opacity="0.5"/>
            <line x1="1" y1="15" x2="15" y2="15" stroke="currentColor" stroke-width="0.5" opacity="0.5"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_delete_icon() -> wx.BitmapBundle:
    """Create SVG icon for delete operations."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Trash can -->
            <rect x="5" y="6" width="6" height="8" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="4" y="4" width="8" height="1"/>
            <rect x="6" y="2" width="4" height="2"/>
            <!-- Delete lines -->
            <line x1="7" y1="8" x2="7" y2="12" stroke="currentColor" stroke-width="1"/>
            <line x1="9" y1="8" x2="9" y2="12" stroke="currentColor" stroke-width="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_find_icon() -> wx.BitmapBundle:
    """Create SVG icon for find operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke="currentColor" stroke-width="1.5">
            <!-- Magnifying glass -->
            <circle cx="6" cy="6" r="4"/>
            <line x1="12" y1="12" x2="9" y2="9"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_undo_icon() -> wx.BitmapBundle:
    """Create SVG icon for undo operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke="currentColor" stroke-width="1.5">
            <!-- Curved arrow -->
            <path d="M3 7 C3 4, 5 2, 8 2 C11 2, 13 4, 13 7 C13 10, 11 12, 8 12 L6 12"/>
            <!-- Arrow head -->
            <path d="M3 5 L3 7 L5 7"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_redo_icon() -> wx.BitmapBundle:
    """Create SVG icon for redo operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke="currentColor" stroke-width="1.5">
            <!-- Curved arrow -->
            <path d="M13 7 C13 4, 11 2, 8 2 C5 2, 3 4, 3 7 C3 10, 5 12, 8 12 L10 12"/>
            <!-- Arrow head -->
            <path d="M13 5 L13 7 L11 7"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_view_switch_icon() -> wx.BitmapBundle:
    """Create SVG icon for view switching."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Grid view -->
            <rect x="1" y="1" width="6" height="6" fill="none" stroke="currentColor" stroke-width="1"/>
            <line x1="3" y1="1" x2="3" y2="7"/>
            <line x1="5" y1="1" x2="5" y2="7"/>
            <line x1="1" y1="3" x2="7" y2="3"/>
            <line x1="1" y1="5" x2="7" y2="5"/>

            <!-- Text view -->
            <line x1="9" y1="2" x2="15" y2="2" stroke="currentColor" stroke-width="1"/>
            <line x1="9" y1="4" x2="13" y2="4" stroke="currentColor" stroke-width="1"/>
            <line x1="9" y1="6" x2="14" y2="6" stroke="currentColor" stroke-width="1"/>

            <!-- Switch arrows -->
            <path d="M2 10 L6 12 L2 14" fill="none" stroke="currentColor" stroke-width="1"/>
            <path d="M14 10 L10 12 L14 14" fill="none" stroke="currentColor" stroke-width="1"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_insert_row_icon() -> wx.BitmapBundle:
    """Create SVG icon for insert row operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Grid lines representing rows -->
            <rect x="2" y="2" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="2" y="6" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="2" y="12" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- Plus sign for insert -->
            <rect x="7" y="9" width="2" height="2"/>
            <rect x="6" y="10" width="4" height="0.5"/>
            <rect x="7.5" y="8.5" width="0.5" height="3"/>

            <!-- Arrow pointing to insertion point -->
            <path d="M1 10 L2 9.5 L2 10.5 Z" fill="currentColor"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data.encode('utf-8'), wx.Size(16, 16))


def create_svg_delete_row_icon() -> wx.BitmapBundle:
    """Create SVG icon for delete row operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Grid lines representing rows -->
            <rect x="2" y="2" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="2" y="12" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- Row being deleted (highlighted) -->
            <rect x="2" y="6" width="12" height="2" fill="currentColor" opacity="0.3"/>
            <rect x="2" y="6" width="12" height="2" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- X mark for delete -->
            <line x1="6" y1="6.5" x2="8" y2="7.5" stroke="red" stroke-width="1.5"/>
            <line x1="8" y1="6.5" x2="6" y2="7.5" stroke="red" stroke-width="1.5"/>

            <!-- Arrow pointing to deletion -->
            <path d="M1 7 L2 6.5 L2 7.5 Z" fill="red"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_insert_column_icon() -> wx.BitmapBundle:
    """Create SVG icon for insert column operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Grid lines representing columns -->
            <rect x="2" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="6" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="12" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- Plus sign for insert -->
            <rect x="9" y="7" width="2" height="2"/>
            <rect x="10" y="6" width="0.5" height="4"/>
            <rect x="8.5" y="7.5" width="3" height="0.5"/>

            <!-- Arrow pointing to insertion point -->
            <path d="M10 1 L9.5 2 L10.5 2 Z" fill="currentColor"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


def create_svg_delete_column_icon() -> wx.BitmapBundle:
    """Create SVG icon for delete column operation."""
    svg_data = '''<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
        <g fill="currentColor">
            <!-- Grid lines representing columns -->
            <rect x="2" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>
            <rect x="12" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- Column being deleted (highlighted) -->
            <rect x="6" y="2" width="2" height="12" fill="currentColor" opacity="0.3"/>
            <rect x="6" y="2" width="2" height="12" fill="none" stroke="currentColor" stroke-width="1"/>

            <!-- X mark for delete -->
            <line x1="6.5" y1="6" x2="7.5" y2="8" stroke="red" stroke-width="1.5"/>
            <line x1="7.5" y1="6" x2="6.5" y2="8" stroke="red" stroke-width="1.5"/>

            <!-- Arrow pointing to deletion -->
            <path d="M7 1 L6.5 2 L7.5 2 Z" fill="red"/>
        </g>
    </svg>'''
    return wx.BitmapBundle.FromSVG(svg_data, wx.Size(16, 16))


# Icon registry for easy access
SVG_ICONS: Dict[str, callable] = {
    'paste': create_svg_paste_icon,
    'copy': create_svg_copy_icon,
    'cut': create_svg_cut_icon,
    'bold': create_svg_bold_icon,
    'italic': create_svg_italic_icon,
    'underline': create_svg_underline_icon,
    'align_left': create_svg_align_left_icon,
    'align_center': create_svg_align_center_icon,
    'align_right': create_svg_align_right_icon,
    'comma_style': create_svg_comma_style_icon,
    'percent_style': create_svg_percent_style_icon,
    'increase_decimal': create_svg_increase_decimal_icon,
    'decrease_decimal': create_svg_decrease_decimal_icon,
    'transpose': create_svg_transpose_icon,
    'insert': create_svg_insert_icon,
    'delete': create_svg_delete_icon,
    'insert_row': create_svg_insert_row_icon,
    'delete_row': create_svg_delete_row_icon,
    'insert_column': create_svg_insert_column_icon,
    'delete_column': create_svg_delete_column_icon,
    'find': create_svg_find_icon,
    'undo': create_svg_undo_icon,
    'redo': create_svg_redo_icon,
    'view_switch': create_svg_view_switch_icon,
}


def get_svg_icon(name: str, size: wx.Size = wx.Size(16, 16)) -> Optional[wx.BitmapBundle]:
    """Get an SVG icon by name.
    
    Args:
        name: Icon name from SVG_ICONS registry
        size: Desired icon size
        
    Returns:
        wx.BitmapBundle or None if icon not found
    """
    if name in SVG_ICONS:
        try:
            return SVG_ICONS[name]()
        except Exception as e:
            print(f"Failed to create SVG icon '{name}': {e}")
            return None
    return None


def get_svg_bitmap(name: str, size: wx.Size = wx.Size(16, 16)) -> Optional[wx.Bitmap]:
    """Get an SVG icon as a bitmap.
    
    Args:
        name: Icon name from SVG_ICONS registry
        size: Desired icon size
        
    Returns:
        wx.Bitmap or None if icon not found
    """
    bundle = get_svg_icon(name, size)
    if bundle:
        try:
            return bundle.GetBitmap(size)
        except Exception as e:
            print(f"Failed to get bitmap from SVG icon '{name}': {e}")
            return None
    return None 