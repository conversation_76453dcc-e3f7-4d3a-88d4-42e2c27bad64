<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,16H0V0H16Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.792,7.69C5.317,7.346,5,7.109,5,7.109v-1h.5V1.6h2V6.109H8V8.987A5.244,5.244,0,0,0,5.792,7.69ZM4,7.51v-1.4H3.5V1.6h-2V6.109H1V8.876A5.09,5.09,0,0,1,4,7.51Zm7.542-6.027L9.611,2l2.847,10.625,1.931-.518Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M1,8.877V1.6l.5-.5h2l.5.5V7.51a4.844,4.844,0,0,0-1,.2V2.1H2V8.147A5.4,5.4,0,0,0,1,8.877Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5,7.531V1.6l.5-.5h2l.5.5V8.987a5.415,5.415,0,0,0-1-.763V2.1H6V7.743A4.9,4.9,0,0,0,5,7.531Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12.025,1.354,11.412,1,9.48,1.518l-.353.613,2.848,10.625.613.353,1.931-.517.354-.613Zm.786,10.66-2.588-9.66.965-.258,2.589,9.659Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M3.443,15.494c-1.784-.351-2.93-1.113-2.93-1.955,0-2.568,1.767-4.657,3.939-4.657a3.953,3.953,0,0,1,3.559,2.663l-.063.243L3.637,15.442Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M8.516,11.489h0l-.011-.023A4.462,4.462,0,0,0,4.439,8.376C1.992,8.376,0,10.689,0,13.532,0,14.662,1.321,15.607,3.448,16l.414-.11,4.516-3.829.146-.555ZM7.005,10.98c-.224-.028-.45-.054-.681-.069a1.072,1.072,0,0,1,.188-.085A.609.609,0,0,1,7.005,10.98Zm-2.566-1.6a2.985,2.985,0,0,1,1.671.532A3.153,3.153,0,0,0,4.766,10.92a8.35,8.35,0,0,0-3.442.852A3.467,3.467,0,0,1,4.439,9.376Zm-.4,2.613a8.668,8.668,0,0,0-.6,1.356,6.16,6.16,0,0,0-.755-1.063A8.2,8.2,0,0,1,4.041,11.989ZM1,13.532c0-.261.25-.562.711-.834a4.666,4.666,0,0,1,1.258,2.166C1.677,14.515,1,13.961,1,13.532Zm3.216.746a7.584,7.584,0,0,1,1.055-2.385c.083,0,.158-.01.243-.01a10.774,10.774,0,0,1,1.419.092Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
