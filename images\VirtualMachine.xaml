<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M14,14.5H1l2-4H4v2l.5.5h6l.5-.5v-2h1Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.447,14.276,14,15H1l-.447-.724,2-4L3,10H4v1H3.309l-1.5,3H13.191l-1.5-3H11V10h1l.447.276Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M10.5,2.5v10h-6V2.5a1,1,0,0,1,1-1h4A1,1,0,0,1,10.5,2.5Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M10.5,13h-6L4,12.5V2.5A1.5,1.5,0,0,1,5.5,1h4A1.5,1.5,0,0,1,11,2.5v10ZM5,12h5V2.5A.5.5,0,0,0,9.5,2h-4a.5.5,0,0,0-.5.5Z" />
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M9,4V5H6V4ZM6,9H9V8H6Zm0,2H9V10H6Z" />
              </DrawingGroup>
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
