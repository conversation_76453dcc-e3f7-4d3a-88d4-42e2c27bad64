<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
      <System:Double x:Key="cls-1">0.75</System:Double>
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M0,0H16V16H0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <DrawingGroup Opacity="{DynamicResource cls-1}">
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M4.5,1.5a3,3,0,0,1,.15,5.99A3.042,3.042,0,0,0,3,7a.757.757,0,0,0-.15.01A3,3,0,0,1,4.5,1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M8.5,1.5v.94A4.478,4.478,0,0,1,9,4.5V6.88l3,3,.27.27-2.12,2.12L9,11.12l-.5-.5V14.5h6V1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M14.5,1h-6L8,1.5v.18a4.339,4.339,0,0,1,.5.76A4.478,4.478,0,0,1,9,4.5V2h5V14H9V11.12l-1-1V14.5l.5.5h6l.5-.5V1.5Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M12,6V3H11V6H10V7h1V8.88l1,1V7h1V6Zm-1,7h1V10.42l-1,1Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M7.3,6.59A3.458,3.458,0,0,0,8,4.5,3.5,3.5,0,1,0,2.19,7.12a2.4,2.4,0,0,1,.66-.11A.757.757,0,0,1,3,7a3.042,3.042,0,0,1,1.65.49,3.1,3.1,0,0,1,.51.44A3.428,3.428,0,0,0,6.59,7.3l3.56,3.55.7-.7ZM4.5,7A2.5,2.5,0,1,1,7,4.5,2.5,2.5,0,0,1,4.5,7Z" />
                <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M6,4V5H5V6H4V5H3V4H4V3H5V4Z" />
              </DrawingGroup>
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,11.5v4H.5v-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M5.5,11H5V10a2,2,0,0,0-4,0v1H.5l-.5.5v4l.5.5h5l.5-.5v-4ZM2,10a1,1,0,0,1,2,0v1H2Zm3,5H1V12H5Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
