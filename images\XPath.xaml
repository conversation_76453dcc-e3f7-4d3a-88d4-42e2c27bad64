<Viewbox Width="16 " Height="16" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:System="clr-namespace:System;assembly=mscorlib">
  <Rectangle Width="16 " Height="16">
    <Rectangle.Resources>
      <SolidColorBrush x:Key="canvas" Opacity="0" />
      <SolidColorBrush x:Key="light-defaultgrey-10" Color="#212121" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue-10" Color="#005dba" Opacity="0.1" />
      <SolidColorBrush x:Key="light-blue" Color="#005dba" Opacity="1" />
      <SolidColorBrush x:Key="light-defaultgrey" Color="#212121" Opacity="1" />
    </Rectangle.Resources>
    <Rectangle.Fill>
      <DrawingBrush Stretch="None">
        <DrawingBrush.Drawing>
          <DrawingGroup>
            <DrawingGroup x:Name="canvas">
              <GeometryDrawing Brush="{DynamicResource canvas}" Geometry="F1M16,0V16H0V0Z" />
            </DrawingGroup>
            <DrawingGroup x:Name="level_1">
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,13.5h4v-2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M9.5,9.5h4v-2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey-10}" Geometry="F1M5.5,3.5h4v-2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M1.5,9.5h4v-2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.5,10h-4L1,9.5v-2L1.5,7h4l.5.5v2ZM2,9H5V8H2Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue-10}" Geometry="F1M1.5,13.5h4v-2h-4Z" />
              <GeometryDrawing Brush="{DynamicResource light-blue}" Geometry="F1M5.5,14h-4L1,13.5v-2l.5-.5h4l.5.5v2ZM2,13H5V12H2Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M4,11H3V10H4Z" />
              <GeometryDrawing Brush="{DynamicResource light-defaultgrey}" Geometry="F1M13.5,10l.5-.5v-2L13.5,7H12V5.5L11.5,5H8V4H9.5l.5-.5v-2L9.5,1h-4L5,1.5v2l.5.5H7V5H3.5L3,5.5V7H4V6h7V7H9.5L9,7.5v2l.5.5H11v1H9.5l-.5.5v2l.5.5h4l.5-.5v-2l-.5-.5H12V10ZM6,3V2H9V3Zm7,9v1H10V12ZM10,9V8h3V9Z" />
            </DrawingGroup>
          </DrawingGroup>
        </DrawingBrush.Drawing>
      </DrawingBrush>
    </Rectangle.Fill>
  </Rectangle>
</Viewbox>
